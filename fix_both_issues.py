#!/usr/bin/env python3
"""
Fix both syntax errors in main.py (line 659 indentation and line 4013 missing except)
"""

import os
import shutil

def fix_both_issues():
    """Fix both syntax errors in main.py using direct line edits"""
    print("=== Fixing both syntax errors in main.py ===")
    
    # Create backup
    input_file = "main.py"
    backup_file = "main.py.backup"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 1. Fix line 659 (indentation issue)
    print("Fixing line 659 indentation issue...")
    if len(lines) >= 659:
        # Replace problematic line with properly indented version
        lines[658] = "    def auto_refresh_missing_account_info(self):\n"
        print("Line 659 fixed")
    
    # 2. Fix line 4013 (missing except block)
    print("Fixing line 4013 syntax error...")
    
    # Find the nearest try statement before line 4013
    try_line = None
    for i in range(4012, 3900, -1):
        if i < len(lines) and "try:" in lines[i]:
            try_line = i
            break
    
    if try_line is not None:
        # Get indentation level of the try statement
        indent = len(lines[try_line]) - len(lines[try_line].lstrip())
        indent_str = " " * indent
        
        # Find corresponding else statement
        else_line = None
        for i in range(try_line + 1, min(try_line + 50, len(lines))):
            if i < len(lines) and lines[i].strip() == "else:":
                else_line = i
                break
        
        if else_line is not None:
            # Insert except block just before the else
            except_block = f"{indent_str}except Exception as e:\n{indent_str}    print(f\"Error: {{e}}\")\n"
            lines.insert(else_line, except_block)
            print(f"Added except block before else at line {else_line+1}")
    
    # Write fixed content
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("\nBoth issues fixed in main.py")
    
    # Create batch file
    with open("run_fixed_app.bat", "w") as f:
        f.write("""@echo off
echo Running TG Checker with fixed code...
python main.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_fixed_app.bat")
    
    # Create Kurdish version
    with open("run_fixed_app_kurdish.bat", "w") as f:
        f.write("""@echo off
echo TG Checker - Barnama charasarkrawa...
python main.py
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_fixed_app_kurdish.bat")
    
    return True

if __name__ == "__main__":
    fix_both_issues() 