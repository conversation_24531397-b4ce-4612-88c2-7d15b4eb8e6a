#!/usr/bin/env python3
"""
Simple test script to run TG Checker on the 9 test links
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Test links
test_links = [
    "https://t.me/imperiamarket",
    "https://t.me/infocoindogroup", 
    "https://t.me/instaaccountbuying",
    "https://t.me/hyipinformer_com",
    "https://t.me/islamic_hacker_army",
    "https://t.me/RareHandle",
    "https://t.me/wallethuntersio",
    "https://t.me/beklopptundgeil",
    "https://t.me/belgieiswakkera"
]

def main():
    print("🔍 Running TG Checker test on 9 specific links...")
    print(f"📋 Testing {len(test_links)} links:")
    for i, link in enumerate(test_links, 1):
        print(f"   {i}. {link}")
    
    print("\n⚙️ Current Filter Settings:")
    print("   - Min Members: 400")
    print("   - Min Activity Time: 24 hours (UPDATED)")
    print("   - Min Total Messages: 100")
    
    print("\n📂 Expected Results:")
    print("   Groups_Valid_Filter: imperiamarket, infocoindogroup, instaaccountbuying")
    print("   Groups_Valid_Only: hyipinformer_com, islamic_hacker_army")
    print("   Topics_Groups_Only_Valid: RareHandle")
    print("   Channels_Only_Valid: wallethuntersio")
    print("   Invalid_Groups_Channels: beklopptundgeil, belgieiswakkera")
    
    print("\n🚀 Please run the TG Checker GUI and:")
    print("   1. Copy-paste the links from test_9_links.txt")
    print("   2. Click 'Start Checking'")
    print("   3. Wait for completion")
    print("   4. Check Results folders")
    
    # Write the links to a file for easy copy-paste
    with open("test_9_links.txt", "w", encoding="utf-8") as f:
        f.write("\n".join(test_links))
    
    print(f"\n📄 Links saved to: test_9_links.txt")
    print("✨ Ready for testing!")

if __name__ == "__main__":
    main() 