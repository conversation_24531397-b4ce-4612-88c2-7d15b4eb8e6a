2025-07-03 05:08:10,258 - INFO - Starting TG Checker application...
2025-07-03 05:08:10,642 - ERROR - Exception occurred: cannot import name 'qRegisterMetaType' from 'PyQt5.QtCore' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\PyQt5\QtCore.pyd)
2025-07-03 05:08:10,649 - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\run_tg_checker.py", line 37, in run_tg_checker
    import main
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 16, in <module>
    from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings, QMetaObject, Q_ARG, qRegisterMetaType
ImportError: cannot import name 'qRegisterMetaType' from 'PyQt5.QtCore' (C:\Users\<USER>\AppData\Local\Packages\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\LocalCache\local-packages\Python311\site-packages\PyQt5\QtCore.pyd)

2025-07-03 05:08:52,257 - INFO - Starting TG Checker application...
2025-07-03 05:08:53,773 - ERROR - Exception occurred: name 'pyqtSlot' is not defined
2025-07-03 05:08:53,778 - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\run_tg_checker.py", line 37, in run_tg_checker
    import main
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 140, in <module>
    class TGCheckerApp(QMainWindow):
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 734, in TGCheckerApp
    @pyqtSlot()
     ^^^^^^^^
NameError: name 'pyqtSlot' is not defined

2025-07-03 05:09:41,606 - INFO - Starting TG Checker application...
2025-07-03 05:09:43,114 - INFO - Calling main() function...
