#!/usr/bin/env python3
"""
Fix the syntax error on line 4013 in main.py (expected 'except' or 'finally' block)
"""

import os
import shutil

def fix_syntax_error():
    """Fix the syntax error on line 4013 in main.py"""
    print("Fixing syntax error on line 4013...")
    
    # Create a backup of the original file
    input_file = "main.py"
    backup_file = f"{input_file}.bak_syntax"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the problematic try block around line 4013
    start_line = None
    for i in range(4013 - 50, 4013 + 50):  # Look in a range around line 4013
        if i < 0 or i >= len(lines):
            continue
            
        if "try:" in lines[i]:
            # Found a try block, check if it has a corresponding except/finally
            try_line = i
            try_indent = len(lines[i]) - len(lines[i].lstrip())
            has_except = False
            
            # Look ahead for an except with the same indentation
            for j in range(try_line + 1, min(try_line + 20, len(lines))):
                if j >= len(lines):
                    break
                    
                if not lines[j].strip():  # Skip empty lines
                    continue
                    
                line_indent = len(lines[j]) - len(lines[j].lstrip())
                if line_indent == try_indent:
                    if lines[j].lstrip().startswith(("except", "finally")):
                        has_except = True
                        break
                    elif lines[j].lstrip().startswith("else:"):
                        # Found an else without an except - this is the problem
                        start_line = try_line
                        end_line = j
                        print(f"Found problematic try-else block at lines {start_line+1}-{end_line+1}")
                        break
            
            if not has_except and start_line is not None:
                break  # We found the problematic block
    
    if start_line is None:
        print("Could not find the problematic try block")
        return False
    
    # Fix the error by adding an except block before the else
    indent = len(lines[start_line]) - len(lines[start_line].lstrip())
    indent_str = " " * indent
    
    # Create an except block that will handle any exceptions
    except_line = f"{indent_str}except Exception as e:\n"
    except_body = f"{indent_str}    print(f\"An error occurred: {{e}}\")\n"
    
    # Insert the except block just before the else
    lines.insert(end_line, except_line)
    lines.insert(end_line + 1, except_body)
    
    print(f"Added except block at line {end_line+1}")
    
    # Write the fixed content back
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("Fixed syntax error on line 4013")
    print("Note: The indentation issue at line 659 still needs to be fixed separately")
    
    return True

if __name__ == "__main__":
    fix_syntax_error() 