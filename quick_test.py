#!/usr/bin/env python3
"""
🚀 Quick Test for TG Checker Classification
Tests our specific test groups to verify filter logic
"""

# Test Groups - just these 4 to verify the classification
test_groups = """https://t.me/hyipinformer_com
https://t.me/islamic_hacker_army
https://t.me/imperiamarket
https://t.me/infocoindogroup"""

# Save test groups to a file
with open("test_groups.txt", "w") as f:
    f.write(test_groups)

print("🚀 Created test_groups.txt with our test cases")
print("📋 Test Groups:")
for line in test_groups.strip().split('\n'):
    print(f"   • {line}")

print("\n🎯 Expected Results:")
print("   • hyipinformer_com → Groups_Valid_Only (should fail filters)")  
print("   • islamic_hacker_army → Groups_Valid_Only (should fail filters)")
print("   • imperiamarket → Groups_Valid_Filter (should pass filters)")
print("   • infocoindogroup → Groups_Valid_Filter (should pass filters)")

print("\n💡 Now run the main TG Checker tool and:")
print("   1. Load test_groups.txt as your input") 
print("   2. Check the results to see which folder each group goes to")
print("   3. Compare with expected results above") 