#!/usr/bin/env python3
"""
Emergency patch for TG Checker - Adds reset_joining_task_progress function
and modifies start_joining_task function to reset progress counters.
"""

import re
import os
import shutil
from datetime import datetime

def main():
    # Backup the file
    backup_file = f"main.py.emergency_backup"
    print(f"Creating backup as {backup_file}")
    shutil.copy("main.py", backup_file)
    
    # Read the main.py file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Find the reset_forwarder_task_progress function
    reset_func_pattern = r"def reset_forwarder_task_progress\(self, task_id\):[^@]*?raise"
    reset_func_match = re.search(reset_func_pattern, content, re.DOTALL)
    
    if not reset_func_match:
        print("Could not find reset_forwarder_task_progress function. Aborting.")
        return
        
    # Add our new reset_joining_task_progress function after it
    new_reset_func = '''
    def reset_joining_task_progress(self, task_id):
        """Reset joining task progress to allow re-running or new task creation."""
        try:
            conn = sqlite3.connect(self.joining_db_path, timeout=30)
            cursor = conn.cursor()
            
            query = """UPDATE joining_tasks SET 
                       current_index = 0, 
                       successful_joins = 0, 
                       failed_joins = 0,
                       updated_at = ?
                       WHERE id = ?"""
            
            cursor.execute(query, [datetime.now().isoformat(), task_id])
            conn.commit()
            conn.close()
            
            # Update local cache
            if task_id in self.joining_tasks:
                self.joining_tasks[task_id]['current_index'] = 0
                self.joining_tasks[task_id]['successful_joins'] = 0
                self.joining_tasks[task_id]['failed_joins'] = 0
            
            self.logger.info(f"Joining task {task_id} progress reset successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to reset joining task progress {task_id}: {str(e)}")
            raise
'''
    
    # Insert the new function after reset_forwarder_task_progress
    insert_pos = reset_func_match.end()
    content = content[:insert_pos] + new_reset_func + content[insert_pos:]
    
    # Now find the start_joining_task function
    start_func_pattern = r"def start_joining_task\(self, task_id\):.*?if task_id not in self\.joining_tasks:.*?return"
    start_func_match = re.search(start_func_pattern, content, re.DOTALL)
    
    if not start_func_match:
        print("Could not find start_joining_task function. Only adding the reset function.")
    else:
        # Add the reset code to start_joining_task
        reset_code = '''
            # ALWAYS reset task progress when manually starting (allows re-running any task)
            self.logger.info(f"Resetting joining task progress for manual restart: {task_id}")
            self.log_joining_message("info", task_id, "🔄 Resetting progress counters for clean start")
            self.reset_joining_task_progress(task_id)
            
            # Refresh task data after reset
            self.refresh_joining_tasks()'''
            
        # Find where to insert the reset code
        insert_pattern = r"(if task_id not in self\.joining_tasks:.*?return\s+)(\s+task = self\.joining_tasks\[task_id\])"
        insert_match = re.search(insert_pattern, content, re.DOTALL)
        
        if insert_match:
            content = content.replace(
                insert_match.group(0),
                insert_match.group(1) + reset_code + insert_match.group(2)
            )
    
    # Write the modified content back to main.py
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ Fix applied successfully!")
    print("Please restart the application for changes to take effect.")

if __name__ == "__main__":
    main() 