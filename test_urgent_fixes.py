"""
TEST URGENT FIXES
Quick test to verify all critical fixes are properly implemented and working.
"""

import os
import sys
import importlib.util
import threading
import time

def test_file_existence():
    """Test if all required files exist."""
    print("🔍 Testing file existence...")
    
    required_files = [
        "critical_network_fix.py",
        "URGENT_FIX_APPLY.py",
        "main.py"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} - Found")
        else:
            print(f"❌ {file} - Missing")
            all_exist = False
    
    return all_exist

def test_imports():
    """Test if all critical modules can be imported."""
    print("\n🔍 Testing imports...")
    
    try:
        # Test critical network fix import
        from critical_network_fix import CriticalNetworkFix, apply_critical_network_fix
        print("✅ critical_network_fix - Import successful")
        
        # Test required packages
        import telethon
        print("✅ telethon - Available")
        
        import PyQt5
        print("✅ PyQt5 - Available")
        
        import psutil
        print("✅ psutil - Available")
        
        import asyncio
        print("✅ asyncio - Available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_network_fix_functionality():
    """Test if the network fix can be instantiated and basic methods work."""
    print("\n🔍 Testing network fix functionality...")
    
    try:
        from critical_network_fix import CriticalNetworkFix
        
        # Create a mock main app
        class MockMainApp:
            def __init__(self):
                self.joining_tasks = {}
                
            def log_joining_message(self, level, task_id, message):
                print(f"[{level}] {task_id}: {message}")
        
        mock_app = MockMainApp()
        
        # Test instantiation
        network_fix = CriticalNetworkFix(mock_app)
        print("✅ CriticalNetworkFix instantiation - Success")
        
        # Test network connectivity check
        connectivity = network_fix._check_network_connectivity()
        if connectivity:
            print("✅ Network connectivity check - Connected")
        else:
            print("⚠️ Network connectivity check - No connection (this may be normal)")
        
        # Test resource monitoring methods
        try:
            status = network_fix._get_network_status()
            print("✅ Network status method - Working")
        except Exception as e:
            print(f"⚠️ Network status method - Error: {e}")
        
        # Test flood wait methods
        network_fix._set_flood_wait("test_phone", 60)
        is_waiting = network_fix._is_in_flood_wait("test_phone")
        remaining = network_fix._get_flood_wait_remaining("test_phone")
        
        if is_waiting and remaining > 0:
            print("✅ Flood wait management - Working")
        else:
            print("⚠️ Flood wait management - Unexpected behavior")
        
        return True
        
    except Exception as e:
        print(f"❌ Network fix functionality test failed: {e}")
        return False

def test_performance_monitoring():
    """Test if performance monitoring is working."""
    print("\n🔍 Testing performance monitoring...")
    
    try:
        import psutil
        
        # Test CPU monitoring
        cpu_percent = psutil.cpu_percent(interval=1)
        print(f"✅ CPU monitoring - Current: {cpu_percent}%")
        
        # Test memory monitoring
        memory = psutil.virtual_memory()
        print(f"✅ Memory monitoring - Current: {memory.percent}%")
        
        # Test if monitoring can detect high usage
        if cpu_percent > 80:
            print("⚠️ High CPU usage detected - Fix should help with this")
        elif cpu_percent < 30:
            print("✅ CPU usage is healthy")
        else:
            print("ℹ️ CPU usage is moderate")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance monitoring test failed: {e}")
        return False

def test_threading_system():
    """Test if the threading system is working properly."""
    print("\n🔍 Testing threading system...")
    
    try:
        from concurrent.futures import ThreadPoolExecutor
        import threading
        
        # Test ThreadPoolExecutor creation
        executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="Test")
        print("✅ ThreadPoolExecutor creation - Success")
        
        # Test task submission
        def test_task():
            time.sleep(0.1)
            return "Task completed"
        
        future = executor.submit(test_task)
        result = future.result(timeout=5)
        
        if result == "Task completed":
            print("✅ Task execution - Success")
        else:
            print("⚠️ Task execution - Unexpected result")
        
        executor.shutdown(wait=True)
        print("✅ Executor shutdown - Success")
        
        return True
        
    except Exception as e:
        print(f"❌ Threading system test failed: {e}")
        return False

def test_main_app_compatibility():
    """Test if the fixes are compatible with the main app."""
    print("\n🔍 Testing main app compatibility...")
    
    try:
        # Try to import main app
        main_spec = importlib.util.spec_from_file_location("main", "main.py")
        main_module = importlib.util.module_from_spec(main_spec)
        
        print("✅ Main app import preparation - Success")
        
        # Check if TGCheckerApp class exists
        if hasattr(main_module, 'TGCheckerApp') or 'class TGCheckerApp' in open('main.py').read():
            print("✅ TGCheckerApp class - Found")
        else:
            print("❌ TGCheckerApp class - Not found")
            return False
        
        # Check for required methods in main.py
        main_content = open('main.py').read()
        
        required_methods = [
            'start_joining_task',
            'stop_joining_task',
            'log_joining_message'
        ]
        
        for method in required_methods:
            if method in main_content:
                print(f"✅ Method {method} - Found")
            else:
                print(f"⚠️ Method {method} - Not found (may still work)")
        
        return True
        
    except Exception as e:
        print(f"❌ Main app compatibility test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive test of all urgent fixes."""
    print("🔥 URGENT FIXES COMPREHENSIVE TEST")
    print("=" * 50)
    
    test_results = []
    
    # Run all tests
    test_results.append(("File Existence", test_file_existence()))
    test_results.append(("Imports", test_imports()))
    test_results.append(("Network Fix Functionality", test_network_fix_functionality()))
    test_results.append(("Performance Monitoring", test_performance_monitoring()))
    test_results.append(("Threading System", test_threading_system()))
    test_results.append(("Main App Compatibility", test_main_app_compatibility()))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        if result:
            print(f"✅ {test_name}")
            passed += 1
        else:
            print(f"❌ {test_name}")
    
    print("\n" + "=" * 50)
    
    if passed == total:
        print("🎯 ALL TESTS PASSED! ✅")
        print("🎯 The urgent fixes are ready to apply!")
        print("🎯 Run: python URGENT_FIX_APPLY.py")
    elif passed >= total * 0.8:  # 80% pass rate
        print("⚠️ MOST TESTS PASSED")
        print(f"⚠️ {passed}/{total} tests successful")
        print("⚠️ You can try applying the fix, but monitor for issues")
    else:
        print("❌ MANY TESTS FAILED")
        print(f"❌ Only {passed}/{total} tests successful")
        print("❌ Please resolve the issues before applying fixes")
    
    print("=" * 50)

if __name__ == "__main__":
    run_comprehensive_test()
    input("\nPress Enter to exit...") 