@echo off
echo TG Checker Uninstall

echo This will remove the virtual environment and clean up temporary files.
echo Your database and configuration will not be removed.
echo.

set /p CONFIRM=Are you sure you want to proceed? (Y/N): 

if /i "%CONFIRM%"=="Y" (
    echo.
    echo Removing virtual environment...
    if exist "venv" (
        rmdir /s /q venv
        echo Virtual environment removed.
    ) else (
        echo Virtual environment not found.
    )
    
    echo.
    echo Cleaning up temporary files...
    if exist "__pycache__" (
        rmdir /s /q __pycache__
        echo Python cache files removed.
    )
    
    if exist "*.pyc" (
        del /f /q *.pyc
        echo Compiled Python files removed.
    )
    
    echo.
    echo Uninstall completed!
) else (
    echo.
    echo Uninstall cancelled.
)

pause 