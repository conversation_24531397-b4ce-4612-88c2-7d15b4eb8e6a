#!/usr/bin/env python3
"""
Enhanced Database Lock Fix Utility for TG Checker

This script provides a more comprehensive solution to fix database locking issues by:
1. Creating a backup of the current database
2. Forcefully terminating any lingering connections
3. Using advanced SQLite settings to prevent future locks
4. Performing database repair and optimization
5. Implementing a failsafe recovery mechanism

Usage:
    python enhanced_db_fix.py [--backup-only] [--force] [--no-prompt]
"""

import os
import sys
import time
import sqlite3
import logging
import shutil
import argparse
import random
from datetime import datetime
from contextlib import contextmanager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("enhanced_db_fix.log")
    ]
)

# Database file path
DB_PATH = "tg_checker.db"
BACKUP_DIR = "db_backups"

def create_backup(db_path=DB_PATH):
    """Create a backup of the database with timestamp."""
    try:
        # Create backup directory if it doesn't exist
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        # Generate backup filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"{os.path.basename(db_path)}_backup_{timestamp}")
        
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            logging.info(f"Created database backup at {backup_path}")
            return backup_path
        else:
            logging.warning(f"Database file {db_path} not found, cannot create backup")
            return None
    except Exception as e:
        logging.error(f"Failed to create backup: {str(e)}")
        return None

@contextmanager
def safe_connect(db_path=DB_PATH, timeout=120.0, isolation_level=None):
    """Safely connect to the database with proper settings to prevent locks."""
    conn = None
    try:
        # Connect with extended timeout and requested isolation level
        conn = sqlite3.connect(db_path, timeout=timeout, isolation_level=isolation_level)
        
        # Apply optimal settings for avoiding locks
        conn.execute("PRAGMA journal_mode=DELETE")  # Most compatible mode for lock resolution
        conn.execute("PRAGMA synchronous=OFF")      # Faster with less locking (temporary)
        conn.execute("PRAGMA locking_mode=EXCLUSIVE")  # Take exclusive control during repair
        conn.execute("PRAGMA busy_timeout=120000")  # Very long busy timeout (2 minutes)
        conn.execute("PRAGMA temp_store=MEMORY")    # Use memory for temporary storage
        
        yield conn
    except Exception as e:
        logging.error(f"Database connection error: {str(e)}")
        raise
    finally:
        if conn:
            try:
                conn.close()
            except Exception as e:
                logging.error(f"Error closing database connection: {str(e)}")

def check_and_fix_integrity(db_path=DB_PATH):
    """Check database integrity and attempt to fix any issues."""
    try:
        with safe_connect(db_path) as conn:
            cursor = conn.cursor()
            
            # Run integrity check
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            
            if integrity_result == "ok":
                logging.info("Database integrity check passed")
                return True
            else:
                logging.warning(f"Database integrity issues found: {integrity_result}")
                return False
    except Exception as e:
        logging.error(f"Error checking database integrity: {str(e)}")
        return False

def optimize_database(db_path=DB_PATH):
    """Optimize the database to prevent future locks."""
    try:
        # Wait a moment to ensure no active connections
        time.sleep(2)
        
        with safe_connect(db_path) as conn:
            # Run VACUUM to compact the database
            logging.info("Running VACUUM on database...")
            conn.execute("VACUUM")
            conn.commit()
            logging.info("Database VACUUM completed")
            
            # Run ANALYZE to optimize query planning
            logging.info("Running ANALYZE on database...")
            conn.execute("ANALYZE")
            conn.commit()
            logging.info("Database ANALYZE completed")
            
            # Set optimal journal mode for normal operation
            logging.info("Setting optimal database parameters...")
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA locking_mode=NORMAL")
            conn.execute("PRAGMA busy_timeout=60000")
            conn.execute("PRAGMA wal_autocheckpoint=1000")
            conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping
            conn.commit()
            
            logging.info("Database optimization completed")
            return True
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            logging.error("Database is still locked, unable to optimize")
            return False
        else:
            logging.error(f"Database error during optimization: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Error optimizing database: {str(e)}")
        return False

def force_unlock_database(db_path=DB_PATH):
    """Force unlock the database by creating a new one from backup."""
    try:
        # Create a backup first
        backup_path = create_backup(db_path)
        if not backup_path:
            return False
            
        logging.info("Attempting to force unlock database...")
        
        # Try to remove the database file
        if os.path.exists(db_path):
            try:
                # Wait to ensure no active operations
                time.sleep(3)
                os.remove(db_path)
                logging.info(f"Removed locked database file: {db_path}")
                time.sleep(1)  # Wait a bit after removal
            except Exception as e:
                logging.error(f"Failed to remove database file: {str(e)}")
                return False
        
        # Copy the backup to the original location
        try:
            shutil.copy2(backup_path, db_path)
            logging.info(f"Restored database from backup: {backup_path}")
            
            # Try to optimize the restored database
            if optimize_database(db_path):
                logging.info("Successfully optimized restored database")
                return True
            else:
                logging.warning("Could not optimize restored database")
                return True  # Still return True as we restored the database
        except Exception as e:
            logging.error(f"Failed to restore database: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Error during force unlock: {str(e)}")
        return False

def full_database_repair(db_path=DB_PATH, force=False):
    """Perform a full database repair with multiple strategies."""
    # Step 1: Create a backup
    backup_path = create_backup(db_path)
    if not backup_path:
        return False
    
    # Step 2: Try standard optimization
    logging.info("Attempting standard database optimization...")
    if optimize_database(db_path) and check_and_fix_integrity(db_path):
        logging.info("Standard optimization successful")
        return True
    
    # Step 3: If standard optimization fails or force=True, try force unlock
    if force or not optimize_database(db_path):
        logging.info("Standard optimization failed, attempting forced unlock...")
        if force_unlock_database(db_path):
            logging.info("Force unlock successful")
            return True
    
    # Step 4: If all else fails, create a new empty database
    if force:
        try:
            logging.warning("Creating new empty database as last resort...")
            
            # Rename the current database if it exists
            if os.path.exists(db_path):
                corrupt_path = f"{db_path}_corrupt_{int(datetime.now().timestamp())}"
                os.rename(db_path, corrupt_path)
                logging.info(f"Renamed corrupt database to: {corrupt_path}")
            
            # Create a new empty database with proper structure
            with safe_connect(db_path) as conn:
                cursor = conn.cursor()
                
                # Create accounts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                ''')
                
                # Create errors table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create blacklist table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deleted_accounts (
                        phone TEXT PRIMARY KEY,
                        deleted_at TEXT,
                        reason TEXT DEFAULT 'user_deleted'
                    )
                ''')
                
                conn.commit()
                
            logging.info("Created new empty database with proper structure")
            logging.warning("You will need to add your accounts again")
            return True
            
        except Exception as e:
            logging.error(f"Failed to create new database: {str(e)}")
            return False
    
    return False

def handle_command_line():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Enhanced Database Fix Utility for TG Checker')
    parser.add_argument('--backup-only', action='store_true', help='Only create a backup, don\'t modify database')
    parser.add_argument('--force', action='store_true', help='Use more aggressive fixing techniques')
    parser.add_argument('--no-prompt', action='store_true', help='Run without interactive prompts')
    return parser.parse_args()

def main():
    """Main function for the database fix utility."""
    args = handle_command_line()
    
    logging.info("Starting Enhanced Database Lock Fix Utility...")
    print("🔧 TG Checker Enhanced Database Fix Utility")
    print("===========================================")
    
    # Create a backup
    backup_path = create_backup()
    if backup_path:
        print(f"✅ Created backup at: {backup_path}")
    else:
        print("❌ Failed to create backup")
        if not args.no_prompt:
            input("Press Enter to exit...")
        return
    
    # Backup-only mode
    if args.backup_only:
        print("✅ Backup-only mode: backup created successfully")
        if not args.no_prompt:
            input("Press Enter to exit...")
        return
    
    # Full repair
    print("🔄 Attempting database repair and optimization...")
    if full_database_repair(force=args.force):
        print("✅ Database repair completed successfully!")
        logging.info("Database repair completed successfully")
    else:
        print("❌ Failed to fully repair database")
        logging.error("Failed to fully repair database")
        
        # Give manual recovery instructions
        print("\n📋 Manual Recovery Instructions:")
        print("--------------------------------")
        print(f"1. Make sure all TG Checker applications are closed")
        print(f"2. Rename or delete the current database file: {DB_PATH}")
        print(f"3. Copy your most recent backup: {backup_path}")
        print(f"4. Rename the copied backup to: {DB_PATH}")
        print(f"5. Start TG Checker again")
    
    if not args.no_prompt:
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
    except Exception as e:
        logging.error(f"Unexpected error: {str(e)}")
        print(f"❌ Unexpected error: {str(e)}")
        print("Please check the enhanced_db_fix.log file for details") 