#!/usr/bin/env python3
"""
Fix all syntax errors in main.py caused by malformed f-strings.
"""

import re

def fix_all_syntax_errors():
    """Fix all broken f-string patterns in main.py."""
    
    print("🔧 Fixing all syntax errors in main.py...")
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix all broken f-string patterns
    # Pattern 1: f.write(f"{link}\n (missing closing quotes and parenthesis)
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\n',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Pattern 2: f.write(f"{link} (missing \n and closing quotes)
    content = re.sub(
        r'f\.write\(f"\{link\}\s*$',
        r'f.write(f"{link}\n")',
        content,
        flags=re.MULTILINE
    )
    
    # Pattern 3: Fix any remaining broken patterns
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        # Check for broken f.write patterns
        if 'f.write(f"{link}' in line and not line.strip().endswith('")'):
            # This is a broken f-string, fix it
            if 'f.write(f"{link}' in line:
                # Replace the broken pattern with the correct one
                line = re.sub(r'f\.write\(f"\{link\}.*', r'f.write(f"{link}\n")', line)
        
        fixed_lines.append(line)
    
    # Join the lines back
    content = '\n'.join(fixed_lines)
    
    # Final cleanup - replace any remaining broken patterns
    content = re.sub(
        r'f\.write\(f"\{link\}[^"]*$',
        r'f.write(f"{link}\n")',
        content,
        flags=re.MULTILINE
    )
    
    # Write the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ All syntax errors fixed!")
    return True

if __name__ == "__main__":
    try:
        fix_all_syntax_errors()
        print("✅ Syntax fix completed!")
    except Exception as e:
        print(f"❌ Error fixing syntax: {e}")
        import traceback
        traceback.print_exc() 