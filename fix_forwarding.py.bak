#!/usr/bin/env python
"""
This script fixes the forwarding functionality in the TG Checker tool.

It applies multiple fixes:
1. Improves entity resolution for forwarding
2. Ensures proper usage of forward_messages() instead of send_message()
3. Handles forum topics correctly
4. Fixes the QVector<int> registration
5. <PERSON><PERSON><PERSON> handles db_path attribute issues
"""

import os
import re
import shutil
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("fix_forwarding")

def create_backup(file_path):
    """Create a backup of the file."""
    if not os.path.exists(file_path):
        logger.error(f"Error: File {file_path} not found")
        return False
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        logger.info(f"Created backup at {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Error creating backup: {str(e)}")
        return False

def check_improved_forwarding_module():
    """Check if the improved_forwarding.py module exists, create if not."""
    if not os.path.exists('improved_forwarding.py'):
        logger.info("Creating improved_forwarding.py module")
        try:
            with open('improved_forwarding.py', 'w') as f:
                f.write('''import logging
import asyncio
import re
from telethon.errors import (
    FloodWaitError, ChatAdminRequiredError, ChannelPrivateError,
    UserBannedInChannelError, PeerIdInvalidError, MessageIdInvalidError
)
from telethon.tl.types import InputPeerChannel, InputPeerUser, InputPeerChat
from telethon.tl.functions.channels import JoinChannelRequest, GetFullChannelRequest
from telethon.tl.functions.messages import GetHistoryRequest

class ImprovedForwarder:
    """Enhanced message forwarding functionality for Telegram."""
    
    def __init__(self, client, logger=None):
        """
        Initialize with a Telethon client.
        
        Args:
            client: A connected Telethon client
            logger: Optional logger
        """
        self.client = client
        self.logger = logger or logging.getLogger('improved_forwarder')
    
    async def resolve_entity_properly(self, entity_str):
        """
        Properly resolve an entity with multiple fallback mechanisms.
        
        Args:
            entity_str: Username, URL, or entity ID string
            
        Returns:
            Resolved entity or None if resolution fails
        """
        if not entity_str:
            return None
            
        # Remove common prefixes
        if entity_str.startswith('https://t.me/'):
            entity_str = entity_str.split('https://t.me/')[1]
        elif entity_str.startswith('@'):
            entity_str = entity_str[1:]
            
        # Split by slashes to handle topics
        parts = entity_str.split('/')
        username = parts[0]
        topic_id = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else None
        
        # Try multiple resolution methods
        entity = None
        try:
            # Method 1: Direct resolution
            try:
                self.logger.info(f"Attempting direct entity resolution for: {username}")
                entity = await self.client.get_entity(username)
                self.logger.info(f"Successfully resolved entity type: {type(entity).__name__}")
                return entity
            except Exception as e:
                self.logger.info(f"Direct resolution failed: {str(e)}, trying alternatives")
                
            # Method 2: Get via recent messages
            try:
                self.logger.info(f"Trying to resolve entity through messages")
                messages = await self.client.get_messages(username, limit=1)
                if messages and len(messages) > 0:
                    entity = await self.client.get_entity(messages[0].peer_id)
                    self.logger.info(f"Successfully resolved entity through messages")
                    return entity
            except Exception as e:
                self.logger.info(f"Message-based resolution failed: {str(e)}")
                
            # Method 3: Join channel first
            try:
                self.logger.info(f"Trying to join channel first")
                await self.client(JoinChannelRequest(username))
                entity = await self.client.get_entity(username)
                self.logger.info(f"Successfully joined and resolved entity")
                return entity
            except Exception as e:
                self.logger.info(f"Join-based resolution failed: {str(e)}")
                
            # If username is numeric, try as chat ID
            if username.isdigit():
                try:
                    chat_id = int(username)
                    self.logger.info(f"Trying numeric ID: {chat_id}")
                    entity = await self.client.get_entity(chat_id)
                    self.logger.info(f"Successfully resolved numeric entity")
                    return entity
                except Exception as e:
                    self.logger.info(f"Numeric ID resolution failed: {str(e)}")
                    
            return None
                
        except Exception as e:
            self.logger.error(f"Entity resolution failed: {str(e)}")
            return None
    
    async def get_message_by_id(self, source, message_id):
        """
        Get a specific message by ID with proper error handling.
        
        Args:
            source: Source entity (channel, chat, etc.)
            message_id: Message ID to retrieve
            
        Returns:
            Message object or None if retrieval fails
        """
        try:
            # Try to resolve the source entity first
            source_entity = await self.resolve_entity_properly(source)
            
            if not source_entity:
                self.logger.error(f"Could not resolve source entity: {source}")
                return None
                
            # Get the message
            message = await self.client.get_messages(source_entity, ids=int(message_id))
            
            if not message:
                self.logger.error(f"Message {message_id} not found in {source}")
                return None
                
            return message
            
        except Exception as e:
            self.logger.error(f"Error getting message: {str(e)}")
            return None
    
    async def forward_message(self, source, message_id, destination, topic_id=None):
        """
        Forward a message using the most reliable method.
        
        Args:
            source: Source entity (channel, chat, username)
            message_id: Message ID to forward
            destination: Destination entity (channel, chat, username)
            topic_id: Optional forum topic ID
            
        Returns:
            dict with status and result information
        """
        try:
            # Step 1: Resolve the source entity
            source_entity = await self.resolve_entity_properly(source)
            if not source_entity:
                return {"success": False, "error": "Could not resolve source entity"}
                
            # Step 2: Resolve the destination entity
            dest_entity = await self.resolve_entity_properly(destination)
            if not dest_entity:
                return {"success": False, "error": "Could not resolve destination entity"}
                
            # Step 3: Get the message
            message = await self.get_message_by_id(source_entity, message_id)
            if not message:
                return {"success": False, "error": f"Could not find message {message_id}"}
                
            # Step 4: Forward the message
            self.logger.info(f"Attempting to forward message {message_id} from {source} to {destination}")
            
            # Try multiple methods in sequence
            forwarded = None
            error = None
            
            # Method 1: Direct forwarding - most preserves the forwarded appearance
            try:
                # If we have a topic, try to use it as reply_to
                if topic_id:
                    try:
                        forwarded = await self.client.forward_messages(
                            entity=dest_entity,
                            messages=message,
                            from_peer=source_entity,
                            silent=False,
                            # Try the topic_id as reply_to (works in some Telethon versions)
                            reply_to=topic_id
                        )
                    except Exception as e:
                        # If reply_to fails, try getting a message in the topic
                        self.logger.info(f"Forward with topic_id as reply_to failed: {str(e)}")
                        error = str(e)
                        
                        # Get messages in this topic
                        try:
                            topic_msgs = await self.client.get_messages(
                                entity=dest_entity,
                                limit=1,
                                # Some versions of Telethon support reply_to for topics
                                reply_to=topic_id if hasattr(self.client, 'get_messages') and 'reply_to' in self.client.get_messages.__code__.co_varnames else None
                            )
                            
                            if topic_msgs and len(topic_msgs) > 0:
                                forwarded = await self.client.forward_messages(
                                    entity=dest_entity,
                                    messages=message,
                                    from_peer=source_entity,
                                    reply_to=topic_msgs[0].id
                                )
                        except Exception as topic_msg_error:
                            self.logger.info(f"Failed to get topic messages: {str(topic_msg_error)}")
                            # Fall through to next method
                else:
                    # Regular forwarding
                    forwarded = await self.client.forward_messages(
                        entity=dest_entity,
                        messages=message,
                        from_peer=source_entity
                    )
                    
                if forwarded:
                    self.logger.info(f"Successfully forwarded message using native forwarding")
                    return {"success": True, "method": "native_forward", "message": forwarded}
                    
            except Exception as e:
                error = str(e)
                self.logger.info(f"Native forwarding failed: {error}")
                # Fall through to next method
                
            # Method 2: Send message with content
            if not forwarded:
                try:
                    # Extract content from original message
                    text = message.text or message.message or ""
                    
                    # Format as forwarded
                    formatted_text = f"📨 Forwarded from {source}:\\n\\n{text}"
                    
                    # Send as new message
                    sent = await self.client.send_message(
                        entity=dest_entity,
                        message=formatted_text,
                        file=message.media if hasattr(message, 'media') else None,
                        reply_to=topic_id if topic_id else None
                    )
                    
                    if sent:
                        self.logger.info(f"Successfully sent message content as new message")
                        return {"success": True, "method": "content_send", "message": sent}
                        
                except Exception as e:
                    error = f"{error}, then content sending failed: {str(e)}"
                    self.logger.info(f"Content sending failed: {str(e)}")
                    # Fall through to final method
            
            # Method 3: Minimal text message
            if not forwarded:
                try:
                    # Send minimal text
                    sent = await self.client.send_message(
                        entity=destination,  # Use raw destination as last resort
                        message=f"⚠️ Forwarded message from {source}, ID: {message_id}\\n\\nUnable to forward content directly."
                    )
                    
                    if sent:
                        self.logger.info(f"Sent minimal text fallback")
                        return {"success": True, "method": "minimal_fallback", "message": sent}
                        
                except Exception as e:
                    error = f"{error}, then minimal fallback failed: {str(e)}"
                    self.logger.error(f"All forwarding methods failed: {error}")
                    
            return {"success": False, "error": error or "Unknown error in forwarding"}
            
        except Exception as e:
            self.logger.error(f"Error in forward_message: {str(e)}")
            return {"success": False, "error": str(e)}
''')
            logger.info("Successfully created improved_forwarding.py module")
            return True
        except Exception as e:
            logger.error(f"Error creating improved_forwarding.py: {str(e)}")
            return False
    return True

def fix_register_qt_types(content):
    """Fix the register_qt_types function to properly handle QVector<int> registration."""
    logger.info("Fixing QVector<int> registration")
    
    pattern = r'def register_qt_types\(\):.*?try:.*?except Exception as e:.*?print\(.*?\)'
    
    improved_function = '''def register_qt_types():
    """Register Qt types that need to be passed between threads."""
    try:
        # Try multiple methods to register QVector<int>
        try:
            # Method 1: Direct qRegisterMetaType
            from PyQt5.QtCore import qRegisterMetaType
            qRegisterMetaType("QVector<int>")
        except (ImportError, AttributeError):
            try:
                # Method 2: Using QMetaType
                from PyQt5.QtCore import QMetaType
                QMetaType.type("QVector<int>")
            except:
                # Method 3: Last resort - register through the QApplication
                from PyQt5.QtWidgets import QApplication
                app = QApplication.instance()
                if app:
                    # Tell the PyQt internals about this type via existing app instance
                    for typ in ['QVector<int>', 'QList<int>']:
                        try:
                            from PyQt5.QtCore import QMetaType
                            QMetaType.type(typ)
                        except:
                            pass
    except Exception as e:
        # Non-fatal error - we can continue without it
        print(f"Failed to register QVector<int>: {str(e)}")
        print("QVector<int> registration failed, some signals may not work correctly.")'''
    
    # Use regex with DOTALL to match across multiple lines
    updated_content = re.sub(pattern, improved_function, content, flags=re.DOTALL)
    
    if updated_content != content:
        logger.info("Successfully updated QVector<int> registration function")
    else:
        logger.warning("Could not update QVector<int> registration, pattern not found")
    
    return updated_content

def fix_log_activity_method(content):
    """Fix the _log_activity_to_ui method to safely check for attributes."""
    logger.info("Fixing _log_activity_to_ui method")
    
    # Find all instances of the method
    pattern = r'def _log_activity_to_ui\(self, message\):.*?timestamp = datetime.now\(\).strftime\("%Y-%m-%d %H:%M:%S"\).*?self.log_display.append\(.*?\).*?self.logger.info\(message\)'
    
    improved_function = '''def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'log_display') and self.log_display is not None:
                self.log_display.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            if hasattr(self, 'logger') and self.logger is not None:
                self.logger.info(message)
            
        except Exception as e:
            print(f"Failed to log activity: {str(e)}")'''
    
    # Use regex with DOTALL to match across multiple lines
    updated_content = re.sub(pattern, improved_function, content, flags=re.DOTALL)
    
    if updated_content != content:
        logger.info("Successfully updated _log_activity_to_ui method")
    else:
        logger.warning("Could not update _log_activity_to_ui method, pattern not found")
    
    return updated_content

def fix_process_task_function(content):
    """Fix the process_task function to use the improved forwarder."""
    logger.info("Fixing process_task function for better forwarding")
    
    # Find the start of the process_task function
    start_pattern = r'async def process_task\(\):'
    start_match = re.search(start_pattern, content)
    
    if not start_match:
        logger.error("Could not find process_task function")
        return content
    
    start_pos = start_match.start()
    
    # Find the end of the imports
    import_pattern = r'# Import locally to ensure availability.*?from telethon\.errors import FloodWaitError'
    import_match = re.search(import_pattern, content[start_pos:], re.DOTALL)
    
    if not import_match:
        logger.error("Could not find import section in process_task function")
        return content
    
    import_end = start_pos + import_match.end()
    
    # Add import for ImprovedForwarder
    improved_imports = "    # Import locally to ensure availability\n    from telethon.errors import FloodWaitError\n    from improved_forwarding import ImprovedForwarder"
    
    # Replace the imports
    updated_content = content[:start_pos] + content[start_pos:import_end].replace(
        "    # Import locally to ensure availability\n    from telethon.errors import FloodWaitError",
        improved_imports
    ) + content[import_end:]
    
    # Find where we initialize the client and add the forwarder initialization
    client_init_pattern = r'self\.log_forwarding\(f"Connected to Telegram with account {account_phone}"\)'
    client_init_match = re.search(client_init_pattern, updated_content[start_pos:], re.DOTALL)
    
    if client_init_match:
        client_init_end = start_pos + client_init_match.end()
        updated_content = updated_content[:client_init_end] + "\n        \n        # Initialize the improved forwarder\n        forwarder = ImprovedForwarder(client_wrapper.client, logger=logging.getLogger(f\"forwarder.{account_phone}\"))" + updated_content[client_init_end:]
    
    # Now let's find where we do the forwarding and replace it
    forward_pattern = r'# Forward message to group.*?self\.log_forwarding\(f"Forwarding to group: {group}"\).*?try:.*?# Using the wrapper'
    forward_match = re.search(forward_pattern, updated_content[start_pos:], re.DOTALL)
    
    if forward_match:
        forward_start = start_pos + forward_match.start()
        
        # Find where the try block for forwarding ends - look for the next exception handler
        try_end_pattern = r'except.*?FloodWaitError as flood_error:'
        try_end_match = re.search(try_end_pattern, updated_content[forward_start:], re.DOTALL)
        
        if try_end_match:
            forward_end = forward_start + try_end_match.start()
            
            # Replace the forwarding logic with our improved version
            improved_forwarding = '''            # Forward message to group
            self.log_forwarding(f"Forwarding to group: {group}")
            
            try:
                # Parse any topic ID from the group link
                topic_id = None
                original_group = group
                
                # Handle forum topics in the URL
                if '/' in group:
                    try:
                        if 't.me/' in group:
                            # Standard format: https://t.me/groupname/123
                            parts = group.split('t.me/')[1].split('/')
                            if len(parts) >= 2 and parts[1].isdigit():
                                group_username = parts[0]
                                topic_id = int(parts[1])
                                self.log_forwarding(f"Parsed topic URL: group={group_username}, topic_id={topic_id}", "info")
                    except Exception as parse_error:
                        self.log_forwarding(f"Error parsing topic URL {original_group}: {str(parse_error)}", "error")
                
                # Use improved forwarder to handle the message forwarding
                result = await forwarder.forward_message(
                    source=from_peer,
                    message_id=message_id,
                    destination=group,
                    topic_id=topic_id
                )
                
                if result["success"]:
                    self.log_forwarding(f"Successfully forwarded to {group}", "success")
                    
                    # Mark this group as processed
                    with self.db_lock:
                        # Get db_path safely
                        if not hasattr(self, 'account_manager') or not hasattr(self.account_manager, 'db_path'):
                            if hasattr(self, 'db_path'):
                                db_path = self.db_path
                            else:
                                db_path = "tg_checker.db"
                                # Set the db_path attribute for future use
                                self.db_path = db_path
                        else:
                            db_path = self.account_manager.db_path
                        
                        conn = sqlite3.connect(db_path)
                        cursor = conn.cursor()
                        cursor.execute(
                            "UPDATE task_groups SET processed = 1 WHERE task_id = ? AND group_link = ?",
                            (task_id, original_group)
                        )
                        cursor.execute(
                            "UPDATE forwarder_tasks SET current_index = current_index + 1, last_processed_time = ? WHERE id = ?",
                            (datetime.now().strftime("%Y-%m-%d %H:%M:%S"), task_id)
                        )
                        conn.commit()
                        conn.close()
                else:
                    error_msg = result.get("error", "Unknown error")
                    self.log_forwarding(f"Error forwarding to {group}: {error_msg}", "error")
                    
                    # Log the error for this group
                    if log_failed:
                        with self.db_lock:
                            # Get db_path safely
                            if not hasattr(self, 'account_manager') or not hasattr(self.account_manager, 'db_path'):
                                if hasattr(self, 'db_path'):
                                    db_path = self.db_path
                                else:
                                    db_path = "tg_checker.db"
                                    # Set the db_path attribute for future use
                                    self.db_path = db_path
                            else:
                                db_path = self.account_manager.db_path
                            
                            conn = sqlite3.connect(db_path)
                            cursor = conn.cursor()
                            cursor.execute(
                                "UPDATE task_groups SET error_message = ? WHERE task_id = ? AND group_link = ?",
                                (error_msg, task_id, original_group)
                            )
                            conn.commit()
                            conn.close()
                
                # Wait between forwarding messages
                if i < len(groups) - 1:  # Don't wait after the last group
                    # Determine sleep time
                    if use_random_sleep and sleep_min and sleep_max:
                        sleep_time = random.randint(sleep_min, sleep_max)
                    else:
                        sleep_time = random.randint(interval_min or 20, interval_max or 25)
                    
                    self.log_forwarding(f"Waiting {sleep_time} seconds before next forward...")
                    await asyncio.sleep(sleep_time)
                    
                    # After each N groups, take a longer break
                    if after_each_second and (i + 1) % 10 == 0:
                        self.log_forwarding(f"Taking a longer break after 10 groups ({after_each_second} seconds)...")
                        await asyncio.sleep(after_each_second)'''
            
            updated_content = updated_content[:forward_start] + improved_forwarding + updated_content[forward_end:]
    
    return updated_content

def fix_db_path_access(content):
    """Fix db_path access throughout the code for safety."""
    logger.info("Fixing db_path access")
    
    # Find all places where self.account_manager.db_path is used directly
    pattern = r'(conn = sqlite3\.connect\()self\.account_manager\.db_path(\))'
    
    replacement = r'\1self._get_db_path()\2'
    
    # Use regex to add safe db_path access
    updated_content = re.sub(pattern, replacement, content)
    
    # Add the _get_db_path helper method
    if '_get_db_path' not in content:
        # Find the class declaration
        class_def_pattern = r'class TGCheckerApp\(QMainWindow\):'
        class_match = re.search(class_def_pattern, updated_content)
        
        if class_match:
            class_pos = class_match.end()
            # Find the end of the class docstring
            docstring_pattern = r'""".*?"""'
            docstring_match = re.search(docstring_pattern, updated_content[class_pos:], re.DOTALL)
            
            if docstring_match:
                docstring_end = class_pos + docstring_match.end()
                
                # Add our helper method
                helper_method = '''
    
    def _get_db_path(self):
        """Safely get the database path, handling various attribute possibilities."""
        if hasattr(self, 'account_manager') and hasattr(self.account_manager, 'db_path'):
            return self.account_manager.db_path
        elif hasattr(self, 'db_path'):
            return self.db_path
        else:
            # Create a default path and store it
            db_path = "tg_checker.db"
            self.db_path = db_path
            return db_path'''
                
                updated_content = updated_content[:docstring_end] + helper_method + updated_content[docstring_end:]
    
    return updated_content

def main():
    """Main function to fix forwarding issues."""
    main_py = "main.py"
    
    if not os.path.exists(main_py):
        logger.error(f"File {main_py} not found")
        return
    
    # Create backup first
    if not create_backup(main_py):
        logger.error("Failed to create backup, aborting")
        return
    
    # Check/create improved forwarding module
    if not check_improved_forwarding_module():
        logger.error("Failed to create improved_forwarding.py module, aborting")
        return
    
    # Read the file content
    try:
        with open(main_py, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"Error reading {main_py}: {str(e)}")
        return
    
    # Apply fixes
    try:
        # 1. Fix QVector<int> registration
        content = fix_register_qt_types(content)
        
        # 2. Fix _log_activity_to_ui method
        content = fix_log_activity_method(content)
        
        # 3. Fix process_task function
        content = fix_process_task_function(content)
        
        # 4. Fix db_path access
        content = fix_db_path_access(content)
        
        # Write the changes back to the file
        with open(main_py, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"Successfully fixed {main_py}")
        logger.info("Restart the application to apply the changes.")
    except Exception as e:
        logger.error(f"Error fixing {main_py}: {str(e)}")

if __name__ == "__main__":
    main() 