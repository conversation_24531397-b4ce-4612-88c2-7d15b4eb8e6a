#!/usr/bin/env python3
"""
Fix both indentation error and syntax error in main.py in one script
"""

import os
import re
import shutil

def fix_all_issues():
    """Fix both the indentation error at line 659 and syntax error at line 4013"""
    print("=== Fixing all issues in main.py ===")
    
    # Create a backup of the original file
    input_file = "main.py"
    output_file = "main_fixed_complete.py"
    backup_file = f"{input_file}.bak_complete"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace tabs with spaces to standardize indentation
    content = content.replace('\t', '    ')
    
    # Fix the indentation error at line 659
    print("\nFixing indentation error at line 659...")
    
    # Use regex to find the problematic method and fix its indentation
    method_pattern = r'([ \t]*)def auto_refresh_missing_account_info\(self\):([^\n]*\n)((?:[ \t]+[^\n]*\n)*)'
    
    def fix_indent_match(match):
        # Set a fixed indentation for the method definition (4 spaces)
        method_def = "    def auto_refresh_missing_account_info(self):" + match.group(2)
        
        # Fix indentation for the method body
        body_lines = match.group(3).splitlines()
        fixed_body = []
        
        for line in body_lines:
            if line.strip():
                # Indent body lines with 8 spaces (4 for class + 4 for method body)
                fixed_body.append("        " + line.lstrip())
            else:
                fixed_body.append("")
        
        return method_def + "\n" + "\n".join(fixed_body)
    
    fixed_content = re.sub(method_pattern, fix_indent_match, content)
    
    # Fix the syntax error at line 4013
    print("Fixing syntax error around line 4013...")
    
    # Find all 'try' blocks without matching 'except' or 'finally' blocks
    # and with an 'else' block following
    lines = fixed_content.splitlines()
    modified_lines = lines.copy()
    
    for i in range(len(lines)):
        line = lines[i].rstrip()
        if not line.strip():
            continue
            
        # Check if this is a 'try' statement
        match = re.match(r'^(\s*)try\s*:', line)
        if match:
            indent = match.group(1)
            
            # Look for a matching 'except' or 'finally' at the same indentation level
            has_except = False
            has_else = False
            else_line = -1
            
            for j in range(i + 1, min(i + 20, len(lines))):
                if j >= len(lines):
                    break
                    
                curr_line = lines[j].rstrip()
                if not curr_line.strip():
                    continue
                
                # Check if the indentation matches the try block
                curr_match = re.match(r'^(\s*)', curr_line)
                curr_indent = curr_match.group(1)
                
                if len(curr_indent) == len(indent):
                    if re.match(r'^\s*except\b', curr_line) or re.match(r'^\s*finally\b', curr_line):
                        has_except = True
                        break
                    elif re.match(r'^\s*else\s*:', curr_line):
                        has_else = True
                        else_line = j
                        
                # If we hit a line with less indentation, we've exited the try block
                elif len(curr_indent) < len(indent):
                    break
            
            # If we found an 'else' without an 'except', insert an 'except' block
            if has_else and not has_except and else_line != -1:
                except_block = f"{indent}except Exception as e:\n{indent}    print(f\"Error: {{e}}\")"
                modified_lines.insert(else_line, except_block)
                print(f"Added missing except block before else at line {else_line+1}")
                
                # Adjust line numbers for subsequent checks
                lines = modified_lines.copy()
    
    # Join the modified lines back together
    fixed_content = "\n".join(modified_lines)
    
    # Write the fixed content to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"\nFixed all issues! Output saved to {output_file}")
    
    # Create batch files to run the fixed version
    with open("run_fixed_complete.bat", "w") as f:
        f.write(f"""@echo off
echo Running TG Checker with all fixes applied...
python {output_file}
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_fixed_complete.bat")
    
    # Create Kurdish version
    with open("run_fixed_complete_kurdish.bat", "w") as f:
        f.write(f"""@echo off
echo TG Checker - Barnama ba hamw charasarkanu...
python {output_file}
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_fixed_complete_kurdish.bat")
    
    # Option to replace the original file
    print("\nTo use the fixed version as the main file, run:")
    print(f"copy {output_file} {input_file}")
    
    return True

if __name__ == "__main__":
    fix_all_issues() 