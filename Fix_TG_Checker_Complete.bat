@echo off
chcp 65001 > nul
echo TG Checker Complete Fix
echo ===================================
echo This will fix both database locking and missing method issues.
echo Please wait, the process is starting...
echo.

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.7 or later and try again.
    pause
    exit /b 1
)

REM Run the complete fix utility
python fix_app_complete.py

echo.
if %errorlevel% equ 0 (
    echo All fixes have been applied successfully!
    echo You can now run TG Checker using the Run_Fixed_TG_Checker.bat file.
) else (
    echo An error occurred during the fix process.
    echo Please check the log messages above for details.
)

pause 