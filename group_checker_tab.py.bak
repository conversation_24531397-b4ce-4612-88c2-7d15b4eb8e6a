"""
Group Checker Tab for TG Checker application.
"""

import os
import sys
import asyncio
import threading
import json
import time
import random
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
                           QTextEdit, QTableWidget, QTableWidgetItem, QComboBox, 
                           QSpinBox, QCheckBox, QLineEdit, QGroupBox, QFormLayout,
                           QFileDialog, QHeaderView, QSplitter, QMessageBox,
                           QListWidget, QListWidgetItem, QProgressBar, QTabWidget)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt5.QtGui import QIcon, QFont, QTextCursor, QColor

from settings import get_settings
from group_checker import GroupChecker
from tg_client import TelegramClient
from telethon.errors import FloodWaitError

class GroupCheckerWorker(QThread):
    """Worker thread for checking groups."""
    
    progress_signal = pyqtSignal(int, int)  # current, total
    result_signal = pyqtSignal(dict)  # single result
    finished_signal = pyqtSignal(list)  # all results
    error_signal = pyqtSignal(str)  # error message
    
    def __init__(self, client, group_links, batch_size=10):
        super().__init__()
        self.client = client
        self.group_links = group_links
        self.batch_size = batch_size
        self.checker = None
        self.running = True
    
    def run(self):
        """Run the worker."""
        try:
            # Create event loop for asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run group checking
            self.checker = GroupChecker(self.client)
            results = []
            
            # Send total count
            total = len(self.group_links)
            self.progress_signal.emit(0, total)
            
            # Process in batches
            for i in range(0, total, self.batch_size):
                # Check if thread should stop
                if not self.running:
                    break
                    
                batch = self.group_links[i:i+self.batch_size]
                
                # Create tasks for each group in the batch
                batch_results = loop.run_until_complete(self._process_batch(batch))
                results.extend(batch_results)
                
                # Update progress
                self.progress_signal.emit(min(i + self.batch_size, total), total)
            
            # Automatically save results
            if self.checker and self.running:
                self.checker.save_results()
            
            # Emit all results
            self.finished_signal.emit(results)
            
        except Exception as e:
            self.error_signal.emit(f"Error checking groups: {str(e)}")
        finally:
            loop.close()
    
    async def _process_batch(self, batch):
        """Process a batch of groups with anti-flood protection."""
        results = []
        groups_checked_in_batch = 0
        
        # Process groups sequentially with anti-flood delays
        for i, group_link in enumerate(batch):
            if not self.running:
                break
            
            # Anti-flood delay: 1-1.5 seconds between checks
            if i > 0:  # Don't delay before the first check in batch
                delay = random.uniform(1.0, 1.5)
                await asyncio.sleep(delay)
            
            # Check the group
            result = await self._check_group(group_link)
            results.append(result)
            groups_checked_in_batch += 1
            
            # Emit single result signal
            self.result_signal.emit(result)
            
            # Check if we hit flood protection and need to stop
            if result.get("status") == "flood_wait":
                break
            
            # Wait between batches for additional safety
            await asyncio.sleep(2)
        
        return results
    
    async def _check_group(self, group_link):
        """Check a single group."""
        try:
            if not self.running:
                return {"status": "cancelled", "group_id": group_link}
                
            result = await self.checker.check_group(group_link)
            return result
        except Exception as e:
            return {
                "group_id": group_link,
                "status": "error",
                "error": str(e)
            }
    
    def stop(self):
        """Stop the worker."""
        self.running = False

class GroupCheckerTab(QWidget):
    """Group Checker tab for the TG Checker application."""
    
    def __init__(self, account_manager, logger):
        super().__init__()
        self.account_manager = account_manager
        self.logger = logger
        self.settings = get_settings()
        self.client = None
        self.worker = None
        self.results = []
        
        # Set up the UI
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface."""
        main_layout = QVBoxLayout(self)
        
        # Create dashboard section
        dashboard_group = QGroupBox("Dashboard")
        dashboard_layout = QVBoxLayout()
        
        # Start checker button
        self.start_checker_button = QPushButton("Start Checker")
        self.start_checker_button.setMinimumHeight(40)
        self.start_checker_button.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.start_checker_button.clicked.connect(self.start_checking)
        dashboard_layout.addWidget(self.start_checker_button)
        
        # Add Groups section
        groups_layout = QHBoxLayout()
        
        self.group_links_input = QTextEdit()
        self.group_links_input.setPlaceholderText("Enter group links, one per line.\nExample: https://t.me/example or @example")
        groups_layout.addWidget(self.group_links_input, 3)
        
        # Currently being analyzed
        current_layout = QVBoxLayout()
        current_layout.addWidget(QLabel("Currently Being Analyzed:"))
        self.current_group_label = QLabel("None")
        self.current_group_label.setStyleSheet("font-weight: bold;")
        current_layout.addWidget(self.current_group_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        current_layout.addWidget(self.progress_bar)
        
        # Add buttons for loading and clearing links
        btn_layout = QHBoxLayout()
        self.load_links_button = QPushButton("Load from File")
        self.clear_links_button = QPushButton("Clear Links")
        self.stop_button = QPushButton("Stop")
        
        self.load_links_button.clicked.connect(self.load_links_from_file)
        self.clear_links_button.clicked.connect(self.clear_links)
        self.stop_button.clicked.connect(self.stop_checking)
        
        self.stop_button.setEnabled(False)
        
        btn_layout.addWidget(self.load_links_button)
        btn_layout.addWidget(self.clear_links_button)
        btn_layout.addWidget(self.stop_button)
        current_layout.addLayout(btn_layout)
        
        groups_layout.addLayout(current_layout, 2)
        dashboard_layout.addLayout(groups_layout)
        
        dashboard_group.setLayout(dashboard_layout)
        main_layout.addWidget(dashboard_group)
        
        # Create splitter for settings and results
        splitter = QSplitter(Qt.Horizontal)
        
        # Settings section (left panel)
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        
        # Filter settings
        filter_group = QGroupBox("Filter Settings")
        filter_layout = QFormLayout()
        
        # Min members
        self.min_members_spin = QSpinBox()
        self.min_members_spin.setRange(0, 1000000)
        self.min_members_spin.setValue(self.settings.get("min_members", 500))
        filter_layout.addRow("Min Members:", self.min_members_spin)
        
        # Min chat message time (renamed from max_last_message_hours)
        self.max_hours_spin = QSpinBox()
        self.max_hours_spin.setRange(1, 720)
        self.max_hours_spin.setValue(self.settings.get("max_last_message_hours", 1))
        filter_layout.addRow("Min Chat Message Time (hours):", self.max_hours_spin)
        
        # Min total messages
        self.min_messages_spin = QSpinBox()
        self.min_messages_spin.setRange(0, 10000)
        self.min_messages_spin.setValue(self.settings.get("min_total_messages", 100))
        filter_layout.addRow("Min Total Messages:", self.min_messages_spin)
        
        # Language filter
        self.language_combo = QComboBox()
        self.language_combo.addItem("All Languages", "all")
        for lang_code in self.settings.get("supported_languages", ["en", "ar", "ru", "es", "fr", "de", "zh", "hi", "pt", "tr", "ku"]):
            self.language_combo.addItem(lang_code.upper(), lang_code)
        self.language_combo.setCurrentText(self.settings.get("language_filter", "all").upper())
        filter_layout.addRow("Language Filter:", self.language_combo)
        
        # Additional checks
        self.public_only_check = QCheckBox()
        self.public_only_check.setChecked(self.settings.get("public_groups_only", True))
        filter_layout.addRow("Public Groups Only:", self.public_only_check)
        
        self.check_post_freq_check = QCheckBox()
        self.check_post_freq_check.setChecked(self.settings.get("check_post_frequency", True))
        filter_layout.addRow("Check Post Frequency:", self.check_post_freq_check)
        
        self.check_real_users_check = QCheckBox()
        self.check_real_users_check.setChecked(self.settings.get("check_real_users", True))
        filter_layout.addRow("Check Real Users %:", self.check_real_users_check)
        
        # Batch size
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 50)
        self.batch_size_spin.setValue(self.settings.get("batch_size", 10))
        filter_layout.addRow("Batch Size:", self.batch_size_spin)
        
        filter_group.setLayout(filter_layout)
        settings_layout.addWidget(filter_group)
        
        # Account selection
        account_group = QGroupBox("Account Selection")
        account_layout = QFormLayout()
        
        self.account_combo = QComboBox()
        self.update_account_list()
        
        account_layout.addRow("Use Account:", self.account_combo)
        
        # Add button to refresh accounts
        self.refresh_accounts_button = QPushButton("Refresh Accounts")
        self.refresh_accounts_button.clicked.connect(self.update_account_list)
        account_layout.addRow("", self.refresh_accounts_button)
        
        account_group.setLayout(account_layout)
        settings_layout.addWidget(account_group)
        
        # Add settings widget to splitter
        splitter.addWidget(settings_widget)
        
        # Results section (right panel)
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        
        # Create tabs for different result categories
        self.results_tabs = QTabWidget()
        
        # Create tabs for each category
        self.good_tab = self.create_result_tab("Groups Valid & Filter ON")
        self.valid_only_tab = self.create_result_tab("Groups Valid Only")
        self.topics_tab = self.create_result_tab("Topics Groups Only Valid")
        self.channels_tab = self.create_result_tab("Channels Only Valid")
        self.invalid_tab = self.create_result_tab("Invalid Groups/Channels")
        
        # Add tabs to tab widget
        self.results_tabs.addTab(self.good_tab, "Groups Valid & Filter ON")
        self.results_tabs.addTab(self.valid_only_tab, "Groups Valid Only")
        self.results_tabs.addTab(self.topics_tab, "Topics Groups Only Valid")
        self.results_tabs.addTab(self.channels_tab, "Channels Only Valid")
        self.results_tabs.addTab(self.invalid_tab, "Invalid Groups/Channels")
        
        results_layout.addWidget(self.results_tabs)
        
        # Add real-time results summary section
        summary_group = QGroupBox("Results Summary")
        summary_layout = QFormLayout()
        
        # Initialize count labels with live updates
        self.good_count_label = QLabel("0")
        self.valid_only_count_label = QLabel("0")
        self.topics_count_label = QLabel("0")
        self.channels_count_label = QLabel("0")
        self.invalid_count_label = QLabel("0")
        
        # Style the count labels to make them more prominent
        for label in [self.good_count_label, self.valid_only_count_label, 
                     self.topics_count_label, self.channels_count_label, 
                     self.invalid_count_label]:
            label.setStyleSheet("font-weight: bold; font-size: 14px; color: #2c3e50;")
        
        summary_layout.addRow("Groups Valid & Filter ON:", self.good_count_label)
        summary_layout.addRow("Groups Valid Only:", self.valid_only_count_label)
        summary_layout.addRow("Topics Groups Only Valid:", self.topics_count_label)
        summary_layout.addRow("Channels Only Valid:", self.channels_count_label)
        summary_layout.addRow("Invalid Groups/Channels:", self.invalid_count_label)
        
        # Add last updated timestamp
        self.last_updated_label = QLabel("Last updated: Never")
        self.last_updated_label.setStyleSheet("font-size: 10px; color: #7f8c8d; font-style: italic;")
        summary_layout.addRow("", self.last_updated_label)
        
        summary_group.setLayout(summary_layout)
        results_layout.addWidget(summary_group)
        
        # Add log area
        log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        results_layout.addWidget(log_group)
        
        # Add results widget to splitter
        splitter.addWidget(results_widget)
        
        # Set the splitter sizes
        splitter.setSizes([300, 700])
        
        main_layout.addWidget(splitter, 1)  # Give the splitter more weight
        
        # Initialize result counters for real-time updates
        self.result_counts = {
            "good": 0,
            "valid_only": 0,
            "topics": 0,
            "channels": 0,
            "invalid": 0
        }
    
    def create_result_tab(self, title):
        """Create a tab for a result category."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create table for results
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["Group", "Members", "Last Active", "Messages", "Details"])
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(table)
        
        return tab
    
    def update_account_list(self):
        """Update the account dropdown list."""
        self.account_combo.clear()
        
        active_accounts = self.account_manager.get_active_accounts()
        if not active_accounts:
            self.account_combo.addItem("No active accounts found")
            self.start_checker_button.setEnabled(False)
            return
            
        for account in active_accounts:
            display_name = f"{account.get('phone')} ({account.get('status', 'unknown')})"
            self.account_combo.addItem(display_name, account.get('phone'))
        
        self.start_checker_button.setEnabled(True)
    
    def load_links_from_file(self):
        """Load group links from a file."""
        file_path, _ = QFileDialog.getOpenFileName(self, "Load Group Links", "", "Text Files (*.txt);;All Files (*)")
        if not file_path:
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                links = f.read().strip()
            
            self.group_links_input.setText(links)
            self.log_message(f"Loaded {len(links.splitlines())} group links from {file_path}")
            
        except Exception as e:
            self.log_message(f"Error loading links: {str(e)}", level="error")
            QMessageBox.critical(self, "Error", f"Failed to load links: {str(e)}")
    
    def clear_links(self):
        """Clear all group links."""
        self.group_links_input.clear()
        
    def clear_result_tables(self):
        """Clear all result tables and reset counters."""
        # Clear all result tables
        for tab in [self.good_tab, self.valid_only_tab, self.topics_tab, self.channels_tab, self.invalid_tab]:
            table = tab.findChild(QTableWidget)
            if table:
                table.setRowCount(0)
        
        # Reset result counters
        self.result_counts = {
            "good": 0,
            "valid_only": 0,
            "topics": 0,
            "channels": 0,
            "invalid": 0
        }
        
        # Reset counter labels
        self.good_count_label.setText("0")
        self.valid_only_count_label.setText("0")
        self.topics_count_label.setText("0")
        self.channels_count_label.setText("0")
        self.invalid_count_label.setText("0")
        
        # Reset timestamp
        self.last_updated_label.setText("Last updated: Never")
        
        # Reset tab titles
        self.results_tabs.setTabText(0, "Groups Valid & Filter ON")
        self.results_tabs.setTabText(1, "Groups Valid Only")
        self.results_tabs.setTabText(2, "Topics Groups Only Valid")
        self.results_tabs.setTabText(3, "Channels Only Valid")
        self.results_tabs.setTabText(4, "Invalid Groups/Channels")
        
        # Update tab titles with counts for better visibility
        self.update_tab_titles()
        
        # Clear results list
        self.results = []
    
    def start_checking(self):
        """Start checking groups."""
        # Get selected account
        if self.account_combo.count() == 0 or self.account_combo.currentData() is None:
            self.log_message("No active account selected", level="error")
            QMessageBox.warning(self, "Error", "Please select an active account")
            return
        
        # Get group links
        links_text = self.group_links_input.toPlainText().strip()
        if not links_text:
            self.log_message("No group links provided", level="error")
            QMessageBox.warning(self, "Error", "Please enter group links")
            return
            
        group_links = [link.strip() for link in links_text.splitlines() if link.strip()]
        
        # Save settings
        self.save_settings()
        
        # Clear previous results
        self.results = []
        self.clear_result_tables()
        
        # Set up client
        try:
            phone = self.account_combo.currentData()
            account = self.account_manager.get_account(phone)
            
            if not account:
                self.log_message(f"Account not found: {phone}", level="error")
                QMessageBox.critical(self, "Error", f"Account not found: {phone}")
                return
                
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            session_file = account.get("session_file")
            
            self.log_message(f"Creating client for account {phone}")
            
            # Create the client in a background thread
            threading.Thread(target=self._setup_client_thread, 
                           args=(api_id, api_hash, phone, session_file, group_links),
                           daemon=True).start()
            
            # Update UI
            self.start_checker_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            
        except Exception as e:
            self.log_message(f"Error starting group checker: {str(e)}", level="error")
            QMessageBox.critical(self, "Error", f"Failed to start group checker: {str(e)}")
    
    def _setup_client_thread(self, api_id, api_hash, phone, session_file, group_links):
        """Set up the client in a background thread."""
        loop = None
        try:
            # Create event loop for asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Create and connect client
            self.client = TelegramClient(api_id, api_hash, phone, session_file, self.logger)
            
            # Connect with timeout
            try:
                connect_task = loop.create_task(self.client.connect())
                connected = loop.run_until_complete(asyncio.wait_for(connect_task, timeout=15))
            except asyncio.TimeoutError:
                self.log_message(f"Connection timeout for {phone}", level="error")
                return
            except Exception as e:
                self.log_message(f"Failed to connect client for {phone}: {str(e)}", level="error")
                return
            
            if not connected:
                self.log_message(f"Failed to connect client for {phone}", level="error")
                return
                
            try:
                authorized = loop.run_until_complete(self.client.check_authorization())
                if not authorized:
                    self.log_message(f"Client not authorized for {phone}", level="error")
                    return
            except Exception as e:
                self.log_message(f"Authorization check failed for {phone}: {str(e)}", level="error")
                return
            
            self.log_message(f"Client connected for {phone}")
            
            # Start the worker
            self.start_worker(group_links)
            
        except Exception as e:
            self.log_message(f"Error setting up client: {str(e)}", level="error")
        finally:
            if loop and not loop.is_closed():
                loop.close()
    
    def start_worker(self, group_links):
        """Start the worker thread for checking groups."""
        # Create and start worker
        self.worker = GroupCheckerWorker(
            self.client.client,  # Get the actual Telethon client
            group_links,
            batch_size=self.batch_size_spin.value()
        )
        
        # Connect signals
        self.worker.progress_signal.connect(self.update_progress)
        self.worker.result_signal.connect(self.process_result)
        self.worker.finished_signal.connect(self.checking_finished)
        self.worker.error_signal.connect(self.handle_error)
        
        # Start worker
        self.worker.start()
        self.log_message(f"Started checking {len(group_links)} groups")
    
    def stop_checking(self):
        """Stop checking groups."""
        if self.worker and self.worker.isRunning():
            self.log_message("Stopping group checker...")
            self.worker.stop()
            self.worker.wait(3000)  # Wait up to 3 seconds
            
            if self.worker.isRunning():
                self.worker.terminate()
            
            self.log_message("Group checker stopped")
        
        # Update UI
        self.start_checker_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.current_group_label.setText("Finished")
    
    def update_progress(self, current, total):
        """Update the progress bar."""
        percent = int(current / total * 100) if total > 0 else 0
        self.progress_bar.setValue(percent)
    
    def process_result(self, result):
        """Process a single group check result."""
        if not result:
            return
        
        # Add to results list
        self.results.append(result)
        
        # Update currently analyzing label
        group_id = result.get("group_id", "unknown")
        title = result.get("title", group_id)
        self.current_group_label.setText(title)
        
        # Add to appropriate result table
        self.add_result_to_table(result)
        
        # Log and color feedback
        status = result.get("status", "unknown")
        error_type = result.get("error_type", "")
        error = result.get("error", result.get("reason", "Unknown error"))
        
        if status == "good":
            self.log_message(f"Good group: {title}")
        elif status == "topic":
            self.log_message(f"Topics group: {title}")
        elif status == "channel":
            self.log_message(f"Channel: {title}")
        elif error_type == "invalid_group":
            # Group not found or invalid
            self.log_message(f"🔴 Group not found: {title} - {error}", level="error")
        elif error_type == "join_request":
            self.log_message(f"🟡 Join request needed: {title} - {error}", level="warning")
        elif error_type == "account_issue":
            self.log_message(f"⚠️ Account Issue: {title} - {error}", level="warning")
        else:
            self.log_message(f"Error checking group: {title} - {error}", level="error")
        
        # Update real-time result counters
        if status == "good":
            self.result_counts["good"] += 1
            self.good_count_label.setText(str(self.result_counts["good"]))
        elif status == "invalid" and "non_filter_groups" in error:
            self.result_counts["valid_only"] += 1
            self.valid_only_count_label.setText(str(self.result_counts["valid_only"]))
        elif status == "topic":
            self.result_counts["topics"] += 1
            self.topics_count_label.setText(str(self.result_counts["topics"]))
        elif status == "channel":
            self.result_counts["channels"] += 1
            self.channels_count_label.setText(str(self.result_counts["channels"]))
        elif status == "error" or status == "invalid":
            self.result_counts["invalid"] += 1
            self.invalid_count_label.setText(str(self.result_counts["invalid"]))
        
        # Update tab titles with counts for better visibility
        self.update_tab_titles()
        
        # Update last updated timestamp
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.last_updated_label.setText(f"Last updated: {current_time}")
    
    def add_result_to_table(self, result):
        """Add a result to the appropriate result table."""
        # Determine which table to use
        status = result.get("status", "unknown")
        error_type = result.get("error_type", "")
        error = result.get("error", result.get("reason", ""))
        
        if status == "good":
            table = self.good_tab.findChild(QTableWidget)
        elif status == "invalid" and "non_filter_groups" in error:
            table = self.valid_only_tab.findChild(QTableWidget)
        elif status == "topic":
            table = self.topics_tab.findChild(QTableWidget)
        elif status == "channel":
            table = self.channels_tab.findChild(QTableWidget)
        elif status == "error" or status == "invalid":
            table = self.invalid_tab.findChild(QTableWidget)
        else:
            # Unknown status, skip
            return
        
        # Add to table
        row = table.rowCount()
        table.insertRow(row)
        
        # Get values
        group_id = result.get("group_id", "")
        title = result.get("title", group_id)
        member_count = str(result.get("member_count", 0))
        last_message_date = result.get("last_message_date", "")
        total_messages = str(result.get("total_messages", 0))
        
        # Format last message date
        if last_message_date:
            try:
                dt = datetime.fromisoformat(last_message_date.replace('Z', '+00:00'))
                hours = (datetime.now() - dt.replace(tzinfo=None)).total_seconds() / 3600
                last_message = f"{int(hours)}h ago"
            except:
                last_message = last_message_date
        else:
            last_message = "N/A"
        
        # Details
        details = ""
        if error_type == "invalid_group":
            details = "🔴 Group not found"
        elif error_type == "join_request":
            details = "🟡 Join request needed"
        elif error_type == "account_issue":
            details = "⚠️ Account Issue"
        elif status == "invalid" or status == "error":
            details = error
        elif status == "good":
            post_freq = result.get("post_frequency", 0)
            real_users = result.get("real_users_percentage", 0)
            if post_freq > 0:
                details = f"Posts: {post_freq:.1f}/day"
            if real_users > 0:
                if details:
                    details += ", "
                details += f"Real users: {real_users:.1f}%"
        
        # Create table items
        group_item = QTableWidgetItem(title)
        member_item = QTableWidgetItem(member_count)
        last_message_item = QTableWidgetItem(last_message)
        total_messages_item = QTableWidgetItem(total_messages)
        details_item = QTableWidgetItem(details)
        
        # Set items
        table.setItem(row, 0, group_item)
        table.setItem(row, 1, member_item)
        table.setItem(row, 2, last_message_item)
        table.setItem(row, 3, total_messages_item)
        table.setItem(row, 4, details_item)
    
    def checking_finished(self, results):
        """Handle completion of group checking."""
        # Cleanup client connection
        if self.client:
            loop = None
            try:
                # Create event loop for asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Disconnect client with timeout
                disconnect_task = loop.create_task(self.client.disconnect())
                loop.run_until_complete(asyncio.wait_for(disconnect_task, timeout=5))
                self.log_message("Client disconnected successfully")
                
            except asyncio.TimeoutError:
                self.log_message("Client disconnect timeout", level="warning")
            except Exception as e:
                self.log_message(f"Error disconnecting client: {str(e)}", level="error")
            finally:
                if loop and not loop.is_closed():
                    loop.close()
                self.client = None
        
        # Update UI
        self.start_checker_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.current_group_label.setText("Finished")
        
        # Show summary
        good_count = sum(1 for r in self.results if r.get("status") == "good")
        valid_only_count = sum(1 for r in self.results if r.get("status") == "invalid" and "non_filter_groups" in r.get("error", ""))
        topic_count = sum(1 for r in self.results if r.get("status") == "topic")
        channel_count = sum(1 for r in self.results if r.get("status") == "channel")
        invalid_count = sum(1 for r in self.results if r.get("status") == "error" or (r.get("status") == "invalid" and "non_filter_groups" not in r.get("error", "")))
        
        summary = f"Finished checking groups: "
        summary += f"{good_count} groups passed all filters, {valid_only_count} groups valid only, "
        summary += f"{topic_count} topics, {channel_count} channels, {invalid_count} invalid"
        
        self.log_message(summary)
    
    def handle_error(self, error_message):
        """Handle error from worker."""
        self.log_message(error_message, level="error")
    
    def save_settings(self):
        """Save the current settings."""
        settings = self.settings
        
        # Update settings with UI values
        settings.set("min_members", self.min_members_spin.value())
        settings.set("max_last_message_hours", self.max_hours_spin.value())
        settings.set("min_total_messages", self.min_messages_spin.value())
        settings.set("language_filter", self.language_combo.currentData())
        settings.set("public_groups_only", self.public_only_check.isChecked())
        settings.set("check_post_frequency", self.check_post_freq_check.isChecked())
        settings.set("check_real_users", self.check_real_users_check.isChecked())
        settings.set("batch_size", self.batch_size_spin.value())
        
        # Save to database
        settings.save_to_db()
        
        self.log_message("Settings saved")
    
    def log_message(self, message, level="info"):
        """Add a message to the log."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        color = "black"
        
        if level == "error":
            self.logger.error(message)
            prefix = "ERROR"
            color = "red"
        elif level == "warning":
            self.logger.warning(message)
            prefix = "WARNING"
            color = "orange"
        else:
            self.logger.info(message)
            prefix = "INFO"
        
        log_text = f"[{timestamp}] {prefix}: {message}"
        
        # Add to log with color
        self.log_text.setTextColor(QColor(color))
        self.log_text.append(log_text)
        self.log_text.setTextColor(QColor("black"))
        
        # Move cursor to end
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.End)
        self.log_text.setTextCursor(cursor)
    
    def update_tab_titles(self):
        """Update the tab titles with live counts."""
        self.results_tabs.setTabText(0, f"Groups Valid & Filter ON ({self.result_counts['good']})")
        self.results_tabs.setTabText(1, f"Groups Valid Only ({self.result_counts['valid_only']})")
        self.results_tabs.setTabText(2, f"Topics Groups Only Valid ({self.result_counts['topics']})")
        self.results_tabs.setTabText(3, f"Channels Only Valid ({self.result_counts['channels']})")
        self.results_tabs.setTabText(4, f"Invalid Groups/Channels ({self.result_counts['invalid']})") 