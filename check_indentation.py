#!/usr/bin/env python3
import ast
import sys

def check_indentation(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Try to compile the content
        ast.parse(content, filename=filename)
        print("No syntax errors found!")
        
    except IndentationError as e:
        print(f"IndentationError at line {e.lineno}: {e.msg}")
        print(f"Text: {e.text}")
        print(f"Position: {' ' * (e.offset - 1)}^")
        
        # Read the file to show context
        with open(filename, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        start = max(0, e.lineno - 3)
        end = min(len(lines), e.lineno + 2)
        
        print("\nContext:")
        for i in range(start, end):
            prefix = ">>> " if i + 1 == e.lineno else "    "
            print(f"{prefix}{i+1:4d}: {repr(lines[i])}")
    
    except SyntaxError as e:
        print(f"SyntaxError at line {e.lineno}: {e.msg}")
        print(f"Text: {e.text}")
        if e.offset:
            print(f"Position: {' ' * (e.offset - 1)}^")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    filename = sys.argv[1] if len(sys.argv) > 1 else "main.py"
    check_indentation(filename) 