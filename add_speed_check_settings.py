#!/usr/bin/env python3
"""
Add Speed Check Time settings to simulate human-like timing
"""

import os
import re
import random
import time

def add_speed_check_settings():
    """Add Speed Check Time Per 1 Group settings to the application"""
    # First, modify settings tab to add the new group speed check settings
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if the setting already exists
    if "Speed Check Time Per 1 Group" in content:
        print("Speed Check Time settings already exist")
        return
    
    # 1. Add settings to the settings tab
    # Look for the settings tab creation code
    settings_tab_match = re.search(r'def create_settings_tab\(self\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content)
    if not settings_tab_match:
        print("Could not find create_settings_tab method")
        return
    
    settings_tab_code = settings_tab_match.group(0)
    
    # Find a good insertion point for our new settings group - before the "Filter settings" or at the end
    insertion_point = None
    
    # Try to find "Filter settings" or another logical place
    if "# Filter settings" in settings_tab_code:
        insertion_point = settings_tab_code.find("# Filter settings")
    elif "# Auto-check settings" in settings_tab_code:
        insertion_point = settings_tab_code.find("# Auto-check settings")
    elif "# SpamBox settings" in settings_tab_code:
        insertion_point = settings_tab_code.find("# SpamBox settings")
    
    if insertion_point:
        # Create the new settings group
        new_settings_code = """
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(1)
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(1)
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
        
"""
        
        # Insert our new settings group
        modified_settings_tab = settings_tab_code[:insertion_point] + new_settings_code + settings_tab_code[insertion_point:]
        content = content.replace(settings_tab_code, modified_settings_tab)
        
        # 2. Add method to update min/max range
        if "def update_speed_check_range" not in content:
            update_method = """
    def update_speed_check_range(self):
        \"\"\"Ensure min seconds <= max seconds.\"\"\"
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
        
"""
            # Find a good place to add this method - after create_settings_tab
            if "def create_settings_tab" in content:
                end_of_settings_tab = content.find("def ", content.find("def create_settings_tab") + 20)
                if end_of_settings_tab != -1:
                    content = content[:end_of_settings_tab] + update_method + content[end_of_settings_tab:]
        
        # 3. Add save settings code
        save_settings_method = re.search(r'def save_settings\(self\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content)
        if save_settings_method:
            save_settings_code = save_settings_method.group(0)
            
            # Find where to insert our settings saving code
            if "QMessageBox.information" in save_settings_code:
                insert_point = save_settings_code.find("QMessageBox.information")
                save_speed_settings = """
            # Save Speed Check Time settings
            self.settings.setValue("min_check_seconds", self.min_seconds_input.value())
            self.settings.setValue("max_check_seconds", self.max_seconds_input.value())
            
"""
                # Insert the save code
                modified_save = save_settings_code[:insert_point] + save_speed_settings + save_settings_code[insert_point:]
                content = content.replace(save_settings_code, modified_save)
        
        # 4. Modify group checking logic to add random delay
        # Find the _checker_thread method
        checker_thread_match = re.search(r'def _checker_thread\(self, [^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content)
        if checker_thread_match:
            checker_code = checker_thread_match.group(0)
            
            # Look for where each group is checked - usually in a loop
            if "for i, link in enumerate(group_links):" in checker_code:
                # Add the random delay before each group check
                delay_code = """
                # Add random delay to simulate human-like timing
                min_seconds = self.settings.value("min_check_seconds", 2, type=int)
                max_seconds = self.settings.value("max_check_seconds", 5, type=int)
                
                # Ensure min <= max
                if min_seconds > max_seconds:
                    min_seconds, max_seconds = max_seconds, min_seconds
                
                # Only add delay after the first group
                if i > 0:
                    delay = random.uniform(min_seconds, max_seconds)
                    self.log_activity_signal.emit(f"⏱️ Waiting {delay:.1f}s before checking next group...")
                    time.sleep(delay)
                """
                
                # Insert after the for loop but before the actual check
                for_line_start = checker_code.find("for i, link in enumerate(group_links):")
                for_line_end = checker_code.find("\n", for_line_start) + 1
                
                # Find the line after the loop start
                next_line = checker_code[for_line_end:].lstrip()
                next_line_indent = len(next_line) - len(next_line.lstrip())
                
                # Adjust indent for our delay code
                indent = " " * (next_line_indent + 4)  # Add 4 spaces for being inside the loop
                delay_code_indented = "\n".join([indent + line for line in delay_code.strip().split("\n")])
                
                # Insert the delay code after the for loop starts
                modified_checker = checker_code[:for_line_end] + delay_code_indented + "\n" + checker_code[for_line_end:]
                content = content.replace(checker_code, modified_checker)
        
        # 5. Also add delay to _enhanced_account_task_thread for multi-account checking
        enhanced_task_match = re.search(r'def _enhanced_account_task_thread\(self, [^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content)
        if enhanced_task_match:
            enhanced_task_code = enhanced_task_match.group(0)
            
            # Look for where each group is checked - usually in a loop
            if "for i, group_link in enumerate(group_links):" in enhanced_task_code:
                # Add the random delay before each group check
                delay_code = """
                # Add random delay to simulate human-like timing
                min_seconds = self.settings.value("min_check_seconds", 2, type=int)
                max_seconds = self.settings.value("max_check_seconds", 5, type=int)
                
                # Ensure min <= max
                if min_seconds > max_seconds:
                    min_seconds, max_seconds = max_seconds, min_seconds
                
                # Only add delay after the first group
                if i > 0:
                    delay = random.uniform(min_seconds, max_seconds)
                    self.log_activity_signal.emit(f"⏱️ Account {phone}: Waiting {delay:.1f}s before checking next group...")
                    time.sleep(delay)
                """
                
                # Insert after the for loop but before the actual check
                for_line_start = enhanced_task_code.find("for i, group_link in enumerate(group_links):")
                for_line_end = enhanced_task_code.find("\n", for_line_start) + 1
                
                # Find the line after the loop start
                next_line = enhanced_task_code[for_line_end:].lstrip()
                next_line_indent = len(next_line) - len(next_line.lstrip())
                
                # Adjust indent for our delay code
                indent = " " * (next_line_indent + 4)  # Add 4 spaces for being inside the loop
                delay_code_indented = "\n".join([indent + line for line in delay_code.strip().split("\n")])
                
                # Insert the delay code after the for loop starts
                modified_enhanced = enhanced_task_code[:for_line_end] + delay_code_indented + "\n" + enhanced_task_code[for_line_end:]
                content = content.replace(enhanced_task_code, modified_enhanced)
        
        # 6. Make sure we have the necessary imports
        if "import random" not in content:
            # Find the imports section at the beginning of the file
            import_match = re.search(r'import [^\n]+', content)
            if import_match:
                import_line = import_match.group(0)
                content = content.replace(import_line, import_line + "\nimport random")
        
        # Write the updated content back to the file
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("Successfully added Speed Check Time Per 1 Group settings")
        print("- Added settings inputs for Min and Max seconds")
        print("- Implemented random delay between group checks")
        print("- Added validation to ensure Min <= Max")
        
        # Create a batch file to run the updated application
        with open("run_with_speed_check.bat", "w") as f:
            f.write("""@echo off
echo Running TG Checker with Speed Check Settings...
python main.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
        
        print("Created batch file: run_with_speed_check.bat")
        
        # Create Kurdish version
        with open("run_with_speed_check_kurdish.bat", "w") as f:
            f.write("""@echo off
echo TG Checker - Ba Speed Check...
python main.py
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
        
        print("Created Kurdish batch file: run_with_speed_check_kurdish.bat")
        return True
    else:
        print("Could not find a suitable insertion point for the Speed Check settings")
        return False

if __name__ == "__main__":
    add_speed_check_settings() 