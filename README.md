# TG Checker

A lightweight Telegram group checking tool with a modern GUI interface.

## Features

- Modern, clean PyQt5 interface
- Account management (add, remove, activate, deactivate)
- Automatic background monitoring
- Account status tracking
- Error detection and recovery
- Detailed logging system
- Database backup and management

## Requirements

- Python 3.8+
- Dependencies listed in `requirements.txt`

## Installation

1. Make sure you have Python 3.8 or higher installed
2. Run `run.bat` to set up the virtual environment and install dependencies
3. The application will start automatically after installation

## Usage

### Main Dashboard

The dashboard provides an overview of your system status and quick actions:

- Active accounts count
- Total accounts
- Monitor status
- Last sync time
- Quick action buttons (Sync, Toggle Monitor, Activate/Deactivate All)

### Accounts Tab

Manage your Telegram accounts:

- View all accounts with status
- Add new accounts
- Remove accounts
- Activate/deactivate individual accounts
- Fix accounts with issues

### Monitor Tab

Configure and view the background monitor:

- Set check interval
- Toggle auto-fix feature
- View real-time monitor logs

### Logs Tab

Access detailed system logs:

- Filter logs by level (Info, Warning, Error, etc.)
- Clear logs
- Export logs to file

### Settings Tab

Configure application settings:

- API credentials
- Auto-start options
- Interface preferences
- Database location and backup

## Automatic Operation

This tool is designed to work automatically without requiring manual intervention:

- Accounts are synchronized automatically in the background
- The monitor runs at configurable intervals
- Issues are automatically detected and fixed
- All processes run in the background for seamless operation

## Troubleshooting

If you encounter any issues:

1. Check the Logs tab for detailed error information
2. Make sure your API credentials are correct
3. Verify that your accounts are properly configured
4. Check your internet connection

## License

This software is proprietary and confidential.

## Support

For support, please contact the developer directly.

# TG Checker Fixes

## Overview

This package contains comprehensive fixes for TG Checker to address three critical issues:

1. **Database Locking Issues**: Fixed SQLite database locking errors that occurred when multiple parts of the application tried to access the database simultaneously
2. **Missing Method Error**: Added the missing `start_checker` method to the `TGCheckerApp` class that was causing application crashes
3. **Indentation Error**: Fixed Python indentation issues that were causing "unindent does not match any outer indentation level" errors

## How to Use the Fixes

### To Fix All Issues (Recommended)

1. Close all instances of TG Checker
2. Run `Fix_TG_Checker_Complete.bat` (or `Fix_TG_Checker_Complete_Kurdish.bat` for Kurdish language)
3. Once the fix is complete, run the application using `Run_Fixed_TG_Checker.bat` (or `Run_Fixed_TG_Checker_Kurdish.bat`)

### For Specific Issues

If you only need to fix specific issues:

#### Indentation Issues
1. Close all instances of TG Checker
2. Run `Fix_Indentation.bat` (or `Fix_Indentation_Kurdish.bat` for Kurdish language)
3. Wait for the utility to complete

#### Database Issues
1. Close all instances of TG Checker
2. Run `Fix_Database.bat` (or `Fix_Database_Kurdish.bat` for Kurdish language)
3. Wait for the utility to complete

## Documentation

- For detailed information about the fixes, see `TG_CHECKER_COMPLETE_SOLUTION.md`
- For specific information about database fixes, see `TG_CHECKER_DATABASE_FIX_GUIDE.md`
- To view these guides, run `View_Guides.bat`

## Support

If you encounter any issues:

1. Make sure all instances of TG Checker are closed before running any fix utilities
2. Check that you have sufficient permissions to modify files in the application directory
3. Try running the fix utilities as administrator if problems persist
4. If you see indentation errors, specifically run `Fix_Indentation.bat`

For a complete breakdown of the technical solution, please refer to the documentation files included in this package. 