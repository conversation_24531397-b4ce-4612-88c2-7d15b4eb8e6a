#!/usr/bin/env python3
"""
Targeted Checker Fix - Addresses specific issues without syntax errors:
1. Checker stopping at 103/499 groups (early termination)
2. 61 account issues with 0 valid groups (overly strict error handling)
3. Better progress tracking and error classification

This fix is more conservative and focuses on the root causes.
"""

import re
import os
import shutil

def apply_targeted_fixes():
    """Apply targeted fixes to address the specific checker issues."""
    
    print("🔧 Applying targeted fixes to main.py...")
    
    # Create backup first
    shutil.copy("main.py", "main.py.backup_before_targeted_fix")
    print("✅ Created backup: main.py.backup_before_targeted_fix")
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix 1: Increase consecutive error threshold to prevent early termination
    print("🔧 Fix 1: Increasing consecutive error threshold...")
    content = re.sub(
        r'if consecutive_errors >= 5:',
        'if consecutive_errors >= 15:',
        content
    )
    
    # Fix 2: Improve timeout handling - classify timeouts as invalid groups instead of account issues
    print("🔧 Fix 2: Improving timeout error classification...")
    timeout_pattern = r'(\s+)return \{\s*"valid": False,\s*"error_type": "account_issue",\s*"reason": "Check timeout \(30s\)",'
    timeout_replacement = r'\1return {\n\1    "valid": False,\n\1    "error_type": "invalid_group",\n\1    "reason": "Group check timeout - may be unreachable",'
    content = re.sub(timeout_pattern, timeout_replacement, content)
    
    # Fix 3: Better error classification - network errors should be invalid groups, not account issues
    print("🔧 Fix 3: Improving error classification...")
    network_error_pattern = r'(\s+)return \{\s*"valid": False,\s*"error_type": "account_issue",\s*"reason": f"Checker error: \{str\(e\)\}",'
    network_error_replacement = r'''\1# Classify errors more accurately
\1error_str = str(e).lower()
\1if any(keyword in error_str for keyword in ["timeout", "connection", "network", "unreachable"]):
\1    return {
\1        "valid": False,
\1        "error_type": "invalid_group",
\1        "reason": f"Group unreachable: {str(e)}",'''
    
    content = re.sub(network_error_pattern, network_error_replacement, content)
    
    # Fix 4: Add continuation logic to ensure all groups are processed
    print("🔧 Fix 4: Adding continuation logic...")
    continuation_pattern = r'(\s+)# If too many consecutive errors, pause account\s*if consecutive_errors >= \d+:'
    continuation_replacement = r'''\1# If too many consecutive errors, pause briefly and continue
\1if consecutive_errors >= 15:
\1    self.log_activity_signal.emit(f"⚠️ Many consecutive errors ({consecutive_errors}) - pausing 30s then continuing...")
\1    time.sleep(30)  # Brief pause
\1    consecutive_errors = 0  # Reset counter and continue
\1    continue
\1
\1# Original logic (commented out to prevent early termination):
\1# if consecutive_errors >= 15:'''
    
    content = re.sub(continuation_pattern, continuation_replacement, content)
    
    # Fix 5: Improve progress tracking to ensure accurate counting
    print("🔧 Fix 5: Improving progress tracking...")
    progress_pattern = r'(\s+)# Update progress AFTER processing each group\s*groups_completed = self\.current_group_index \+ i \+ 1'
    progress_replacement = r'''\1# Update progress AFTER processing each group
\1groups_completed = self.current_group_index + i + 1
\1print(f"🔍 DEBUG: Progress {groups_completed}/{self.total_groups} - Processing: {link}")
\1self.log_activity_signal.emit(f"📊 Progress: {groups_completed}/{self.total_groups} groups checked")'''
    
    content = re.sub(progress_pattern, progress_replacement, content)
    
    # Fix 6: Add better debugging for account issues
    print("🔧 Fix 6: Adding better debugging for account issues...")
    account_issue_pattern = r'(\s+)account_issues\.append\(link\)\s*self\.log_activity_signal\.emit\(f"\[GROUP #\{actual_group_number\}\] ACCOUNT ISSUE: \{result\[\'reason\'\]\} → Saved to AccountIssue\.txt"\)'
    account_issue_replacement = r'''\1account_issues.append(link)
\1print(f"⚠️ DEBUG: Account issue for {link}: {result['reason']}")
\1self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: {result['reason']} → Saved to AccountIssue.txt")'''
    
    content = re.sub(account_issue_pattern, account_issue_replacement, content)
    
    # Fix 7: Add debugging for the main checker loop
    print("🔧 Fix 7: Adding main loop debugging...")
    main_loop_pattern = r'(\s+)for i, link in enumerate\(group_links\):'
    main_loop_replacement = r'''\1print(f"🔍 DEBUG: Starting main checker loop for {len(group_links)} groups")
\1self.log_activity_signal.emit(f"🔍 Starting to check {len(group_links)} groups...")
\1
\1for i, link in enumerate(group_links):
\1    print(f"🔍 DEBUG: Processing group {i+1}/{len(group_links)}: {link}")'''
    
    content = re.sub(main_loop_pattern, main_loop_replacement, content)
    
    # Fix 8: Ensure the checker doesn't break early
    print("🔧 Fix 8: Removing early break conditions...")
    early_break_pattern = r'(\s+)# Schedule recovery after 5 minutes\s*self\._schedule_account_retry\(phone, 300\)\s*# 5 minutes\s*break'
    early_break_replacement = r'''\1# Schedule recovery after 5 minutes
\1self._schedule_account_retry(phone, 300)  # 5 minutes
\1# Continue instead of breaking to ensure all groups are processed
\1continue'''
    
    content = re.sub(early_break_pattern, early_break_replacement, content)
    
    # Write the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ All targeted fixes applied successfully!")
    return True

def verify_fixes():
    """Verify that the fixes work and the file compiles."""
    
    print("🔍 Verifying fixes...")
    
    try:
        import py_compile
        py_compile.compile("main.py", doraise=True)
        print("✅ main.py compiles successfully!")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Compilation failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = apply_targeted_fixes()
        if success and verify_fixes():
            print("\n🎉 SUCCESS: Targeted fixes applied!")
            print("\n📋 Fixes applied:")
            print("1. ✅ Increased consecutive error threshold from 5 to 15")
            print("2. ✅ Improved timeout error classification (timeout → invalid_group)")
            print("3. ✅ Better network error classification")
            print("4. ✅ Added continuation logic to prevent early termination")
            print("5. ✅ Enhanced progress tracking with debugging")
            print("6. ✅ Added better debugging for account issues")
            print("7. ✅ Added main loop debugging")
            print("8. ✅ Removed early break conditions")
            print("\n🔧 Expected improvements:")
            print("- Checker should now process all 499 groups instead of stopping at 103")
            print("- Reduced false account issues due to better error classification")
            print("- Better debugging to identify real issues")
            print("- More accurate progress tracking")
            print("\n🔄 Please restart TG Checker to test the fixes")
        else:
            print("\n❌ Fix failed or compilation error!")
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc() 