@echo off
echo ===================================
echo  TG CHECKER - HAMU AMRAZEKAN
echo ===================================
echo.
echo 1. Sandreke Rênma
echo 2. Barnamay Speed Checker
echo 3. <PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON>)
echo 4. <PERSON><PERSON><PERSON> (Rêgay jigre)
echo 5. Darcun
echo.
set /p choice=Hal<PERSON><PERSON><PERSON><PERSON> bnusa (1-5): 

if "%choice%"=="1" goto view_guide
if "%choice%"=="2" goto speed_checker
if "%choice%"=="3" goto simple_fix
if "%choice%"=="4" goto emergency_fix
if "%choice%"=="5" goto end

:view_guide
echo.
echo Krdnaway Rênma...
notepad TG_CHECKER_FIX_GUIDE.txt
goto end

:speed_checker
echo.
echo Krdnaway Speed Checker...
python speed_checker_app.py
goto end

:simple_fix
echo.
echo Drustkrdni chareseri sada...
python simple_stub.py
echo.
echo Tawaw! Esta datwanit Run_Simple_Fix_Kurdish.bat akayawa.
pause
goto end

:emergency_fix
echo.
echo Drustkrdni chareseri kutupir...
python emergency_fix.py
echo.
echo Tawaw! Esta datwanit Run_Emergency_Fix_Kurdish.bat akayawa.
pause
goto end

:end
echo.
echo Supas bo bakarhenani amrazakani chareserkrdn. 