#!/usr/bin/env python3
"""
🎯 TG CHECKER CLASSIFICATION ACCURACY TEST
Test script to verify 100% accurate group type detection with provided examples.
"""

import sys
import os
import time

# Test cases provided by user
TEST_CASES = {
    "Groups Valid (Filter ON) → Groups_Valid_Filter": [
        "https://t.me/imperiamarket",
        "https://t.me/infocoindogroup", 
        "https://t.me/instaaccountbuying"
    ],
    "Groups Valid (No Filter) → Groups_Valid_Only": [
        "https://t.me/hyipinformer_com",
        "https://t.me/islamic_hacker_army"
    ],
    "Topic Groups Valid → Topics_Groups_Only_Valid": [
        "https://t.me/RareHandle"
    ],
    "Channels Valid → Channels_Only_Valid": [
        "https://t.me/wallethuntersio"
    ],
    "Invalid Groups/Channels → Invalid_Groups_Channels": [
        "https://t.me/beklopptundgeil",
        "https://t.me/belgieiswakker"
    ]
}

def test_classification():
    """Test the enhanced classification system."""
    print("🎯 TESTING TG CHECKER CLASSIFICATION ACCURACY")
    print("=" * 60)
    
    # Import the enhanced TG client
    try:
        from tg_client import TelegramClient
        print("✅ Successfully imported enhanced TelegramClient")
    except Exception as e:
        print(f"❌ Failed to import TelegramClient: {e}")
        return False
    
    # Import account manager to get test account
    try:
        from account_manager import AccountManager
        account_manager = AccountManager()
        accounts = account_manager.get_active_accounts()
        
        if not accounts:
            print("❌ No active accounts found for testing")
            return False
            
        test_account = accounts[0]
        print(f"✅ Using test account: {test_account.get('phone', 'Unknown')}")
    except Exception as e:
        print(f"❌ Failed to get test account: {e}")
        return False
    
    # Create test client
    try:
        client = TelegramClient(
            test_account.get("api_id"),
            test_account.get("api_hash"), 
            test_account.get("phone"),
            f"sessions/{test_account.get('phone', 'test').replace('+', '')}"
        )
        print("✅ Created test client")
    except Exception as e:
        print(f"❌ Failed to create test client: {e}")
        return False
    
    # Test each category
    all_tests_passed = True
    total_tests = 0
    passed_tests = 0
    
    for category, links in TEST_CASES.items():
        print(f"\n📂 Testing Category: {category}")
        print("-" * 50)
        
        for link in links:
            total_tests += 1
            print(f"🔍 Testing: {link}")
            
            try:
                # Test the classification
                result = client.get_entity_info(link)
                
                if result.get("valid", False):
                    detected_type = result.get("type", "unknown")
                    member_count = result.get("member_count", 0)
                    messages = result.get("total_messages", 0)
                    activity = result.get("last_message_age_hours", 999)
                    
                    print(f"   ✅ Valid {detected_type}")
                    print(f"   📊 Members: {member_count}, Messages: {messages}, Activity: {activity:.1f}h")
                    
                    # Verify correct classification
                    expected_folder = category.split("→")[1].strip()
                    
                    if detected_type == "channel" and "Channels_Only_Valid" in expected_folder:
                        print(f"   🎯 CORRECT: Channel → {expected_folder}")
                        passed_tests += 1
                    elif detected_type == "topic" and "Topics_Groups_Only_Valid" in expected_folder:
                        print(f"   🎯 CORRECT: Topic → {expected_folder}")  
                        passed_tests += 1
                    elif detected_type == "group":
                        # For groups, we need to check filter logic
                        # Simulate filter check (500 members, 1 hour activity, 100 messages)
                        passes_filters = (member_count >= 500 and activity <= 1 and messages >= 100)
                        
                        if passes_filters and "Groups_Valid_Filter" in expected_folder:
                            print(f"   🎯 CORRECT: Group (Filter ON) → {expected_folder}")
                            passed_tests += 1
                        elif not passes_filters and "Groups_Valid_Only" in expected_folder:
                            print(f"   🎯 CORRECT: Group (No Filter) → {expected_folder}")
                            passed_tests += 1
                        else:
                            print(f"   ❌ WRONG: Group filter mismatch for {expected_folder}")
                            all_tests_passed = False
                    else:
                        print(f"   ❌ WRONG: {detected_type} should go to {expected_folder}")
                        all_tests_passed = False
                        
                else:
                    error_type = result.get("error_type", "unknown")
                    reason = result.get("reason", "unknown")
                    print(f"   ❌ Invalid ({error_type}): {reason}")
                    
                    if "Invalid_Groups_Channels" in category:
                        print(f"   🎯 CORRECT: Invalid → Invalid_Groups_Channels")
                        passed_tests += 1
                    else:
                        print(f"   ❌ WRONG: Should be valid but marked invalid")
                        all_tests_passed = False
                        
            except Exception as e:
                print(f"   ❌ ERROR: {str(e)}")
                all_tests_passed = False
            
            # Small delay between tests
            time.sleep(1)
    
    # Final results
    print("\n" + "=" * 60)
    print("🎯 FINAL TEST RESULTS")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Accuracy: {(passed_tests/total_tests)*100:.1f}%")
    
    if all_tests_passed:
        print("🎉 ALL TESTS PASSED - 100% ACCURATE CLASSIFICATION!")
        return True
    else:
        print("❌ SOME TESTS FAILED - CLASSIFICATION NEEDS IMPROVEMENT")
        return False

if __name__ == "__main__":
    success = test_classification()
    sys.exit(0 if success else 1) 