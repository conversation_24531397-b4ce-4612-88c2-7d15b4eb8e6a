import os
import logging
from datetime import datetime
from logging.handlers import RotatingFileHandler

# Define the logs directory constant
LOG_DIR = "logs"

def setup_logger(name="tg_checker", log_dir="logs", log_level=logging.INFO):
    """Set up and configure a logger."""
    # Create logs directory if it doesn't exist
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # Get logger
    logger = logging.getLogger(name)
    logger.setLevel(log_level)
    
    # Clear any existing handlers
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)
    
    # Create rotating file handler for main log
    log_file = os.path.join(log_dir, f"{name}.log")
    file_handler = RotatingFileHandler(
        log_file, 
        maxBytes=5*1024*1024,  # 5 MB
        backupCount=5,
        encoding='utf-8'
    )
    
    # Create a separate auth log file
    auth_log_file = os.path.join(log_dir, "auth.log")
    auth_file_handler = RotatingFileHandler(
        auth_log_file, 
        maxBytes=5*1024*1024,  # 5 MB
        backupCount=5,
        encoding='utf-8'
    )
    
    # Create console handler
    console_handler = logging.StreamHandler()
    
    # Create formatters
    standard_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    auth_formatter = logging.Formatter('[%(asctime)s] %(levelname)s: %(message)s', 
                                       datefmt='%Y-%m-%d %H:%M:%S')
    
    file_handler.setFormatter(standard_formatter)
    auth_file_handler.setFormatter(auth_formatter)
    console_handler.setFormatter(standard_formatter)
    
    # Set filter for auth handler to only capture auth-related logs
    class AuthFilter(logging.Filter):
        def filter(self, record):
            return getattr(record, 'auth_log', False)
    
    auth_filter = AuthFilter()
    auth_file_handler.addFilter(auth_filter)
    
    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(auth_file_handler)
    logger.addHandler(console_handler)
    
    # Log that the logger was set up
    logger.info(f"Logger initialized (level: {logging.getLevelName(log_level)})")
    
    return logger

def get_logger(name="tg_checker"):
    """Get an existing logger or create a new one."""
    logger = logging.getLogger(name)
    
    # If logger doesn't have handlers, set it up
    if not logger.handlers:
        return setup_logger(name)
    
    return logger

def log_auth(logger, message, level=logging.INFO):
    """
    Log an authentication-related message to both the main log and auth log.
    Properly formats the message with timestamp and ensures proper display.
    """
    extra = {'auth_log': True}
    
    if level == logging.DEBUG:
        logger.debug(message, extra=extra)
    elif level == logging.INFO:
        logger.info(message, extra=extra)
    elif level == logging.WARNING:
        logger.warning(message, extra=extra)
    elif level == logging.ERROR:
        logger.error(message, extra=extra)
    elif level == logging.CRITICAL:
        logger.critical(message, extra=extra)
    else:
        logger.info(message, extra=extra)

class LogFilter:
    """Filter to control what log entries are shown."""
    
    def __init__(self, level=logging.NOTSET):
        self.level = level
    
    def filter(self, record):
        """Filter log records based on level."""
        return record.levelno >= self.level

def read_log_file(log_file, max_lines=1000):
    """Read lines from a log file."""
    if not os.path.exists(log_file):
        return []
    
    with open(log_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Return the last max_lines lines
    return lines[-max_lines:]

def read_auth_log(log_dir="logs", max_lines=1000):
    """Read the authentication log file."""
    auth_log_file = os.path.join(log_dir, "auth.log")
    return read_log_file(auth_log_file, max_lines)

def filter_logs_by_level(logs, level_str):
    """Filter log entries by level string."""
    level_map = {
        'DEBUG': logging.DEBUG,
        'INFO': logging.INFO,
        'WARNING': logging.WARNING,
        'ERROR': logging.ERROR,
        'CRITICAL': logging.CRITICAL
    }
    
    level = level_map.get(level_str.upper(), logging.NOTSET)
    
    if level == logging.NOTSET:
        return logs
    
    return [log for log in logs if any(f" - {l} - " in log for l in level_map.keys() 
                                      if level_map[l] >= level)]
    
def clear_log_file(log_file):
    """Clear a log file."""
    if os.path.exists(log_file):
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"Log cleared at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        return True
    return False

def log_usage_checker(logger, message, level=logging.INFO):
    """Log a usage checker message to a specific file."""
    usage_log_path = os.path.join(LOG_DIR, "usage_checker.log")
    
    # Create a separate logger for usage checker
    usage_logger = logging.getLogger("usage_checker")
    usage_logger.setLevel(logging.INFO)
    
    # Remove existing handlers to avoid duplicates
    for handler in usage_logger.handlers[:]:
        usage_logger.removeHandler(handler)
    
    # Create file handler for usage checker logs
    usage_handler = logging.FileHandler(usage_log_path, encoding='utf-8')
    usage_formatter = logging.Formatter('%(asctime)s - USAGE - %(levelname)s - %(message)s')
    usage_handler.setFormatter(usage_formatter)
    
    usage_logger.addHandler(usage_handler)
    usage_logger.log(level, message)
    
    # Also log to the main logger
    if logger:
        logger.log(level, f"[USAGE] {message}")

def read_usage_checker_log(max_lines=1000):
    """Read usage checker log file and return recent entries."""
    usage_log_path = os.path.join(LOG_DIR, "usage_checker.log")
    
    if not os.path.exists(usage_log_path):
        return []
    
    try:
        with open(usage_log_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Return the last max_lines entries
        return lines[-max_lines:] if len(lines) > max_lines else lines
    except Exception as e:
        return [f"Error reading usage checker log: {str(e)}"] 