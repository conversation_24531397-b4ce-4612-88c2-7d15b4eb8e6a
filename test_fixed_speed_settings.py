#!/usr/bin/env python3
"""
Test if Speed Check Time settings are working in the fixed main.py
"""

import os
import re

def test_speed_settings():
    """Test if the speed check settings are working in the fixed main.py"""
    print("=== Testing Speed Check Time Settings in main.py ===\n")
    
    # Check if main.py exists
    if not os.path.exists('main.py'):
        print("Error: main.py not found")
        return False
    
    # Read the file content
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    results = {
        "speed_settings_group": False,
        "input_fields": False,
        "update_method": False,
        "save_settings": False,
        "random_delay": False,
        "import_random": False,
        "valid_syntax": True
    }
    
    # Test for Speed Check Time settings group
    if "Speed Check Time Per 1 Group" in content:
        print("✅ Speed Check Time settings group found")
        results["speed_settings_group"] = True
    else:
        print("❌ Speed Check Time settings group not found")
    
    # Test for input fields
    if "min_seconds_input" in content and "max_seconds_input" in content:
        print("✅ Min/Max seconds input fields found")
        results["input_fields"] = True
    else:
        print("❌ Min/Max seconds input fields not found")
    
    # Test for update method
    if "def update_speed_check_range" in content:
        print("✅ update_speed_check_range method found")
        results["update_method"] = True
    else:
        print("❌ update_speed_check_range method not found")
    
    # Test for saving settings
    if "min_check_seconds" in content and "max_check_seconds" in content:
        print("✅ Speed check settings save code found")
        results["save_settings"] = True
    else:
        print("❌ Speed check settings save code not found")
    
    # Test for random delay
    if "random.uniform(min_seconds, max_seconds)" in content:
        print("✅ Random delay code found")
        results["random_delay"] = True
    else:
        print("❌ Random delay code not found")
    
    # Test for import random
    if "import random" in content:
        print("✅ import random statement found")
        results["import_random"] = True
    else:
        print("❌ import random statement not found")
    
    # Test for indentation issues
    indentation_check = False
    if "def auto_refresh_missing_account_info" in content:
        # Check if the line is properly indented
        lines = content.splitlines()
        for i, line in enumerate(lines):
            if "def auto_refresh_missing_account_info" in line:
                # Get the indentation of this line
                current_indent = len(line) - len(line.lstrip())
                
                # Find the TGCheckerApp class to check proper indentation
                for j in range(i, 0, -1):
                    if "class TGCheckerApp" in lines[j]:
                        class_indent = len(lines[j]) - len(lines[j].lstrip())
                        # Class methods should be indented 4 spaces from class definition
                        if current_indent == class_indent + 4:
                            print("✅ auto_refresh_missing_account_info method has correct indentation")
                            indentation_check = True
                        else:
                            print("❌ auto_refresh_missing_account_info method has incorrect indentation")
                            results["valid_syntax"] = False
                        break
                break
    
    if not indentation_check:
        print("⚠️ Could not check auto_refresh_missing_account_info indentation")
    
    # Test for missing except/finally clauses
    try_blocks = re.findall(r'\btry\s*:', content)
    except_blocks = re.findall(r'\bexcept\b', content)
    finally_blocks = re.findall(r'\bfinally\s*:', content)
    
    print(f"Found {len(try_blocks)} try blocks, {len(except_blocks)} except blocks, and {len(finally_blocks)} finally blocks")
    
    # Check for the specific syntax error at line 4013
    syntax_error_fixed = True
    try_without_except = False
    
    # Check if there's a try block at line 3968-3972
    lines = content.splitlines()
    for i in range(3965, 3975):
        if i < len(lines) and "try:" in lines[i]:
            # Found the try block, now check if it has an except
            try_line = i
            try_indent = len(lines[i]) - len(lines[i].lstrip())
            has_except = False
            
            # Look for an except with the same indentation
            for j in range(try_line + 1, min(try_line + 150, len(lines))):
                if lines[j].strip() and len(lines[j]) - len(lines[j].lstrip()) == try_indent:
                    if lines[j].lstrip().startswith(("except", "finally")):
                        has_except = True
                        break
                    elif not lines[j].lstrip().startswith(("if", "elif", "else", "try", "for", "while", "def", "class")):
                        continue
                    else:
                        # Found another control structure at the same indentation level
                        # This means the try block ended without an except
                        break
            
            if not has_except:
                try_without_except = True
                syntax_error_fixed = False
                print(f"❌ Found try block at line {try_line+1} without except/finally clause")
                break
    
    if not try_without_except:
        print("✅ The try block that was causing the syntax error has been fixed")
    
    # Final syntax check
    results["valid_syntax"] = syntax_error_fixed
    
    # Print the results
    print("\nTest Results:")
    print(f"Speed Settings Group: {'✅' if results['speed_settings_group'] else '❌'}")
    print(f"Min/Max Input Fields: {'✅' if results['input_fields'] else '❌'}")
    print(f"Update Method: {'✅' if results['update_method'] else '❌'}")
    print(f"Save Settings: {'✅' if results['save_settings'] else '❌'}")
    print(f"Random Delay: {'✅' if results['random_delay'] else '❌'}")
    print(f"Import Random: {'✅' if results['import_random'] else '❌'}")
    print(f"Valid Syntax: {'✅' if results['valid_syntax'] else '❌'}")
    
    print("\nSummary:")
    if all(results.values()):
        print("🎉 All tests passed! The Speed Check settings are correctly implemented and the syntax is valid.")
        print("You can now run the application using: python main.py")
        
        # Print the batch files that can be used
        if os.path.exists("run_fixed_combined.bat"):
            print("Or use the batch file: run_fixed_combined.bat")
        
        if os.path.exists("run_fixed_combined_kurdish.bat"):
            print("Or use the Kurdish batch file: run_fixed_combined_kurdish.bat")
    else:
        print("❌ Some tests failed. Please check the results above for details.")
    
    return all(results.values())

if __name__ == "__main__":
    test_speed_settings() 