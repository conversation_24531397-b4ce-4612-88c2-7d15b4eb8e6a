#!/usr/bin/env python3
"""
Debug script: For each session in sessions/, print the latest 30 messages (ID, type, date, text) from a group.
Fill in your API_ID, API_HASH, and GROUP below.
"""

import os
from telethon.sync import TelegramClient
from telethon.tl.types import MessageService
from datetime import datetime

# === FILL IN YOUR OWN CREDENTIALS ===
API_ID = 123456  # <-- your API ID here
API_HASH = 'your_api_hash_here'  # <-- your API hash here
GROUP = 'https://t.me/BuysellZone1'  # Change to your test group link

SESSIONS_DIR = 'sessions'

session_files = [f for f in os.listdir(SESSIONS_DIR) if f.endswith('.session')]

if not session_files:
    print('No session files found in sessions/.')
    exit(1)

for session_file in session_files:
    session_path = os.path.join(SESSIONS_DIR, session_file)
    print(f"\n=== Using session: {session_file} ===")
    try:
        client = TelegramClient(session_path, API_ID, API_HASH)
        with client:
            me = client.get_me()
            print(f"Logged in as: {me.username or me.first_name or me.id}")
            messages = client.get_messages(GROUP, limit=30)
            for idx, m in enumerate(messages):
                if m is None:
                    msg_type = 'None'
                elif isinstance(m, MessageService):
                    msg_type = 'service'
                elif getattr(m, 'message', None) is None and not getattr(m, 'media', None):
                    msg_type = 'empty/deleted'
                else:
                    msg_type = 'user'
                date_str = str(getattr(m, 'date', ''))
                text = getattr(m, 'message', '<media/other>')
                print(f"[{idx:02d}] id={getattr(m, 'id', None)}, type={msg_type}, date={date_str}, text={text[:40]}")
    except Exception as e:
        print(f"❌ Error with session {session_file}: {e}") 