"""
OPTIMIZED DATABASE MANAGER
Reduces database connections and improves performance
"""

import sqlite3
import threading
import time
from contextlib import contextmanager
from typing import Dict, Any, Optional
import queue
import logging

class OptimizedDBManager:
    """
    Optimized database manager with connection pooling and performance improvements.
    Reduces CPU usage by reusing connections and batching operations.
    """
    
    def __init__(self, db_path: str, pool_size: int = 3):
        self.db_path = db_path
        self.pool_size = pool_size
        self.connection_pool = queue.Queue(maxsize=pool_size)
        self.lock = threading.RLock()
        self.logger = logging.getLogger(__name__)
        
        # Performance settings
        self.pragma_settings = {
            'journal_mode': 'WAL',
            'synchronous': 'NORMAL', 
            'cache_size': 2000,
            'temp_store': 'MEMORY',
            'busy_timeout': 30000
        }
        
        # Initialize connection pool
        self._init_pool()
    
    def _init_pool(self):
        """Initialize the connection pool with optimized connections."""
        for _ in range(self.pool_size):
            conn = self._create_optimized_connection()
            self.connection_pool.put(conn)
    
    def _create_optimized_connection(self) -> sqlite3.Connection:
        """Create an optimized database connection."""
        conn = sqlite3.connect(
            self.db_path,
            timeout=30,
            check_same_thread=False,
            isolation_level=None  # Autocommit mode for better performance
        )
        
        # Apply performance optimizations
        for pragma, value in self.pragma_settings.items():
            conn.execute(f"PRAGMA {pragma} = {value}")
        
        # Set row factory for dict-like access
        conn.row_factory = sqlite3.Row
        
        return conn
    
    @contextmanager
    def get_connection(self):
        """Get a connection from the pool."""
        conn = None
        try:
            # Get connection from pool (with timeout)
            conn = self.connection_pool.get(timeout=10)
            yield conn
        except queue.Empty:
            # Pool exhausted, create temporary connection
            self.logger.warning("Connection pool exhausted, creating temporary connection")
            conn = self._create_optimized_connection()
            yield conn
        finally:
            if conn:
                try:
                    # Return connection to pool if it's healthy
                    if self.connection_pool.qsize() < self.pool_size:
                        self.connection_pool.put(conn)
                    else:
                        conn.close()
                except:
                    # Connection is broken, close it
                    try:
                        conn.close()
                    except:
                        pass
    
    def execute_query(self, query: str, params: tuple = (), fetch_one: bool = False, fetch_all: bool = False):
        """Execute a query with automatic connection management."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(query, params)
            
            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            else:
                return cursor.rowcount
    
    def execute_many(self, query: str, params_list: list):
        """Execute multiple queries in a batch for better performance."""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            return cursor.rowcount
    
    def close_all(self):
        """Close all connections in the pool."""
        while not self.connection_pool.empty():
            try:
                conn = self.connection_pool.get_nowait()
                conn.close()
            except:
                pass

class DatabaseOptimizer:
    """
    Database optimization utilities for TG Checker.
    """
    
    @staticmethod
    def optimize_database(db_path: str):
        """Apply optimization to an existing database."""
        try:
            conn = sqlite3.connect(db_path, timeout=30)
            
            # Apply optimizations
            optimizations = [
                "PRAGMA journal_mode = WAL",
                "PRAGMA synchronous = NORMAL", 
                "PRAGMA cache_size = 2000",
                "PRAGMA temp_store = MEMORY",
                "PRAGMA mmap_size = 268435456",  # 256MB
                "VACUUM",  # Defragment database
                "ANALYZE"  # Update statistics
            ]
            
            for optimization in optimizations:
                try:
                    conn.execute(optimization)
                    print(f"✅ Applied: {optimization}")
                except Exception as e:
                    print(f"⚠️ Failed to apply {optimization}: {e}")
            
            conn.close()
            print(f"✅ Database {db_path} optimized successfully!")
            
        except Exception as e:
            print(f"❌ Failed to optimize database {db_path}: {e}")
    
    @staticmethod
    def cleanup_old_data(db_path: str, table: str, date_column: str, days_to_keep: int = 30):
        """Clean up old data to reduce database size."""
        try:
            conn = sqlite3.connect(db_path, timeout=30)
            cursor = conn.cursor()
            
            # Delete old records
            query = f"""
                DELETE FROM {table} 
                WHERE {date_column} < datetime('now', '-{days_to_keep} days')
            """
            
            cursor.execute(query)
            deleted_count = cursor.rowcount
            conn.commit()
            conn.close()
            
            print(f"✅ Cleaned up {deleted_count} old records from {table}")
            return deleted_count
            
        except Exception as e:
            print(f"❌ Failed to cleanup {table}: {e}")
            return 0

def optimize_all_databases():
    """Optimize all TG Checker databases."""
    
    print("🔧 OPTIMIZING ALL DATABASES...")
    
    databases = [
        "tg_checker.db",
        "accounts.db", 
        "joining.db",
        "forwarder.db",
        "forwarder_tasks.db"
    ]
    
    optimizer = DatabaseOptimizer()
    
    for db_name in databases:
        if os.path.exists(db_name):
            print(f"\n📊 Optimizing {db_name}...")
            optimizer.optimize_database(db_name)
        else:
            print(f"⚠️ Database {db_name} not found, skipping...")
    
    print("\n✅ ALL DATABASES OPTIMIZED!")

def create_optimized_db_managers():
    """Create optimized database managers for the main application."""
    
    managers = {
        'main': OptimizedDBManager("tg_checker.db"),
        'accounts': OptimizedDBManager("accounts.db"),
        'joining': OptimizedDBManager("joining.db"), 
        'forwarder': OptimizedDBManager("forwarder.db"),
        'forwarder_tasks': OptimizedDBManager("forwarder_tasks.db")
    }
    
    return managers

if __name__ == "__main__":
    import os
    optimize_all_databases()
