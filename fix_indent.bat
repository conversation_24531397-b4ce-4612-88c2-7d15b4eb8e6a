@echo off
echo Creating a Python script to fix the indentation...

echo import shutil > fix.py
echo from datetime import datetime >> fix.py
echo. >> fix.py
echo # Create a backup >> fix.py
echo backup_file = f"main.py.backup_{datetime.now().strftime('%%Y%%m%%d_%%H%%M%%S')}" >> fix.py
echo shutil.copy("main.py", backup_file) >> fix.py
echo print(f"Created backup at: {backup_file}") >> fix.py
echo. >> fix.py
echo # Read the file >> fix.py
echo with open("main.py", "r", encoding="utf-8") as f: >> fix.py
echo     lines = f.readlines() >> fix.py
echo. >> fix.py
echo # Find the problematic function >> fix.py
echo target_line = None >> fix.py
echo for i, line in enumerate(lines): >> fix.py
echo     if "def stop_joining_task(self, task_id):" in line: >> fix.py
echo         target_line = i >> fix.py
echo         break >> fix.py
echo. >> fix.py
echo if target_line is not None: >> fix.py
echo     print(f"Found stop_joining_task function at line {target_line + 1}") >> fix.py
echo. >> fix.py
echo     # Fix the indentation of the function definition >> fix.py
echo     lines[target_line] = "    def stop_joining_task(self, task_id):\n" >> fix.py
echo. >> fix.py
echo     # Fix the indentation of the docstring >> fix.py
echo     if target_line + 1 ^< len(lines) and '"""' in lines[target_line + 1]: >> fix.py
echo         lines[target_line + 1] = "        \"\"\"Stop a specific joining task with high-performance system.\"\"\"\n" >> fix.py
echo. >> fix.py
echo     # Fix the indentation of the try statement >> fix.py
echo     if target_line + 2 ^< len(lines) and "try:" in lines[target_line + 2]: >> fix.py
echo         lines[target_line + 2] = "        try:\n" >> fix.py
echo. >> fix.py
echo     # Write the fixed content back >> fix.py
echo     with open("main.py", "w", encoding="utf-8") as f: >> fix.py
echo         f.writelines(lines) >> fix.py
echo. >> fix.py
echo     print("Fixed indentation of stop_joining_task function") >> fix.py
echo     print("You can now run the application with: python main.py") >> fix.py
echo else: >> fix.py
echo     print("Could not find the stop_joining_task function") >> fix.py

echo Running the fix script...
python fix.py

echo.
echo If the script was successful, you can now run: python main.py
echo.
pause 