import asyncio
import console
import time
import re
import random
from telethon import TelegramClient, events, errors
from telethon.tl.functions.messages import ForwardMessagesRequest
from telethon.tl.functions.channels import Join<PERSON>hannelRequest
from telethon.errors import <PERSON><PERSON>ait<PERSON>rror, PeerIdInvalidError

class Telegram:
    def __init__(self, settings, channels):
        self.session_name = settings["name"]
        self.app_id = settings["appId"]
        self.app_hash = settings["appHash"]
        self.interval = settings["interval"]
        self.loop_interval = settings["intervalBetweenLoops"]
        self.message_to_forward = settings["messageToForward"]
        self.reply_message = settings.get("replyMessage", None)
        self.should_reply_message = settings.get("shouldReplyMessage", True)
        self.channels = channels
        self.success = 0
        self.failed_channels = []
        self.log_failed_channels = settings.get("logFailedChannels", True)
        self.use_random_sleep_time = settings.get("useRandomSleepTime", True)
        self.random_sleep_time_min = settings.get("randomSleepTimeMin", 14400)
        self.random_sleep_time_max = settings.get("randomSleepTimeMax", 21600)
        self.last_reply_times = {}
        
    async def join_all_channels(self):
        async with TelegramClient(self.session_name, self.app_id, self.app_hash) as client:
            for channel in self.channels:
                channel_name = channel.split('/')[3]
                joined, err = await self.try_join_channel(client, channel_name)
                self.handle_response_join(joined, err, channel_name)
                print(f"[{console.blue('*')}] Sleeping for {self.interval} seconds before joining to the next channel")
                await asyncio.sleep(self.interval)

    async def try_join_channel(self, client: TelegramClient, channel):
        try:
            join_channel_request = JoinChannelRequest(channel=channel)
            await client(join_channel_request)
            return True, None
        except errors.FloodWaitError as ex:
            print(f"[{console.blue('*')}] Rate limited. Sleeping for {ex.seconds} seconds")
            await asyncio.sleep(ex.seconds)
            return await self.try_join_channel(client, channel)
        except Exception as ex:
            return False, ex

    def handle_response_join(self, joined_status, error, channel):
        if not joined_status:
            if self.log_failed_channels:
                self.failed_channels.append((channel, error))
            print(f"[{console.red('-')}] Failed => {channel} | {error}")
            self.write_failed_channels_to_file()
            return

        self.success += 1
        print(f"[{console.green(self.success)}] Joined => {channel}")
        
    async def forward_to_all_continuously(self):
        async with TelegramClient(self.session_name, self.app_id, self.app_hash) as client:
            @client.on(events.NewMessage(incoming=True))
            async def handler(event):
                if self.should_reply_message and self.reply_message and event.is_private:
                    user_id = event.sender_id
                    current_time = time.time()
                    if user_id not in self.last_reply_times or current_time - self.last_reply_times[user_id] >= 30:
                        self.last_reply_times[user_id] = current_time
                        await event.reply(self.reply_message)
                        
            listening_task = asyncio.create_task(client.run_until_disconnected())
            
            while True:
                for channel in self.channels:
                    forwarded, err = await self.try_forward_message(client, channel)
                    self.handle_response_message(forwarded, err, channel)
                    print(f"[{console.blue('*')}] Sleeping for {self.interval} seconds before forwarding to the next channel")
                    await asyncio.sleep(self.interval)
                    
                if self.use_random_sleep_time:
                    random_sleep_time = random.randint(self.random_sleep_time_min, self.random_sleep_time_max)
                    print(f"[{console.blue('*')}] Sleeping for {random_sleep_time} seconds")
                    await asyncio.sleep(random_sleep_time)
                else:
                    print(f"[{console.blue('*')}] Sleeping for {self.loop_interval} seconds")
                    await asyncio.sleep(self.loop_interval)
                    
            await listening_task
            
    async def try_forward_message(self, client: TelegramClient, channel):
        try:
            _ = await self.forward_message(client, channel)
            return True, None
        except errors.FloodWaitError as ex:
            print(f"[{console.blue('*')}] Rate limited. Sleeping for {ex.seconds} seconds")
            time.sleep(ex.seconds)
            return await self.try_forward_message(client, channel)
        except Exception as ex:
            return False, ex
        
    async def forward_message(self, client: TelegramClient, channel):
        message_to_forward_split = self.message_to_forward.rsplit("/", 1)
        from_peer = message_to_forward_split[0]
        message_id = int(message_to_forward_split[1])
        print(f"Channel: {channel}")
        if "/" in channel:
            parts = channel.split("/")
            if len(parts) > 4:
                topic_id = parts[-1]
                print(f"topic list: {topic_id}")
                forward_message_request = ForwardMessagesRequest(
                    from_peer=from_peer,
                    id=[message_id],
                    to_peer=str(parts[-2]),
                    top_msg_id=int(topic_id)
                )
            elif len(parts) == 4:
                forward_message_request = ForwardMessagesRequest(
                    from_peer=from_peer,
                    id=[message_id],
                    to_peer=str(parts[-1])
                )
        else:
            forward_message_request = ForwardMessagesRequest(
                from_peer=from_peer,
                id=[message_id],
                to_peer=str(parts[-2])
            )
            
        return await client(forward_message_request)
    
    def handle_response_message(self, message_status, error, channel):
        if not message_status:
            if self.log_failed_channels:
                self.failed_channels.append((channel, error))
            print(f"[{console.red('-')}] Failed => {channel} | {error}")
            self.write_failed_channels_to_file()
            return
        
        self.success += 1
        print(f"[{console.green(self.success)}] Forwarded => {channel}")
        
    def write_failed_channels_to_file(self):
        if self.log_failed_channels:
            with open("failed_channels.txt", "a") as file:
                for channel, error in self.failed_channels:
                    file.write(f"{channel} => {error}\n")
            self.failed_channels = []
