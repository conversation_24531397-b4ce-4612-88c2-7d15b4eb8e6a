#!/usr/bin/env python3
"""
EMERGENCY UI FREEZE FIX - Move all joining operations to background threads
This fixes the critical "Not Responding" issue when clicking Start Joiner
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtCore import QTimer, pyqtSignal, QObject
import weakref
import sys
import traceback

# Import crash prevention system
try:
    from crash_prevention_system import apply_crash_prevention
    CRASH_PREVENTION_AVAILABLE = True
except ImportError:
    CRASH_PREVENTION_AVAILABLE = False
    print("⚠️ Crash prevention system not available - UI fix only")

class EmergencyUIFreezeFix:
    """Emergency fix to prevent UI freezing during joining operations"""
    
    def __init__(self, main_app):
        self.main_app = main_app
        self.background_executor = ThreadPoolExecutor(
            max_workers=10, 
            thread_name_prefix="UIFreezeFix"
        )
        
    def apply_fix(self):
        """Apply the emergency UI freeze fix with crash prevention"""
        
        # Apply crash prevention system first
        if CRASH_PREVENTION_AVAILABLE:
            try:
                self.crash_handler = apply_crash_prevention(self.main_app)
                print("🛡️ Crash prevention system applied")
            except Exception as e:
                print(f"⚠️ Crash prevention failed: {e}")
                self.crash_handler = None
        else:
            self.crash_handler = None
        
        # Store original methods
        self.main_app._original_start_all_joining_tasks = self.main_app.start_all_joining_tasks
        self.main_app._original_start_joining_task = self.main_app.start_joining_task
        
        # Replace with non-blocking versions
        self.main_app.start_all_joining_tasks = self._start_all_joining_tasks_non_blocking
        self.main_app.start_joining_task = self._start_joining_task_non_blocking
        
        # Set up global exception handler for additional safety
        self._setup_emergency_exception_handler()
        
        print("✅ Emergency UI freeze fix applied - joining operations now run in background")
        print("🛡️ Crash prevention active - TG Checker is now crash-proof!")
    
    def _start_all_joining_tasks_non_blocking(self):
        """Non-blocking version that runs in background thread"""
        def background_start_all():
            try:
                print("🚀 Starting all joining tasks in background thread...")
                
                tasks_to_start = []
                
                # Collect tasks to start (quick operation)
                for task_id, task in self.main_app.joining_tasks.items():
                    if task['status'] != 'running':
                        tasks_to_start.append(task_id)
                
                print(f"Found {len(tasks_to_start)} tasks to start")
                
                # Start each task with delay to prevent overwhelming
                for i, task_id in enumerate(tasks_to_start):
                    try:
                        print(f"Starting task {i+1}/{len(tasks_to_start)}: {task_id}")
                        
                        # Call the background start method
                        self._start_joining_task_background_safe(task_id)
                        
                        # Small delay between starts to prevent resource conflicts
                        time.sleep(0.5)
                        
                    except Exception as task_error:
                        print(f"Error starting task {task_id}: {task_error}")
                        continue
                
                # Log completion using Qt signal
                QTimer.singleShot(0, lambda: self._log_completion(len(tasks_to_start)))
                
            except Exception as e:
                print(f"Error in background start all: {e}")
                QTimer.singleShot(0, lambda: self._log_error(f"Failed to start tasks: {e}"))
        
        # Immediate UI feedback
        try:
            if hasattr(self.main_app, 'start_all_joining_tasks_btn'):
                self.main_app.start_all_joining_tasks_btn.setEnabled(False)
                self.main_app.start_all_joining_tasks_btn.setText("Starting...")
        except:
            pass
        
        # Run in background
        self.background_executor.submit(background_start_all)
    
    def _start_joining_task_non_blocking(self, task_id):
        """Non-blocking version of start_joining_task"""
        def background_start_single():
            try:
                self._start_joining_task_background_safe(task_id)
            except Exception as e:
                print(f"Error starting task {task_id}: {e}")
                QTimer.singleShot(0, lambda: self._log_error(f"Failed to start task {task_id}: {e}"))
        
        # Run in background immediately
        self.background_executor.submit(background_start_single)
    
    def _start_joining_task_background_safe(self, task_id):
        """Safely start a joining task in background thread"""
        try:
            if task_id not in self.main_app.joining_tasks:
                QTimer.singleShot(0, lambda: self._log_error(f"Task {task_id} not found"))
                return
            
            task = self.main_app.joining_tasks[task_id]
            phone = task['account_phone']
            
            # Check flood wait (non-blocking)
            if hasattr(self.main_app, 'flood_wait_tracker') and phone in self.main_app.flood_wait_tracker.active_timers:
                remaining = self.main_app.flood_wait_tracker.get_remaining_time(phone)
                if remaining > 0:
                    QTimer.singleShot(0, lambda: self._log_message("warning", task_id, 
                        f"⏳ Account {phone} in flood wait for {remaining}s - will auto-retry"))
                    
                    # Schedule retry after flood wait
                    def retry_after_flood_wait():
                        time.sleep(remaining)
                        self._start_joining_task_background_safe(task_id)
                    
                    threading.Thread(target=retry_after_flood_wait, daemon=True).start()
                    return
            
            # Create high-performance executor if needed
            if not hasattr(self.main_app, 'emergency_executor'):
                self.main_app.emergency_executor = ThreadPoolExecutor(
                    max_workers=50,
                    thread_name_prefix="EmergencyJoin"
                )
            
            # Submit to emergency executor with safety wrapper
            weak_app = weakref.ref(self.main_app)
            
            def safe_task_runner():
                app = weak_app()
                if app is None:
                    print(f"App deleted, cancelling task {task_id}")
                    return
                
                try:
                    # Call original async task runner
                    if hasattr(app, '_run_joining_task_optimized_safe'):
                        return app._run_joining_task_optimized_safe(task)
                    elif hasattr(app, '_run_joining_task_thread'):
                        app._run_joining_task_thread(task)
                        return 'completed'
                    else:
                        print(f"No task runner found for {task_id}")
                        return 'failed'
                        
                except Exception as e:
                    print(f"Task runner error for {task_id}: {e}")
                    QTimer.singleShot(0, lambda: app.log_joining_message("error", task_id, f"Task error: {e}"))
                    return 'failed'
            
            future = self.main_app.emergency_executor.submit(safe_task_runner)
            
            # Track active task
            if not hasattr(self.main_app, 'emergency_active_tasks'):
                self.main_app.emergency_active_tasks = {}
            self.main_app.emergency_active_tasks[task_id] = future
            
            # Update UI via Qt signal (non-blocking)
            QTimer.singleShot(0, lambda: self._update_task_status(task_id, 'running'))
            QTimer.singleShot(0, lambda: self._log_message("info", task_id, f"🚀 Emergency task started: {task['name']}"))
            
        except Exception as e:
            print(f"Emergency start error for {task_id}: {e}")
            QTimer.singleShot(0, lambda: self._log_error(f"Failed to start {task_id}: {e}"))
    
    def _update_task_status(self, task_id, status):
        """Update task status via main thread"""
        try:
            if hasattr(self.main_app, 'update_joining_task_status'):
                self.main_app.update_joining_task_status(task_id, status)
            if hasattr(self.main_app, 'refresh_joining_tasks'):
                self.main_app.refresh_joining_tasks()
        except Exception as e:
            print(f"Status update error: {e}")
    
    def _log_message(self, level, task_id, message):
        """Log message via main thread"""
        try:
            if hasattr(self.main_app, 'log_joining_message'):
                self.main_app.log_joining_message(level, task_id, message)
        except Exception as e:
            print(f"Log error: {e}")
    
    def _log_completion(self, count):
        """Log completion message"""
        try:
            self._log_message("info", "", f"✅ Started {count} joining tasks in background")
            
            # Re-enable button
            if hasattr(self.main_app, 'start_all_joining_tasks_btn'):
                self.main_app.start_all_joining_tasks_btn.setEnabled(True)
                self.main_app.start_all_joining_tasks_btn.setText("Start All Tasks")
        except:
            pass
    
    def _log_error(self, message):
        """Log error message"""
        try:
            self._log_message("error", "", message)
            
            # Re-enable button
            if hasattr(self.main_app, 'start_all_joining_tasks_btn'):
                self.main_app.start_all_joining_tasks_btn.setEnabled(True)
                self.main_app.start_all_joining_tasks_btn.setText("Start All Tasks")
        except:
            pass
    
    def _setup_emergency_exception_handler(self):
        """Set up emergency exception handler"""
        original_excepthook = sys.excepthook
        
        def emergency_excepthook(exc_type, exc_value, exc_traceback):
            """Emergency exception handler that prevents total crashes"""
            try:
                error_msg = f"EMERGENCY: {exc_type.__name__}: {exc_value}"
                print(f"🚨 {error_msg}")
                
                # Try to log and notify user
                try:
                    if hasattr(self.main_app, 'logger'):
                        self.main_app.logger.error(error_msg)
                        self.main_app.logger.error("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
                    
                    # Notify user via UI if possible
                    QTimer.singleShot(0, lambda: self._emergency_notify(error_msg))
                    
                except Exception:
                    print(f"Emergency notification failed for: {error_msg}")
                
                # Force garbage collection to clean up
                import gc
                gc.collect()
                
                # Don't call original handler to prevent crash
                print("🛡️ Emergency handler prevented total crash")
                
            except Exception:
                # Last resort - call original handler
                original_excepthook(exc_type, exc_value, exc_traceback)
        
        sys.excepthook = emergency_excepthook
    
    def _emergency_notify(self, error_msg):
        """Emergency notification to user"""
        try:
            if hasattr(self.main_app, 'log_joining_message'):
                self.main_app.log_joining_message("error", "", f"🚨 Emergency: {error_msg[:100]}...")
        except Exception:
            print(f"Emergency UI notification failed: {error_msg}")

def apply_emergency_ui_freeze_fix(main_app):
    """Apply the emergency UI freeze fix to the main app"""
    fix = EmergencyUIFreezeFix(main_app)
    fix.apply_fix()
    return fix

if __name__ == "__main__":
    print("🚑 Emergency UI Freeze Fix - Ready to apply")
    print("This fix moves all joining operations to background threads")
    print("to prevent the 'Not Responding' UI freeze issue.") 