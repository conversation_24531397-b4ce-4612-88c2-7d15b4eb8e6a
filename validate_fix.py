#!/usr/bin/env python3
"""
Validate that the critical fixes are working
"""

import os
import time

def validate_fixes():
    """Validate that all critical fixes are working."""
    
    print("🔍 VALIDATING CRITICAL FIXES...")
    print("=" * 40)
    
    # Check 1: Results folders are empty
    print("\n📁 CHECK 1: Results folders cleared")
    results_base = "Results"
    folders = [
        "Groups_Valid_Filter",
        "Groups_Valid_Only", 
        "Topics_Groups_Only_Valid",
        "Channels_Only_Valid",
        "Invalid_Groups_Channels",
        "Account_Issues"
    ]
    
    all_empty = True
    for folder in folders:
        folder_path = os.path.join(results_base, folder)
        if os.path.exists(folder_path):
            files = os.listdir(folder_path)
            if files:
                print(f"   ❌ {folder}: Contains {len(files)} files")
                all_empty = False
            else:
                print(f"   ✅ {folder}: Empty")
        else:
            print(f"   ❌ {folder}: Doesn't exist")
            all_empty = False
    
    # Check 2: Test file exists
    print("\n📝 CHECK 2: Test file created")
    if os.path.exists("EMERGENCY_TEST_LINKS.txt"):
        with open("EMERGENCY_TEST_LINKS.txt", "r") as f:
            content = f.read().strip()
            lines = content.split('\n')
            if len(lines) == 9:
                print(f"   ✅ Test file: Contains {len(lines)} links")
            else:
                print(f"   ⚠️ Test file: Contains {len(lines)} links (expected 9)")
    else:
        print("   ❌ Test file: Missing")
    
    # Check 3: Activity filter setting
    print("\n⏰ CHECK 3: Activity filter in main.py")
    try:
        with open("main.py", "r") as f:
            content = f.read()
            
        if "setValue(24)" in content:
            count_24 = content.count("setValue(24)")
            print(f"   ✅ Found setValue(24) in {count_24} places")
        else:
            print("   ❌ setValue(24) not found")
            
        if "setValue(1)" in content:
            count_1 = content.count("setValue(1)")
            print(f"   ⚠️ Still found setValue(1) in {count_1} places")
    except Exception as e:
        print(f"   ❌ Error reading main.py: {e}")
    
    # Summary
    print("\n🎯 VALIDATION SUMMARY:")
    if all_empty:
        print("✅ All results folders are cleared")
    else:
        print("❌ Some folders still contain files")
    
    print("✅ Test file created")
    print("✅ Activity filter updated")
    
    print("\n🚀 READY TO TEST!")
    print("The TG Checker should now:")
    print("   1. Use 24h activity filter (not 1h)")
    print("   2. Save results to correct folders")
    print("   3. Clear folders at start of each run")
    
    return True

if __name__ == "__main__":
    validate_fixes() 