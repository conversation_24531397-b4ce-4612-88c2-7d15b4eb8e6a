import re
import os
import sys
import shutil

def fix_final(filename="main.py"):
    """Fix all syntax issues comprehensively."""
    # Create a backup
    backup_file = f"{filename}.backup"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix all issues
    content = fix_trailing_colons(content)
    content = fix_indentation_issues(content)
    
    # Save to a new file
    output_file = "main_final_fix.py"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed all issues and saved to {output_file}")
    return output_file

def fix_trailing_colons(content):
    """Fix all trailing colons that cause syntax errors."""
    # Split into lines
    lines = content.split('\n')
    fixed_count = 0
    
    # Fix each line
    for i in range(len(lines)):
        line = lines[i]
        
        # Check for statement-ending colons that shouldn't be there
        if any(pattern in line for pattern in [
            "self.logger.",
            ".emit(",
            "return",
            "print(",
            ".setText(",
        ]) and line.strip().endswith(':'):
            # Remove the trailing colon
            lines[i] = line.rstrip(':')
            fixed_count += 1
            print(f"Fixed invalid colon on line {i+1}")
        
        # Check for method calls ending with colons
        method_call_pattern = r'^(\s*)(.+\.(emit|info|error|warning|debug|setText|append|write)\([^)]*\))(:)(\s*)$'
        match = re.match(method_call_pattern, line)
        if match:
            # Remove the trailing colon
            lines[i] = match.group(1) + match.group(2) + match.group(5)
            fixed_count += 1
            print(f"Fixed invalid method call colon on line {i+1}")
    
    print(f"Fixed {fixed_count} trailing colons")
    return '\n'.join(lines)

def fix_indentation_issues(content):
    """Fix indentation issues in the code."""
    # Fix lines around 575-576 - start_monitor not indented after if
    lines = content.split('\n')
    
    for i in range(570, 580):
        if i < len(lines):
            if "if self.settings.value(\"auto_start_monitor\"" in lines[i]:
                # Found the if statement, make sure the next line is properly indented
                if i+1 < len(lines) and "self.start_monitor()" in lines[i+1]:
                    # Get the indentation of the if statement
                    if_indent = re.match(r"(\s*)", lines[i]).group(1)
                    # Set proper indentation for the start_monitor line
                    lines[i+1] = if_indent + "    " + lines[i+1].lstrip()
                    print(f"Fixed indentation on line {i+1}")
    
    # Fix indentation around line 2676 - for statement
    for i in range(2670, 2680):
        if i < len(lines):
            # Look for a for statement followed by unindented line
            if "for" in lines[i] and lines[i].strip().endswith(":"):
                if i+1 < len(lines):
                    # Get indentation of for statement
                    for_indent = re.match(r"(\s*)", lines[i]).group(1)
                    # Set proper indentation for the next line
                    next_line = lines[i+1]
                    next_indent = re.match(r"(\s*)", next_line).group(1)
                    
                    if len(next_indent) <= len(for_indent):
                        lines[i+1] = for_indent + "    " + next_line.lstrip()
                        print(f"Fixed for loop indentation on line {i+1}")
    
    # Fix indentation around line 2677 - if statement
    for i in range(2675, 2685):
        if i < len(lines):
            # Look for an if statement followed by unindented line
            if "if" in lines[i] and lines[i].strip().endswith(":"):
                if i+1 < len(lines):
                    # Get indentation of if statement
                    if_indent = re.match(r"(\s*)", lines[i]).group(1)
                    # Set proper indentation for the next line
                    next_line = lines[i+1]
                    next_indent = re.match(r"(\s*)", next_line).group(1)
                    
                    if len(next_indent) <= len(if_indent):
                        lines[i+1] = if_indent + "    " + next_line.lstrip()
                        print(f"Fixed if statement indentation on line {i+1}")
    
    # Fix else/elif/except/finally alignment around lines 3013-3014
    for i in range(3010, 3020):
        if i < len(lines) and i > 0:
            if any(keyword in lines[i] for keyword in ["else:", "elif ", "except ", "finally:"]):
                # Check if this is a misaligned else/except
                prev_lines = lines[max(0, i-10):i]
                
                # Find the indentation of the corresponding if/try
                for j, prev_line in enumerate(reversed(prev_lines)):
                    if "if " in prev_line or "try:" in prev_line:
                        prev_indent = re.match(r"(\s*)", prev_line).group(1)
                        curr_indent = re.match(r"(\s*)", lines[i]).group(1)
                        
                        # If indentation doesn't match, fix it
                        if len(curr_indent) != len(prev_indent):
                            lines[i] = prev_indent + lines[i].lstrip()
                            print(f"Fixed control statement alignment on line {i+1}")
                        break
    
    # Fix try statements without except/finally around line 3062
    for i in range(3055, 3070):
        if i < len(lines):
            if lines[i].strip() == "try:":
                # Look ahead to find an except or finally
                has_except = False
                try_indent = re.match(r"(\s*)", lines[i]).group(1)
                
                for j in range(i+1, min(i+15, len(lines))):
                    if lines[j].strip().startswith(("except", "finally")):
                        has_except = True
                        break
                    # If we hit a line with less indentation, we're out of the try block
                    curr_indent = re.match(r"(\s*)", lines[j]).group(1)
                    if curr_indent and len(curr_indent) <= len(try_indent):
                        break
                
                # If no except found, add one
                if not has_except:
                    # Find where to insert the except block
                    insert_pos = i + 1
                    while insert_pos < len(lines):
                        curr_indent = re.match(r"(\s*)", lines[insert_pos]).group(1)
                        if len(curr_indent) <= len(try_indent):
                            break
                        insert_pos += 1
                    
                    # Insert except block
                    except_line = try_indent + "except Exception as e:"
                    handler_line = try_indent + "    self.logger.error(f\"Error: {str(e)}\")"
                    
                    lines.insert(insert_pos, handler_line)
                    lines.insert(insert_pos, except_line)
                    print(f"Added missing except block after try on line {i+1}")
    
    # Fix indentation on lines 3073-3074
    for i in range(3070, 3080):
        if i < len(lines) and i > 0:
            curr_line = lines[i]
            prev_line = lines[i-1]
            
            # If previous line ends with colon and current line is not indented
            if prev_line.strip().endswith(":") and curr_line.strip() and not curr_line.startswith(" "):
                prev_indent = re.match(r"(\s*)", prev_line).group(1)
                lines[i] = prev_indent + "    " + curr_line.lstrip()
                print(f"Fixed missing indentation on line {i+1}")
    
    # Fix missing colon on line 3086
    for i in range(3080, 3090):
        if i < len(lines):
            # Look for control statements without colon
            control_keywords = ["if ", "else", "elif ", "except ", "finally", "while ", "for "]
            if any(keyword in lines[i] for keyword in control_keywords) and not lines[i].strip().endswith(":"):
                # Add missing colon
                lines[i] = lines[i].rstrip() + ":"
                print(f"Added missing colon on line {i+1}")
    
    return '\n'.join(lines)

def fix_indentation():
    """Fix the indentation of the PRAGMA busy_timeout statement"""
    print("Making a backup of main.py...")
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    with open("main.py.bak_final", "w", encoding="utf-8") as backup:
        backup.writelines(lines)
    
    # Find the PRAGMA line and fix its indentation
    for i, line in enumerate(lines):
        if "PRAGMA busy_timeout" in line:
            # The line before this should have indentation we can match
            if i > 0 and lines[i-1].strip().startswith("#"):
                indent = " " * (len(lines[i-1]) - len(lines[i-1].lstrip()))
                lines[i] = indent + line.lstrip()
    
    # Write the fixed content back
    with open("main.py", "w", encoding="utf-8") as file:
        file.writelines(lines)
    
    print("Fixed indentation of PRAGMA busy_timeout statement in main.py")

if __name__ == "__main__":
    output_file = fix_final()
    
    # Create batch files
    batch_content = """@echo off
echo Running TG Checker with all syntax issues fixed...
python main_final_fix.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_final_fix.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Jarandni programi chakkrawi tawaw...
echo Hamu kishakani syntax charesar kra.
python main_final_fix.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Bo zanini ziatr sairi faily log bka.
    pause
)
pause
"""
    
    with open("run_final_fix_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print(f"Created batch files to run the fixed application")
    print(f"You can now run: python {output_file}")
    print(f"Or use the batch files: run_final_fix.bat or run_final_fix_kurdish.bat")
    
    fix_indentation() 