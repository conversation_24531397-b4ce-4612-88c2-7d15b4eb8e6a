#!/usr/bin/env python3
"""
Crash Handler Module for TG Checker
Handles fatal errors, memory management, and crash logging
"""

import os
import sys
import traceback
import logging
import threading
import time
import gc
import psutil
from datetime import datetime
from typing import Optional, Callable

class CrashHandler:
    """Comprehensive crash handler for the TG Checker application."""
    
    def __init__(self, app_name: str = "TG_Checker"):
        self.app_name = app_name
        self.crash_log_file = os.path.join("logs", "crash.log")
        self.memory_log_file = os.path.join("logs", "memory.log")
        self.exception_log_file = os.path.join("logs", "exception.log")
        
        # Ensure logs directory exists
        os.makedirs("logs", exist_ok=True)
        
        # Set up crash logger
        self.crash_logger = self._setup_crash_logger()
        self.memory_logger = self._setup_memory_logger()
        self.exception_logger = self._setup_exception_logger()
        
        # Memory monitoring
        self.memory_threshold_mb = 1024  # 1GB threshold
        self.memory_check_interval = 30  # Check every 30 seconds
        self.memory_monitor_active = False
        
        # Exception handling
        self.original_excepthook = sys.excepthook
        sys.excepthook = self.handle_exception
        
        # Thread tracking
        self.active_threads = set()
        self.thread_lock = threading.Lock()
        
        self.crash_logger.info(f"CrashHandler initialized for {app_name}")
    
    def _setup_crash_logger(self) -> logging.Logger:
        """Set up crash-specific logger."""
        logger = logging.getLogger(f"{self.app_name}_crash")
        logger.setLevel(logging.ERROR)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler for crash logs
        handler = logging.FileHandler(self.crash_log_file, encoding='utf-8')
        formatter = logging.Formatter(
            '%(asctime)s - CRASH - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def _setup_memory_logger(self) -> logging.Logger:
        """Set up memory monitoring logger."""
        logger = logging.getLogger(f"{self.app_name}_memory")
        logger.setLevel(logging.INFO)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler for memory logs
        handler = logging.FileHandler(self.memory_log_file, encoding='utf-8')
        formatter = logging.Formatter(
            '%(asctime)s - MEMORY - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def _setup_exception_logger(self) -> logging.Logger:
        """Set up exception logger."""
        logger = logging.getLogger(f"{self.app_name}_exception")
        logger.setLevel(logging.WARNING)
        
        # Remove existing handlers
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # File handler for exception logs
        handler = logging.FileHandler(self.exception_log_file, encoding='utf-8')
        formatter = logging.Formatter(
            '%(asctime)s - EXCEPTION - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        return logger
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """Handle uncaught exceptions."""
        if issubclass(exc_type, KeyboardInterrupt):
            # Allow keyboard interrupts to pass through
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        # Log the crash
        crash_info = {
            'timestamp': datetime.now().isoformat(),
            'exception_type': exc_type.__name__,
            'exception_message': str(exc_value),
            'traceback': ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback)),
            'memory_usage': self.get_memory_usage(),
            'active_threads': len(threading.enumerate()),
            'process_id': os.getpid()
        }
        
        self.crash_logger.error(f"FATAL CRASH DETECTED:")
        self.crash_logger.error(f"Type: {crash_info['exception_type']}")
        self.crash_logger.error(f"Message: {crash_info['exception_message']}")
        self.crash_logger.error(f"Memory Usage: {crash_info['memory_usage']:.2f} MB")
        self.crash_logger.error(f"Active Threads: {crash_info['active_threads']}")
        self.crash_logger.error(f"Traceback:\n{crash_info['traceback']}")
        
        # Try to save crash dump
        try:
            self.save_crash_dump(crash_info)
        except Exception as e:
            self.crash_logger.error(f"Failed to save crash dump: {str(e)}")
        
        # Call original exception hook
        self.original_excepthook(exc_type, exc_value, exc_traceback)
    
    def save_crash_dump(self, crash_info: dict):
        """Save detailed crash dump."""
        crash_dump_file = os.path.join("logs", f"crash_dump_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        with open(crash_dump_file, 'w', encoding='utf-8') as f:
            f.write(f"TG Checker Crash Dump\n")
            f.write(f"Generated: {crash_info['timestamp']}\n")
            f.write(f"{'='*50}\n\n")
            
            f.write(f"Exception Type: {crash_info['exception_type']}\n")
            f.write(f"Exception Message: {crash_info['exception_message']}\n")
            f.write(f"Memory Usage: {crash_info['memory_usage']:.2f} MB\n")
            f.write(f"Active Threads: {crash_info['active_threads']}\n")
            f.write(f"Process ID: {crash_info['process_id']}\n\n")
            
            f.write(f"Full Traceback:\n")
            f.write(f"{'-'*30}\n")
            f.write(crash_info['traceback'])
            f.write(f"\n{'-'*30}\n\n")
            
            # Add system information
            try:
                f.write(f"System Information:\n")
                f.write(f"Platform: {sys.platform}\n")
                f.write(f"Python Version: {sys.version}\n")
                f.write(f"Working Directory: {os.getcwd()}\n")
                
                # Memory details
                process = psutil.Process()
                memory_info = process.memory_info()
                f.write(f"RSS Memory: {memory_info.rss / 1024 / 1024:.2f} MB\n")
                f.write(f"VMS Memory: {memory_info.vms / 1024 / 1024:.2f} MB\n")
                
                # Thread details
                f.write(f"\nActive Threads:\n")
                for thread in threading.enumerate():
                    f.write(f"  - {thread.name} (alive: {thread.is_alive()})\n")
                
            except Exception as e:
                f.write(f"Error collecting system info: {str(e)}\n")
        
        self.crash_logger.info(f"Crash dump saved to: {crash_dump_file}")
    
    def log_exception(self, exception: Exception, context: str = ""):
        """Log non-fatal exceptions."""
        exc_info = {
            'timestamp': datetime.now().isoformat(),
            'context': context,
            'exception_type': type(exception).__name__,
            'exception_message': str(exception),
            'traceback': traceback.format_exc()
        }
        
        self.exception_logger.warning(f"Exception in {context}: {exc_info['exception_type']} - {exc_info['exception_message']}")
        self.exception_logger.debug(f"Traceback:\n{exc_info['traceback']}")
    
    def get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def start_memory_monitoring(self):
        """Start memory monitoring in background thread."""
        if self.memory_monitor_active:
            return
        
        self.memory_monitor_active = True
        monitor_thread = threading.Thread(target=self._memory_monitor_loop, daemon=True)
        monitor_thread.name = "MemoryMonitor"
        monitor_thread.start()
        
        self.memory_logger.info("Memory monitoring started")
    
    def stop_memory_monitoring(self):
        """Stop memory monitoring."""
        self.memory_monitor_active = False
        self.memory_logger.info("Memory monitoring stopped")
    
    def _memory_monitor_loop(self):
        """Memory monitoring loop."""
        while self.memory_monitor_active:
            try:
                memory_mb = self.get_memory_usage()
                thread_count = len(threading.enumerate())
                
                # Log memory usage
                self.memory_logger.info(f"Memory: {memory_mb:.2f} MB, Threads: {thread_count}")
                
                # Check for memory threshold
                if memory_mb > self.memory_threshold_mb:
                    self.memory_logger.warning(f"HIGH MEMORY USAGE: {memory_mb:.2f} MB (threshold: {self.memory_threshold_mb} MB)")
                    
                    # Force garbage collection
                    collected = gc.collect()
                    new_memory = self.get_memory_usage()
                    self.memory_logger.info(f"Garbage collection: {collected} objects collected, memory: {new_memory:.2f} MB")
                
                # Check for thread accumulation
                if thread_count > 20:  # Threshold for too many threads
                    self.memory_logger.warning(f"HIGH THREAD COUNT: {thread_count} active threads")
                    
                    # Log thread details
                    for thread in threading.enumerate():
                        self.memory_logger.debug(f"Thread: {thread.name} (alive: {thread.is_alive()})")
                
                time.sleep(self.memory_check_interval)
                
            except Exception as e:
                self.memory_logger.error(f"Error in memory monitoring: {str(e)}")
                time.sleep(5)  # Short sleep on error
    
    def force_cleanup(self):
        """Force cleanup of resources."""
        try:
            # Force garbage collection
            collected = gc.collect()
            self.memory_logger.info(f"Force cleanup: {collected} objects collected")
            
            # Log memory after cleanup
            memory_after = self.get_memory_usage()
            self.memory_logger.info(f"Memory after cleanup: {memory_after:.2f} MB")
            
        except Exception as e:
            self.memory_logger.error(f"Error during force cleanup: {str(e)}")
    
    def register_thread(self, thread_name: str):
        """Register a thread for tracking."""
        with self.thread_lock:
            self.active_threads.add(thread_name)
        self.memory_logger.debug(f"Thread registered: {thread_name}")
    
    def unregister_thread(self, thread_name: str):
        """Unregister a thread."""
        with self.thread_lock:
            self.active_threads.discard(thread_name)
        self.memory_logger.debug(f"Thread unregistered: {thread_name}")
    
    def get_active_threads(self) -> set:
        """Get set of active tracked threads."""
        with self.thread_lock:
            return self.active_threads.copy()
    
    def shutdown(self):
        """Shutdown crash handler."""
        try:
            self.stop_memory_monitoring()
            
            # Restore original exception hook
            sys.excepthook = self.original_excepthook
            
            self.crash_logger.info("CrashHandler shutdown complete")
            
        except Exception as e:
            print(f"Error during CrashHandler shutdown: {str(e)}")

# Global crash handler instance
crash_handler: Optional[CrashHandler] = None

def initialize_crash_handler(app_name: str = "TG_Checker") -> CrashHandler:
    """Initialize global crash handler."""
    global crash_handler
    if crash_handler is None:
        crash_handler = CrashHandler(app_name)
        crash_handler.start_memory_monitoring()
    return crash_handler

def get_crash_handler() -> Optional[CrashHandler]:
    """Get global crash handler instance."""
    return crash_handler

def log_exception(exception: Exception, context: str = ""):
    """Log exception using global crash handler."""
    if crash_handler:
        crash_handler.log_exception(exception, context)

def force_cleanup():
    """Force cleanup using global crash handler."""
    if crash_handler:
        crash_handler.force_cleanup()

def shutdown_crash_handler():
    """Shutdown global crash handler."""
    global crash_handler
    if crash_handler:
        crash_handler.shutdown()
        crash_handler = None 