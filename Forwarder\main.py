import fileio
import constants
import console
import telegram
import asyncio
import json
import textwrap
from rich.console import Console
from rich.prompt import Prompt
from rich.table import Table
from rich import box
import pyfiglet
import os

def build_option_prompt():
    msg = f"[{console.blue('1')}] Group Joiner\n"
    msg += f"[{console.blue('2')}] Message Forwarder\n"
    msg += f"[{console.blue('3')}] Exit\n\n"
    msg += f"[{console.yellow('?')}] Select Option: "
    return msg

def main():
    channels = fileio.read_lines(constants.GROUPS_FILE_PATH)
    if len(channels) == 0:
        exit(f"\n{console.style('[!]', fg='red')} Error: No channel links found in file.\n")

    settings = json.load(fileio.read_file(constants.CONFIG_FILE_PATH))

    t = telegram.Telegram(settings, channels)

    console = Console()
    console.clear()
    console.print(pyfiglet.figlet_format("AutoSight", font="slant"))
    console.print("Welcome to AutoSight! The best advertising bot in the community.", style="#fd9e0c")
    console.print("Brought to you by Eviona.\n", style="#fd9e0c")
    table = Table(show_header=True, header_style="bold", box=box.ROUNDED)
    table.add_column("Option")
    table.add_column("Description")
    table.add_row(
        ("1"),
        "Group Joiner"
    )
    table.add_row(
        ("2"),
        "Message Forwarder"
    )
    table.add_row(
        ("3"),
        "Exit"
    )
    console.print("Please select an option:")
    console.print(table)
    console.print()
    option = Prompt.ask("Enter your choice", choices=["1", "2", "3"])

    print()

    if option == "1":
        console.print("Joining all channels...\n")
        asyncio.run(t.join_all_channels())
    
    elif option == "2":
        console.print("Forwarding messages to all channels...\n")
        asyncio.run(t.forward_to_all_continuously())

    elif option == "3":
        exit("Exiting program...")

    else:
        exit("\nInvalid option. Exiting...\n")

if __name__ == "__main__":
    main()
