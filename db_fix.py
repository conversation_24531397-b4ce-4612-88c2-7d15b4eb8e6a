"""
Fix SQLite set_busy_timeout compatibility issue in main.py
"""
import re

def fix_sqlite_timeout():
    """Fix the 'set_busy_timeout' compatibility issue in main.py"""
    print("Making a backup of main.py...")
    with open("main.py", "r", encoding="utf-8") as file:
        content = file.read()
    
    with open("main.py.bak_db", "w", encoding="utf-8") as backup:
        backup.write(content)
    
    # Replace set_busy_timeout with a compatible approach
    # Find patterns like: conn.set_busy_timeout(10000)
    fixed_content = re.sub(
        r'conn\.set_busy_timeout\((\d+)\)',
        r'# Fixed set_busy_timeout compatibility\nconn.execute("PRAGMA busy_timeout = \\1")',
        content
    )
    
    # Write the fixed content back
    with open("main.py", "w", encoding="utf-8") as file:
        file.write(fixed_content)
    
    print("Fixed SQLite compatibility issue in main.py")

if __name__ == "__main__":
    fix_sqlite_timeout() 