#!/usr/bin/env python3
"""
Fix script for joining task progress counter issues in TG Checker.
This script creates a new file with the necessary fixes.
"""

import os
import shutil
from datetime import datetime

def main():
    print("Starting fix for joining task progress counter issues...")
    
    # Create backup of main.py
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"Created backup at: {backup_file}")
    except Exception as e:
        print(f"Warning: Could not create backup: {str(e)}")
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Create a fix file with the function implementation
    fix_content = '''
# Add this function after reset_forwarder_task_progress function
def reset_joining_task_progress(self, task_id):
    """Reset joining task progress to allow re-running or new task creation."""
    try:
        conn = sqlite3.connect(self.joining_db_path, timeout=30)
        cursor = conn.cursor()
        
        query = """UPDATE joining_tasks SET 
                   current_index = 0, 
                   successful_joins = 0, 
                   failed_joins = 0,
                   updated_at = ?
                   WHERE id = ?"""
        
        cursor.execute(query, [datetime.now().isoformat(), task_id])
        conn.commit()
        conn.close()
        
        # Update local cache
        if task_id in self.joining_tasks:
            self.joining_tasks[task_id]['current_index'] = 0
            self.joining_tasks[task_id]['successful_joins'] = 0
            self.joining_tasks[task_id]['failed_joins'] = 0
        
        self.logger.info(f"Joining task {task_id} progress reset successfully")
        
    except Exception as e:
        self.logger.error(f"Failed to reset joining task progress {task_id}: {str(e)}")
        raise

# Modify the start_joining_task function to include these lines after checking task existence:
# # ALWAYS reset task progress when manually starting (allows re-running any task)
# self.logger.info(f"Resetting joining task progress for manual restart: {task_id}")
# self.log_joining_message("info", task_id, "🔄 Resetting progress counters for clean start")
# self.reset_joining_task_progress(task_id)
#            
# # Refresh task data after reset
# self.refresh_joining_tasks()
'''
    
    # Write the fix instructions to a file
    fix_file = "joining_fix_instructions.txt"
    try:
        with open(fix_file, "w", encoding="utf-8") as f:
            f.write(fix_content)
        print(f"✅ Created fix instructions at: {fix_file}")
    except Exception as e:
        print(f"Error creating fix instructions: {str(e)}")
        return

    print("\n✅ Fix preparation completed successfully!")
    print(f"Backup saved at: {backup_file}")
    print(f"Instructions saved at: {fix_file}")
    print("\nFollow these steps to apply the fix:")
    print("1. Open main.py in your code editor")
    print("2. Find the 'reset_forwarder_task_progress' function")
    print("3. Add the 'reset_joining_task_progress' function right after it")
    print("4. Find the 'start_joining_task' function")
    print("5. Add the reset code right after checking if the task exists")
    print("\nAfter making these changes, restart the application.")

if __name__ == "__main__":
    main() 