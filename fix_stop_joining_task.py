#!/usr/bin/env python3
"""
Fix indentation of the stop_joining_task function in main.py
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file."""
    backup_name = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("main.py", backup_name)
    print(f"Created backup: {backup_name}")
    return backup_name

def fix_indentation():
    """Fix the indentation of the stop_joining_task function."""
    print("Fixing indentation of stop_joining_task function...")
    
    # Create a backup
    backup_file = backup_main_file()
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    # Find the problematic function
    target_line = None
    for i, line in enumerate(lines):
        if "def stop_joining_task(self, task_id):" in line:
            target_line = i
            break
    
    if target_line is None:
        print("Could not find the stop_joining_task function.")
        return False
    
    print(f"Found stop_joining_task function at line {target_line + 1}")
    
    # Replace the function with a properly indented version
    # We'll replace 13 lines starting from the function definition
    properly_indented_function = [
        "    def stop_joining_task(self, task_id):\n",
        "        \"\"\"Stop a specific joining task with high-performance system.\"\"\"\n",
        "        try:\n",
        "            if hasattr(self, 'active_hp_tasks') and task_id in self.active_hp_tasks:\n",
        "                self.active_hp_tasks[task_id]['stop_requested'] = True\n",
        "                self.logger.info(f\"Stop requested for joining task: {task_id}\")\n",
        "                return True\n",
        "            else:\n",
        "                self.logger.warning(f\"Cannot stop task {task_id}: Task not found in active tasks\")\n",
        "                return False\n",
        "        except Exception as e:\n",
        "            self.logger.error(f\"Error stopping joining task {task_id}: {str(e)}\")\n",
        "            return False\n"
    ]
    
    # Replace the lines in the file
    for i, line in enumerate(properly_indented_function):
        if target_line + i < len(lines):
            lines[target_line + i] = line
    
    # Write the fixed content back to the file
    with open("main.py", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    print(f"Fixed indentation of stop_joining_task function (lines {target_line + 1} to {target_line + len(properly_indented_function)})")
    print(f"Original file backed up at: {backup_file}")
    return True

if __name__ == "__main__":
    if fix_indentation():
        print("\n✅ Fix applied successfully!")
        print("You can now run the application with: python main.py")
    else:
        print("\n❌ Failed to apply the fix.") 