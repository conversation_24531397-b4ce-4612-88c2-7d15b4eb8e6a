﻿"""
Performance Integration Layer for TG Checker
Replaces basic threading with high-performance concurrency system
"""

import threading
import time
import logging
from typing import List, Dict, Callable, Any
from performance_manager import get_performance_manager, PerformanceManager

class HighPerformanceIntegration:
    """Integration layer for replacing basic threading with performance manager"""
    
    def __init__(self, main_app, logger=None):
        self.main_app = main_app
        self.logger = logger or logging.getLogger(__name__)
        self.performance_manager = get_performance_manager(logger=logger)
        
        # UI callback references
        self.ui_callbacks = {
            'update_status': getattr(main_app, 'update_status_signal', None),
            'log_activity': getattr(main_app, 'log_activity_signal', None),
            'update_progress': getattr(main_app, 'update_progress_signal', None),
            'update_analyzing': getattr(main_app, 'update_analyzing_signal', None)
        }
        
        # Account operations tracking
        self.active_operations = {}
        
    def replace_basic_threading(self):
        """Replace basic threading calls with performance manager"""
        self.logger.info("🚀 Replacing basic threading with high-performance concurrency system")
        
        # Replace thread creation methods
        self._replace_checker_threads()
        self._replace_account_threads()
        self._replace_sync_threads()
        
        self.logger.info("✅ High-performance threading integration complete")
        
    def _replace_checker_threads(self):
        """Replace checker thread methods"""
        # Store original methods
        original_checker = getattr(self.main_app, '_checker_thread', None)
        original_multi_checker = getattr(self.main_app, '_multi_account_checker_thread', None)
        
        if original_checker:
            self.main_app._original_checker_thread = original_checker
            self.main_app._checker_thread = self._enhanced_checker_thread
            
        if original_multi_checker:
            self.main_app._original_multi_account_checker_thread = original_multi_checker
            self.main_app._multi_account_checker_thread = self._enhanced_multi_account_checker
            
    def _replace_account_threads(self):
        """Replace account management threads"""
        methods_to_replace = [
            '_sync_accounts_thread',
            '_refresh_all_accounts_thread', 
            '_fix_account_thread',
            '_activate_all_accounts_thread',
            '_deactivate_all_accounts_thread'
        ]
        
        for method_name in methods_to_replace:
            original_method = getattr(self.main_app, method_name, None)
            if original_method:
                setattr(self.main_app, f"_original_{method_name}", original_method)
                setattr(self.main_app, method_name, self._create_enhanced_method(method_name))
                
    def _replace_sync_threads(self):
        """Replace sync and monitoring threads"""
        sync_methods = [
            '_sync_and_monitor_thread',
            '_monitor_tasks_thread',
            '_task_reassignment_monitor'
        ]
        
        for method_name in sync_methods:
            original_method = getattr(self.main_app, method_name, None)
            if original_method:
                setattr(self.main_app, f"_original_{method_name}", original_method)
                setattr(self.main_app, method_name, self._create_enhanced_method(method_name))
                
    def _create_enhanced_method(self, method_name: str):
        """Create enhanced version of a method using performance manager"""
        def enhanced_method(*args, **kwargs):
            # Register this operation
            operation_id = f"{method_name}_{int(time.time()*1000)}"
            self.active_operations[operation_id] = {
                'method': method_name,
                'start_time': time.time(),
                'args': args,
                'kwargs': kwargs
            }
            
            # Determine task type
            task_type = self._get_task_type(method_name)
            
            # Get original method
            original_method = getattr(self.main_app, f"_original_{method_name}")
            
            # Submit to performance manager
            self.performance_manager.task_manager.submit_task(
                task_type=task_type,
                task_id=operation_id,
                func=original_method,
                args=args,
                kwargs=kwargs,
                callback=lambda result: self._operation_completed(operation_id, result)
            )
            
        return enhanced_method
        
    def _get_task_type(self, method_name: str) -> str:
        """Determine task type based on method name"""
        if 'check' in method_name.lower():
            return 'check'
        elif 'join' in method_name.lower():
            return 'join'
        elif 'forward' in method_name.lower():
            return 'forward'
        else:
            return 'sync'
            
    def _operation_completed(self, operation_id: str, result: Any):
        """Handle completion of an operation"""
        if operation_id in self.active_operations:
            operation = self.active_operations.pop(operation_id)
            duration = time.time() - operation['start_time']
            self.logger.debug(f"Operation {operation['method']} completed in {duration:.2f}s")
            
    def _enhanced_checker_thread(self, group_links, start_index=0, min_seconds=2, max_seconds=5):
        """Enhanced checker thread using performance manager"""
        self.logger.info(f"🔥 Enhanced checker starting with {len(group_links)} groups from index {start_index}")
        
        # Get active accounts
        accounts = self.main_app.account_manager.get_accounts()
        active_accounts = [acc for acc in accounts if acc.get("active", False)]
        
        if not active_accounts:
            if self.ui_callbacks['update_status']:
                self.ui_callbacks['update_status'].emit("Error: No active accounts available")
            return
            
        # Register account workers
        for account in active_accounts:
            phone = account.get("phone")
            self.performance_manager.register_account_worker(phone, worker_count=3)
            
        # Create tasks for each group
        tasks = []
        total_groups = len(group_links) - start_index
        groups_per_account = max(1, total_groups // len(active_accounts))
        
        for i, account in enumerate(active_accounts):
            phone = account.get("phone")
            start_idx = start_index + (i * groups_per_account)
            
            if i == len(active_accounts) - 1:
                # Last account gets remaining groups
                account_groups = group_links[start_idx:]
            else:
                end_idx = start_idx + groups_per_account
                account_groups = group_links[start_idx:end_idx]
                
            if account_groups:
                task = {
                    'phone': phone,
                    'type': 'check',
                    'func': self._check_groups_for_account,
                    'args': (phone, account_groups, min_seconds, max_seconds),
                    'priority': 1
                }
                tasks.append(task)
                
        # Submit all tasks
        self.performance_manager.bulk_submit_tasks(tasks)
        self.logger.info(f"✅ Submitted {len(tasks)} checking tasks across {len(active_accounts)} accounts")
        
    def _enhanced_multi_account_checker(self, group_links, start_index=0):
        """Enhanced multi-account checker with optimal load balancing"""
        self.logger.info(f"🚀 Enhanced multi-account checker: {len(group_links)} groups, {start_index} start index")
        
        # Get active accounts
        accounts = self.main_app.account_manager.get_accounts()
        active_accounts = [acc for acc in accounts if acc.get("active", False)]
        
        if not active_accounts:
            if self.ui_callbacks['update_status']:
                self.ui_callbacks['update_status'].emit("Error: No active accounts available")
            return
            
        # Advanced load balancing based on account performance
        account_weights = self._calculate_account_weights(active_accounts)
        distributed_groups = self._distribute_groups_weighted(group_links[start_index:], account_weights)
        
        # Submit distributed tasks
        tasks = []
        for phone, groups in distributed_groups.items():
            if groups:
                task = {
                    'phone': phone,
                    'type': 'check',
                    'func': self._multi_check_groups_for_account,
                    'args': (phone, groups),
                    'priority': 1
                }
                tasks.append(task)
                
        self.performance_manager.bulk_submit_tasks(tasks)
        
        # Start monitoring thread
        self._start_progress_monitoring(len(group_links) - start_index, len(active_accounts))
        
    def _calculate_account_weights(self, accounts: List[Dict]) -> Dict[str, float]:
        """Calculate performance-based weights for accounts"""
        weights = {}
        
        for account in accounts:
            phone = account.get("phone")
            
            # Base weight
            weight = 1.0
            
            # Adjust based on recent performance
            if phone in self.performance_manager.account_states:
                state = self.performance_manager.account_states[phone]
                if state == 'busy':
                    weight *= 0.5  # Reduce weight for busy accounts
                elif state == 'error':
                    weight *= 0.2  # Heavily reduce weight for error accounts
                    
            # Adjust based on flood wait status
            if hasattr(self.main_app, 'flood_wait_tracker'):
                remaining = self.main_app.flood_wait_tracker.get_remaining_time(phone)
                if remaining > 0:
                    weight *= 0.1  # Heavily reduce weight for flood-waited accounts
                    
            weights[phone] = weight
            
        return weights
        
    def _distribute_groups_weighted(self, groups: List, weights: Dict[str, float]) -> Dict[str, List]:
        """Distribute groups based on account weights"""
        if not weights:
            return {}
            
        # Normalize weights
        total_weight = sum(weights.values())
        if total_weight == 0:
            # All weights are 0, distribute equally
            phones = list(weights.keys())
            groups_per_phone = len(groups) // len(phones)
            result = {}
            for i, phone in enumerate(phones):
                start = i * groups_per_phone
                end = start + groups_per_phone if i < len(phones) - 1 else len(groups)
                result[phone] = groups[start:end]
            return result
            
        normalized_weights = {phone: w/total_weight for phone, w in weights.items()}
        
        # Distribute groups
        result = {phone: [] for phone in weights.keys()}
        
        for i, group in enumerate(groups):
            # Find account with highest adjusted weight for this position
            best_phone = max(normalized_weights.keys(), 
                           key=lambda p: normalized_weights[p] - len(result[p])/len(groups))
            result[best_phone].append(group)
            
        return result
        
    def _check_groups_for_account(self, phone: str, groups: List, min_seconds: int, max_seconds: int):
        """Check groups for a specific account with performance monitoring"""
        try:
            # Update UI
            if self.ui_callbacks['update_analyzing']:
                self.ui_callbacks['update_analyzing'].emit(f"Account {phone}: Checking {len(groups)} groups")
                
            # Call original checker method with single account
            original_checker = getattr(self.main_app, '_original_checker_thread', None)
            if original_checker:
                return original_checker(groups, 0, min_seconds, max_seconds)
                
        except Exception as e:
            self.logger.error(f"Error checking groups for account {phone}: {e}")
            raise
            
    def _multi_check_groups_for_account(self, phone: str, groups: List):
        """Multi-account check for specific account"""
        try:
            # Update UI
            if self.ui_callbacks['update_analyzing']:
                self.ui_callbacks['update_analyzing'].emit(f"Multi-check {phone}: {len(groups)} groups")
                
            # Call enhanced account task thread
            original_multi = getattr(self.main_app, '_original_multi_account_checker_thread', None)
            if original_multi:
                return original_multi(groups, 0)
                
        except Exception as e:
            self.logger.error(f"Error in multi-check for account {phone}: {e}")
            raise
            
    def _start_progress_monitoring(self, total_groups: int, num_accounts: int):
        """Start progress monitoring for multi-account operations"""
        def monitor_progress():
            start_time = time.time()
            last_report_time = start_time
            
            while True:
                current_time = time.time()
                
                # Get performance stats
                stats = self.performance_manager.get_performance_report()
                
                # Update UI every 5 seconds
                if current_time - last_report_time >= 5:
                    active_tasks = sum(s['active_tasks'] for s in stats['task_stats'].values())
                    
                    if self.ui_callbacks['update_status']:
                        self.ui_callbacks['update_status'].emit(
                            f"Processing: {active_tasks} active tasks, "
                            f"Memory: {stats['current_memory_mb']:.1f}MB, "
                            f"CPU: {stats['cpu_percent']:.1f}%"
                        )
                        
                    last_report_time = current_time
                    
                # Check if all tasks completed
                if active_tasks == 0 and current_time - start_time > 10:
                    break
                    
                time.sleep(1)
                
        # Submit monitoring as a sync task
        self.performance_manager.task_manager.submit_task(
            task_type='sync',
            task_id=f"progress_monitor_{int(time.time())}",
            func=monitor_progress
        )
        
    def get_performance_statistics(self) -> Dict:
        """Get current performance statistics"""
        return self.performance_manager.get_performance_report()
        
    def optimize_performance(self):
        """Trigger performance optimization"""
        self.performance_manager.optimize_resources()
        
    def shutdown(self):
        """Shutdown performance integration"""
        self.logger.info("Shutting down performance integration...")
        self.performance_manager.shutdown()

def integrate_performance_manager(main_app, logger=None) -> HighPerformanceIntegration:
    """Integrate performance manager with main application"""
    integration = HighPerformanceIntegration(main_app, logger)
    integration.replace_basic_threading()
    return integration
