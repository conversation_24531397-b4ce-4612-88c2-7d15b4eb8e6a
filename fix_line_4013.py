#!/usr/bin/env python3
"""
Script to fix the syntax error at line 4013 in main.py
"""

import os
import re
import shutil

def fix_line_4013():
    """Fix the syntax error at line 4013 (else: without try block)"""
    input_file = "main.py"
    output_file = "main_fixed.py"
    
    # Create a backup if it doesn't exist
    backup_file = f"{input_file}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Check if the file has enough lines
    if len(lines) < 4020:
        print(f"The file only has {len(lines)} lines, but we need to access line 4013.")
        return False
    
    # Print the line with the error for context
    print(f"Line 4013: {lines[4012].strip()}")
    
    # Since the error is at line 4013, we need to look around for the context
    # From our investigation, we need to look at context around line 4012-4014
    # Find a try block that is missing an except clause
    
    # Check the actual error area around line 4013
    start_line = max(0, 4000)
    end_line = min(len(lines), 4025)
    
    print("\nCode context around the error:")
    for i in range(start_line, end_line):
        print(f"{i+1}: {lines[i].rstrip()}")
    
    # The error is "expected 'except' or 'finally' block", which means there's a try 
    # without an except or finally in this region
    
    # Find the nearest try block before line 4013
    try_line = None
    for i in range(4012, max(0, 4012-50), -1):
        if "try:" in lines[i]:
            try_line = i
            break
    
    if try_line is not None:
        print(f"\nFound 'try:' at line {try_line+1}: {lines[try_line].strip()}")
        
        # Check if there's an except block for this try
        try_indent = len(lines[try_line]) - len(lines[try_line].lstrip())
        has_except = False
        
        for i in range(try_line+1, 4020):
            if i >= len(lines):
                break
                
            line_strip = lines[i].strip()
            if not line_strip:
                continue
                
            line_indent = len(lines[i]) - len(lines[i].lstrip())
            
            # If we hit a line with less or equal indentation to the try, but not an except/finally,
            # then we've found the end of the try block without an except
            if line_indent <= try_indent:
                if line_strip.startswith(("except", "finally")):
                    has_except = True
                break
        
        if not has_except:
            print(f"The try block at line {try_line+1} is missing an except clause.")
            
            # Find where the try block ends
            try_end = try_line + 1
            while try_end < len(lines):
                if not lines[try_end].strip():
                    try_end += 1
                    continue
                    
                curr_indent = len(lines[try_end]) - len(lines[try_end].lstrip())
                if curr_indent <= try_indent and lines[try_end].strip():
                    break
                    
                try_end += 1
            
            # Insert an except block at the end of the try block
            indent_str = ' ' * try_indent
            lines.insert(try_end, f"{indent_str}except Exception as e:\n")
            lines.insert(try_end+1, f"{indent_str}    self.logger.error(f\"Error: {{str(e)}}\")\n")
            
            print(f"Added except block after the try block at line {try_end+1}")
    else:
        # Check if there's an else: without a corresponding if or try
        print("No try block found near line 4013. Looking for unmatched 'else:'")
        
        for i in range(4010, 4020):
            if i < len(lines) and "else:" in lines[i]:
                print(f"Found 'else:' at line {i+1}: {lines[i].strip()}")
                
                # Check if this is a syntax error by looking for a matching if
                else_indent = len(lines[i]) - len(lines[i].lstrip())
                matching_if = False
                
                # Look back for a matching if at the same indentation level
                for j in range(i-1, max(0, i-50), -1):
                    if not lines[j].strip():
                        continue
                        
                    j_indent = len(lines[j]) - len(lines[j].lstrip())
                    if j_indent < else_indent:
                        # We've moved out of the block, so there's no matching if
                        break
                        
                    if j_indent == else_indent and lines[j].lstrip().startswith("if "):
                        matching_if = True
                        break
                
                if not matching_if:
                    print(f"The 'else:' at line {i+1} has no matching 'if' statement.")
                    # This is likely the error - we need to add a try-except block
                    
                    # First, add a try: statement before the else:
                    indent_str = ' ' * else_indent
                    lines.insert(i, f"{indent_str}try:\n")
                    
                    # Add a placeholder pass if needed
                    if i+1 < len(lines) and (not lines[i+1].strip() or len(lines[i+1]) - len(lines[i+1].lstrip()) <= else_indent):
                        lines.insert(i+1, f"{indent_str}    pass  # Add appropriate code here\n")
                    
                    # Convert the else: to except:
                    lines[i+2] = lines[i+2].replace("else:", "except Exception as e:")
                    
                    print(f"Fixed by adding a try block before 'else:' and converting 'else:' to 'except Exception as e:'")
                    break
    
    # Write the fixed content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"\nFixed the syntax error and saved to {output_file}")
    print(f"Please review the changes and then run:")
    print(f"python {output_file}")
    
    # Also create a batch file for easy testing
    with open("run_fixed.bat", "w") as f:
        f.write(f"""@echo off
echo Running TG Checker with fixed syntax...
python {output_file}
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print(f"Or use the batch file: run_fixed.bat")
    
    # Create Kurdish version too - with simpler text to avoid encoding issues
    with open("run_fixed_kurdish.bat", "w") as f:
        f.write(f"""@echo off
echo TG Checker - Bernameya chakkrawa...
python {output_file}
if %errorlevel% neq 0 (
    echo Helayak ruida! Bo zaniari ziatr logakan bxwenerawa.
    pause
)
pause
""")
    
    print(f"Or use the Kurdish batch file: run_fixed_kurdish.bat")
    return True

if __name__ == "__main__":
    success = fix_line_4013()
    
    if success:
        print("\nSuccessfully fixed the syntax error at line 4013.")
    else:
        print("\nFailed to fix the syntax error at line 4013.") 