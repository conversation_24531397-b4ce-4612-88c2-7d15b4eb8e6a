#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix the Status column logic in the TG Checker application.
This script separates the Enabled/Disabled state from the Telegram account status.
"""

import re

def main():
    # Read the original file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. First fix the table header to change "Status" to "Enabled"
    header_pattern = r'self\.accounts_table\.setHorizontalHeaderLabels\(\["Phone", "Name / Username", "Status", "Last Check", "Errors", "Status", "Actions"\]\)'
    header_replacement = 'self.accounts_table.setHorizontalHeaderLabels(["Phone", "Name / Username", "Enabled", "Last Check", "Errors", "Status", "Actions"])'
    content = re.sub(header_pattern, header_replacement, content)
    
    # 2. Find and replace the old Status code with the new Enabled code
    old_status_pattern = r'''# Status
                status = "Active" if account\.get\("active", False\) else "Inactive"
                
                # Check if account is disabled due to FloodWait
                disabled_until = account\.get\("disabled_until"\)
                if disabled_until:
                    try:
                        disabled_datetime = datetime\.fromisoformat\(disabled_until\)
                        if disabled_datetime > datetime\.now\(\):
                            # Calculate remaining time
                            remaining_time = disabled_datetime - datetime\.now\(\)
                            hours, remainder = divmod\(remaining_time\.total_seconds\(\), 3600\)
                            minutes, _ = divmod\(remainder, 60\)
                            
                            if hours > 0:
                                status = f"Limited \(resets in {int\(hours\)}h {int\(minutes\)}m\)"
                            else:
                                status = f"Limited \(resets in {int\(minutes\)}m\)"
                    except:
                        pass
                
                # Check for banned or limited account based on status field
                account_status = account\.get\("status", ""\)\.lower\(\)
                if "banned" in account_status:
                    status = "Banned"
                elif "limit" in account_status and not disabled_until:
                    status = "Limited"
                
                status_item = QTableWidgetItem\(status\)
                if status == "Active":
                    status_item\.setForeground\(QColor\("green"\)\)
                elif "Limited" in status:
                    status_item\.setForeground\(QColor\("orange"\)\)
                elif status == "Banned":
                    status_item\.setForeground\(QColor\("red"\)\)
                else:
                    status_item\.setForeground\(QColor\("red"\)\)
                    
                self\.accounts_table\.setItem\(row, 2, status_item\)'''
    
    new_status_code = '''# Enabled/Disabled status (shows database account state)
                enabled_status = "Enabled" if account.get("active", False) else "Disabled"
                enabled_item = QTableWidgetItem(enabled_status)
                
                if enabled_status == "Enabled":
                    enabled_item.setForeground(QColor("green"))
                else:
                    enabled_item.setForeground(QColor("red"))
                
                self.accounts_table.setItem(row, 2, enabled_item)'''
    
    content = re.sub(old_status_pattern, new_status_code, content, flags=re.DOTALL)
    
    # 3. Update the toggle button code to use account.get("active") directly
    old_button_pattern = r'''# Create buttons with specific colors as requested
                if status == "Active":
                    toggle_button = QPushButton\("Disable"\)
                    toggle_button\.setStyleSheet\("background-color: #ffaaaa; color: black;"\)
                else:
                    toggle_button = QPushButton\("Enable"\)
                    toggle_button\.setStyleSheet\("background-color: #88cc88; color: black;"\)'''
    
    new_button_code = '''# Create buttons with specific colors as requested
                if account.get("active", False):
                    toggle_button = QPushButton("Disable")
                    toggle_button.setStyleSheet("background-color: #ffaaaa; color: black;")
                else:
                    toggle_button = QPushButton("Enable")
                    toggle_button.setStyleSheet("background-color: #88cc88; color: black;")'''
    
    content = re.sub(old_button_pattern, new_button_code, content, flags=re.DOTALL)
    
    # 4. Update the group_limit column to use account status directly not using the status variable
    old_group_limit_pattern = r'''else:
                    # For active accounts that are not limited or banned
                    if status == "Active":
                        limit_text = "Active"
                        group_limit_item = QTableWidgetItem\(limit_text\)
                        group_limit_item\.setForeground\(QColor\("green"\)\)
                    else:
                        limit_text = "Inactive"
                        group_limit_item = QTableWidgetItem\(limit_text\)
                        group_limit_item\.setForeground\(QColor\("gray"\)\)'''
    
    new_group_limit_code = '''else:
                    # For active accounts that are not limited or banned
                    limit_text = "Active"
                    group_limit_item = QTableWidgetItem(limit_text)
                    group_limit_item.setForeground(QColor("green"))'''
    
    content = re.sub(old_group_limit_pattern, new_group_limit_code, content, flags=re.DOTALL)
    
    # Write the updated content
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Status column logic has been successfully fixed.")

if __name__ == "__main__":
    main() 