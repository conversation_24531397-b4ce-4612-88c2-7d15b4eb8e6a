import os
import threading
import asyncio
import logging
import time
from datetime import datetime
from queue import Queue

class Monitor:
    """Monitor class to check accounts in the background with full-sync-mode enabled."""
    
    def __init__(self, account_manager, logger=None):
        self.account_manager = account_manager
        self.logger = logger or logging.getLogger(__name__)
        self.is_running = False
        self.check_interval = 300  # Default 5 minutes (in seconds)
        self.thread = None
        self.log_queue = Queue(maxsize=1000)
        self.auto_fix = True  # Always auto-fix by default  # Always auto-fix by default
        self.full_sync_mode = True  # Always enable full-sync-mode
        self.last_sync_time = None
        self.sync_callback = None  # Callback to update UI after sync
    
    def start(self, interval=None, sync_callback=None):
        """Start the monitor thread."""
        if self.is_running:
            self.logger.warning("Monitor is already running")
            return
            
        if interval:
            self.check_interval = interval
        
        if sync_callback:
            self.sync_callback = sync_callback
        
        # Always ensure auto_fix is enabled with full-sync-mode    
        self.auto_fix = True  # Always auto-fix by default
            
        self.is_running = True
        self.thread = threading.Thread(target=self._monitor_thread, daemon=True)
        self.thread.start()
        
        self.logger.info(f"Monitor started with interval of {self.check_interval} seconds (full-sync-mode enabled)")
        self._log("Monitor started with full-sync-mode enabled")
    
    def stop(self):
        """Stop the monitor thread."""
        if not self.is_running:
            self.logger.warning("Monitor is not running")
            return
            
        self.is_running = False
        if self.thread:
            self.thread.join(timeout=1.0)
            
        self.logger.info("Monitor stopped")
        self._log("Monitor stopped")
    
    def _monitor_thread(self):
        """Main monitor thread method with full-sync-mode implementation."""
        self.logger.info("Monitor thread started")
        
        # Perform initial sync
        self._perform_full_sync()
        
        while self.is_running:
            try:
                # Run the check in an asyncio event loop
                self._perform_full_sync()
                
                # Sleep for the check interval
                for _ in range(int(self.check_interval / 10)):
                    if not self.is_running:
                        break
                    time.sleep(10)
                    
            except Exception as e:
                self.logger.error(f"Error in monitor thread: {str(e)}")
                self._log(f"Error: {str(e)}")
                time.sleep(60)  # Wait a bit before retrying after an error
    
    def _perform_full_sync(self):
        """Performs a full synchronization cycle in full-sync-mode."""
        self._log("Running full sync cycle...")
        
        try:
            # Create a new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Check accounts
                results = loop.run_until_complete(self._check_accounts())
                self._process_results(results)
                
                # Auto-fix accounts is always enabled in full-sync-mode
                fix_results = loop.run_until_complete(self._fix_accounts_with_issues())
                
                # Verify fixes worked
                if fix_results and any(fix_results):
                    # Re-check accounts after fixing
                    loop.run_until_complete(self._check_accounts())
            finally:
                loop.close()
                
            # Update last sync time
            self.last_sync_time = datetime.now()
            
            # Notify UI if callback is provided
            if self.sync_callback:
                self.sync_callback()
                
        except Exception as e:
            self.logger.error(f"Full sync error: {str(e)}")
            self._log(f"Full sync error: {str(e)}")
    
    async def _check_accounts(self):
        """Check all active accounts."""
        self._log("Checking all active accounts...")
        start_time = time.time()
        
        try:
            results = await self.account_manager.check_all_accounts()
            
            elapsed = time.time() - start_time
            self._log(f"Account check completed in {elapsed:.2f} seconds")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error checking accounts: {str(e)}")
            self._log(f"Account check failed: {str(e)}")
            return []
    
    async def _fix_accounts_with_issues(self):
        """Fix accounts with detected issues."""
        accounts = self.account_manager.get_accounts()
        accounts_with_issues = [acc for acc in accounts if acc.get("errors", 0) > 0]
        
        if not accounts_with_issues:
            return []
            
        self._log(f"Automatically fixing {len(accounts_with_issues)} accounts with issues...")
        start_time = time.time()
        
        try:
            results = await self.account_manager.fix_all_accounts()
            
            # Count successful fixes
            success_count = sum(1 for _, result in results if result)
            
            elapsed = time.time() - start_time
            self._log(f"Fixed {success_count} of {len(results)} accounts in {elapsed:.2f} seconds")
            
            return results
            
        except Exception as e:
            self.logger.error(f"Error fixing accounts: {str(e)}")
            self._log(f"Account fix failed: {str(e)}")
            return []
    
    def _process_results(self, results):
        """Process the results of account checks."""
        if not results:
            self._log("No accounts were checked")
            return
            
        success_count = sum(1 for _, result in results if result)
        
        self._log(f"Checked {len(results)} accounts, {success_count} successful, {len(results) - success_count} with issues")
        
        for phone, result in results:
            if not result:
                self._log(f"Account {phone} has issues")
    
    def _log(self, message):
        """Add a log entry to the monitor log queue."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        
        # Add to the queue, removing oldest if full
        if self.log_queue.full():
            try:
                self.log_queue.get_nowait()
            except:
                pass
                
        try:
            self.log_queue.put_nowait(log_entry)
        except:
            self.logger.warning("Monitor log queue is full")
    
    def get_recent_logs(self, max_count=100):
        """Get recent log entries from the monitor."""
        logs = []
        
        # Create a temporary list of all logs
        temp_logs = []
        while not self.log_queue.empty():
            try:
                temp_logs.append(self.log_queue.get_nowait())
            except:
                break
                
        # Put all logs back into the queue
        for log in temp_logs:
            try:
                self.log_queue.put_nowait(log)
            except:
                self.logger.warning("Could not put log back into queue")
                
        # Return the most recent logs up to max_count
        return temp_logs[-max_count:]
    
    def set_auto_fix(self, enabled):
        """Enable or disable automatic fixing of accounts."""
        # In full-sync-mode, auto-fix is always enabled
        if not enabled:
            self.logger.info("Auto-fix cannot be disabled in full-sync-mode")
            return
            
        self.auto_fix = True  # Always auto-fix by default
        self.logger.info("Auto-fix accounts enabled (required for full-sync-mode)")
        
    def get_status(self):
        """Get the current status of the monitor."""
        return {
            "running": self.is_running,
            "interval": self.check_interval,
            "auto_fix": self.auto_fix,
            "full_sync_mode": self.full_sync_mode,
            "last_sync_time": self.last_sync_time,
            "active_since": getattr(self, "start_time", None),
        }
        
    def force_sync(self):
        """Force an immediate synchronization."""
        if not self.is_running:
            self.logger.warning("Cannot force sync when monitor is not running")
            return False
        
        threading.Thread(target=self._perform_full_sync, daemon=True).start()
        return True 