#!/usr/bin/env python3
"""
TG Checker Complete Fix Utility

This script fixes both the missing 'start_checker' method and database locking issues:
1. Creates a backup of the original main.py
2. Injects the missing 'start_checker' method into main.py
3. Applies database locking fixes by installing enhanced_db_fix.py
4. Updates account_manager.py with improved connection handling

Usage:
    python fix_app_complete.py
"""

import os
import sys
import shutil
import re
import time
from datetime import datetime

def create_backup(file_path):
    """Create a backup of a file with timestamp."""
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = f"{file_path}.backup_{timestamp}"
        
        if os.path.exists(file_path):
            shutil.copy2(file_path, backup_path)
            print(f"✅ Created backup of {file_path} at {backup_path}")
            return backup_path
        else:
            print(f"❌ File {file_path} not found, cannot create backup")
            return None
    except Exception as e:
        print(f"❌ Failed to create backup: {str(e)}")
        return None

def fix_start_checker_method(file_path="main.py"):
    """Fix the missing 'start_checker' method in main.py."""
    try:
        # Create backup first
        backup_path = create_backup(file_path)
        if not backup_path:
            return False
        
        # Read the file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"✅ Successfully read {file_path}, size: {len(content)} bytes")
        except Exception as read_err:
            print(f"❌ Error reading {file_path}: {str(read_err)}")
            return False
        
        # Check if the method already exists
        if "def start_checker(self):" in content:
            print("✅ The start_checker method already exists in the file")
            return True
        
        # Find the right location to insert the method - before stop_checker
        match = re.search(r'def stop_checker\(self\):', content)
        if not match:
            print("❌ Could not find 'stop_checker' method to determine insertion point")
            # Try to find an alternative insertion point
            alt_match = re.search(r'def create_dashboard_tab\(self\):', content)
            if alt_match:
                print("✅ Found alternative insertion point: create_dashboard_tab")
                insertion_point = alt_match.start()
            else:
                print("❌ Could not find any suitable insertion point")
                return False
        else:
            insertion_point = match.start()
            print(f"✅ Found insertion point at position {insertion_point}")
        
        # Prepare the start_checker method and related methods
        start_checker_code = """
    def start_checker(self):
        \"\"\"Start the group/channel checker.\"\"\"
        if self.is_checker_running:
            QMessageBox.information(self, "Info", "Checker is already running.")
            return
            
        # Get the list of groups/channels from the input
        group_links = self.groups_input.toPlainText().strip().split('\\n')
        group_links = [link.strip() for link in group_links if link.strip()]
        
        if not group_links:
            QMessageBox.warning(self, "Warning", "Please enter at least one group or channel link.")
            return
        
        # Check for resume option
        is_resuming, final_group_links, start_index = self.check_resume_option(group_links)
        
        # Make sure we have at least one active account
        accounts = self.account_manager.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        
        if not active_accounts:
            QMessageBox.warning(self, "Warning", "Please activate at least one account before starting the checker.")
            return
        
        # Clear previous results if not resuming
        if not is_resuming:
            self.clear_results()
        
        # Set up progress tracking
        self.current_group_links = group_links
        self.total_groups = len(group_links)
        self.current_group_index = start_index
        
        # Update progress display
        self.update_progress_signal.emit(self.current_group_index, self.total_groups)
        
        # Update state and UI
        self.is_checker_running = True
        self.checker_should_stop = False
        self.start_checker_button.setEnabled(False)
        self.stop_checker_button.setEnabled(True)
        
        # Get speed check settings
        min_seconds = self.settings.value("min_check_seconds", 2, type=int)
        max_seconds = self.settings.value("max_check_seconds", 5, type=int)
        
        # Start checker thread
        self.checker_thread = threading.Thread(
            target=self._checker_thread,
            args=(final_group_links, start_index, min_seconds, max_seconds),
            daemon=True
        )
        self.checker_thread.start()
        
        if is_resuming:
            self.log_activity(f"🚀 Checker resumed from group {start_index+1} of {len(final_group_links)}")
        else:
            self.log_activity(f"🚀 Checker started with {len(final_group_links)} groups")
    
    def check_resume_option(self, group_links):
        \"\"\"Check if we should resume from a previous check.\"\"\"
        try:
            # Check if we have a saved progress file
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r') as f:
                    saved_index = int(f.read().strip())
                    
                # Ask user if they want to resume
                reply = QMessageBox.question(
                    self, 
                    "Resume Checking", 
                    f"Found a saved progress (group {saved_index+1} of {len(group_links)}).\\nWould you like to resume from where you left off?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    # Make sure the index is valid
                    if 0 <= saved_index < len(group_links):
                        return True, group_links, saved_index
            
            # If no saved progress or user chose not to resume
            return False, group_links, 0
        except Exception as e:
            self.logger.error(f"Error checking resume option: {str(e)}")
            return False, group_links, 0
    
    def _checker_thread(self, group_links, start_index=0, min_seconds=2, max_seconds=5):
        \"\"\"Background thread for checking groups/channels.\"\"\"
        try:
            # Get active account for checking
            accounts = self.account_manager.get_accounts()
            active_accounts = [acc for acc in accounts if acc.get("active", False)]
            
            if not active_accounts:
                self.update_status_signal.emit("Error: No active accounts available")
                self.log_activity_signal.emit("❌ No active accounts available for checking")
                return
            
            # Select the first active account
            account = active_accounts[0]
            phone = account.get("phone", "")
            
            self.log_activity_signal.emit(f"Using account: {phone}")
            
            # Get filter settings
            min_members = self.settings.value("min_members", 500, type=int)
            min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
            min_total_messages = self.settings.value("min_total_messages", 100, type=int)
            
            # Initialize result collections
            valid_filtered = []
            valid_only = []
            topics_groups = []
            channels_only = []
            invalid_groups = []
            account_issues = []
            join_requests = []
            
            # Start checking from the given index
            for i in range(start_index, len(group_links)):
                # Check if we should stop
                if self.checker_should_stop:
                    self.log_activity_signal.emit("🛑 Checker stopped by user")
                    break
                
                link = group_links[i]
                
                # Update progress
                self.current_group_index = i
                self.update_progress_signal.emit(i, len(group_links))
                self.update_analyzing_signal.emit(f"Currently analyzing: {link}")
                
                # Save progress to file
                self._save_progress(i)
                
                # Anti-flood delay
                if i > start_index:
                    delay = random.uniform(min_seconds, max_seconds)
                    time.sleep(delay)
                
                # Check group/channel
                try:
                    self.log_activity_signal.emit(f"Checking: {link}")
                    
                    result = self.check_group_or_channel(link)
                    
                    if not result["valid"]:
                        # Handle invalid groups
                        error_type = result.get("error_type", "invalid_group")
                        
                        if error_type == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"❌ INVALID: {link}")
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            self.log_activity_signal.emit(f"⚠️ ACCOUNT ISSUE: {result.get('reason', 'Unknown error')}")
                        elif error_type == "join_request":
                            # Add to separate list for join requests
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"👋 JOIN REQUEST: {link}")
                        
                        # Update UI
                        self.update_result_counts_signal.emit(
                            len(valid_filtered), len(valid_only), len(topics_groups), 
                            len(channels_only), len(invalid_groups), len(account_issues)
                        )
                        continue
                    
                    # Handle valid groups
                    if result["type"] == "channel":
                        channels_only.append(link)
                        self.log_activity_signal.emit(f"📺 Valid channel: {link}")
                    elif result["type"] == "topic":
                        topics_groups.append(link)
                        self.log_activity_signal.emit(f"💬 Valid topic: {link}")
                    elif result["type"] == "group":
                        # Check if it passes filters
                        passes_filters = (
                            result["members"] >= min_members and
                            result["last_message_age_hours"] <= min_message_time_hours and
                            result["total_messages"] >= min_total_messages
                        )
                        
                        if passes_filters:
                            valid_filtered.append(link)
                            self.log_activity_signal.emit(f"✅ Valid group (passes filters): {link}")
                        else:
                            valid_only.append(link)
                            self.log_activity_signal.emit(f"⚠️ Valid group (doesn't pass filters): {link}")
                    
                    # Update UI
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues)
                    )
                    
                except Exception as e:
                    self.logger.error(f"Error checking {link}: {str(e)}")
                    account_issues.append(link)
                    self.log_activity_signal.emit(f"❌ ERROR: {link} - {str(e)}")
                    
                    # Update UI
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues)
                    )
            
            # Save results to files
            self.save_results_3tier(
                valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests
            )
            
            # Remove progress file when complete
            if os.path.exists(self.progress_file) and not self.checker_should_stop:
                try:
                    os.remove(self.progress_file)
                except:
                    pass
            
            # Update UI when done
            self.update_analyzing_signal.emit("Currently analyzing: None")
            if self.checker_should_stop:
                self.update_status_signal.emit("Checking stopped")
                self.log_activity_signal.emit("Checking stopped by user")
            else:
                self.update_status_signal.emit("Checking completed")
                self.log_activity_signal.emit("✅ Checking completed")
            
        except Exception as e:
            self.logger.error(f"Checker thread error: {str(e)}")
            self.log_activity_signal.emit(f"❌ Checker error: {str(e)}")
            self.update_status_signal.emit(f"Checker error: {str(e)}")
        finally:
            # Reset state and UI
            self.is_checker_running = False
            self.checker_should_stop = False
            QMetaObject.invokeMethod(self.start_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            QMetaObject.invokeMethod(self.stop_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, False))
    
    def _save_progress(self, index):
        \"\"\"Save current progress to a file for potential resume.\"\"\"
        try:
            with open(self.progress_file, 'w') as f:
                f.write(str(index))
        except Exception as e:
            self.logger.error(f"Error saving progress: {str(e)}")
    
    def clear_results(self):
        \"\"\"Clear previous results.\"\"\"
        with self.results_lock:
            self.valid_filtered = []
            self.valid_only = []
            self.topics_groups = []
            self.channels_only = []
            self.invalid_groups = []
            self.account_issues = []
            self.join_requests = []
            
            # Update UI
            self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0)

"""
        
        # Insert the method at the right place
        new_content = content[:insertion_point] + start_checker_code + content[insertion_point:]
        
        # Write the new content
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
                print(f"✅ Successfully wrote updated content to {file_path}, size: {len(new_content)} bytes")
        except Exception as write_err:
            print(f"❌ Error writing to {file_path}: {str(write_err)}")
            return False
        
        print("✅ Successfully added 'start_checker' method to main.py")
        return True
        
    except Exception as e:
        print(f"❌ Failed to fix 'start_checker' method: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def apply_database_fixes():
    """Apply database locking fixes by updating account_manager.py."""
    try:
        # Check if enhanced_db_fix.py exists
        if not os.path.exists("enhanced_db_fix.py"):
            print("❌ enhanced_db_fix.py not found, cannot apply database fixes")
            return False
            
        # Check if account_manager_fixed_enhanced.py exists
        if not os.path.exists("account_manager_fixed_enhanced.py"):
            print("❌ account_manager_fixed_enhanced.py not found, cannot apply database fixes")
            return False
        
        # Make sure db_backups directory exists
        if not os.path.exists("db_backups"):
            os.makedirs("db_backups")
            print("✅ Created db_backups directory")
        
        # Back up account_manager.py
        am_backup = create_backup("account_manager.py")
        if not am_backup:
            print("⚠️ Could not back up account_manager.py, but continuing with fix")
        
        # Copy the enhanced account manager to the main file
        shutil.copy2("account_manager_fixed_enhanced.py", "account_manager.py")
        print("✅ Successfully updated account_manager.py with enhanced version")
        
        # Run the enhanced_db_fix.py to fix any existing lock issues
        print("🔄 Running enhanced database fix utility...")
        os.system("python enhanced_db_fix.py --force --no-prompt")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to apply database fixes: {str(e)}")
        return False

def create_launcher_batch_files():
    """Create launcher batch files to start the fixed application."""
    try:
        # English version (without emojis)
        with open("Run_Fixed_TG_Checker.bat", "w") as f:
            f.write("""@echo off
echo Starting TG Checker - Fixed Edition
echo =============================================
echo Please wait, applying fixes...

REM Copy the enhanced account manager to the main account_manager.py file
copy /Y account_manager_fixed_enhanced.py account_manager.py

echo Fixes applied
echo Starting TG Checker...

python main.py

echo Press any key to exit...
pause > nul
""")
        
        # Kurdish version (without emojis)
        with open("Run_Fixed_TG_Checker_Kurdish.bat", "w", encoding='utf-8') as f:
            f.write("""@echo off
chcp 65001 > nul
echo TG Checker دەستپێدەکات - وەشانی چاککراو
echo ===================================================
echo چاوەڕێ بکە، چارەسەرەکان جێبەجێ دەکرێن...

REM Copy the enhanced account manager to the main account_manager.py file
copy /Y account_manager_fixed_enhanced.py account_manager.py

echo چارەسەرەکان جێبەجێ کران
echo TG Checker دەستپێدەکات...

python main.py

echo بۆ دەرچوون، کلیک بکە لەسەر هەر کلیلێک...
pause > nul
""")
        
        print("✅ Created launcher batch files for fixed application")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create launcher batch files: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function for the complete fix utility."""
    print("🔧 TG Checker Complete Fix Utility")
    print("==================================")
    print("This utility will fix both the missing 'start_checker' method and database locking issues")
    
    # Fix the missing 'start_checker' method
    print("\n🔄 Fixing missing 'start_checker' method...")
    if fix_start_checker_method():
        print("✅ Successfully fixed missing 'start_checker' method")
    else:
        print("❌ Failed to fix missing 'start_checker' method")
        return
    
    # Apply database fixes
    print("\n🔄 Applying database locking fixes...")
    if apply_database_fixes():
        print("✅ Successfully applied database locking fixes")
    else:
        print("❌ Failed to apply database locking fixes")
        return
    
    # Create launcher batch files
    print("\n🔄 Creating launcher batch files...")
    if create_launcher_batch_files():
        print("✅ Successfully created launcher batch files")
    else:
        print("❌ Failed to create launcher batch files")
    
    print("\n✅ TG Checker fixed successfully!")
    print("You can now run the application using the 'Run_Fixed_TG_Checker.bat' file")
    print("Kurdish users can use the 'Run_Fixed_TG_Checker_Kurdish.bat' file")
    
if __name__ == "__main__":
    try:
        main()
        # Wait for user to acknowledge
        if "--no-prompt" not in sys.argv:
            input("\nPress Enter to exit...")
    except KeyboardInterrupt:
        print("\n🛑 Operation cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        print("Please check the logs for details") 