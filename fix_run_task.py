"""
Fix missing run_task method in TelegramForwarder class
"""
import re
import os

def fix_telegram_forwarder():
    """Add the missing run_task method to the TelegramForwarder class"""
    print("Making a backup of main.py...")
    main_file = "main.py"
    backup_file = "main.py.bak_run_task"
    
    # Create backup
    with open(main_file, "r", encoding="utf-8") as src, open(backup_file, "w", encoding="utf-8") as dst:
        dst.write(src.read())
    
    # Read the file
    with open(main_file, "r", encoding="utf-8") as file:
        content = file.read()
    
    # Find all TelegramForwarder classes
    forwarder_classes = list(re.finditer(r'class\s+TelegramForwarder:', content))
    
    if not forwarder_classes:
        print("Could not find TelegramForwarder class in main.py")
        return
    
    # Process each instance
    for match in reversed(forwarder_classes):  # Reverse to keep line numbers valid
        class_start = match.start()
        
        # Find the class methods
        methods = list(re.finditer(r'(\s+)def\s+(\w+)\(self', content[class_start:]))
        
        if not methods:
            print("Could not find methods in TelegramForwarder class")
            continue
        
        # Get the indentation level from the first method
        indent = methods[0].group(1)
        
        # Find a good insertion point for the new method
        # Let's put it after set_task if it exists, or after _report_error if not
        set_task_match = re.search(r'(\s+)def\s+set_task\(self', content[class_start:])
        _report_error_match = re.search(r'(\s+)def\s+_report_error\(self', content[class_start:])
        
        if set_task_match:
            # Find the end of set_task method
            insert_pos = class_start + set_task_match.start()
            # Find the next method after set_task
            next_method = re.search(r'(\s+)def\s+\w+\(self', content[class_start + set_task_match.end():])
            if next_method:
                insert_pos = class_start + set_task_match.end() + next_method.start() - len(indent)
            else:
                # If no next method, insert at the end of the class
                insert_pos = class_start + len(content[class_start:])
        elif _report_error_match:
            # Find the end of _report_error method
            insert_pos = class_start + _report_error_match.start()
            # Find the next method after _report_error
            next_method = re.search(r'(\s+)def\s+\w+\(self', content[class_start + _report_error_match.end():])
            if next_method:
                insert_pos = class_start + _report_error_match.end() + next_method.start() - len(indent)
            else:
                # If no next method, insert at the end of the class
                insert_pos = class_start + len(content[class_start:])
        else:
            # Insert after the class definition
            insert_pos = class_start + match.end() + 1
        
        # Create the run_task method
        run_task_method = f"""
{indent}def run_task(self):
{indent}    \"\"\"Run the forwarding task in a synchronous way (wrapper around process_task).\"\"\"
{indent}    import asyncio
{indent}    
{indent}    # Reset state
{indent}    self.should_stop = False
{indent}    
{indent}    # Create new event loop for this thread
{indent}    loop = asyncio.new_event_loop()
{indent}    asyncio.set_event_loop(loop)
{indent}    
{indent}    try:
{indent}        # Run the async task in the loop
{indent}        return loop.run_until_complete(self.process_task())
{indent}    except Exception as e:
{indent}        self._report_error(f"Error in run_task: {{str(e)}}", fatal=True)
{indent}        return False
{indent}    finally:
{indent}        loop.close()
"""
        
        # Insert the method at the chosen position
        new_content = content[:insert_pos] + run_task_method + content[insert_pos:]
        
        # Update content for the next iteration
        content = new_content
        
        print(f"Added run_task method to TelegramForwarder class")
    
    # Write the updated content back to the file
    with open(main_file, "w", encoding="utf-8") as file:
        file.write(content)
    
    print("Fixed TelegramForwarder class by adding run_task method")

if __name__ == "__main__":
    fix_telegram_forwarder() 