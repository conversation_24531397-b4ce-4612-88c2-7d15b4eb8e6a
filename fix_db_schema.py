"""
Fix database schema for forwarder tables
"""
import sqlite3
import os
import logging

def setup_logger():
    """Set up a simple logger"""
    logger = logging.getLogger("db_schema_fix")
    logger.setLevel(logging.INFO)
    
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

def fix_database_schema():
    """Fix the database schema for forwarder functionality"""
    logger = setup_logger()
    db_path = "tg_checker.db"
    
    # Create backup
    if os.path.exists(db_path):
        backup_path = f"{db_path}.schema_bak"
        logger.info(f"Creating database backup at {backup_path}")
        with open(db_path, "rb") as src, open(backup_path, "wb") as dst:
            dst.write(src.read())
    
    # Connect to database
    conn = None
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if forwarder_tasks table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='forwarder_tasks'")
        if not cursor.fetchone():
            logger.info("Creating forwarder_tasks table")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS forwarder_tasks (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    account TEXT,
                    message_link TEXT,
                    target_groups TEXT,
                    part2_message_link TEXT,
                    part2_target_groups TEXT,
                    status TEXT DEFAULT 'pending',
                    current_index INTEGER DEFAULT 0,
                    error_message TEXT,
                    last_processed_time TEXT,
                    created_at TEXT,
                    intervalMin INTEGER DEFAULT 20,
                    intervalMax INTEGER DEFAULT 25,
                    afterEachSecond INTEGER DEFAULT 360,
                    randomSleepTimeMin INTEGER DEFAULT 30,
                    randomSleepTimeMax INTEGER DEFAULT 60,
                    customReplyMessage TEXT
                )
            ''')
        
        # Check for missing columns in forwarder_tasks
        cursor.execute("PRAGMA table_info(forwarder_tasks)")
        columns = {row[1] for row in cursor.fetchall()}
        
        required_columns = {
            "id", "name", "account", "message_link", "target_groups", 
            "part2_message_link", "part2_target_groups", "status",
            "current_index", "error_message", "last_processed_time", "created_at",
            "intervalMin", "intervalMax", "afterEachSecond",
            "randomSleepTimeMin", "randomSleepTimeMax", "customReplyMessage"
        }
        
        missing_columns = required_columns - columns
        if missing_columns:
            logger.info(f"Adding missing columns to forwarder_tasks: {missing_columns}")
            for column in missing_columns:
                if column == "target_groups":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN target_groups TEXT")
                elif column == "part2_target_groups":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN part2_target_groups TEXT")
                elif column == "intervalMin":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN intervalMin INTEGER DEFAULT 20")
                elif column == "intervalMax":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN intervalMax INTEGER DEFAULT 25")
                elif column == "afterEachSecond":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN afterEachSecond INTEGER DEFAULT 360")
                elif column == "randomSleepTimeMin":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN randomSleepTimeMin INTEGER DEFAULT 30")
                elif column == "randomSleepTimeMax":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN randomSleepTimeMax INTEGER DEFAULT 60")
                elif column == "customReplyMessage":
                    cursor.execute("ALTER TABLE forwarder_tasks ADD COLUMN customReplyMessage TEXT")
                else:
                    cursor.execute(f"ALTER TABLE forwarder_tasks ADD COLUMN {column} TEXT")
        
        # Check task_groups table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='task_groups'")
        if not cursor.fetchone():
            logger.info("Creating task_groups table")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS task_groups (
                    task_id TEXT,
                    group_link TEXT,
                    processed INTEGER DEFAULT 0,
                    error_message TEXT,
                    FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                    PRIMARY KEY (task_id, group_link)
                )
            ''')
            
        # Check part2_task_groups table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='part2_task_groups'")
        if not cursor.fetchone():
            logger.info("Creating part2_task_groups table")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS part2_task_groups (
                    task_id TEXT,
                    group_link TEXT,
                    processed INTEGER DEFAULT 0,
                    error_message TEXT,
                    FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                    PRIMARY KEY (task_id, group_link)
                )
            ''')
            
        # Check forwarder_settings table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='forwarder_settings'")
        if not cursor.fetchone():
            logger.info("Creating forwarder_settings table")
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS forwarder_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            ''')
            
            # Initialize default settings
            default_settings = {
                'useRandomSleepTime': 'true',
                'logFailedGroups': 'true',
                'floodwait': 'true',
                'replyMessage': 'true',
                'message': 'Hi! This is an advertising bot. @vipstore. DMs won\'t be seen here'
            }
            
            for key, value in default_settings.items():
                cursor.execute(
                    "INSERT OR IGNORE INTO forwarder_settings (key, value) VALUES (?, ?)",
                    (key, value)
                )
        
        # Commit changes
        conn.commit()
        logger.info("Database schema fixed successfully")
        
    except Exception as e:
        logger.error(f"Error fixing database schema: {str(e)}")
        if conn:
            conn.rollback()
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    fix_database_schema() 