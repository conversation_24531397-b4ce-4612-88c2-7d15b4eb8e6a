import re
import os
import sys
import shutil

def fix_indentation_issues(filename):
    """Fix all indentation issues in the given file."""
    # Create a backup
    backup_file = f"{filename}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix indentation in the TelegramLoginWorker._login_flow method
    content = fix_login_flow_method(content)
    
    # Fix indentation in the main() function
    content = fix_main_function(content)
    
    # Fix any misaligned methods in the TGCheckerApp class
    content = fix_misaligned_methods(content)
    
    # Save to a new file
    fixed_file = "main_all_fixed.py"
    with open(fixed_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed all indentation issues and saved to {fixed_file}")
    return fixed_file

def fix_login_flow_method(content):
    """Fix indentation in the _login_flow method."""
    # Find the start of the _login_flow method
    login_flow_start = content.find("    async def _login_flow(self):")
    if login_flow_start == -1:
        print("Could not find _login_flow method")
        return content
    
    # Determine where the method ends (next method or class)
    login_flow_end = content.find("    def run(self):", login_flow_start + 1)
    if login_flow_end == -1:
        login_flow_end = content.find("class TGCheckerApp", login_flow_start)
    
    if login_flow_end == -1:
        print("Could not determine where _login_flow method ends")
        return content
    
    # Extract the method content
    original_method = content[login_flow_start:login_flow_end]
    
    # Create the correctly indented method
    fixed_method = """    async def _login_flow(self):
        from telethon import TelegramClient
        try:
            self.client = TelegramClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect with timeout
            try:
                await asyncio.wait_for(self.client.connect(), timeout=10)
                if self.logger:
                    self.logger.info(f"Connected to Telegram for {self.phone}")
            except asyncio.TimeoutError:
                if self.logger:
                    self.logger.error(f"Connection timeout for {self.phone}")
                self.login_error.emit("Connection to Telegram timed out. Please try again.")
                return
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Connection error for {self.phone}: {str(e)}")
                self.login_error.emit(f"Connection error: {str(e)}")
                return
                
            if not await self.client.is_user_authorized():
                if self.logger:
                    self.logger.info(f"User not authorized, proceeding to login flow for {self.phone}")
        
                try:
                    if self.password:
                        # 2FA flow
                        if self.logger:
                            self.logger.info(f"Attempting 2FA login for {self.phone}")
                        await asyncio.wait_for(self.client.sign_in(password=self.password), timeout=15)
                    else:
                        # Code flow
                        if self.logger:
                            self.logger.info(f"Attempting code login for {self.phone} with code {self.code}")
                        await asyncio.wait_for(
                            self.client.sign_in(phone=self.phone, code=self.code, phone_code_hash=self.phone_code_hash),
                            timeout=15
                        )
                        
                        # Get user info
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"Login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    
                except asyncio.TimeoutError:
                    if self.logger:
                        self.logger.error(f"Login timeout for {self.phone}")
                    self.login_error.emit("Login verification timed out. Please try again.")
                except SessionPasswordNeededError:
                    if self.logger:
                        self.logger.info(f"2FA required for {self.phone}")
                    self.login_2fa_required.emit()
                except PhoneCodeInvalidError:
                    if self.logger:
                        self.logger.error(f"Invalid code for {self.phone}")
                    self.login_error.emit("Invalid or expired code.")
                except Exception as e:
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        if self.logger:
                            self.logger.info(f"2FA detected from login error for {self.phone}")
                        self.login_2fa_required.emit()
                    elif any(keyword in error_msg for keyword in ["invalid", "code", "expired", "wrong"]):
                        if self.logger:
                            self.logger.error(f"Invalid verification code for {self.phone}")
                        self.login_error.emit("Invalid or expired verification code.")
                    else:
                        if self.logger:
                            self.logger.error(f"Login error for {self.phone}: {str(e)}")
                        self.login_error.emit(str(e))
            else:
                # Already authorized
                me = await self.client.get_me()
                if self.logger:
                    self.logger.info(f"Already authorized for {self.phone}")
                self.login_success.emit({'user': me})
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Connection error for {self.phone}: {str(e)}")
            self.login_error.emit(str(e))
        finally:
            # Proper cleanup
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass
"""
    
    # Replace the original method with the fixed version
    return content.replace(original_method, fixed_method)

def fix_main_function(content):
    """Fix indentation in the main() function."""
    # Find the main function
    main_match = re.search(r"def main\(\):(.*?)(?=\n\s*[^ \n]|$)", content, re.DOTALL)
    if not main_match:
        print("Could not find main() function")
        return content
    
    # Extract the original main function and its content
    original_main = main_match.group(0)
    
    # Create properly indented main function
    fixed_main = """def main():
    app = QApplication(sys.argv)
    window = TGCheckerApp()
    window.show()
    
    # Set up a graceful exit
    app.aboutToQuit.connect(lambda: window.monitor.stop() if window.monitor.is_running else None)
    
    sys.exit(app.exec_())
"""
    
    # Replace the original main function with the fixed version
    return content.replace(original_main, fixed_main)

def fix_misaligned_methods(content):
    """Fix misaligned methods in the TGCheckerApp class."""
    # Find the TGCheckerApp class
    tg_checker_app_match = re.search(r"class TGCheckerApp\(QMainWindow\):(.*?)(?=\n\s*[^ \n]def main\(\):)", content, re.DOTALL)
    if not tg_checker_app_match:
        print("Could not find TGCheckerApp class")
        return content
    
    # Extract the class content
    class_content = tg_checker_app_match.group(1)
    
    # Find methods that are not properly indented (at the wrong level)
    wrong_indentation_pattern = r"\n([^ \n][^\n]*?def [^:]+:)"
    fixed_class_content = re.sub(wrong_indentation_pattern, r"\n    \1", class_content)
    
    # Fix indentation of method bodies that follow incorrectly indented methods
    lines = fixed_class_content.split("\n")
    for i in range(len(lines) - 1):
        if re.match(r"    def [^:]+:", lines[i]) and lines[i+1].strip() and not lines[i+1].startswith("    "):
            # This is a method definition followed by an unindented line
            lines[i+1] = "        " + lines[i+1].lstrip()
    
    fixed_class_content = "\n".join(lines)
    
    # Replace the original class content with the fixed version
    return content.replace(tg_checker_app_match.group(1), fixed_class_content)

def check_and_fix_nested_class_indentation(content):
    """Fix indentation issues in nested classes, especially AccountDialog in add_account."""
    # Find the add_account method
    add_account_match = re.search(r"def add_account\(self\):(.*?)(?=\n    def [^:]+:)", content, re.DOTALL)
    if not add_account_match:
        print("Could not find add_account method")
        return content
    
    # Extract the method content
    method_content = add_account_match.group(1)
    
    # Fix indentation of the nested AccountDialog class and its methods
    fixed_method_content = method_content
    
    # Find methods in the nested class that might have wrong indentation
    nested_methods = re.finditer(r"\n(\s*)def ([^:]+):", fixed_method_content)
    for match in nested_methods:
        indent, method_name = match.groups()
        # If this is a method in the AccountDialog class but doesn't have enough indentation
        if len(indent) < 12 and not method_name.startswith("__"):
            # Fix the indentation of this method
            original = f"\n{indent}def {method_name}:"
            replacement = f"\n            def {method_name}:"
            fixed_method_content = fixed_method_content.replace(original, replacement)
    
    # Replace the original method content with the fixed version
    return content.replace(method_content, fixed_method_content)

if __name__ == "__main__":
    fixed_file = fix_indentation_issues("main.py")
    print(f"You can now run the fixed file: python {fixed_file}") 