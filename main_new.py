#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Telegram Checker - New Version
"""

import os
import sys
import logging
import time
import datetime
import sqlite3
import json
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QPushButton, QLabel, QTabWidget, QLineEdit, QTextEdit, QComboBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QFileDialog, QMessageBox
)
from PyQt5.QtCore import QThread, pyqtSignal, Qt, QTimer
from PyQt5.QtGui import QFont, QIcon, QTextCursor

# Initialize logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime, qRegisterMetaType, pyqtSlot)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("tg_checker.log"),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("tg_checker")


# Register Qt meta types for thread-safe operations
qRegisterMetaType('QTextCursor')
qRegisterMetaType('QString')

class TGCheckerApp(QMainWindow):
    """Main application class for TG Checker."""
    
    def __init__(self):
        """Initialize the application."""
        super().__init__()
        
        # Set up the logger
        self.logger = logging.getLogger("tg_checker")
        
        # Initialize database path
        self.db_path = "tg_checker.db"
        
        # Set up the UI
        self.setup_ui()
        
        # Initialize the database
        self.init_db()
        
        # Log startup
        self.logger.info("Application started")
        
    def _get_db_path(self):
        """Safely get database path."""
        if hasattr(self, 'account_manager') and hasattr(self.account_manager, 'db_path'):
            return self.account_manager.db_path
        elif hasattr(self, 'db_path'):
            return self.db_path
        else:
            # Create a default db path
            self.db_path = "tg_checker.db"
            return self.db_path
    
    def setup_ui(self):
        """Set up the user interface."""
        # Set window properties
        self.setWindowTitle("TG Checker")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # Create tabs
        self.dashboard_tab = self.create_dashboard_tab()
        self.accounts_tab = self.create_accounts_tab()
        self.monitor_tab = self.create_monitor_tab()
        self.logs_tab = self.create_logs_tab()
        self.settings_tab = self.create_settings_tab()
        
        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Dashboard")
        self.tabs.addTab(self.accounts_tab, "Accounts")
        self.tabs.addTab(self.monitor_tab, "Monitor")
        self.tabs.addTab(self.logs_tab, "Logs")
        self.tabs.addTab(self.settings_tab, "Settings")
        
        # Create status bar
        self.status_bar = self.statusBar()
        self.status_label = QLabel("Status: Ready")
        self.status_bar.addWidget(self.status_label)
        
        # Set up signal connections
        self.setup_connections()
    
    def setup_connections(self):
        """Set up signal connections."""
        # Connect tab changed signal
        self.tabs.currentChanged.connect(self.tab_changed)
    
    def tab_changed(self, index):
        """Handle tab changed event."""
        tab_name = self.tabs.tabText(index)
        self.logger.info(f"Switched to {tab_name} tab")
    
    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create controls
        self.start_checker_button = QPushButton("Start Checker")
        self.stop_checker_button = QPushButton("Stop Checker")
        self.stop_checker_button.setEnabled(False)
        
        # Connect buttons
        self.start_checker_button.clicked.connect(self.start_checker)
        self.stop_checker_button.clicked.connect(self.stop_checker)
        
        # Create log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        
        # Add widgets to layout
        control_layout = QHBoxLayout()
        control_layout.addWidget(self.start_checker_button)
        control_layout.addWidget(self.stop_checker_button)
        control_layout.addStretch()
        
        layout.addLayout(control_layout)
        layout.addWidget(QLabel("Log:"))
        layout.addWidget(self.log_display)
        
        return tab
    
    def create_accounts_tab(self):
        """Create the accounts tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create account table
        self.account_table = QTableWidget(0, 5)
        self.account_table.setHorizontalHeaderLabels(["ID", "Phone", "API ID", "API Hash", "Status"])
        self.account_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        # Create account controls
        self.add_account_button = QPushButton("Add Account")
        self.remove_account_button = QPushButton("Remove Account")
        self.import_accounts_button = QPushButton("Import Accounts")
        self.export_accounts_button = QPushButton("Export Accounts")
        
        # Connect buttons
        self.add_account_button.clicked.connect(self.add_account)
        self.remove_account_button.clicked.connect(self.remove_account)
        self.import_accounts_button.clicked.connect(self.import_accounts)
        self.export_accounts_button.clicked.connect(self.export_accounts)
        
        # Add widgets to layout
        button_layout = QHBoxLayout()
        button_layout.addWidget(self.add_account_button)
        button_layout.addWidget(self.remove_account_button)
        button_layout.addWidget(self.import_accounts_button)
        button_layout.addWidget(self.export_accounts_button)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        layout.addWidget(self.account_table)
        
        return tab
    
    def create_monitor_tab(self):
        """Create the monitor tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add a label
        layout.addWidget(QLabel("Monitor functionality will be implemented in a future update."))
        
        return tab
    
    def create_logs_tab(self):
        """Create the logs tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add log filter controls
        filter_layout = QHBoxLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["All", "Info", "Warning", "Error", "Critical"])
        
        self.log_type_combo = QComboBox()
        self.log_type_combo.addItems(["General", "Authentication", "Usage Checker"])
        self.log_type_combo.currentIndexChanged.connect(self.update_log_display)
        
        self.export_logs_button = QPushButton("Export Logs")
        self.export_logs_button.clicked.connect(self.export_logs)
        
        filter_layout.addWidget(QLabel("Log Type:"))
        filter_layout.addWidget(self.log_type_combo)
        filter_layout.addWidget(QLabel("Log Level:"))
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addWidget(self.export_logs_button)
        filter_layout.addStretch()
        
        # Create log text display
        self.logs_text = QTextEdit()
        self.logs_text.setReadOnly(True)
        
        layout.addLayout(filter_layout)
        layout.addWidget(self.logs_text)
        
        return tab
    
    def create_settings_tab(self):
        """Create the settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add a label
        layout.addWidget(QLabel("Settings functionality will be implemented in a future update."))
        
        return tab
    
    def init_db(self):
        """Initialize the database."""
        try:
            conn = sqlite3.connect(self._get_db_path())
            cursor = conn.cursor()
            
            # Create accounts table if it doesn't exist
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY,
                    phone TEXT,
                    api_id TEXT,
                    api_hash TEXT,
                    status TEXT DEFAULT 'active'
                )
            """)
            
            conn.commit()
            conn.close()
            
            # Load accounts into the table
            self.load_accounts()
            
            self.logger.info("Database initialized")
        except Exception as e:
            self.logger.error(f"Error initializing database: {str(e)}")
    
    def load_accounts(self):
        """Load accounts from the database into the table."""
        try:
            conn = sqlite3.connect(self._get_db_path())
            cursor = conn.cursor()
            
            cursor.execute("SELECT id, phone, api_id, api_hash, status FROM accounts")
            accounts = cursor.fetchall()
            
            self.account_table.setRowCount(0)
            
            for account in accounts:
                row_position = self.account_table.rowCount()
                self.account_table.insertRow(row_position)
                
                for i, value in enumerate(account):
                    self.account_table.setItem(row_position, i, QTableWidgetItem(str(value)))
            
            conn.close()
            
            self.logger.info(f"Loaded {len(accounts)} accounts from database")
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
    
    def start_checker(self):
        """Start the checker process."""
        self.log_display.append("Starting checker...")
        self.update_status("Running")
        self.start_checker_button.setEnabled(False)
        self.stop_checker_button.setEnabled(True)
    
    def stop_checker(self):
        """Stop the checker process."""
        if hasattr(self, 'worker') and self.worker is not None:
            self.worker.stop()
            self.worker = None
        self.log_display.append("Checker stopped.")
        self.update_status("Stopped")
        self.start_checker_button.setEnabled(True)
        self.stop_checker_button.setEnabled(False)
    
    def remove_account(self):
        """Remove selected account from the database."""
        selected_items = self.account_table.selectedItems()
        if not selected_items:
            return
            
        row = selected_items[0].row()
        account_id = self.account_table.item(row, 0).text()
        
        try:
            conn = sqlite3.connect(self._get_db_path())
            cursor = conn.cursor()
            cursor.execute("DELETE FROM accounts WHERE id = ?", (account_id,))
            conn.commit()
            conn.close()
            
            self.account_table.removeRow(row)
            self.log_display.append(f"Account {account_id} removed successfully.")
        except Exception as e:
            self.log_display.append(f"Error removing account: {str(e)}")
    
    def add_account(self):
        """Stub for add_account method."""
        self.log_display.append("Add account functionality is not implemented yet.")
    
    def import_accounts(self):
        """Stub for import_accounts method."""
        self.log_display.append("Import accounts functionality is not implemented yet.")
    
    def export_accounts(self):
        """Stub for export_accounts method."""
        self.log_display.append("Export accounts functionality is not implemented yet.")
    
    def update_log_display(self):
        """Update the log display based on selected filters."""
        log_type = self.log_type_combo.currentText()
        log_level = self.log_level_combo.currentText()
        
        self.logs_text.clear()
        self.logs_text.append(f"Displaying {log_level} logs for {log_type}")
        
        # In a real implementation, this would load and filter logs from a file
    
    def export_logs(self):
        """Export logs to a file."""
        self.log_display.append("Export logs functionality is not implemented yet.")
    
    def update_status(self, status):
        """Update the status label."""
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"Status: {status}")

def main():
    """Main application entry point."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Modern style
    
    # Set application metadata
    app.setApplicationName("TG Checker")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("TG Tools")
    
    # Create and show main window
    window = TGCheckerApp()
    window.show()
    
    # Start the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
