#!/usr/bin/env python3
"""
🚨 DIRECT ACCOUNT FALLBACK FIX
Fixes the critical issue where valid groups are skipped due to account problems.

The issue: When ONE account fails (database lock, connection error), 
the group is immediately marked as failed instead of trying other accounts.

This fix implements proper account fallback logic.
"""

def apply_direct_fix():
    """Apply direct account fallback fix to main.py"""
    
    # Create backup first
    import shutil
    import time
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    backup_name = f"main.py.backup_before_account_fix_{timestamp}"
    shutil.copy2("main.py", backup_name)
    print(f"✅ Created backup: {backup_name}")
    
    # Read the current file
    with open("main.py", "r", encoding="utf-8") as f:
        lines = f.readlines()
    
    print("🔧 Applying account fallback fixes...")
    
    # Find the check_group_or_channel method and replace it
    start_line = None
    end_line = None
    indent = ""
    
    for i, line in enumerate(lines):
        if "def check_group_or_channel(self, link):" in line:
            start_line = i
            indent = line[:len(line) - len(line.lstrip())]
            break
    
    if start_line is None:
        print("❌ ERROR: Could not find check_group_or_channel method")
        return False
    
    # Find the end of the method
    for i in range(start_line + 1, len(lines)):
        line = lines[i]
        if line.strip() and not line.startswith(indent + " ") and not line.startswith(indent + "\t"):
            if line.strip() and not line.startswith("#"):
                end_line = i
                break
    
    if end_line is None:
        end_line = len(lines)
    
    print(f"✅ Found method at lines {start_line + 1} to {end_line}")
    
    # Create the new enhanced method
    new_method = f'''{indent}def check_group_or_channel(self, link):
{indent}    """Enhanced group checking with multi-account fallback and comprehensive logging."""
{indent}    print(f"🔍 DEBUG: Starting check for: {{link}}")
{indent}    self.log_activity_signal.emit(f"🔍 DEBUG: Starting check for: {{link}}")
{indent}    
{indent}    # Get all available accounts for fallback
{indent}    available_accounts = []
{indent}    try:
{indent}        accounts = self.account_manager.get_active_accounts()
{indent}        for account in accounts:
{indent}            phone = account.get("phone")
{indent}            status = account.get("status", "").lower()
{indent}            # Skip accounts with known issues
{indent}            if "banned" in status or "limit" in status or "disable" in status:
{indent}                continue
{indent}            available_accounts.append(phone)
{indent}    except Exception as e:
{indent}        self.log_activity_signal.emit(f"⚠️ WARNING: Could not get accounts list: {{str(e)}}")
{indent}        # Fallback to trying with default account selection
{indent}        available_accounts = ["default"]
{indent}    
{indent}    if not available_accounts:
{indent}        self.log_activity_signal.emit(f"🚨 CRITICAL: No available accounts for: {{link}}")
{indent}        return {{
{indent}            "valid": False,
{indent}            "error_type": "account_issue", 
{indent}            "reason": "No available accounts",
{indent}            "type": "unknown",
{indent}            "members": 0,
{indent}            "last_message_age_hours": 999,
{indent}            "total_messages": 0
{indent}        }}
{indent}    
{indent}    self.log_activity_signal.emit(f"✅ DEBUG: Found {{len(available_accounts)}} available accounts")
{indent}    
{indent}    # Try each account until success or all accounts exhausted
{indent}    last_error = None
{indent}    account_errors = []
{indent}    
{indent}    for attempt, phone in enumerate(available_accounts, 1):
{indent}        try:
{indent}            self.log_activity_signal.emit(f"✅ DEBUG: Using account {{phone}} for check (attempt {{attempt}}/{{len(available_accounts)}})")
{indent}            
{indent}            # Create client for this specific account
{indent}            try:
{indent}                from tg_client import TelegramClient
{indent}                client = TelegramClient(phone)
{indent}                print(f"🔍 DEBUG: Created TelegramClient for account: {{phone}}")
{indent}                self.log_activity_signal.emit(f"🔍 DEBUG: Checking entity info for: {{link}}")
{indent}                
{indent}                # Attempt to get entity info with this account
{indent}                result = None
{indent}                error = None

{indent}                def check_with_timeout():
{indent}                    nonlocal result, error
{indent}                    try:
{indent}                        result = client.get_entity_info(link)
{indent}                        print(f"✅ DEBUG: get_entity_info completed for: {{link}}")
{indent}                    except Exception as _e:
{indent}                        error = _e
{indent}                        print(f"❌ DEBUG: get_entity_info error: {{_e}}")

{indent}                # Run with 30 second timeout
{indent}                import threading
{indent}                thread = threading.Thread(target=check_with_timeout, daemon=True)
{indent}                thread.start()
{indent}                thread.join(timeout=30.0)

{indent}                if thread.is_alive():
{indent}                    print(f"⏰ DEBUG: Timeout checking {{link}} with account {{phone}}")
{indent}                    self.log_activity_signal.emit(f"⏰ TIMEOUT: Group check timed out for: {{link}} on account {{phone}}")
{indent}                    account_errors.append(f"Account {{phone}}: Timeout")
{indent}                    continue  # Try next account

{indent}                if error:
{indent}                    error_str = str(error).lower()
{indent}                    print(f"❌ DEBUG: Error with account {{phone}}: {{error_str}}")
{indent}                    
{indent}                    # Check if this is an account-specific issue that should trigger fallback
{indent}                    if any(keyword in error_str for keyword in [
{indent}                        "database is locked", "could not connect", "connection error",
{indent}                        "session", "auth", "login", "flood", "rate limit"
{indent}                    ]):
{indent}                        account_errors.append(f"Account {{phone}}: {{str(error)}}")
{indent}                        self.log_activity_signal.emit(f"🔄 FALLBACK: Account {{phone}} failed ({{str(error)[:50]}}...), trying next account")
{indent}                        continue  # Try next account
{indent}                    else:
{indent}                        # This is likely a group-level error, not account-specific
{indent}                        raise error

{indent}                print(f"✅ DEBUG: Successfully checked {{link}}: {{result}}")
{indent}                self.log_activity_signal.emit(f"✅ DEBUG: Check completed for: {{link}}")
{indent}                
{indent}                # Check if result indicates account issue but group might be valid
{indent}                if (not result["valid"] and 
{indent}                    result.get("error_type") == "account_issue" and 
{indent}                    attempt < len(available_accounts)):
{indent}                    
{indent}                    account_errors.append(f"Account {{phone}}: {{result.get('reason', 'Account issue')}}")
{indent}                    self.log_activity_signal.emit(f"🔄 ACCOUNT ISSUE: {{phone}} failed, trying next account")
{indent}                    continue  # Try next account
{indent}                
{indent}                # Success or definitive group-level result
{indent}                if account_errors:
{indent}                    self.log_activity_signal.emit(f"✅ SUCCESS: After {{len(account_errors)}} failed attempts, account {{phone}} succeeded for: {{link}}")
{indent}                
{indent}                return result

{indent}            except Exception as e:
{indent}                error_str = str(e).lower()
{indent}                account_errors.append(f"Account {{phone}}: {{str(e)}}")
{indent}                self.log_activity_signal.emit(f"❌ ERROR: Account {{phone}} failed: {{str(e)[:100]}}...")
{indent}                last_error = e
{indent}                
{indent}                # If this is the last account, don't continue the loop
{indent}                if attempt >= len(available_accounts):
{indent}                    break
{indent}                
{indent}                # Check if we should try next account
{indent}                if any(keyword in error_str for keyword in [
{indent}                    "database is locked", "could not connect", "connection error", 
{indent}                    "session", "auth", "login", "timeout", "flood", "rate limit"
{indent}                ]):
{indent}                    self.log_activity_signal.emit(f"🔄 FALLBACK: Trying next account due to: {{str(e)[:50]}}...")
{indent}                    continue
{indent}                else:
{indent}                    # Group-level error, no point trying other accounts
{indent}                    break
{indent}                    
{indent}        except Exception as e:
{indent}            account_errors.append(f"Account {{phone}}: Critical error - {{str(e)}}")
{indent}            last_error = e
{indent}            continue
{indent}    
{indent}    # All accounts failed
{indent}    self.log_activity_signal.emit(f"🚨 ALL ACCOUNTS FAILED for {{link}}: {{len(account_errors)}} attempts")
{indent}    for i, error in enumerate(account_errors, 1):
{indent}        self.log_activity_signal.emit(f"  {{i}}. {{error}}")
{indent}    
{indent}    # Classify the final error
{indent}    if last_error:
{indent}        error_str = str(last_error).lower()
{indent}        if any(keyword in error_str for keyword in ["timeout", "connection", "network", "unreachable"]):
{indent}            return {{
{indent}                "valid": False,
{indent}                "error_type": "invalid_group",
{indent}                "reason": f"Group unreachable after {{len(account_errors)}} attempts: {{str(last_error)}}",
{indent}                "type": "unknown",
{indent}                "members": 0,
{indent}                "last_message_age_hours": 999,
{indent}                "total_messages": 0
{indent}            }}
{indent}        else:
{indent}            return {{
{indent}                "valid": False,
{indent}                "error_type": "account_issue",
{indent}                "reason": f"All {{len(available_accounts)}} accounts failed: {{str(last_error)}}",
{indent}                "type": "unknown", 
{indent}                "members": 0,
{indent}                "last_message_age_hours": 999,
{indent}                "total_messages": 0
{indent}            }}
{indent}    
{indent}    # No specific error, just failed
{indent}    return {{
{indent}        "valid": False,
{indent}        "error_type": "account_issue",
{indent}        "reason": f"All {{len(available_accounts)}} accounts failed to check group",
{indent}        "type": "unknown",
{indent}        "members": 0, 
{indent}        "last_message_age_hours": 999,
{indent}        "total_messages": 0
{indent}    }}

'''
    
    # Replace the method
    new_lines = lines[:start_line] + [new_method] + lines[end_line:]
    
    # Write the updated file
    with open("main.py", "w", encoding="utf-8") as f:
        f.writelines(new_lines)
    
    print("✅ ACCOUNT FALLBACK FIX APPLIED SUCCESSFULLY!")
    print("\n🎯 FIXES APPLIED:")
    print("  ✅ Enhanced account fallback system")
    print("  ✅ Multi-account attempts per group")
    print("  ✅ Comprehensive error logging")
    print("  ✅ Database lock handling")
    print("  ✅ Connection error recovery")
    print("\n🚀 The checker will now:")
    print("  • Try each group with multiple accounts before giving up")
    print("  • Log every decision and attempt clearly") 
    print("  • Never silently skip valid groups")
    print("  • Handle database locks properly")
    print("  • Retry with different accounts on connection errors")
    
    return True

if __name__ == "__main__":
    apply_direct_fix() 