#!/usr/bin/env python3
"""
Telegram Group Message Debugger
- Loads all session files from sessions/
- For each session/account, checks all provided groups
- Joins the group if not already a member (if possible)
- Fetches the latest 30 messages per group
- Prints: message ID, type (user/service/deleted), date, and content (first 80 chars)
- Uses all accounts, not just one

Fill in your API_ID and API_HASH below.
"""

import os
from telethon.sync import TelegramClient
from telethon.errors import UserAlreadyParticipantError, InviteHashInvalidError, InviteHashExpiredError
from telethon.tl.types import MessageService
from datetime import datetime
from telethon.tl.functions.channels import JoinChannelRequest

# === FILL IN YOUR OWN CREDENTIALS ===
API_ID = 123456  # <-- your API ID here
API_HASH = 'your_api_hash_here'  # <-- your API hash here

GROUPS = [
    'https://t.me/THE_SELLERS_MARKET',
    'https://t.me/BuysellZone1',
    'https://t.me/EscrowersCommunity',
    'https://t.me/Bineros_Full',
    'https://t.me/TeleSellersHub'
]

SESSIONS_DIR = 'sessions'
session_files = [f for f in os.listdir(SESSIONS_DIR) if f.endswith('.session')]

if not session_files:
    print('No session files found in sessions/.')
    exit(1)

for session_file in session_files:
    session_path = os.path.join(SESSIONS_DIR, session_file)
    print(f"\n=== Using session: {session_file} ===")
    try:
        client = TelegramClient(session_path, API_ID, API_HASH)
        with client:
            me = client.get_me()
            print(f"Logged in as: {me.username or me.first_name or me.id}")
            for group in GROUPS:
                print(f"\n--- Group: {group} ---")
                try:
                    # Try to join the group if not already a participant
                    try:
                        client(JoinChannelRequest(group))
                        print("Joined group successfully.")
                    except UserAlreadyParticipantError:
                        pass  # Already a member
                    except (InviteHashInvalidError, InviteHashExpiredError):
                        print("❌ Invalid or expired invite link.")
                    except Exception as join_e:
                        # Not fatal, just log
                        print(f"(Join skipped: {join_e})")
                    messages = client.get_messages(group, limit=30)
                    for idx, m in enumerate(messages):
                        if m is None:
                            msg_type = 'None'
                        elif isinstance(m, MessageService):
                            msg_type = 'service'
                        elif getattr(m, 'message', None) is None and not getattr(m, 'media', None):
                            msg_type = 'empty/deleted'
                        else:
                            msg_type = 'user'
                        date_str = str(getattr(m, 'date', ''))
                        text = getattr(m, 'message', '<media/other>')
                        print(f"[{idx:02d}] id={getattr(m, 'id', None)}, type={msg_type}, date={date_str}, text={text[:80]}")
                except Exception as group_e:
                    print(f"❌ Error fetching messages for {group}: {group_e}")
    except Exception as e:
        print(f"❌ Error with session {session_file}: {e}") 