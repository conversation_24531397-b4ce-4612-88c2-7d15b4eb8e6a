"""
Settings module for TG Checker application.
This module contains configuration settings that can be modified by the user.
"""

import os
import json
import logging
import sqlite3
from datetime import datetime

logger = logging.getLogger(__name__)

# Default settings
DEFAULT_SETTINGS = {
    # Group checking settings
    "min_members": 500,
    "max_last_message_hours": 1,
    "min_total_messages": 100,
    "check_post_frequency": True,
    "check_real_users": True,
    "public_groups_only": True,
    "language_filter": "all",
    "wait_time_after_check": 2,
    "history_message_limit": 100,
    
    # Monitor settings
    "check_interval": 300,
    "auto_fix": True,
    "auto_start_monitor": True,
    "minimize_to_tray": True,
    "dark_mode": False,
    
    # Performance settings
    "max_memory_percent": 80,
    "max_restart_attempts": 3,
    "batch_size": 10,
    
    # UI settings
    "show_welcome": True,
    "log_level": "INFO",
    
    # Language settings
    "supported_languages": ["en", "ar", "ru", "es", "fr", "de", "zh", "hi", "pt", "tr"],
    
    # Path settings
    "logs_dir": "logs",
    "sessions_dir": "sessions",
    "backup_dir": "backups",
    "results_dir": "Results"
}

class Settings:
    """Settings manager for TG Checker application."""
    
    def __init__(self, db_path="tg_checker.db"):
        self.db_path = db_path
        self.settings = DEFAULT_SETTINGS.copy()
        self.load_from_db()
    
    def load_from_db(self):
        """Load settings from database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Check if settings table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='settings'")
            if not cursor.fetchone():
                logger.info("Settings table does not exist, using defaults")
                return
            
            # Get settings from database
            cursor.execute("SELECT key, value FROM settings")
            rows = cursor.fetchall()
            
            # Update settings with values from database
            for key, value in rows:
                # Convert string values to appropriate types
                if key in self.settings:
                    original_type = type(self.settings[key])
                    if original_type == bool:
                        self.settings[key] = value.lower() in ('true', '1', 't', 'y', 'yes')
                    elif original_type == int:
                        self.settings[key] = int(value)
                    elif original_type == float:
                        self.settings[key] = float(value)
                    elif original_type == list:
                        try:
                            self.settings[key] = json.loads(value)
                        except:
                            pass
                    else:
                        self.settings[key] = value
            
            conn.close()
            logger.info("Settings loaded from database")
            
        except Exception as e:
            logger.error(f"Error loading settings from database: {str(e)}")
    
    def save_to_db(self):
        """Save settings to database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create settings table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT,
                    description TEXT
                )
            ''')
            
            # Save settings to database
            for key, value in self.settings.items():
                # Convert values to strings
                if isinstance(value, bool):
                    str_value = str(int(value))
                elif isinstance(value, (list, dict)):
                    str_value = json.dumps(value)
                else:
                    str_value = str(value)
                
                cursor.execute(
                    "INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)",
                    (key, str_value)
                )
            
            conn.commit()
            conn.close()
            logger.info("Settings saved to database")
            
        except Exception as e:
            logger.error(f"Error saving settings to database: {str(e)}")
    
    def get(self, key, default=None):
        """Get a setting value."""
        return self.settings.get(key, default)
    
    def set(self, key, value):
        """Set a setting value."""
        self.settings[key] = value
    
    def update(self, settings_dict):
        """Update multiple settings at once."""
        self.settings.update(settings_dict)
    
    def reset_to_defaults(self):
        """Reset settings to defaults."""
        self.settings = DEFAULT_SETTINGS.copy()
    
    def export_to_json(self, filepath="settings.json"):
        """Export settings to a JSON file."""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.settings, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Error exporting settings to JSON: {str(e)}")
            return False
    
    def import_from_json(self, filepath="settings.json"):
        """Import settings from a JSON file."""
        try:
            with open(filepath, 'r') as f:
                imported_settings = json.load(f)
            
            # Only update keys that already exist in settings
            for key in self.settings.keys():
                if key in imported_settings:
                    self.settings[key] = imported_settings[key]
            
            return True
        except Exception as e:
            logger.error(f"Error importing settings from JSON: {str(e)}")
            return False


# Global settings instance
settings = Settings()

def get_settings():
    """Get the global settings instance."""
    return settings 
