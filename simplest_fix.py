#!/usr/bin/env python3
"""
The simplest possible fix for the indentation error in main.py
"""

import os
import re
import shutil

def simplest_fix():
    """Apply the simplest possible fix to main.py"""
    print("=== Applying simplest fix to main.py ===")
    
    # Create backup
    input_file = "main.py"
    backup_file = "main.py.simplest_backup"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find line 659 and fix its indentation
    print("Fixing line 659 indentation issue...")
    if len(lines) >= 659:
        # Fix indentation of method definition
        lines[658] = "    def auto_refresh_missing_account_info(self):\n"
        
        # Ensure the method body is properly indented (only first few lines)
        for i in range(659, min(669, len(lines))):
            if lines[i].strip() and not lines[i].lstrip().startswith("def "):
                # Add 8 spaces of indentation (4 for class + 4 for method body)
                lines[i] = "        " + lines[i].lstrip()
            elif lines[i].lstrip().startswith("def "):
                # We've reached the next method
                break
        
        print("Fixed indentation")
    
    # Write fixed content back
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("\nIndentation issue fixed in main.py")
    print("You can now run: python main.py")
    
    return True

if __name__ == "__main__":
    simplest_fix() 