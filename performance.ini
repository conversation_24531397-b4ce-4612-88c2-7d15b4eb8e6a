# TG CHECKER PERFORMANCE CONFIGURATION
# Optimized settings to reduce CPU usage and improve performance

[DATABASE]
# Database connection settings for better performance
timeout = 30
pool_size = 3
cache_size = 1000
journal_mode = WAL
synchronous = NORMAL
temp_store = MEMORY

[UI]
# User interface optimization settings
update_interval = 1000
batch_size = 50
enable_animations = false
reduce_redraws = true
buffer_updates = true

[THREADING]
# Thread management for optimal performance
max_worker_threads = 2
thread_stack_size = 524288
enable_thread_pooling = true
cleanup_interval = 300

[MEMORY]
# Memory management settings
gc_threshold_0 = 700
gc_threshold_1 = 10
gc_threshold_2 = 10
enable_memory_monitoring = true
memory_limit_mb = 512

[NETWORK]
# Network optimization settings
connection_timeout = 20
retry_delay = 3
max_retries = 2
enable_connection_pooling = true
keep_alive = true

[JOINING]
# Telegram joining optimization
batch_size = 5
delay_between_joins = 2.0
flood_wait_buffer = 10
max_concurrent_joins = 2
enable_smart_throttling = true

[CHECKING]
# Group checking optimization
batch_size = 10
delay_between_checks = 1.0
max_concurrent_checks = 3
enable_result_caching = true
cache_duration = 300

[LOGGING]
# Logging optimization to reduce I/O
level = WARNING
enable_file_logging = true
max_log_size_mb = 10
backup_count = 3
buffer_size = 8192

[OPTIMIZATION]
# General optimization flags
enable_all_optimizations = true
reduce_cpu_usage = true
optimize_memory_usage = true
enable_smart_scheduling = true
auto_cleanup = true
