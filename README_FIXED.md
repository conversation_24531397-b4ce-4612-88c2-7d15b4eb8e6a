# TG Checker - Fixed Application

## English

This is the fixed version of the TG Checker application. All indentation issues and syntax errors have been corrected, allowing the application to run smoothly.

### How to Run

You have several options to run the application:

1. **Recommended:** Use the safe runner with error handling:
   ```
   run_safe.bat
   ```
   This will run the application with error logging and provide details if anything goes wrong.

2. Run the fixed application directly:
   ```
   run_comprehensive_fixed_app.bat
   ```
   or
   ```
   python main_comprehensive_fixed.py
   ```

3. If you want to see details about the fixes applied, check the `COMPREHENSIVE_FIX_SUMMARY.md` file.

### Features

- Account management for Telegram
- Group and channel validation
- Multi-account task handling
- Automatic account rotation
- Advanced flood wait handling
- Detailed logging and monitoring

## Kurdish / کوردی

ئەمە وەشانی چاککراوی ئەپلیکەیشنی TG Checker ـە. هەموو کێشەکانی ڕیزبەندی و هەڵەکانی سینتاکس چارەسەر کراون، کە ڕێگە دەدات بە ئەپلیکەیشنەکە بەبێ گرفت کار بکات.

### چۆنیەتی بەکارهێنان

چەند بژاردەیەکت هەیە بۆ بەکارهێنانی ئەپلیکەیشنەکە:

١. **پێشنیارکراو:** بەکارهێنانی بەڕێوەبەری پارێزراو لەگەڵ هەڵگرتنی هەڵەکان:
   ```
   run_safe_kurdish.bat
   ```
   ئەمە ئەپلیکەیشنەکە لەگەڵ تۆمارکردنی هەڵەکان بەکاردێنێت و زانیاری پێویست دەدات ئەگەر هەر شتێک هەڵە بوو.

٢. ڕاستەوخۆ ئەپلیکەیشنە چاککراوەکە بەکاربهێنە:
   ```
   run_comprehensive_fixed_app.bat
   ```
   یان
   ```
   python main_comprehensive_fixed.py
   ```

٣. ئەگەر دەتەوێت وردەکاری دەربارەی چاککردنەکان ببینیت، فایلی `COMPREHENSIVE_FIX_SUMMARY.md` بکەرەوە.

### تایبەتمەندییەکان

- بەڕێوەبردنی هەژماری تێلێگرام
- دڵنیابوون لە گروپ و چەناڵەکان
- بەڕێوەبردنی چەندین هەژمار بەیەکەوە
- خولانەوەی خۆکاری هەژمارەکان
- چارەسەری پێشکەوتووی FloodWait
- تۆمارکردن و چاودێری وردەکاری 