#!/usr/bin/env python3
"""
Comprehensive Filter Fix Script
Fixes the incorrect field name in filtering logic across all main files.

The issue: Files are using result["members"] but should use result["member_count"]
This causes the member count filter to always fail, making groups incorrectly bypass filters.
"""

import os
import re

def fix_filtering_logic():
    """Fix the filtering logic in all main Python files."""
    
    # Files to fix (excluding backups and .bak files)
    files_to_fix = [
        "main_working.py",
        "main_testing.py", 
        "main_temp_testing.py",
        "main_stub.py",
        "main_specific_fixed.py",
        "main_prefixed.py",
        "main_original_latest.py",
        "main_manual_fixed.py",
        "main_latest_version.py",
        "main_minimal_fixed.py",
        "main_indentation_fixed.py",
        "main_indent_fixed.py",
        "main_fully_fixed.py",
        "main_fixed_specific.py",
        "main_fixed_final.py",
        "main_fixed_complete.py",
        "main_fixed_conditional.py",
        "main_fixed_emergency.py",
        "main_fixed_combined.py",
        "main_fixed.py",
        "main_final_fixed.py",
        "main_final_fix.py",
        "main_else_fixed.py",
        "main_final.py",
        "main_comprehensive_fixed.py",
        "main_clean_version.py",
        "main_emergency.py",
        "main_clean_fixed.py",
        "main_clean.py",
        "main_backup_new.py",
        "main_backup_before_fix.py",
        "main_backup.py",
        "main_all_fixed.py",
        "main_backup_before_emergency_fix.py",
        "main.original.py",
        "main.fixed.py",
        "main_ultimate_fix.py",
        "main.clean.py"
    ]
    
    # Pattern to find and replace
    old_pattern = r'result\["members"\]\s*>=\s*min_members'
    new_pattern = r'result["member_count"] >= min_members'
    
    fixed_files = []
    
    for filename in files_to_fix:
        if os.path.exists(filename):
            try:
                print(f"🔧 Fixing {filename}...")
                
                # Read the file
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Count occurrences before fix
                matches_before = len(re.findall(old_pattern, content))
                
                if matches_before > 0:
                    # Apply the fix
                    updated_content = re.sub(old_pattern, new_pattern, content)
                    
                    # Count occurrences after fix
                    matches_after = len(re.findall(old_pattern, updated_content))
                    
                    # Write back the file
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(updated_content)
                    
                    fixed_files.append({
                        'file': filename,
                        'fixes': matches_before - matches_after
                    })
                    
                    print(f"   ✅ Fixed {matches_before - matches_after} instances")
                else:
                    print(f"   ℹ️  No issues found")
                    
            except Exception as e:
                print(f"   ❌ Error fixing {filename}: {str(e)}")
        else:
            print(f"   ⚠️  File not found: {filename}")
    
    # Summary
    print(f"\n📊 SUMMARY:")
    print(f"   Files processed: {len(files_to_fix)}")
    print(f"   Files fixed: {len(fixed_files)}")
    
    if fixed_files:
        print(f"\n✅ FIXED FILES:")
        for fix in fixed_files:
            print(f"   • {fix['file']}: {fix['fixes']} fixes")
    
    print(f"\n🎯 FILTER LOGIC FIX COMPLETE!")
    print(f"   The filtering now correctly uses 'member_count' instead of 'members'")
    print(f"   All 3 filter conditions will now be properly enforced:")
    print(f"   ✅ Min Members (member_count >= min_members)")
    print(f"   ✅ Min Chat Message Time (last_message_age_hours <= min_message_time_hours)")
    print(f"   ✅ Min Total Messages (total_messages >= min_total_messages)")

if __name__ == "__main__":
    fix_filtering_logic() 