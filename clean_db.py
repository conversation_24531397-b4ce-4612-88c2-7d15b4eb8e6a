#!/usr/bin/env python3
import sqlite3
import os

db_path = "tg_checker.db"

def normalize_phone(phone):
    """Normalize phone number for comparison"""
    if phone:
        return phone.replace('+', '').strip()
    return phone

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("Cleaning up duplicate accounts...")
    
    # Get all accounts
    cursor.execute("SELECT rowid, phone, api_id, api_hash, account_type, status, active FROM accounts")
    all_accounts = cursor.fetchall()
    
    # Group by normalized phone number
    phone_groups = {}
    for account in all_accounts:
        rowid, phone, api_id, api_hash, account_type, status, active = account
        normalized = normalize_phone(phone)
        
        if normalized not in phone_groups:
            phone_groups[normalized] = []
        phone_groups[normalized].append(account)
    
    # Process each group
    accounts_to_delete = []
    
    for normalized_phone, accounts in phone_groups.items():
        if len(accounts) > 1:
            print(f"Found {len(accounts)} entries for normalized phone {normalized_phone}")
            
            # Sort accounts by preference: normal with valid creds > session > invalid
            def account_priority(account):
                rowid, phone, api_id, api_hash, account_type, status, active = account
                if account_type == 'normal' and api_id not in ['0', 'session_import'] and api_hash not in ['session_import']:
                    return 0  # Highest priority
                elif account_type == 'session':
                    return 1  # Medium priority
                else:
                    return 2  # Lowest priority
            
            accounts.sort(key=account_priority)
            
            # Keep the first (best) account, mark others for deletion
            best_account = accounts[0]
            for account in accounts[1:]:
                accounts_to_delete.append(account[0])  # rowid
                print(f"  Will delete: {account[1]} ({account[4]})")
    
    # Delete duplicate accounts
    for rowid in accounts_to_delete:
        cursor.execute("DELETE FROM accounts WHERE rowid = ?", (rowid,))
        print(f"Deleted account with rowid {rowid}")
    
    # Also delete errors for removed accounts
    cursor.execute("DELETE FROM errors WHERE phone NOT IN (SELECT phone FROM accounts)")
    
    conn.commit()
    
    # Show final state
    print("\nFinal state:")
    cursor.execute("SELECT phone, api_id, api_hash, account_type, status, active FROM accounts ORDER BY phone")
    rows = cursor.fetchall()
    
    for row in rows:
        phone, api_id, api_hash, account_type, status, active = row
        print(f"Phone: {phone}")
        print(f"  API ID: {api_id}")
        print(f"  API Hash: {api_hash[:10]}..." if api_hash and len(str(api_hash)) > 10 else f"  API Hash: {api_hash}")
        print(f"  Type: {account_type}")
        print(f"  Status: {status}")
        print(f"  Active: {active}")
        print("-" * 40)
    
    conn.close()
    print(f"Total accounts: {len(rows)}")
else:
    print(f"Database {db_path} not found!") 