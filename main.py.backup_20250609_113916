import os
import sys
import time
import json
import logging
import threading
import async<PERSON>
from datetime import datetime, timedelta
import random
import uuid
import re
from collections import deque
import sqlite3

from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings, QMetaObject, Q_ARG, QObject, pyqtSlot, QPoint
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QTabWidget, QLabel, QPushButton,
    QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QDialog, QLineEdit, QMessageBox, QHeaderView, QSplitter,
    QCheckBox, QSpinBox, QDoubleSpinBox, QComboBox, QFileDialog, QRadioButton,
    QButtonGroup, QScrollArea, QProgressBar, QMenu, QGridLayout, QTabBar
)
from PyQt5.QtGui import QIcon, QFont, QCursor, QColor

# Suppress specific PyQt5 deprecation warnings
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*sipPyTypeDict.*")

from telethon.errors import (
    PhoneCodeInvalidError, SessionPasswordNeededError, FloodWaitError, 
    ChatAdminRequiredError, UserBannedInChannelError, ChannelPrivateError
)

from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

# Import tg_client for TelegramClient
from tg_client import TelegramClient

def format_time_remaining(seconds):
    """Format seconds into a human-readable time (e.g., 13h 49m)."""
    if seconds <= 0:
        return "0m"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m"
    elif minutes > 0:
        return f"{minutes}m"
    else:
        return f"{int(seconds)}s"

class TelegramLoginWorker(QThread):
    login_success = pyqtSignal(dict)
    login_2fa_required = pyqtSignal()
    login_error = pyqtSignal(str)

    def __init__(self, api_id, api_hash, phone, code=None, password=None, phone_code_hash=None, session_file=None, logger=None):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.code = code
        self.password = password
        self.phone_code_hash = phone_code_hash
        self.session_file = session_file or f'sessions/{phone}'
        self.logger = logger
        self.client = None

    def run(self):
        import asyncio
        asyncio.run(self._login_flow())

    async def _login_flow(self):
        from telethon import TelegramClient
        try:
            self.client = TelegramClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect with timeout
            try:
                await asyncio.wait_for(self.client.connect(), timeout=10)
                if self.logger:
                    self.logger.info(f"Connected to Telegram for {self.phone}")
            except asyncio.TimeoutError:
                if self.logger:
                    self.logger.error(f"Connection timeout for {self.phone}")
                self.login_error.emit("Connection to Telegram timed out. Please try again.")
                return
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Connection error for {self.phone}: {str(e)}")
                self.login_error.emit(f"Connection error: {str(e)}")
                return
                
            if not await self.client.is_user_authorized():
                if self.logger:
                    self.logger.info(f"User not authorized, proceeding to login flow for {self.phone}")
        
                try:
                    if self.password:
                        # 2FA flow
                        if self.logger:
                            self.logger.info(f"Attempting 2FA login for {self.phone}")
                        await asyncio.wait_for(self.client.sign_in(password=self.password), timeout=15)
                        
                        # Ensure client is fully started after 2FA authentication
                        if not self.client.is_connected():
                            await self.client.connect()
                        
                        # Verify the client is fully authorized
                        if not await self.client.is_user_authorized():
                            if self.logger:
                                self.logger.error(f"2FA login failed for {self.phone} - still not authorized after password")
                            self.login_error.emit("2FA authentication failed. Please try again.")
                            return
                        
                        # Get user info after successful 2FA login
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"2FA login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    else:
                        # Code flow
                        if self.logger:
                            self.logger.info(f"Attempting code login for {self.phone} with code {self.code}")
                        await asyncio.wait_for(
                            self.client.sign_in(phone=self.phone, code=self.code, phone_code_hash=self.phone_code_hash),
                            timeout=15
                        )
                        
                        # Get user info
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"Login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    
                except asyncio.TimeoutError:
                    if self.logger:
                        self.logger.error(f"Login timeout for {self.phone}")
                    self.login_error.emit("Login verification timed out. Please try again.")
                except SessionPasswordNeededError:
                    if self.logger:
                        self.logger.info(f"2FA required for {self.phone}")
                    self.login_2fa_required.emit()
                except PhoneCodeInvalidError:
                    if self.logger:
                        self.logger.error(f"Invalid code for {self.phone}")
                    self.login_error.emit("Invalid or expired code.")
                except Exception as e:
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        if self.logger:
                            self.logger.info(f"2FA detected from login error for {self.phone}")
                        self.login_2fa_required.emit()
                    elif any(keyword in error_msg for keyword in ["invalid", "code", "expired", "wrong"]):
                        if self.logger:
                            self.logger.error(f"Invalid verification code for {self.phone}")
                        self.login_error.emit("Invalid or expired verification code.")
                    else:
                        if self.logger:
                            self.logger.error(f"Login error for {self.phone}: {str(e)}")
                        self.login_error.emit(str(e))
            else:
                # Already authorized
                me = await self.client.get_me()
                if self.logger:
                    self.logger.info(f"Already authorized for {self.phone}")
                self.login_success.emit({'user': me})
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Connection error for {self.phone}: {str(e)}")
            self.login_error.emit(str(e))
        finally:
            # Proper cleanup
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass

class TGCheckerApp(QMainWindow):
    """Main application window for the TG Checker tool."""
    
    # Qt signals for thread-safe UI updates
    update_ui_signal = pyqtSignal()
    update_status_signal = pyqtSignal(str)
    update_analyzing_signal = pyqtSignal(str)
    update_result_counts_signal = pyqtSignal(int, int, int, int, int, int, int)  # valid_filtered, valid_only, topics, channels, invalid, account_issues, join_requests
    update_progress_signal = pyqtSignal(int, int)  # current, total
    log_activity_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # Initialize settings
        self.settings = QSettings("TG PY", "TGChecker")
        
        # Set up logging
        self.logger = setup_logger()
        
        # Initialize components
        self.account_manager = AccountManager(self.logger)
        
        # Database lock for thread-safe access
        self.db_lock = threading.RLock()
        
        # Clean database to fix locking issues
        self.logger.info("Cleaning database to fix potential locking issues...")
        self.account_manager.clean_database()
        
        # Initialize monitor
        self.monitor = Monitor(self.account_manager, self.logger)
        
        # Initialize checker state tracking
        self.is_checker_running = False
        self.is_task_checker_running = False
        self.checker_should_stop = False
        self.task_checker_should_stop = False
        
        # Progress tracking for crash recovery
        self.current_group_index = 0
        self.total_groups = 0
        self.progress_file = "last_checked.txt"
        self.current_group_links = []
        
        # Enhanced FloodWait handling
        self.account_states = {}  # Track account availability states
        self.pending_groups_queue = []  # Queue for groups that need reassignment
        self.retry_scheduled_groups = []  # Groups scheduled for retry after wait
        
        # Thread-safe result collections for multi-account task checker
        self.valid_filtered = set()
        self.valid_only = set()
        self.topics_groups = set()
        self.channels_only = set()
        self.invalid_groups = set()
        self.account_issues = set()
        self.join_requests = set()
        self.results_lock = threading.RLock()
        self.account_states_lock = threading.RLock()
        
        # Global progress tracking for multi-account checker
        self.global_groups_processed = 0
        self.global_progress_lock = threading.RLock()
        
        # Connect signals to their handlers
        self.update_ui_signal.connect(self.update_ui)
        self.update_status_signal.connect(self._update_status_label)
        self.update_analyzing_signal.connect(self._update_analyzing_label)
        self.update_result_counts_signal.connect(self._update_result_counts)
        self.update_progress_signal.connect(self._update_progress)
        self.log_activity_signal.connect(self._log_activity_to_ui)
        
        # Set up the UI
        self.setup_ui()
        
        # Load and apply settings (including theme)
        self.load_settings()
        
        # Start background processes
        self.start_background_processes()
    
    def _update_status_label(self, text):
        """Thread-safe status label update."""
        self.status_label.setText(text)
    
    def _update_analyzing_label(self, text):
        """Thread-safe analyzing label update."""
        self.analyzing_label.setText(text)
    
    def _update_result_counts(self, valid_filtered, valid_only, topics, channels, invalid, account_issues, join_requests=0):
        """Update the result count labels in the UI."""
        try:
            total = valid_filtered + valid_only + topics + channels + invalid + account_issues + join_requests
            
            # Update the tab texts with HTML formatting
            self.results_tab_widget.setTabText(0, f"Valid Filtered<br>{valid_filtered}")
            self.results_tab_widget.setTabText(1, f"Valid Only<br>{valid_only}")
            self.results_tab_widget.setTabText(2, f"Topics<br>{topics}")
            self.results_tab_widget.setTabText(3, f"Channels<br>{channels}")
            self.results_tab_widget.setTabText(4, f"Invalid<br>{invalid}")
            self.results_tab_widget.setTabText(5, f"Account Issues<br>{account_issues}")
            self.results_tab_widget.setTabText(6, f"Join Requests<br>{join_requests}")
            self.results_tab_widget.setTabText(7, f"Total<br>{total}")
            
            # Log the counts
            self.log_activity(f"Results - Valid Filtered: {valid_filtered}, Valid Only: {valid_only}, Topics: {topics}, Channels: {channels}, Invalid: {invalid}, Account Issues: {account_issues}, Join Requests: {join_requests}, Total: {total}")
        except Exception as e:
            self.log_activity(f"Error updating result counts: {str(e)}")
    
    def _update_progress(self, current, total):
        """Update progress bar and progress counter."""
        try:
            # Update progress bar
            if total > 0:
                self.progress_bar.setMaximum(total)
                self.progress_bar.setValue(current)
                percentage = (current / total) * 100
                self.logger.info(f"[PROGRESS] Groups Checked: {current} / {total} ({percentage:.1f}%)")
            else:
                self.progress_bar.setMaximum(1)
                self.progress_bar.setValue(0)
        except Exception as e:
            self.logger.error(f"Error updating progress: {str(e)}")
    
    def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_display.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            self.logger.info(message)
            
        except Exception as e:
            print(f"Failed to log activity: {str(e)}")
    
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("TG Checker")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create tabs
        self.dashboard_tab = self.create_dashboard_tab()
        self.forwarder_tab = self.create_forwarder_tab()
        self.accounts_tab = self.create_accounts_tab()
        self.monitor_tab = self.create_monitor_tab()
        self.logs_tab = self.create_logs_tab()
        self.settings_tab = self.create_settings_tab()
        
        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Checker")
        self.tabs.addTab(self.forwarder_tab, "Forwarder")
        self.tabs.addTab(self.accounts_tab, "Accounts")
        self.tabs.addTab(self.monitor_tab, "Monitor")
        self.tabs.addTab(self.logs_tab, "Logs")
        self.tabs.addTab(self.settings_tab, "Settings")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_label = QLabel("Ready")
        self.status_bar = self.statusBar()
        self.status_bar.addPermanentWidget(self.status_label)
        
        # Set the main widget
        self.setCentralWidget(main_widget)
        
        # Initialize forwarder database
        self.initialize_forwarder_db()
        
    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout(dashboard_tab)
        
        # Create the group input section
        input_group = QGroupBox("Group/Channel Links")
        input_layout = QVBoxLayout()
        
        # Input text area
        self.group_input = QTextEdit()
        self.group_input.setPlaceholderText("Enter Telegram group/channel links (one per line)...")
        self.group_input.setAcceptRichText(False)
        input_layout.addWidget(self.group_input)
        
        # Add import/export buttons
        import_export_layout = QHBoxLayout()
        self.import_button = QPushButton("Import Links")
        self.export_button = QPushButton("Export Links")
        self.import_remaining_button = QPushButton("Import Remaining")
        import_export_layout.addWidget(self.import_button)
        import_export_layout.addWidget(self.export_button)
        import_export_layout.addWidget(self.import_remaining_button)
        input_layout.addLayout(import_export_layout)
        
        # Set the layout
        input_group.setLayout(input_layout)
        dashboard_layout.addWidget(input_group)
        
        # Create the checking controls section
        control_group = QGroupBox("Checking Controls")
        control_layout = QVBoxLayout()
        
        # Add buttons
        button_layout = QHBoxLayout()
        # Important: Make sure these buttons have the correct names and connect to the right functions
        self.start_checker_button = QPushButton("Start Checking")
        self.stop_checker_button = QPushButton("Stop Checking")
        self.clear_results_button = QPushButton("Clear Results")
        button_layout.addWidget(self.start_checker_button)
        button_layout.addWidget(self.stop_checker_button)
        button_layout.addWidget(self.clear_results_button)
        control_layout.addLayout(button_layout)
        
        # Set the layout
        control_group.setLayout(control_layout)
        dashboard_layout.addWidget(control_group)
        
        # Create the status and progress section
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready")
        status_layout.addWidget(self.status_label)
        
        # Currently checking label
        self.analyzing_label = QLabel("Idle")
        status_layout.addWidget(self.analyzing_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)
        
        # Set the layout
        status_group.setLayout(status_layout)
        dashboard_layout.addWidget(status_group)
        
        # Results section
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        # Create a tab widget for different result categories
        self.results_tab_widget = QTabWidget()
        
        # Valid Filtered tab
        valid_filtered_tab = QWidget()
        valid_filtered_layout = QVBoxLayout(valid_filtered_tab)
        self.results_tab_widget.addTab(valid_filtered_tab, "Valid Filtered")
        
        # Valid Only tab
        valid_only_tab = QWidget()
        valid_only_layout = QVBoxLayout(valid_only_tab)
        self.results_tab_widget.addTab(valid_only_tab, "Valid Only")
        
        # Topics Groups tab
        topics_tab = QWidget()
        topics_layout = QVBoxLayout(topics_tab)
        self.results_tab_widget.addTab(topics_tab, "Topics")
        
        # Channels tab
        channels_tab = QWidget()
        channels_layout = QVBoxLayout(channels_tab)
        self.results_tab_widget.addTab(channels_tab, "Channels")
        
        # Invalid tab
        invalid_tab = QWidget()
        invalid_layout = QVBoxLayout(invalid_tab)
        self.results_tab_widget.addTab(invalid_tab, "Invalid")
        
        # Account Issues tab
        account_issues_tab = QWidget()
        account_issues_layout = QVBoxLayout(account_issues_tab)
        
        # Add retry button
        retry_account_issues_btn = QPushButton("Retry Checking with Available Accounts")
        retry_account_issues_btn.clicked.connect(self._retry_account_issues)
        account_issues_layout.addWidget(retry_account_issues_btn)
        
        # Add view button
        view_account_issues_btn = QPushButton("View Account Issues & Pending Groups")
        view_account_issues_btn.clicked.connect(self.view_pending_groups)
        account_issues_layout.addWidget(view_account_issues_btn)
        
        self.results_tab_widget.addTab(account_issues_tab, "Account Issues")
        
        # Join Requests tab
        join_requests_tab = QWidget()
        join_requests_layout = QVBoxLayout(join_requests_tab)
        self.results_tab_widget.addTab(join_requests_tab, "Join Requests")
        
        # Total tab
        total_tab = QWidget()
        total_layout = QVBoxLayout(total_tab)
        self.results_tab_widget.addTab(total_tab, "Total")
        
        # Setup tab bar with proper spacing and styling
        self.setup_tab_bar()
        
        # Set initial counts
        self._update_result_counts(0, 0, 0, 0, 0, 0, 0)
        
        results_layout.addWidget(self.results_tab_widget)
        dashboard_layout.addWidget(results_group)
        
        # Activity Log section
        log_group = QGroupBox("Activity Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        log_layout.addWidget(self.log_display)
        
        dashboard_layout.addWidget(log_group)
        
        # Connect signals
        self.start_checker_button.clicked.connect(self.start_task_checker)  # Connect to start_task_checker
        self.stop_checker_button.clicked.connect(self.stop_task_checker)    # Connect to stop_task_checker
        self.clear_results_button.clicked.connect(self.clear_results)
        self.import_button.clicked.connect(self.import_links)
        self.export_button.clicked.connect(self.export_links)
        self.import_remaining_button.clicked.connect(self.import_remaining_links)
        
        # Disable stop button initially
        self.stop_checker_button.setEnabled(False)
        
        # Set tab
        self.tabs.addTab(dashboard_tab, "Dashboard")
        
        return dashboard_tab
        
    def setup_tab_bar(self):
        """Configure the tab bar for proper size and appearance."""
        tab_bar = self.results_tab_widget.tabBar()
        
        # Set fixed height for tabs to accommodate two lines of text
        tab_bar.setFixedHeight(50)
        
        # Remove right-side tab buttons if any
        for i in range(self.results_tab_widget.count()):
            tab_bar.setTabButton(i, QTabBar.RightSide, None)
            
        # Apply custom style sheet for tab formatting
        self.results_tab_widget.setStyleSheet("""
            QTabBar::tab {
                padding: 5px;
                min-width: 100px;
                text-align: center;
            }
        """)
    
    def create_accounts_tab(self):
        """Create the accounts tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add account table
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(7)
        self.accounts_table.setHorizontalHeaderLabels(["Phone", "Name / Username", "Enabled", "Age", "Errors", "Status", "Actions"])
        
        # Set specific column widths
        self.accounts_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions column
        self.accounts_table.setColumnWidth(6, 320)  # Set Actions column width to fit all buttons
        
        # Set other columns to stretch
        for i in range(6):
            self.accounts_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Stretch)
        
        # Connect cellClicked signal to handle action buttons
        self.accounts_table.cellClicked.connect(self.handle_account_action_click)
        
        layout.addWidget(self.accounts_table)
        
        # Add account actions
        actions_layout = QHBoxLayout()
        
        self.add_account_button = QPushButton("Add Account")
        self.remove_account_button = QPushButton("Remove Account")
        self.fix_account_button = QPushButton("Fix Selected")
        self.refresh_info_button = QPushButton("Refresh Account Info")
        self.reset_all_statuses_button = QPushButton("Reset All Statuses")
        self.check_ages_button = QPushButton("Check Ages")
        self.activate_all_button = QPushButton("Activate All")
        self.deactivate_all_button = QPushButton("Deactivate All")
        
        self.add_account_button.clicked.connect(self.add_account)
        self.remove_account_button.clicked.connect(self.remove_account)
        self.fix_account_button.clicked.connect(self.fix_account)
        self.refresh_info_button.clicked.connect(self.refresh_all_accounts_info)
        self.reset_all_statuses_button.clicked.connect(lambda: self.fix_limited_status_for_account())
        self.activate_all_button.clicked.connect(self.activate_all_accounts)
        self.deactivate_all_button.clicked.connect(self.deactivate_all_accounts)
        
        actions_layout.addWidget(self.add_account_button)
        actions_layout.addWidget(self.remove_account_button)
        actions_layout.addWidget(self.fix_account_button)
        actions_layout.addWidget(self.refresh_info_button)
        actions_layout.addWidget(self.reset_all_statuses_button)
        actions_layout.addWidget(self.activate_all_button)
        actions_layout.addWidget(self.deactivate_all_button)
        layout.addLayout(actions_layout)
        
        return tab
    
    def create_monitor_tab(self):
        """Create the monitor tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add monitor settings
        settings_group = QGroupBox("Monitor Settings")
        settings_layout = QFormLayout()
        
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 60)
        self.check_interval_spin.setValue(5)
        self.check_interval_spin.setSuffix(" minutes")
        
        self.auto_fix_check = QCheckBox()
        self.auto_fix_check.setChecked(True)
        
        settings_layout.addRow("Check Interval:", self.check_interval_spin)
        settings_layout.addRow("Auto-Fix Issues:", self.auto_fix_check)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Add monitor status
        status_group = QGroupBox("Monitor Status")
        status_layout = QVBoxLayout()
        
        self.monitor_log = QTextEdit()
        self.monitor_log.setReadOnly(True)
        
        status_layout.addWidget(self.monitor_log)
        
        # Add apply settings button
        apply_button = QPushButton("Apply Monitor Settings")
        apply_button.clicked.connect(self.apply_monitor_settings)
        status_layout.addWidget(apply_button)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        return tab
    
    def create_logs_tab(self):
        """Create the logs tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add log filter controls
        filter_layout = QHBoxLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["All", "Info", "Warning", "Error", "Critical"])
        
        self.log_type_combo = QComboBox()
        self.log_type_combo.addItems(["General", "Authentication", "Usage Checker"])
        self.log_type_combo.currentIndexChanged.connect(self.update_log_display)
        
        self.export_logs_button = QPushButton("Export Logs")
        self.export_logs_button.clicked.connect(self.export_logs)
        
        filter_layout.addWidget(QLabel("Log Type:"))
        filter_layout.addWidget(self.log_type_combo)
        filter_layout.addWidget(QLabel("Filter Level:"))
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addStretch()
        filter_layout.addWidget(self.export_logs_button)
        
        layout.addLayout(filter_layout)
        
        # Add log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # Set up a timer to refresh logs
        self.log_refresh_timer = QTimer(self)
        self.log_refresh_timer.timeout.connect(self.update_log_display)
        self.log_refresh_timer.start(2000)  # Refresh every 2 seconds
        
        return tab
    
import os
import sys
import time
import json
import logging
import threading
import asyncio
from datetime import datetime, timedelta
import random
import uuid
import re
from collections import deque
import sqlite3

from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer, QSettings, QMetaObject, Q_ARG, QObject, pyqtSlot, QPoint
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QTabWidget, QLabel, QPushButton,
    QVBoxLayout, QHBoxLayout, QFormLayout, QGroupBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QDialog, QLineEdit, QMessageBox, QHeaderView, QSplitter,
    QCheckBox, QSpinBox, QDoubleSpinBox, QComboBox, QFileDialog, QRadioButton,
    QButtonGroup, QScrollArea, QProgressBar, QMenu, QGridLayout, QTabBar
)
from PyQt5.QtGui import QIcon, QFont, QCursor, QColor

# Suppress specific PyQt5 deprecation warnings
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*sipPyTypeDict.*")

from telethon.errors import (
    PhoneCodeInvalidError, SessionPasswordNeededError, FloodWaitError, 
    ChatAdminRequiredError, UserBannedInChannelError, ChannelPrivateError
)

from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

# Import tg_client for TelegramClient
from tg_client import TelegramClient

def format_time_remaining(seconds):
    """Format seconds into a human-readable time (e.g., 13h 49m)."""
    if seconds <= 0:
        return "0m"
    
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    
    if hours > 0:
        return f"{hours}h {minutes}m"
    elif minutes > 0:
        return f"{minutes}m"
    else:
        return f"{int(seconds)}s"

class TelegramLoginWorker(QThread):
    login_success = pyqtSignal(dict)
    login_2fa_required = pyqtSignal()
    login_error = pyqtSignal(str)

    def __init__(self, api_id, api_hash, phone, code=None, password=None, phone_code_hash=None, session_file=None, logger=None):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.code = code
        self.password = password
        self.phone_code_hash = phone_code_hash
        self.session_file = session_file or f'sessions/{phone}'
        self.logger = logger
        self.client = None

    def run(self):
        import asyncio
        asyncio.run(self._login_flow())

    async def _login_flow(self):
        from telethon import TelegramClient
        try:
            self.client = TelegramClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect with timeout
            try:
                await asyncio.wait_for(self.client.connect(), timeout=10)
                if self.logger:
                    self.logger.info(f"Connected to Telegram for {self.phone}")
            except asyncio.TimeoutError:
                if self.logger:
                    self.logger.error(f"Connection timeout for {self.phone}")
                self.login_error.emit("Connection to Telegram timed out. Please try again.")
                return
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Connection error for {self.phone}: {str(e)}")
                self.login_error.emit(f"Connection error: {str(e)}")
                return
                
            if not await self.client.is_user_authorized():
                if self.logger:
                    self.logger.info(f"User not authorized, proceeding to login flow for {self.phone}")
        
                try:
                    if self.password:
                        # 2FA flow
                        if self.logger:
                            self.logger.info(f"Attempting 2FA login for {self.phone}")
                        await asyncio.wait_for(self.client.sign_in(password=self.password), timeout=15)
                        
                        # Ensure client is fully started after 2FA authentication
                        if not self.client.is_connected():
                            await self.client.connect()
                        
                        # Verify the client is fully authorized
                        if not await self.client.is_user_authorized():
                            if self.logger:
                                self.logger.error(f"2FA login failed for {self.phone} - still not authorized after password")
                            self.login_error.emit("2FA authentication failed. Please try again.")
                            return
                        
                        # Get user info after successful 2FA login
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"2FA login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    else:
                        # Code flow
                        if self.logger:
                            self.logger.info(f"Attempting code login for {self.phone} with code {self.code}")
                        await asyncio.wait_for(
                            self.client.sign_in(phone=self.phone, code=self.code, phone_code_hash=self.phone_code_hash),
                            timeout=15
                        )
                        
                        # Get user info
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"Login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    
                except asyncio.TimeoutError:
                    if self.logger:
                        self.logger.error(f"Login timeout for {self.phone}")
                    self.login_error.emit("Login verification timed out. Please try again.")
                except SessionPasswordNeededError:
                    if self.logger:
                        self.logger.info(f"2FA required for {self.phone}")
                    self.login_2fa_required.emit()
                except PhoneCodeInvalidError:
                    if self.logger:
                        self.logger.error(f"Invalid code for {self.phone}")
                    self.login_error.emit("Invalid or expired code.")
                except Exception as e:
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        if self.logger:
                            self.logger.info(f"2FA detected from login error for {self.phone}")
                        self.login_2fa_required.emit()
                    elif any(keyword in error_msg for keyword in ["invalid", "code", "expired", "wrong"]):
                        if self.logger:
                            self.logger.error(f"Invalid verification code for {self.phone}")
                        self.login_error.emit("Invalid or expired verification code.")
                    else:
                        if self.logger:
                            self.logger.error(f"Login error for {self.phone}: {str(e)}")
                        self.login_error.emit(str(e))
            else:
                # Already authorized
                me = await self.client.get_me()
                if self.logger:
                    self.logger.info(f"Already authorized for {self.phone}")
                self.login_success.emit({'user': me})
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Connection error for {self.phone}: {str(e)}")
            self.login_error.emit(str(e))
        finally:
            # Proper cleanup
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass

class TGCheckerApp(QMainWindow):
    """Main application window for the TG Checker tool."""
    
    # Qt signals for thread-safe UI updates
    update_ui_signal = pyqtSignal()
    update_status_signal = pyqtSignal(str)
    update_analyzing_signal = pyqtSignal(str)
    update_result_counts_signal = pyqtSignal(int, int, int, int, int, int, int)  # valid_filtered, valid_only, topics, channels, invalid, account_issues, join_requests
    update_progress_signal = pyqtSignal(int, int)  # current, total
    log_activity_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # Initialize settings
        self.settings = QSettings("TG PY", "TGChecker")
        
        # Set up logging
        self.logger = setup_logger()
        
        # Initialize components
        self.account_manager = AccountManager(self.logger)
        
        # Database lock for thread-safe access
        self.db_lock = threading.RLock()
        
        # Clean database to fix locking issues
        self.logger.info("Cleaning database to fix potential locking issues...")
        self.account_manager.clean_database()
        
        # Initialize monitor
        self.monitor = Monitor(self.account_manager, self.logger)
        
        # Initialize checker state tracking
        self.is_checker_running = False
        self.is_task_checker_running = False
        self.checker_should_stop = False
        self.task_checker_should_stop = False
        
        # Progress tracking for crash recovery
        self.current_group_index = 0
        self.total_groups = 0
        self.progress_file = "last_checked.txt"
        self.current_group_links = []
        
        # Enhanced FloodWait handling
        self.account_states = {}  # Track account availability states
        self.pending_groups_queue = []  # Queue for groups that need reassignment
        self.retry_scheduled_groups = []  # Groups scheduled for retry after wait
        
        # Thread-safe result collections for multi-account task checker
        self.valid_filtered = set()
        self.valid_only = set()
        self.topics_groups = set()
        self.channels_only = set()
        self.invalid_groups = set()
        self.account_issues = set()
        self.join_requests = set()
        self.results_lock = threading.RLock()
        self.account_states_lock = threading.RLock()
        
        # Global progress tracking for multi-account checker
        self.global_groups_processed = 0
        self.global_progress_lock = threading.RLock()
        
        # Connect signals to their handlers
        self.update_ui_signal.connect(self.update_ui)
        self.update_status_signal.connect(self._update_status_label)
        self.update_analyzing_signal.connect(self._update_analyzing_label)
        self.update_result_counts_signal.connect(self._update_result_counts)
        self.update_progress_signal.connect(self._update_progress)
        self.log_activity_signal.connect(self._log_activity_to_ui)
        
        # Set up the UI
        self.setup_ui()
        
        # Load and apply settings (including theme)
        self.load_settings()
        
        # Start background processes
        self.start_background_processes()
    
    def _update_status_label(self, text):
        """Thread-safe status label update."""
        self.status_label.setText(text)
    
    def _update_analyzing_label(self, text):
        """Thread-safe analyzing label update."""
        self.analyzing_label.setText(text)
    
    def _update_result_counts(self, valid_filtered, valid_only, topics, channels, invalid, account_issues, join_requests=0):
        """Update the result count labels in the UI."""
        try:
            total = valid_filtered + valid_only + topics + channels + invalid + account_issues + join_requests
            
            # Update the tab texts with HTML formatting
            self.results_tab_widget.setTabText(0, f"Valid Filtered<br>{valid_filtered}")
            self.results_tab_widget.setTabText(1, f"Valid Only<br>{valid_only}")
            self.results_tab_widget.setTabText(2, f"Topics<br>{topics}")
            self.results_tab_widget.setTabText(3, f"Channels<br>{channels}")
            self.results_tab_widget.setTabText(4, f"Invalid<br>{invalid}")
            self.results_tab_widget.setTabText(5, f"Account Issues<br>{account_issues}")
            self.results_tab_widget.setTabText(6, f"Join Requests<br>{join_requests}")
            self.results_tab_widget.setTabText(7, f"Total<br>{total}")
            
            # Log the counts
            self.log_activity(f"Results - Valid Filtered: {valid_filtered}, Valid Only: {valid_only}, Topics: {topics}, Channels: {channels}, Invalid: {invalid}, Account Issues: {account_issues}, Join Requests: {join_requests}, Total: {total}")
        except Exception as e:
            self.log_activity(f"Error updating result counts: {str(e)}")
    
    def _update_progress(self, current, total):
        """Update progress bar and progress counter."""
        try:
            # Update progress bar
            if total > 0:
                self.progress_bar.setMaximum(total)
                self.progress_bar.setValue(current)
                percentage = (current / total) * 100
                self.logger.info(f"[PROGRESS] Groups Checked: {current} / {total} ({percentage:.1f}%)")
            else:
                self.progress_bar.setMaximum(1)
                self.progress_bar.setValue(0)
        except Exception as e:
            self.logger.error(f"Error updating progress: {str(e)}")
    
    def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.log_display.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            self.logger.info(message)
            
        except Exception as e:
            print(f"Failed to log activity: {str(e)}")
    
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("TG Checker")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create tabs
        self.dashboard_tab = self.create_dashboard_tab()
        self.forwarder_tab = self.create_forwarder_tab()
        self.accounts_tab = self.create_accounts_tab()
        self.monitor_tab = self.create_monitor_tab()
        self.logs_tab = self.create_logs_tab()
        self.settings_tab = self.create_settings_tab()
        
        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Checker")
        self.tabs.addTab(self.forwarder_tab, "Forwarder")
        self.tabs.addTab(self.accounts_tab, "Accounts")
        self.tabs.addTab(self.monitor_tab, "Monitor")
        self.tabs.addTab(self.logs_tab, "Logs")
        self.tabs.addTab(self.settings_tab, "Settings")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_label = QLabel("Ready")
        self.status_bar = self.statusBar()
        self.status_bar.addPermanentWidget(self.status_label)
        
        # Set the main widget
        self.setCentralWidget(main_widget)
        
        # Initialize forwarder database
        self.initialize_forwarder_db()
        
    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        dashboard_tab = QWidget()
        dashboard_layout = QVBoxLayout(dashboard_tab)
        
        # Create the group input section
        input_group = QGroupBox("Group/Channel Links")
        input_layout = QVBoxLayout()
        
        # Input text area
        self.group_input = QTextEdit()
        self.group_input.setPlaceholderText("Enter Telegram group/channel links (one per line)...")
        self.group_input.setAcceptRichText(False)
        input_layout.addWidget(self.group_input)
        
        # Add import/export buttons
        import_export_layout = QHBoxLayout()
        self.import_button = QPushButton("Import Links")
        self.export_button = QPushButton("Export Links")
        self.import_remaining_button = QPushButton("Import Remaining")
        import_export_layout.addWidget(self.import_button)
        import_export_layout.addWidget(self.export_button)
        import_export_layout.addWidget(self.import_remaining_button)
        input_layout.addLayout(import_export_layout)
        
        # Set the layout
        input_group.setLayout(input_layout)
        dashboard_layout.addWidget(input_group)
        
        # Create the checking controls section
        control_group = QGroupBox("Checking Controls")
        control_layout = QVBoxLayout()
        
        # Add buttons
        button_layout = QHBoxLayout()
        # Important: Make sure these buttons have the correct names and connect to the right functions
        self.start_checker_button = QPushButton("Start Checking")
        self.stop_checker_button = QPushButton("Stop Checking")
        self.clear_results_button = QPushButton("Clear Results")
        button_layout.addWidget(self.start_checker_button)
        button_layout.addWidget(self.stop_checker_button)
        button_layout.addWidget(self.clear_results_button)
        control_layout.addLayout(button_layout)
        
        # Set the layout
        control_group.setLayout(control_layout)
        dashboard_layout.addWidget(control_group)
        
        # Create the status and progress section
        status_group = QGroupBox("Status")
        status_layout = QVBoxLayout()
        
        # Status label
        self.status_label = QLabel("Ready")
        status_layout.addWidget(self.status_label)
        
        # Currently checking label
        self.analyzing_label = QLabel("Idle")
        status_layout.addWidget(self.analyzing_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        status_layout.addWidget(self.progress_bar)
        
        # Set the layout
        status_group.setLayout(status_layout)
        dashboard_layout.addWidget(status_group)
        
        # Results section
        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        
        # Create a tab widget for different result categories
        self.results_tab_widget = QTabWidget()
        
        # Valid Filtered tab
        valid_filtered_tab = QWidget()
        valid_filtered_layout = QVBoxLayout(valid_filtered_tab)
        self.results_tab_widget.addTab(valid_filtered_tab, "Valid Filtered")
        
        # Valid Only tab
        valid_only_tab = QWidget()
        valid_only_layout = QVBoxLayout(valid_only_tab)
        self.results_tab_widget.addTab(valid_only_tab, "Valid Only")
        
        # Topics Groups tab
        topics_tab = QWidget()
        topics_layout = QVBoxLayout(topics_tab)
        self.results_tab_widget.addTab(topics_tab, "Topics")
        
        # Channels tab
        channels_tab = QWidget()
        channels_layout = QVBoxLayout(channels_tab)
        self.results_tab_widget.addTab(channels_tab, "Channels")
        
        # Invalid tab
        invalid_tab = QWidget()
        invalid_layout = QVBoxLayout(invalid_tab)
        self.results_tab_widget.addTab(invalid_tab, "Invalid")
        
        # Account Issues tab
        account_issues_tab = QWidget()
        account_issues_layout = QVBoxLayout(account_issues_tab)
        
        # Add retry button
        retry_account_issues_btn = QPushButton("Retry Checking with Available Accounts")
        retry_account_issues_btn.clicked.connect(self._retry_account_issues)
        account_issues_layout.addWidget(retry_account_issues_btn)
        
        # Add view button
        view_account_issues_btn = QPushButton("View Account Issues & Pending Groups")
        view_account_issues_btn.clicked.connect(self.view_pending_groups)
        account_issues_layout.addWidget(view_account_issues_btn)
        
        self.results_tab_widget.addTab(account_issues_tab, "Account Issues")
        
        # Join Requests tab
        join_requests_tab = QWidget()
        join_requests_layout = QVBoxLayout(join_requests_tab)
        self.results_tab_widget.addTab(join_requests_tab, "Join Requests")
        
        # Total tab
        total_tab = QWidget()
        total_layout = QVBoxLayout(total_tab)
        self.results_tab_widget.addTab(total_tab, "Total")
        
        # Setup tab bar with proper spacing and styling
        self.setup_tab_bar()
        
        # Set initial counts
        self._update_result_counts(0, 0, 0, 0, 0, 0, 0)
        
        results_layout.addWidget(self.results_tab_widget)
        dashboard_layout.addWidget(results_group)
        
        # Activity Log section
        log_group = QGroupBox("Activity Log")
        log_layout = QVBoxLayout(log_group)
        
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        log_layout.addWidget(self.log_display)
        
        dashboard_layout.addWidget(log_group)
        
        # Connect signals
        self.start_checker_button.clicked.connect(self.start_task_checker)  # Connect to start_task_checker
        self.stop_checker_button.clicked.connect(self.stop_task_checker)    # Connect to stop_task_checker
        self.clear_results_button.clicked.connect(self.clear_results)
        self.import_button.clicked.connect(self.import_links)
        self.export_button.clicked.connect(self.export_links)
        self.import_remaining_button.clicked.connect(self.import_remaining_links)
        
        # Disable stop button initially
        self.stop_checker_button.setEnabled(False)
        
        # Set tab
        self.tabs.addTab(dashboard_tab, "Dashboard")
        
        return dashboard_tab
        
    def setup_tab_bar(self):
        """Configure the tab bar for proper size and appearance."""
        tab_bar = self.results_tab_widget.tabBar()
        
        # Set fixed height for tabs to accommodate two lines of text
        tab_bar.setFixedHeight(50)
        
        # Remove right-side tab buttons if any
        for i in range(self.results_tab_widget.count()):
            tab_bar.setTabButton(i, QTabBar.RightSide, None)
            
        # Apply custom style sheet for tab formatting
        self.results_tab_widget.setStyleSheet("""
            QTabBar::tab {
                padding: 5px;
                min-width: 100px;
                text-align: center;
            }
        """)
    
    def create_accounts_tab(self):
        """Create the accounts tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add account table
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(7)
        self.accounts_table.setHorizontalHeaderLabels(["Phone", "Name / Username", "Enabled", "Age", "Errors", "Status", "Actions"])
        
        # Set specific column widths
        self.accounts_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions column
        self.accounts_table.setColumnWidth(6, 320)  # Set Actions column width to fit all buttons
        
        # Set other columns to stretch
        for i in range(6):
            self.accounts_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Stretch)
        
        # Connect cellClicked signal to handle action buttons
        self.accounts_table.cellClicked.connect(self.handle_account_action_click)
        
        layout.addWidget(self.accounts_table)
        
        # Add account actions
        actions_layout = QHBoxLayout()
        
        self.add_account_button = QPushButton("Add Account")
        self.remove_account_button = QPushButton("Remove Account")
        self.fix_account_button = QPushButton("Fix Selected")
        self.refresh_info_button = QPushButton("Refresh Account Info")
        self.reset_all_statuses_button = QPushButton("Reset All Statuses")
        self.check_ages_button = QPushButton("Check Ages")
        self.activate_all_button = QPushButton("Activate All")
        self.deactivate_all_button = QPushButton("Deactivate All")
        
        self.add_account_button.clicked.connect(self.add_account)
        self.remove_account_button.clicked.connect(self.remove_account)
        self.fix_account_button.clicked.connect(self.fix_account)
        self.refresh_info_button.clicked.connect(self.refresh_all_accounts_info)
        self.reset_all_statuses_button.clicked.connect(lambda: self.fix_limited_status_for_account())
        self.activate_all_button.clicked.connect(self.activate_all_accounts)
        self.deactivate_all_button.clicked.connect(self.deactivate_all_accounts)
        
        actions_layout.addWidget(self.add_account_button)
        actions_layout.addWidget(self.remove_account_button)
        actions_layout.addWidget(self.fix_account_button)
        actions_layout.addWidget(self.refresh_info_button)
        actions_layout.addWidget(self.reset_all_statuses_button)
        actions_layout.addWidget(self.activate_all_button)
        actions_layout.addWidget(self.deactivate_all_button)
        layout.addLayout(actions_layout)
        
        return tab
    
    def create_monitor_tab(self):
        """Create the monitor tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add monitor settings
        settings_group = QGroupBox("Monitor Settings")
        settings_layout = QFormLayout()
        
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 60)
        self.check_interval_spin.setValue(5)
        self.check_interval_spin.setSuffix(" minutes")
        
        self.auto_fix_check = QCheckBox()
        self.auto_fix_check.setChecked(True)
        
        settings_layout.addRow("Check Interval:", self.check_interval_spin)
        settings_layout.addRow("Auto-Fix Issues:", self.auto_fix_check)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Add monitor status
        status_group = QGroupBox("Monitor Status")
        status_layout = QVBoxLayout()
        
        self.monitor_log = QTextEdit()
        self.monitor_log.setReadOnly(True)
        
        status_layout.addWidget(self.monitor_log)
        
        # Add apply settings button
        apply_button = QPushButton("Apply Monitor Settings")
        apply_button.clicked.connect(self.apply_monitor_settings)
        status_layout.addWidget(apply_button)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        return tab
    
    def create_logs_tab(self):
        """Create the logs tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add log filter controls
        filter_layout = QHBoxLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["All", "Info", "Warning", "Error", "Critical"])
        
        self.log_type_combo = QComboBox()
        self.log_type_combo.addItems(["General", "Authentication", "Usage Checker"])
        self.log_type_combo.currentIndexChanged.connect(self.update_log_display)
        
        self.export_logs_button = QPushButton("Export Logs")
        self.export_logs_button.clicked.connect(self.export_logs)
        
        filter_layout.addWidget(QLabel("Log Type:"))
        filter_layout.addWidget(self.log_type_combo)
        filter_layout.addWidget(QLabel("Filter Level:"))
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addStretch()
        filter_layout.addWidget(self.export_logs_button)
        
        layout.addLayout(filter_layout)
        
        # Add log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # Set up a timer to refresh logs
        self.log_refresh_timer = QTimer(self)
        self.log_refresh_timer.timeout.connect(self.update_log_display)
        self.log_refresh_timer.start(2000)  # Refresh every 2 seconds
        
        return tab
    
    def create_settings_tab(self):
        """Create the settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # General settings
        general_group = QGroupBox("General Settings")
        general_layout = QFormLayout()
        
        self.auto_start_check = QCheckBox()
        self.dark_mode_check = QCheckBox()
        self.dark_mode_check.stateChanged.connect(self.on_theme_changed)
        
        general_layout.addRow("Auto-Start Monitor:", self.auto_start_check)
        general_layout.addRow("Dark Mode:", self.dark_mode_check)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(0)  # Allow 0 to disable delays
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 0, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(0)  # Allow 0 to disable delays
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 0, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Optional delay between each group check (set both to 0 to disable delays completely)")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
        
        # Group Check Sleep settings
        sleep_group = QGroupBox("Group Check Sleep Settings")
        sleep_layout = QFormLayout()
        
        # After X groups
        self.groups_before_sleep_spin = QSpinBox()
        self.groups_before_sleep_spin.setRange(1, 1000)
        self.groups_before_sleep_spin.setValue(self.settings.value("groups_before_sleep", 50, type=int))
        self.groups_before_sleep_spin.setSuffix(" groups")
        
        # Sleep for Y minutes
        self.sleep_minutes_spin = QSpinBox()
        self.sleep_minutes_spin.setRange(1, 120)
        self.sleep_minutes_spin.setValue(self.settings.value("sleep_minutes", 5, type=int))
        self.sleep_minutes_spin.setSuffix(" minutes")
        
        # Enable sleep feature
        self.enable_sleep_check = QCheckBox()
        self.enable_sleep_check.setChecked(self.settings.value("enable_sleep", False, type=bool))
        
        # Add to layout
        sleep_layout.addRow("Enable Sleep Feature:", self.enable_sleep_check)
        sleep_layout.addRow("After Checking:", self.groups_before_sleep_spin)
        sleep_layout.addRow("Sleep For:", self.sleep_minutes_spin)
        
        # Add help text
        sleep_help_label = QLabel("Automatically pauses checking after X groups, then resumes after Y minutes")
        sleep_help_label.setStyleSheet("color: gray; font-style: italic;")
        sleep_layout.addRow("", sleep_help_label)
        
        sleep_group.setLayout(sleep_layout)
        layout.addWidget(sleep_group)
        
        # Filter settings
        filter_group = QGroupBox("Filter Settings")
        filter_layout = QFormLayout()
        
        self.min_members_spin = QSpinBox()
        self.min_members_spin.setRange(0, 1000000)
        self.min_members_spin.setValue(500)
        self.min_members_spin.setSingleStep(100)
        
        self.min_message_time_spin = QSpinBox()
        self.min_message_time_spin.setRange(0, 168)  # Up to 1 week (168 hours)
        self.min_message_time_spin.setValue(1)
        self.min_message_time_spin.setSuffix(" hours")
        
        self.min_total_messages_spin = QSpinBox()
        self.min_total_messages_spin.setRange(0, 100000)
        self.min_total_messages_spin.setValue(100)
        self.min_total_messages_spin.setSingleStep(10)
        
        filter_layout.addRow("Min Members:", self.min_members_spin)
        filter_layout.addRow("Min Chat Message Time:", self.min_message_time_spin)
        filter_layout.addRow("Min Total Messages:", self.min_total_messages_spin)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # Save settings button
        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_settings_button)
        
        return tab
    
    
    def update_speed_check_range(self):
        """Ensure min seconds <= max seconds."""
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
        
    def start_background_processes(self):
        """Start all background monitoring and maintenance processes."""
        # Wait for UI to be fully initialized
        time.sleep(1)
        
        # Initialize account states for all accounts
        accounts = self.account_manager.get_accounts()
        with self.account_states_lock:
            for account in accounts:
                phone = account.get("phone", "")
                if phone and phone not in self.account_states:
                    self.account_states[phone] = {
                        "status": "available",
                        "errors": 0
                    }
        
        # Auto-refresh account info for accounts missing name/username
        QTimer.singleShot(3000, self.auto_refresh_missing_account_info)  # Wait 3 seconds after startup
        
        # Sync and validate session accounts
        QTimer.singleShot(5000, self.validate_session_accounts)  # Wait 5 seconds after startup
        
        # Start FloodWait status updater - update every 10 seconds
        QTimer.singleShot(1000, self.update_floodwait_status)
        
        # Update UI
        self.update_ui_timer = QTimer(self)
        self.update_ui_timer.timeout.connect(self.update_ui)
        self.update_ui_timer.start(1000)  # Update every second
    
    def update_ui(self):
        """Update UI with the latest account information."""
        # Update accounts table
        self.update_accounts_table(self.account_manager.get_all_accounts())
        
        # Update pending groups count in Account Issues tab
        total_pending = 0
        with self.account_states_lock:
            if hasattr(self, 'pending_groups_queue'):
                total_pending += len(self.pending_groups_queue)
            if hasattr(self, 'account_specific_queues'):
                for queue in self.account_specific_queues.values():
                    total_pending += len(queue)
        
        # Update Account Issues tab title to show pending count
        if hasattr(self, 'results_tab_widget'):
            account_issues_tab = None
            for i in range(self.results_tab_widget.count()):
                if "Account Issues" in self.results_tab_widget.tabText(i):
                    account_issues_tab = i
                    break
            
            if account_issues_tab is not None:
                with self.results_lock:
                    account_issues_count = len(self.account_issues) if hasattr(self, 'account_issues') else 0
                self.results_tab_widget.setTabText(account_issues_tab, f"Account Issues ({account_issues_count} + {total_pending} pending)")
        
        # Update FloodWait status in status bar
        floodwait_accounts = []
        with self.account_states_lock:
            for phone, state in self.account_states.items():
                if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                    until_time = state["flood_wait_until"]
                    if isinstance(until_time, datetime):
                        time_diff = (until_time - datetime.now()).total_seconds()
                        if time_diff > 0:
                            readable_time = format_time_remaining(time_diff)
                            floodwait_accounts.append(f"{phone}: {readable_time}")
        
        # Update status bar with FloodWait accounts
        if floodwait_accounts:
            floodwait_text = " | ".join(floodwait_accounts)
            self.statusBar().showMessage(f"⏳ FloodWait Active: {floodwait_text}")
        else:
            self.statusBar().showMessage("Ready")

    def _update_result_counts(self, valid_filtered, valid_only, topics, channels, invalid, account_issues, join_requests=0):
        """Update the result count labels in the UI."""
        try:
            total = valid_filtered + valid_only + topics + channels + invalid + account_issues + join_requests
            
            # Update the tab texts with HTML formatting
            self.results_tab_widget.setTabText(0, f"Valid Filtered<br>{valid_filtered}")
            self.results_tab_widget.setTabText(1, f"Valid Only<br>{valid_only}")
            self.results_tab_widget.setTabText(2, f"Topics<br>{topics}")
            self.results_tab_widget.setTabText(3, f"Channels<br>{channels}")
            self.results_tab_widget.setTabText(4, f"Invalid<br>{invalid}")
            self.results_tab_widget.setTabText(5, f"Account Issues<br>{account_issues}")
            self.results_tab_widget.setTabText(6, f"Join Requests<br>{join_requests}")
            self.results_tab_widget.setTabText(7, f"Total<br>{total}")
            
            # Log the counts
            self.log_activity(f"Results - Valid Filtered: {valid_filtered}, Valid Only: {valid_only}, Topics: {topics}, Channels: {channels}, Invalid: {invalid}, Account Issues: {account_issues}, Join Requests: {join_requests}, Total: {total}")
        except Exception as e:
            self.log_activity(f"Error updating result counts: {str(e)}")
    
    def validate_session_accounts(self):
        """Validate session accounts in a separate thread."""
        self.log_activity("Starting account validation process...")
        accounts = []
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                cursor = conn.cursor()
                cursor.execute("SELECT phone, api_id, api_hash, session_file FROM accounts")
                accounts = cursor.fetchall()
                conn.close()
        except Exception as e:
            self.log_activity(f"Error getting accounts from database: {str(e)}")
            return

        if not accounts:
            self.log_activity("No accounts found in database.")
            return

        # Launch validation in a thread
        validation_thread = threading.Thread(target=self._validate_session_accounts_thread, args=(accounts,))
        validation_thread.daemon = True
        validation_thread.start()

    def _validate_session_accounts_thread(self, session_accounts):
        """Thread worker for validating session accounts."""
        try:
            validated_count = 0
            frozen_count = 0
            
            for account in session_accounts:
                phone = account.get("phone", "")
                if not phone:
                    continue
                
                try:
                    # Check if session file exists
                    clean_phone = phone.replace('+', '')
                    session_path = f"sessions/{clean_phone}.session"
                    
                    if not os.path.exists(session_path):
                        self.log_activity_signal.emit(f"⚠️ Session file missing for {phone}")
                        self.account_manager.update_account_status(phone, "session_missing")
                        continue
                    
                    # Test actual session status
                    session_status = self._test_session_status(phone, session_path)
                    
                    if session_status == "frozen":
                        self.log_activity_signal.emit(f"🔒 Account {phone} is frozen")
                        self.account_manager.update_account_status(phone, "frozen")
                        frozen_count += 1
                    elif session_status == "active":
                        self.log_activity_signal.emit(f"✅ Session account {phone} is active")
                        self.account_manager.update_account_status(phone, "active")
                        validated_count += 1
                    elif session_status == "auth_invalid":
                        self.log_activity_signal.emit(f"❌ Session {phone} authorization expired")
                        self.account_manager.update_account_status(phone, "auth_expired")
                    else:
                        self.log_activity_signal.emit(f"⚠️ Could not validate session {phone}")
                        self.account_manager.update_account_status(phone, "validation_failed")
                    
                    time.sleep(1)  # Small delay between validations
                    
                except Exception as e:
                    self.logger.error(f"Error validating session account {phone}: {str(e)}")
                    self.account_manager.update_account_status(phone, "validation_error")
            
            # Update UI and log completion
            self.update_ui_signal.emit()
            result_msg = f"✅ Session validation completed: {validated_count} active, {frozen_count} frozen"
            self.log_activity_signal.emit(result_msg)
            
        except Exception as e:
            self.logger.error(f"Error in session validation thread: {str(e)}")
            self.log_activity_signal.emit(f"❌ Session validation error: {str(e)}")
    
    def _test_session_status(self, phone, session_path):
        """Test session status to detect if account is frozen, active, or invalid."""
        try:
            from telethon import TelegramClient as TelethonClient
            from telethon.errors import AuthKeyInvalidError, UserDeactivatedError, UserDeactivatedBanError
            from telethon.tl.functions.account import GetPrivacyRequest
            import asyncio
            
            # Get API credentials
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                return "error"
            
            api_id = account.get("api_id", "611335")
            api_hash = account.get("api_hash", "session_import_placeholder")
            
            # Create event loop for this thread
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            async def test_session():
                client = None
                try:
                    client = TelethonClient(session_path, api_id, api_hash)
                    await asyncio.wait_for(client.connect(), timeout=10.0)
                    
                    if not await client.is_user_authorized():
                        return "auth_invalid"
                    
                    # Try to get user info to detect frozen status
                    me = await asyncio.wait_for(client.get_me(), timeout=10.0)
                    if me:
                        # Multiple checks to detect frozen status
                        try:
                            # Test 1: Try to get dialogs
                            dialogs = await asyncio.wait_for(client.get_dialogs(limit=1), timeout=8.0)
                            
                            # Test 2: Try to get chat info (frozen accounts often fail here)
                            try:
                                await asyncio.wait_for(client.get_entity("me"), timeout=5.0)
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "suspended", "limited", "deactivated"]):
                                    return "frozen"
                            
                            # Test 3: Try to access account settings/info (more sensitive check)
                            try:
                                # Try to get privacy settings - frozen accounts typically can't access this
                                await asyncio.wait_for(client(GetPrivacyRequest(key="phone_number")), timeout=5.0)
                            except ImportError:
                                # GetPrivacyRequest not available, skip this test
                                pass
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "suspended", "account"]):
                                    return "frozen"
                            
                            # Test 4: Try a simple message-related operation
                            try:
                                # Try to get message history from saved messages
                                await asyncio.wait_for(client.get_messages("me", limit=1), timeout=5.0)
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "account", "suspended"]):
                                    return "frozen"
                                
                            return "active"
                            
                        except Exception as e:
                            error_msg = str(e).lower()
                            # Comprehensive frozen detection with more specific patterns
                            frozen_patterns = [
                                "frozen", "restricted", "suspended", "limited", 
                                "deactivated", "account", "banned", "disabled",
                                "your account", "not allowed", "access denied",
                                "account is", "account has been", "temporarily"
                            ]
                            
                            if any(pattern in error_msg for pattern in frozen_patterns):
                                return "frozen"
                            elif "flood" in error_msg or "wait" in error_msg:
                                return "active"  # Flood wait doesn't mean frozen
                            else:
                                # If we can get user info but fail other operations, likely frozen
                                return "frozen"
                    else:
                        return "auth_invalid"
                        
                except UserDeactivatedError:
                    return "frozen"
                except UserDeactivatedBanError:
                    return "frozen"
                except AuthKeyInvalidError:
                    return "auth_invalid"
                except asyncio.TimeoutError:
                    return "timeout"
                except Exception as e:
                    error_msg = str(e).lower()
                    # Enhanced frozen detection with more comprehensive patterns
                    frozen_patterns = [
                        "frozen", "restricted", "suspended", "deactivated", "banned", 
                        "disabled", "limited", "account", "your account", "not allowed",
                        "access denied", "terminated", "violation", "account is",
                        "account has been", "temporarily", "permanently"
                    ]
                    
                    if any(pattern in error_msg for pattern in frozen_patterns):
                        return "frozen"
                    elif "unauthorized" in error_msg or "auth" in error_msg:
                        return "auth_invalid"
                    else:
                        return "error"
                finally:
                    if client and client.is_connected():
                        try:
                            await asyncio.wait_for(client.disconnect(), timeout=3.0)
                        except:
                            pass
            
            # Run the test
            try:
                result = loop.run_until_complete(test_session())
                return result
            finally:
                # Clean up event loop
                try:
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                except:
                    pass
                finally:
                    loop.close()
                    
        except ImportError:
            return "error"
        except Exception as e:
            error_msg = str(e).lower()
            if "frozen" in error_msg or "restricted" in error_msg or "suspended" in error_msg:
                return "frozen"
            return "error"
    
    def auto_refresh_missing_account_info(self):
        """Automatically refresh account info for accounts missing name/username."""
        try:
            accounts = self.account_manager.get_accounts()
            accounts_needing_refresh = []
            
            for account in accounts:
                if account is None:
                    continue
                    
                name = account.get("name", "").strip() if account.get("name") else ""
                username = account.get("username", "").strip() if account.get("username") else ""
                phone = account.get("phone", "")
                account_type = account.get("account_type", "normal")
                
                # Check if account info is missing or incomplete
                needs_refresh = False
                
                # Session accounts especially need info refresh if missing data
                if account_type == "session":
                    if not name and not username:
                        needs_refresh = True
                        self.log_activity(f"Session account {phone} missing name/username - will refresh")
                    elif name == "N/A" or username == "N/A":
                        needs_refresh = True
                        self.log_activity(f"Session account {phone} has N/A values - will refresh")
                else:
                    # Regular accounts
                    if not name and not username:
                        needs_refresh = True
                
                if needs_refresh and phone:
                    accounts_needing_refresh.append(phone)
            
            if accounts_needing_refresh:
                self.log_activity(f"Found {len(accounts_needing_refresh)} accounts missing info. Auto-refreshing...")
                # Start background refresh for these accounts
                threading.Thread(target=self._auto_refresh_accounts_thread, args=(accounts_needing_refresh,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh: {str(e)}")
    
    def _auto_refresh_accounts_thread(self, phone_list):
        """Background thread for auto-refreshing missing account info."""
        try:
            updated_count = 0
            
            for phone in phone_list:
                try:
                    if self.refresh_account_info(phone):
                        updated_count += 1
                    time.sleep(1)  # Small delay between requests
                except Exception as e:
                    self.logger.error(f"Error refreshing {phone}: {str(e)}")
                    continue
            
            if updated_count > 0:
                # Update UI on main thread using signal
                self.update_ui_signal.emit()
                self.log_activity_signal.emit(f"Auto-refresh completed: {updated_count} accounts updated")
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh thread: {str(e)}")
    
    def sync_accounts(self):
        """Synchronize all accounts to check for availability."""
        self.update_status_signal.emit("Syncing accounts...")
        self.log_activity_signal.emit("Syncing accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_accounts_thread, daemon=True).start()
    
    def _sync_accounts_thread(self):
        """Thread worker for syncing accounts."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Check for accounts needing age verification
            accounts_needing_check = self.account_manager.get_accounts_needing_age_check()
            
            if accounts_needing_check:
                log_usage_checker(self.logger, f"Found {len(accounts_needing_check)} accounts needing age check during sync")
                self.update_status_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                self.log_activity_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                
                # Run age check in a separate thread to avoid blocking
                age_check_thread = threading.Thread(
                    target=self._age_check_background_thread, 
                    args=(accounts_needing_check,), 
                    daemon=True
                )
                age_check_thread.start()
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Account sync completed")
            self.log_activity_signal.emit("Account sync completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync error: {str(e)}")
            self.logger.error(f"Account sync error: {str(e)}")
            self.log_activity_signal.emit(f"Account sync failed: {str(e)}")
    
    def _age_check_background_thread(self, accounts_needing_check):
        """Thread worker for checking account age in background."""
        try:
            import asyncio
            
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(self.account_manager.auto_check_account_ages())
                
                success_count = sum(1 for _, success, _ in results if success)
                log_usage_checker(self.logger, f"Age check completed: {success_count}/{len(results)} successful")
                self.log_activity_signal.emit(f"Age check completed: {success_count}/{len(results)} successful")
                
                # Update UI after age check completes
                self.update_ui_signal.emit()
                
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"Error in age check background thread: {str(e)}"
            self.logger.error(error_msg)
            log_usage_checker(self.logger, error_msg, logging.ERROR)
            self.log_activity_signal.emit(f"Age check failed: {str(e)}")
    
    def toggle_monitor(self):
        """Toggle the monitor on/off."""
        if self.monitor.is_running:
            self.stop_monitor()
        else:
            self.start_monitor()
    
    def start_monitor(self):
        """Start the monitor."""
        try:
            interval = self.check_interval_spin.value() * 60  # Convert to seconds
            self.monitor.start(interval)
            
            self.toggle_monitor_button.setText("Stop Monitor")
            self.monitor_status_label.setText("Running")
            self.log_activity("Monitor started")
            self.status_label.setText("Monitor is running")
            
        except Exception as e:
            self.logger.error(f"Failed to start monitor: {str(e)}")
            self.log_activity(f"Failed to start monitor: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start monitor: {str(e)}")
    
    def stop_monitor(self):
        """Stop the monitor."""
        try:
            self.monitor.stop()
            
            self.toggle_monitor_button.setText("Start Monitor")
            self.monitor_status_label.setText("Stopped")
            self.log_activity("Monitor stopped")
            self.status_label.setText("Monitor is stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop monitor: {str(e)}")
            self.log_activity(f"Failed to stop monitor: {str(e)}")
    
    def sync_and_monitor(self):
        """Sync and monitor accounts."""
        self.update_status_signal.emit("Syncing and monitoring accounts...")
        self.log_activity_signal.emit("Syncing and monitoring accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_and_monitor_thread, daemon=True).start()
    
    def _sync_and_monitor_thread(self):
        """Background thread for syncing and monitoring accounts."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Start monitor (this needs to be called from main thread)
            QMetaObject.invokeMethod(self, "start_monitor", Qt.QueuedConnection)
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Sync and monitor completed")
            self.log_activity_signal.emit("Sync and monitor completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync and monitor error: {str(e)}")
            self.logger.error(f"Sync and monitor error: {str(e)}")
            self.log_activity_signal.emit(f"Sync and monitor failed: {str(e)}")
    
    def add_account(self):
        """Add a new account with a simplified, reliable approach."""
        # First, show a dialog to choose between normal login and session login
        login_choice_dialog = QDialog(self)
        login_choice_dialog.setWindowTitle("Choose Login Method")
        login_choice_dialog.setMinimumWidth(300)
        login_choice_layout = QVBoxLayout(login_choice_dialog)
        
        login_choice_label = QLabel("Choose Login Method:")
        login_choice_layout.addWidget(login_choice_label)
        
        button_layout = QHBoxLayout()
        normal_login_button = QPushButton("Normal Login")
        session_login_button = QPushButton("Session Login")
        
        button_layout.addWidget(normal_login_button)
        button_layout.addWidget(session_login_button)
        login_choice_layout.addLayout(button_layout)
        
        # Use a flag to track which method was chosen
        login_method_chosen = {"method": None}
        
        def choose_normal_login():
            login_method_chosen["method"] = "normal"
            login_choice_dialog.accept()
            
        def choose_session_login():
            login_method_chosen["method"] = "session"
            login_choice_dialog.accept()
            
        normal_login_button.clicked.connect(choose_normal_login)
        session_login_button.clicked.connect(choose_session_login)
        
        # Show the login method choice dialog
        login_choice_dialog.exec_()
        
        # If no method was chosen (dialog was closed), return False
        if login_method_chosen["method"] is None:
            return False
            
        # Handle Session Login
        if login_method_chosen["method"] == "session":
            return self.add_account_via_session()
        
        # Normal Login - create a custom dialog class with proper methods
        class AccountDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Add Telegram Account")
                self.setFixedWidth(500)
                self.setMinimumHeight(400)
                
                # Logger setup
                self.logger = logging.getLogger("tg_checker")
                
                # Store parent reference for database access
                self.parent_app = parent
                
                # Internal state
                self.client = None
                self.login_thread = None
                self.auth_logs = []
                self.phone_code_hash = None
                self.authentication_stage = "initial"  # Can be "initial", "code", "2fa", "complete"
                
                # Setup UI components
                self.setup_ui()
                
                # Setup signals
                self.setup_signals()
                
            def setup_ui(self):
                """Set up the account dialog UI."""
                self.setWindowTitle("Add Telegram Account")
                self.setModal(True)
                self.setFixedSize(500, 600)
                
                # Main layout
                layout = QVBoxLayout()
                self.setLayout(layout)
                
                # Header
                header = QLabel("Add Telegram Account")
                header.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 10px;")
                header.setAlignment(Qt.AlignCenter)
                layout.addWidget(header)
                
                # Form layout
                form_layout = QFormLayout()
                
                # Phone number input
                self.phone_input = QLineEdit()
                self.phone_input.setPlaceholderText("Enter your phone number (e.g., +**********)")
                form_layout.addRow("Phone Number:", self.phone_input)
                
                # API ID input
                self.api_id_input = QLineEdit()
                self.api_id_input.setPlaceholderText("Your Telegram API ID")
                form_layout.addRow("API ID:", self.api_id_input)
                
                # API Hash input
                self.api_hash_input = QLineEdit()
                self.api_hash_input.setPlaceholderText("Your Telegram API Hash")
                form_layout.addRow("API Hash:", self.api_hash_input)
                
                layout.addLayout(form_layout)
                
                # Authentication container (hidden initially)
                self.auth_container = QWidget()
                self.auth_layout = QFormLayout()
                self.auth_container.setLayout(self.auth_layout)
                self.auth_container.setVisible(False)
                layout.addWidget(self.auth_container)
                
                # Status label
                self.status_label = QLabel("Enter your Telegram account details")
                self.status_label.setStyleSheet("margin: 10px 0;")
                self.status_label.setWordWrap(True)
                layout.addWidget(self.status_label)
                
                # Authentication log display
                self.auth_log_display = QTextEdit()
                self.auth_log_display.setMaximumHeight(150)
                self.auth_log_display.setStyleSheet("font-family: monospace; font-size: 10px;")
                self.auth_log_display.setVisible(False)
                layout.addWidget(self.auth_log_display)
                
                # Buttons
                button_layout = QHBoxLayout()
                
                # Connect button
                self.connect_button = QPushButton("Connect & Request Code")
                self.connect_button.clicked.connect(self.on_connect_clicked)
                button_layout.addWidget(self.connect_button)
                
                # Continue button (hidden initially)  
                self.continue_button = QPushButton("Continue")
                self.continue_button.clicked.connect(self.on_continue_clicked)
                self.continue_button.setVisible(False)
                button_layout.addWidget(self.continue_button)
                
                # Cancel button
                self.cancel_button = QPushButton("Cancel")
                self.cancel_button.clicked.connect(self.reject)
                button_layout.addWidget(self.cancel_button)
                
                layout.addLayout(button_layout)
                
                # Initialize auth logs
                self.auth_logs = []
                
                # Initialize authentication stage
                self.authentication_stage = "initial"
                self.phone_code_hash = None
                
                # Create input fields that will be used later
                self.code_input = QLineEdit()
                self.code_input.setPlaceholderText("Enter 5-digit verification code")
                
                self.twofa_input = QLineEdit()
                self.twofa_input.setPlaceholderText("Enter your 2FA password")
                self.twofa_input.setEchoMode(QLineEdit.Password)
            
            def setup_signals(self):
                """Connect signals to slots."""
                # Text change signals
                self.code_input.textChanged.connect(self.on_code_text_changed)
                self.twofa_input.textChanged.connect(self.on_2fa_text_changed)
                
                # Button signals
                self.connect_button.clicked.connect(self.on_connect_clicked)
                self.continue_button.clicked.connect(self.on_continue_clicked)
                self.cancel_button.clicked.connect(self.reject)
                
                # Allow enter key to submit
                self.code_input.returnPressed.connect(self.on_continue_clicked)
                self.twofa_input.returnPressed.connect(self.on_continue_clicked)
            
            def update_verification_status(self, message):
                """Update the verification status label."""
                self.status_label.setText(message)
                
                # Add to auth logs
                self.auth_logs.append(f"{datetime.now().strftime('%H:%M:%S')} - {message}")
                
                # Keep only last 10 logs
                if len(self.auth_logs) > 10:
                    self.auth_logs = self.auth_logs[-10:]
                
                # Update display
                self.update_auth_log_display()
            
            def update_auth_log_display(self):
                """Update the authentication log display."""
                try:
                    # Get recent auth logs
                    auth_logs = []
                    if os.path.exists("auth.log"):
                        with open("auth.log", "r", encoding="utf-8") as f:
                            lines = f.readlines()
                            auth_logs = lines[-10:]  # Last 10 lines
                    
                    # Update the log text
                    if hasattr(self, 'auth_log_text'):
                        self.auth_log_text.clear()
                        for line in auth_logs:
                            self.auth_log_text.append(line.strip())
                        self.auth_log_text.moveCursor(self.auth_log_text.textCursor().End)
                    
                except Exception as e:
                    log_auth(self.logger, f"Error updating auth log display: {str(e)}", logging.WARNING)
            
            def on_reset_clicked(self):
                """Reset functionality has been removed - this method is deprecated."""
                pass
            
            def _cleanup_and_unlock_database(self):
                """Reset functionality has been removed - this method is deprecated."""
                pass
            
            def on_code_text_changed(self, text):
                """Enable continue button when code text is entered"""
                if self.authentication_stage == "code":
                    self.continue_button.setEnabled(len(text.strip()) >= 5)
            
            def on_2fa_text_changed(self, text):
                """Enable continue button when 2FA text is entered"""
                if self.authentication_stage == "2fa":
                    self.continue_button.setEnabled(len(text.strip()) > 0)
            
            @pyqtSlot()
            def show_code_input(self):
                """Show code input UI with fresh layout."""
                try:
                    # Clear the auth container safely
                    while self.auth_layout.count():
                        item = self.auth_layout.takeAt(0)
                        if item.widget():
                            item.widget().deleteLater()
                    
                    # Create fresh code input (don't reuse existing widget)
                    code_input = QLineEdit()
                    code_input.setPlaceholderText("Enter 5-digit verification code")
                    code_input.textChanged.connect(self.on_code_text_changed)
                    code_input.returnPressed.connect(self.on_continue_clicked)
                    
                    # Store reference to prevent deletion
                    self.code_input = code_input
                    
                    # Add to layout
                    self.auth_layout.addRow("Verification Code:", self.code_input)
                    
                    # Show container and focus
                    self.auth_container.setVisible(True)
                    self.code_input.setFocus()
                    
                    # Update button states
                    self.connect_button.setVisible(False)
                    self.continue_button.setVisible(True)
                    self.continue_button.setEnabled(False)
                    
                    # Update authentication stage
                    self.authentication_stage = "code"
                    
                    # Update status
                    self.status_label.setText("Verification code sent! Enter the 5-digit code from your phone.")
                    
                    log_auth(self.logger, "Verification code requested for account {}".format(self.phone_input.text().strip()))
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in show_code_input: {str(e)}", logging.ERROR)
            
            @pyqtSlot()
            def show_2fa_input(self):
                """Show 2FA input UI with fresh layout."""
                try:
                    # Clear the auth container safely
                    while self.auth_layout.count():
                        item = self.auth_layout.takeAt(0)
                        if item.widget():
                            item.widget().deleteLater()
                    
                    # Create fresh 2FA input (don't reuse existing widget)
                    twofa_input = QLineEdit()
                    twofa_input.setPlaceholderText("Enter your 2FA password")
                    twofa_input.setEchoMode(QLineEdit.Password)
                    twofa_input.textChanged.connect(self.on_2fa_text_changed)
                    twofa_input.returnPressed.connect(self.on_continue_clicked)
                    
                    # Store reference to prevent deletion
                    self.twofa_input = twofa_input
                    
                    # Add to layout
                    self.auth_layout.addRow("2FA Password:", self.twofa_input)
                    
                    # Show container and focus
                    self.auth_container.setVisible(True)
                    self.twofa_input.setFocus()
                    
                    # Update button states
                    self.connect_button.setVisible(False)
                    self.continue_button.setVisible(True)
                    self.continue_button.setEnabled(False)
                    
                    # Update authentication stage
                    self.authentication_stage = "2fa"
                    
                    # Update status
                    self.status_label.setText("Two-factor authentication required. Please enter your 2FA password.")
                    
                    log_auth(self.logger, "2FA password required for account {}".format(self.phone_input.text().strip()))
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in show_2fa_input: {str(e)}", logging.ERROR)
            
            def on_connect_clicked(self):
                # Prevent duplicate connections
                if hasattr(self, '_connecting') and self._connecting:
                    return
                
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                # Validate inputs
                if not phone or not api_id or not api_hash:
                    self.status_label.setText("Please fill all required fields")
                    return
                
                # Set connection guard
                self._connecting = True
                
                # Log the connection attempt
                log_auth(self.logger, f"Connect button clicked for account {phone}")
                
                # Disable inputs during connection
                self.phone_input.setEnabled(False)
                self.api_id_input.setEnabled(False)
                self.api_hash_input.setEnabled(False)
                self.connect_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Connecting to Telegram...")
                
                # Process in the background
                threading.Thread(target=self.connect_thread, daemon=True).start()
            
            def connect_thread(self):
                try:
                    # Get account details
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    
                    # Log the attempt
                    log_auth(self.logger, f"Connecting to Telegram for account {phone}")
                    
                    # Always create a fresh session path to avoid file locks and ensure fresh codes
                    fresh_session_suffix = uuid.uuid4().hex[:8]
                    phone_clean = phone.replace('+', '')
                    
                    # Store the session file path for consistent use in all verification steps
                    self.session_file = f"sessions/{phone_clean}_fresh_{fresh_session_suffix}"
                    log_auth(self.logger, f"Using fresh session path: {self.session_file}")
                    
                    # Automatic database cleanup to prevent locks
                    try:
                        if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                            self.parent_app.account_manager.clean_database()
                            log_auth(self.logger, "Database cleaned automatically before connection")
                    except Exception as db_error:
                        log_auth(self.logger, f"Database cleanup warning: {str(db_error)}", logging.WARNING)
                    
                    # Create client with the fresh session file
                    self.client = TelegramClient(api_id, api_hash, phone, session_file=self.session_file)
                    
                    # Request confirmation code (this step includes the connection)
                    log_auth(self.logger, f"Sending code request for account {phone}")
                    result = self.client.send_code_request()
                    
                    # Check result
                    if result.get("success", False):
                        # Store the phone code hash
                        self.phone_code_hash = result.get("phone_code_hash")
                        
                        # Log success
                        log_auth(self.logger, f"Code request successful for account {phone}")
                        
                        # Update UI to show code input
                        QMetaObject.invokeMethod(self, "show_code_input", Qt.QueuedConnection)
                    elif result.get("requires_2fa", False):
                        # 2FA detected during code request
                        log_auth(self.logger, f"2FA detected immediately for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                    elif result.get("already_authorized", False):
                        # Account is already authorized - get proper user info
                        log_auth(self.logger, f"Account {phone} is already authorized")
                        
                        # Get actual user info instead of using dummy data
                        # We need to properly verify this account by connecting and getting real user data
                        try:
                            # Get user info
                            user_info = self.client.get_account_info()
                            
                            if user_info and "success" in user_info and user_info["success"]:
                                # We have actual user info
                                log_auth(self.logger, f"Got user info for already authorized account {phone}")
                                
                                # Format user data to match our expected structure
                                formatted_user = {
                                    "user": {
                                        "first_name": user_info.get("first_name", ""),
                                        "last_name": user_info.get("last_name", ""),
                                        "username": user_info.get("username", "")
                                    }
                                }
                                
                                # Pass to login success handler
                                QMetaObject.invokeMethod(
                                    self, "on_login_success", 
                                    Qt.QueuedConnection,
                                    Q_ARG(dict, formatted_user)
                                )
                            else:
                                # Session is authorized but we couldn't get user info
                                # This suggests possible 2FA requirement or invalid session
                                log_auth(self.logger, f"Session authorized but couldn't get user info, checking 2FA for {phone}")
                                
                                # Check if 2FA is required
                                twofa_check = self.client.check_2fa_required()
                                if twofa_check and twofa_check.get("requires_2fa", False):
                                    # 2FA is required
                                    log_auth(self.logger, f"2FA required for already authorized account {phone}")
                                    QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                                else:
                                    # Some other issue with the session
                                    log_auth(self.logger, f"Session authorized but user info not available for {phone}")
                                    
                                    # Try to proceed with limited info
                                    user_info = {"user": {"first_name": "Unknown", "last_name": "", "username": ""}}
                                    QMetaObject.invokeMethod(
                                        self, "on_login_success", 
                                        Qt.QueuedConnection,
                                        Q_ARG(dict, user_info)
                                    )
                        except Exception as e:
                            # Error getting user info for authorized account
                            log_auth(self.logger, f"Error getting user info for authorized account {phone}: {str(e)}", logging.ERROR)
                            
                            # Show error to user
                            QMetaObject.invokeMethod(
                                self.status_label, 
                                "setText", 
                                Qt.QueuedConnection,
                                Q_ARG(str, f"❌ Error verifying authorized account: {str(e)}")
                            )
                            
                            # Re-enable inputs
                            self._enable_inputs()
                    else:
                        # Handle error - automatically retry with database cleanup
                        error_msg = result.get("error", "Unknown error")
                        log_auth(self.logger, f"Code request failed for account {phone}: {error_msg}", logging.ERROR)
                        
                        # Auto-retry once with database cleanup for locked database
                        if "database is locked" in error_msg.lower():
                            log_auth(self.logger, "Database lock detected - auto-retrying with cleanup")
                            try:
                                if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                                    self.parent_app.account_manager.clean_database()
                                    time.sleep(1)  # Brief pause
                                    
                                    # Create new fresh session and retry
                                    retry_suffix = uuid.uuid4().hex[:8]
                                    retry_session = f"sessions/{phone_clean}_retry_{retry_suffix}"
                                    self.client = TelegramClient(api_id, api_hash, phone, session_file=retry_session)
                                    retry_result = self.client.send_code_request()
                                    
                                    if retry_result.get("success", False):
                                        self.phone_code_hash = retry_result.get("phone_code_hash")
                                        log_auth(self.logger, f"Retry successful for account {phone}")
                                        QMetaObject.invokeMethod(self, "show_code_input", Qt.QueuedConnection)
                                        return  # Success on retry
                                    else:
                                        error_msg = f"Retry failed: {retry_result.get('error', 'Unknown error')}"
                            except Exception as retry_error:
                                error_msg = f"Auto-retry failed: {str(retry_error)}"
                        
                        # Show error to user
                        QMetaObject.invokeMethod(
                            self.status_label, 
                            "setText", 
                            Qt.QueuedConnection,
                            Q_ARG(str, f"❌ Error: {error_msg}")
                        )
                        
                        # Re-enable inputs for manual retry
                        self._enable_inputs()
                        
                except Exception as e:
                    # Log the error
                    phone = self.phone_input.text().strip()
                    log_auth(self.logger, f"Connection error for account {phone}: {str(e)}", logging.ERROR)
                    
                    # Auto-retry for database locks
                    error_msg = str(e).lower()
                    if "database is locked" in error_msg:
                        log_auth(self.logger, "Database lock in exception - auto-retrying")
                        try:
                            if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                                self.parent_app.account_manager.clean_database()
                                time.sleep(1)
                                
                                # Create new session and retry once
                                phone_clean = phone.replace('+', '')
                                retry_suffix = uuid.uuid4().hex[:8]  
                                retry_session = f"sessions/{phone_clean}_retry_{retry_suffix}"
                                api_id = self.api_id_input.text().strip()
                                api_hash = self.api_hash_input.text().strip()
                                
                                self.client = TelegramClient(api_id, api_hash, phone, session_file=retry_session)
                                retry_result = self.client.send_code_request()
                                
                                if retry_result.get("success", False):
                                    self.phone_code_hash = retry_result.get("phone_code_hash")
                                    log_auth(self.logger, f"Exception retry successful for account {phone}")
                                    QMetaObject.invokeMethod(self, "show_code_input", Qt.QueuedConnection)
                                    return
                        except Exception as retry_error:
                            log_auth(self.logger, f"Exception retry failed: {str(retry_error)}", logging.ERROR)
                    
                    # Handle 2FA detection from exceptions
                    elif any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        log_auth(self.logger, f"2FA detected from exception for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                        return  # Don't show error for 2FA detection
                    
                    # Show error message
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"❌ Error: {str(e)}")
                    )
                    
                    # Re-enable inputs
                    self._enable_inputs()
                finally:
                    # Clear connection guard
                    self._connecting = False

            def _enable_inputs(self):
                """Re-enable input fields after an error."""
                QMetaObject.invokeMethod(self.phone_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_id_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_hash_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.connect_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_continue_clicked(self):
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                if self.authentication_stage == "code":
                    # Get code input
                    code = self.code_input.text().strip()
                    
                    if not code:
                        self.status_label.setText("Please enter the verification code")
                        return
                    
                    # Log code submission
                    log_auth(self.logger, f"Verification code submitted for account {phone}")
                    
                    # Disable inputs during verification
                    self.code_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying code...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_code_thread, args=(code,), daemon=True).start()
                    
                elif self.authentication_stage == "2fa":
                    # Get 2FA password
                    password = self.twofa_input.text().strip()
                    
                    if not password:
                        self.status_label.setText("Please enter your 2FA password")
                        return
                    
                    # Log 2FA password submission
                    log_auth(self.logger, f"2FA password submitted for account {phone}")
                    
                    # Disable inputs
                    self.twofa_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying 2FA password...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_2fa_thread, args=(password,), daemon=True).start()
            
            def verify_code_thread(self, code):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    
                    # Use consistent session file path from connect_thread
                    if not hasattr(self, 'session_file') or not self.session_file:
                        # Fallback if session_file wasn't set
                        log_auth(self.logger, f"No session file path found, creating new path for {phone}")
                        self.session_file = f"sessions/{phone.replace('+', '')}"
                    
                    log_auth(self.logger, f"Using session path for verification: {self.session_file}")
                    
                    # Create and start login worker
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, code, 
                        session_file=self.session_file, 
                        phone_code_hash=self.phone_code_hash, 
                        logger=self.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_2fa_required.connect(self.on_2fa_required)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in verify_code_thread: {str(e)}", logging.ERROR)
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"Verification error: {str(e)}")
                    )
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.code_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def verify_2fa_thread(self, password):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    
                    # Use consistent session file path from connect_thread
                    if not hasattr(self, 'session_file') or not self.session_file:
                        # Fallback if session_file wasn't set
                        log_auth(self.logger, f"No session file path found, creating new path for {phone}")
                        self.session_file = f"sessions/{phone.replace('+', '')}"
                    
                    log_auth(self.logger, f"Using session path for 2FA verification: {self.session_file}")
                    
                    # Ensure sessions directory exists
                    os.makedirs("sessions", exist_ok=True)
                    
                    # Log the attempt with better detail
                    log_auth(self.logger, f"Verifying 2FA password for account {phone}")
                    
                    # Create and start login worker for 2FA
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, None, 
                        password=password,
                        phone_code_hash=self.phone_code_hash, 
                        session_file=self.session_file, 
                        logger=self.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_2fa_required.connect(self.on_2fa_required)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in verify_2fa_thread: {str(e)}", logging.ERROR)
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"2FA verification error: {str(e)}")
                    )
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.twofa_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_login_success(self, user_info):
                """Handle successful login and add account to database."""
                try:
                    # Get account details
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    user = user_info.get('user')
                    
                    # Get parent window reference for UI updates
                    parent_app = self.parent()
                    
                    # Get the final session path we want to use
                    phone_clean = phone.replace('+', '')
                    final_session_path = f"sessions/{phone_clean}"
                    
                    # Log session paths for debugging
                    log_auth(self.logger, f"Login successful for {phone}")
                    log_auth(self.logger, f"Temporary session path: {self.session_file}")
                    log_auth(self.logger, f"Final session path will be: {final_session_path}")
                    
                    # Extract user information safely with better handling
                    name = ""
                    username = ""
                    if user:
                        # Handle both Telethon User object and dict
                        if hasattr(user, 'first_name'):  # Telethon User object
                            first_name = getattr(user, 'first_name', '') or ''
                            last_name = getattr(user, 'last_name', '') or ''
                            username = getattr(user, 'username', '') or ''
                        elif isinstance(user, dict):  # Dict format
                            first_name = user.get('first_name', '') or ''
                            last_name = user.get('last_name', '') or ''
                            username = user.get('username', '') or ''
                        else:
                            first_name = str(user.get('first_name', '')) if hasattr(user, 'get') else ''
                            last_name = str(user.get('last_name', '')) if hasattr(user, 'get') else ''
                            username = str(user.get('username', '')) if hasattr(user, 'get') else ''
                        
                        name = f"{first_name} {last_name}".strip()
                    
                    # Verify the session is actually saved and usable
                    if hasattr(self, 'session_file') and self.session_file:
                        session_file = f"{self.session_file}.session"
                        if not os.path.exists(session_file):
                            log_auth(self.logger, f"Warning: Session file not found at {session_file}", logging.WARNING)
                        else:
                            log_auth(self.logger, f"Session file exists: {session_file} ({os.path.getsize(session_file)} bytes)")
                    
                    # Enhanced database handling with automatic cleanup and retry
                    max_retries = 5  # Increased retries
                    success = False
                    for attempt in range(max_retries):
                        try:
                            # Use the final session path when adding to database
                            success = parent_app.account_manager.add_account(
                                phone, api_id, api_hash, session_file=final_session_path
                            )
                            if success:
                                break
                        except Exception as db_err:
                            error_str = str(db_err).lower()
                            if "database is locked" in error_str:
                                log_auth(self.logger, f"Database locked when adding account, attempt {attempt+1}/{max_retries}", logging.WARNING)
                                
                                # Force database cleanup
                                try:
                                    if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                                        self.parent_app.account_manager.clean_database()
                                        log_auth(self.logger, f"Database unlocked successfully on attempt {attempt+1}")
                                except Exception as cleanup_err:
                                    log_auth(self.logger, f"Database cleanup failed on attempt {attempt+1}: {str(cleanup_err)}", logging.ERROR)
                                
                                if attempt < max_retries - 1:
                                    # Wait progressively longer between retries
                                    wait_time = (attempt + 1) * 2
                                    log_auth(self.logger, f"Waiting {wait_time}s before retry {attempt+2}")
                                    time.sleep(wait_time)
                                    continue
                                else:
                                    # Final attempt failed
                                    log_auth(self.logger, f"All database unlock attempts failed", logging.ERROR)
                                    self.update_verification_status("❌ Database locked - please restart the application")
                                    return
                            else:
                                # Non-database error
                                log_auth(self.logger, f"Database error on attempt {attempt+1}: {error_str}", logging.ERROR)
                                if attempt < max_retries - 1:
                                    time.sleep(1)
                                    continue
                                else:
                                    raise
                    
                    if success:
                        # Copy the session file to the final location if needed
                        if hasattr(self, 'session_file') and self.session_file and self.session_file != final_session_path:
                            temp_session = f"{self.session_file}.session"
                            final_session = f"{final_session_path}.session"
                            
                            if os.path.exists(temp_session):
                                try:
                                    # Make sure directory exists
                                    os.makedirs(os.path.dirname(final_session), exist_ok=True)
                                    
                                    # First try to copy the file
                                    import shutil
                                    shutil.copy2(temp_session, final_session)
                                    log_auth(self.logger, f"Copied session from {temp_session} to {final_session}")
                                    
                                    # Verify the copy worked
                                    if os.path.exists(final_session):
                                        log_auth(self.logger, f"Session successfully saved to {final_session}")
                                    else:
                                        log_auth(self.logger, f"Warning: Final session file not created at {final_session}", logging.WARNING)
                                except Exception as copy_err:
                                    log_auth(self.logger, f"Error copying session file: {str(copy_err)}", logging.ERROR)
                        
                        # Update account info with user details
                        if name or username:
                            parent_app.account_manager.update_account_info(phone, name=name, username=username)
                            log_auth(self.logger, f"Successfully added account {phone} with info: {name} / @{username}")
                        else:
                            log_auth(self.logger, f"Successfully added account {phone}")
                        
                        # Start age check in background for new account
                        log_usage_checker(self.logger, f"Starting automatic age check for new account: {phone}")
                        if parent_app:
                            threading.Thread(target=parent_app._auto_check_new_account_age, args=(phone,), daemon=True).start()
                        
                        # Force an immediate UI refresh to show the new account
                        if parent_app:
                            parent_app.log_activity(f"Added account: {phone}")
                            parent_app.update_ui_signal.emit()  # Explicit UI refresh
                        
                        # Update status and close dialog
                        self.update_verification_status("✅ Login successful! Account added.")
                        QMetaObject.invokeMethod(self, "accept", Qt.QueuedConnection)
                    else:
                        log_auth(self.logger, f"Failed to add account {phone} to database after all attempts", logging.ERROR)
                        self.update_verification_status("❌ Failed to add account to database - please restart application")
                        
                except Exception as e:
                    log_auth(self.logger, f"Error in on_login_success: {str(e)}", logging.ERROR)
                    self.update_verification_status(f"❌ Error processing login: {str(e)} - please restart application")
            
            def on_2fa_required(self):
                QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
            
            def on_login_error(self, error_msg):
                """Handle login errors with automatic database lock detection and recovery."""
                try:
                    log_auth(self.logger, f"❌ LOGIN ERROR: {error_msg}", logging.ERROR)
                    
                    # Check if it's a database lock error - handle automatically
                    if "database is locked" in error_msg.lower():
                        log_auth(self.logger, "🔧 AUTO-RECOVERY: Database lock detected, attempting auto-cleanup")
                        
                        # Attempt automatic database cleanup
                        try:
                            if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                                self.parent_app.account_manager.clean_database()
                                log_auth(self.logger, "✅ AUTO-RECOVERY: Database cleaned successfully")
                                self.status_label.setText("⚠️ Database issue resolved. Please click Connect to try again.")
                            else:
                                self.status_label.setText("❌ Database locked. Please restart the application.")
                                
                        except Exception as cleanup_error:
                            log_auth(self.logger, f"❌ AUTO-RECOVERY: Database cleanup failed: {str(cleanup_error)}", logging.ERROR)
                            self.status_label.setText("❌ Database lock error. Please restart the application.")
                            
                    else:
                        # Handle other types of errors
                        if "code" in error_msg.lower() and ("invalid" in error_msg.lower() or "expired" in error_msg.lower()):
                            self.status_label.setText(f"❌ {error_msg} — Please check your code and try again.")
                        elif "2fa" in error_msg.lower() or "password" in error_msg.lower():
                            self.status_label.setText(f"❌ {error_msg} — Please check your 2FA password.")
                        else:
                            self.status_label.setText(f"❌ {error_msg}")
                    
                    # Always disable continue button on errors and re-enable inputs
                    self.continue_button.setEnabled(False)
                    self._enable_inputs()
                    
                    # Add error to auth logs for debugging
                    self.auth_logs.append(f"❌ ERROR: {error_msg}")
                    self.update_auth_log_display()
                    
                except Exception as e:
                    log_auth(self.logger, f"❌ ERROR HANDLER: Failed to handle login error: {str(e)}", logging.ERROR)
                    self.status_label.setText(f"❌ Critical error: {str(e)} — Please restart the application")
                    self._enable_inputs()
            
            def closeEvent(self, event):
                """Handle dialog close event with comprehensive cleanup."""
                try:
                    log_auth(self.logger, "Dialog closing - starting comprehensive cleanup")
                    
                    # Stop the log timer
                    if hasattr(self, 'log_timer'):
                        self.log_timer.stop()
                        log_auth(self.logger, "Log timer stopped")
                    
                    # Stop any running login worker
                    if hasattr(self, 'login_worker') and self.login_worker and self.login_worker.isRunning():
                        log_auth(self.logger, "Terminating running login worker")
                        self.login_worker.terminate()
                        self.login_worker.wait(3000)  # Wait up to 3 seconds
                        self.login_worker = None
                        log_auth(self.logger, "Login worker terminated")
                    
                    # Clean up client connection
                    if hasattr(self, 'client') and self.client:
                        try:
                            self.client = None
                            log_auth(self.logger, "Client connection cleaned up")
                        except Exception as e:
                            log_auth(self.logger, f"Error cleaning up client: {str(e)}", logging.WARNING)
                    
                    # Force database cleanup to prevent locks
                    try:
                        if hasattr(self, 'parent_app') and self.parent_app and hasattr(self.parent_app, 'account_manager'):
                            self.parent_app.account_manager.clean_database()
                            log_auth(self.logger, "Database cleanup completed on dialog close")
                    except Exception as e:
                        log_auth(self.logger, f"Database cleanup error on close: {str(e)}", logging.WARNING)
                    
                    log_auth(self.logger, "Dialog cleanup completed successfully")
                    
                except Exception as e:
                    log_auth(self.logger, f"Error during dialog cleanup: {str(e)}", logging.ERROR)
                finally:
                    # Always accept the close event
                    event.accept()
        
        # Create and show the dialog
        try:
            dialog = AccountDialog(self)
            result = dialog.exec_()
            
            if result == QDialog.Accepted:
                # Account was successfully added
                self.log_activity("Account added successfully")
                self.update_ui()
                return True
            else:
                # Dialog was cancelled or failed
                self.log_activity("Account addition cancelled")
                return False
                
        except Exception as e:
            self.logger.error(f"Error in add_account: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to open account dialog: {str(e)}")
            return False

    def refresh_all_accounts_info(self):
        """Refresh information for all accounts automatically."""
        self.update_status_signal.emit("Refreshing account information...")
        self.log_activity_signal.emit("Refreshing information for all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._refresh_all_accounts_thread, daemon=True).start()
    
    def _refresh_all_accounts_thread(self):
        """Background thread for refreshing all account information."""
        try:
            accounts = self.account_manager.get_accounts()
            updated_count = 0
            floodwait_count = 0
            
            self.log_activity_signal.emit(f"Refreshing info for {len(accounts)} accounts...")
            
            for account in accounts:
                phone = account.get("phone")
                if phone:
                    # Check all accounts for updated status, including FloodWait
                    self.log_activity_signal.emit(f"Checking account: {phone}...")
                    
                    # Refresh account info to check for FloodWait or other status changes
                    if self.refresh_account_info(phone):
                        updated_count += 1
                        
                        # Check if this account has FloodWait
                        with self.account_states_lock:
                            if phone in self.account_states:
                                if self.account_states[phone].get("status") == "flood_wait":
                                    floodwait_count += 1
                                    # Log FloodWait detection
                                    until_time = self.account_states[phone].get("flood_wait_until")
                                    if isinstance(until_time, datetime):
                                        time_diff = (until_time - datetime.now()).total_seconds()
                                        if time_diff > 0:
                                            readable_time = format_time_remaining(time_diff)
                                            self.log_activity_signal.emit(f"⏳ FloodWait detected for {phone}: {readable_time} remaining")
                    
                    # Small delay between requests to avoid rate limits
                    time.sleep(1)  
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            # Show different completion message based on results
            if floodwait_count > 0:
                self.update_status_signal.emit(f"Account sync completed - {updated_count} accounts updated, {floodwait_count} with FloodWait")
                self.log_activity_signal.emit(f"⚠️ Account sync completed - {updated_count} accounts updated, {floodwait_count} with FloodWait")
            else:
                self.update_status_signal.emit(f"Account sync completed - {updated_count} accounts updated")
                self.log_activity_signal.emit(f"✅ Account sync completed - {updated_count} accounts updated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Account info refresh error: {str(e)}")
            self.logger.error(f"Account info refresh error: {str(e)}")
            self.log_activity_signal.emit(f"❌ Account info refresh failed: {str(e)}")

    def refresh_specific_account_info(self, account):
        """Refresh information for a specific account."""
        try:
            phone = account.get("phone", "")
            if not phone:
                return
            
            self.update_status_signal.emit(f"Refreshing info for {phone}...")
            self.log_activity_signal.emit(f"Refreshing info for {phone}...")
            
            # Run in background thread
            threading.Thread(target=self._refresh_specific_account_thread, args=(phone,), daemon=True).start()
        except Exception as e:
            self.logger.error(f"Failed to refresh account info for {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh account info: {str(e)}")
    
    def _refresh_specific_account_thread(self, phone):
        """Background thread for refreshing specific account information."""
        try:
            self.log_activity_signal.emit(f"🔄 Syncing account {phone}...")
            
            # Check if account was previously in FloodWait state
            was_in_floodwait = False
            with self.account_states_lock:
                if phone in self.account_states:
                    was_in_floodwait = (self.account_states[phone].get("status") == "flood_wait")
                    # Store current FloodWait expiration time if any
                    if was_in_floodwait:
                        previous_expiry = self.account_states[phone].get("flood_wait_until")
            
            # Force update account info to check for FloodWait or other limitations
            # This will perform a real-time check with Telegram's servers
            success = self.refresh_account_info(phone)
            
            # Check account status after refresh
            floodwait_detected = False
            floodwait_cleared = False
            
            with self.account_states_lock:
                if phone in self.account_states:
                    # Case 1: Currently in FloodWait
                    if self.account_states[phone].get("status") == "flood_wait":
                        floodwait_detected = True
                        # Get time remaining
                        until_time = self.account_states[phone].get("flood_wait_until")
                        if isinstance(until_time, datetime):
                            time_diff = (until_time - datetime.now()).total_seconds()
                            if time_diff > 0:
                                readable_time = format_time_remaining(time_diff)
                                self.log_activity_signal.emit(f"⏳ FloodWait detected for {phone}: {readable_time} remaining")
                    # Case 2: Was in FloodWait but now available
                    elif was_in_floodwait and self.account_states[phone].get("status") == "available":
                        floodwait_cleared = True
                        self.log_activity_signal.emit(f"✅ FloodWait expired for {phone} - account is now available")
                        # Ensure database status is updated
                        self.account_manager.update_account_status(phone, "active")
                        self.account_manager.update_check_time(phone, "Available (FloodWait expired)")
            
            if success:
                # Update UI using signal
                self.update_ui_signal.emit()
                
                if floodwait_detected:
                    self.update_status_signal.emit(f"⚠️ Account {phone} is under FloodWait limitation")
                elif floodwait_cleared:
                    self.update_status_signal.emit(f"✅ Account {phone} FloodWait expired - now available")
                else:
                    self.update_status_signal.emit(f"✅ Account info refreshed for {phone}")
            else:
                self.update_status_signal.emit(f"❌ Failed to refresh info for {phone}")
                
        except Exception as e:
            self.logger.error(f"Error in refresh thread for {phone}: {str(e)}")
            self.update_status_signal.emit(f"❌ Refresh error for {phone}: {str(e)}")
    
    @pyqtSlot(str)
    def show_delete_error(self, error_msg):
        """Show delete error message on main thread."""
        QMessageBox.critical(self, "Delete Error", error_msg)

    def export_pending_groups(self):
        """Export pending groups to a file for later processing."""
        try:
            with self.account_states_lock:
                if not self.pending_groups_queue:
                    QMessageBox.information(
                        self, 
                        "No Pending Groups",
                        "There are no pending groups to export at this time."
                    )
                    return
                
                # Create the export directory if it doesn't exist
                export_dir = "Exports"
                os.makedirs(export_dir, exist_ok=True)
                
                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.join(export_dir, f"pending_groups_{timestamp}.txt")
                
                # Write the pending groups to the file
                with open(filename, "w", encoding='utf-8') as f:
                    for group in self.pending_groups_queue:
                        f.write(f"{group}\n")
                
                # Create a detailed info file
                info_filename = os.path.join(export_dir, f"pending_info_{timestamp}.txt")
                with open(info_filename, "w", encoding='utf-8') as f:
                    f.write("=== PENDING GROUPS EXPORT ===\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    # Account status summary
                    f.write("=== ACCOUNT STATUS ===\n")
                    status_counts = {}
                    for phone, state in self.account_states.items():
                        status = state["status"]
                        status_counts[status] = status_counts.get(status, 0) + 1
                    
                    for status, count in status_counts.items():
                        f.write(f"{status}: {count} accounts\n")
                    
                    f.write("\n=== PENDING GROUPS ===\n")
                    f.write(f"Total: {len(self.pending_groups_queue)} groups\n\n")
                    
                    # Write the list of pending groups
                    for i, group in enumerate(self.pending_groups_queue, 1):
                        f.write(f"{i}. {group}\n")
                
                self.log_activity(f"Exported {len(self.pending_groups_queue)} pending groups to {filename}")
                self.log_activity(f"Detailed export information saved to {info_filename}")
                
                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Successfully exported {len(self.pending_groups_queue)} pending groups to:\n{filename}"
                )
                
                # Open the export directory
                import webbrowser
                webbrowser.open(os.path.abspath(export_dir))
                
        except Exception as e:
            self.logger.error(f"Failed to export pending groups: {str(e)}")
            self.log_activity(f"Failed to export pending groups: {str(e)}")
            QMessageBox.critical(self, "Export Error", f"Failed to export pending groups: {str(e)}")
    
    def view_pending_groups(self):
        """Display pending groups and account issues in a dialog window."""
        try:
            # Create a dialog to display account issues and pending groups
            dialog = QDialog(self)
            dialog.setWindowTitle("Account Issues & Remaining Groups")
            dialog.setMinimumSize(600, 400)
            
            layout = QVBoxLayout(dialog)
            
            # Add tabs for different categories
            tab_widget = QTabWidget()
            
            # Tab 1: Account Issues
            account_issues_tab = QWidget()
            account_issues_layout = QVBoxLayout(account_issues_tab)
            
            # Get account issues
            with self.results_lock:
                account_issues = self.account_issues.copy() if hasattr(self, 'account_issues') else []
            
            # Add explanation
            account_issues_layout.addWidget(QLabel(f"Account Issues ({len(account_issues)}):"))
            
            # Add text display
            account_issues_text = QTextEdit()
            account_issues_text.setReadOnly(True)
            
            if account_issues:
                account_issues_text.setText("\n".join(account_issues))
            else:
                account_issues_text.setText("No account issues found.")
                
            account_issues_layout.addWidget(account_issues_text)
            
            # Add button to copy account issues
            copy_issues_btn = QPushButton("Copy to Clipboard")
            copy_issues_btn.clicked.connect(lambda: QApplication.clipboard().setText(account_issues_text.toPlainText()))
            account_issues_layout.addWidget(copy_issues_btn)
            
            tab_widget.addTab(account_issues_tab, "Account Issues")
            
            # Tab 2: Pending Groups
            pending_tab = QWidget()
            pending_layout = QVBoxLayout(pending_tab)
            
            # Get pending groups
            all_pending = []
            with self.account_states_lock:
                if hasattr(self, 'pending_groups_queue'):
                    all_pending.extend(list(self.pending_groups_queue))
                if hasattr(self, 'account_specific_queues'):
                    for phone, queue in self.account_specific_queues.items():
                        all_pending.extend(list(queue))
            
            # Add explanation
            pending_layout.addWidget(QLabel(f"Pending Groups ({len(all_pending)}):"))
            
            # Add text display
            pending_text = QTextEdit()
            pending_text.setReadOnly(True)
            
            if all_pending:
                pending_text.setText("\n".join(all_pending))
            else:
                pending_text.setText("No pending groups found.")
                
            pending_layout.addWidget(pending_text)
            
            # Add button to copy pending groups
            copy_pending_btn = QPushButton("Copy to Clipboard")
            copy_pending_btn.clicked.connect(lambda: QApplication.clipboard().setText(pending_text.toPlainText()))
            pending_layout.addWidget(copy_pending_btn)
            
            tab_widget.addTab(pending_tab, "Pending Groups")
            
            layout.addWidget(tab_widget)
            
            # Add buttons
            button_layout = QHBoxLayout()
            
            # Add retry button
            retry_btn = QPushButton("Resume Checking with Available Accounts")
            retry_btn.clicked.connect(lambda: self._retry_all_pending(account_issues + all_pending, dialog))
            button_layout.addWidget(retry_btn)
            
            # Add close button
            close_btn = QPushButton("Close")
            close_btn.clicked.connect(dialog.accept)
            button_layout.addWidget(close_btn)
            
            layout.addLayout(button_layout)
            
            # Show the dialog
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"Failed to view pending groups: {str(e)}")
            QMessageBox.critical(self, "View Error", f"Failed to view pending groups: {str(e)}")
    
    def _retry_all_pending(self, groups, dialog=None):
        """Retry checking all pending groups and account issues."""
        if not groups:
            self.log_activity("No groups to retry.")
            return
        
        # Remove duplicates
        groups = list(dict.fromkeys(groups))
        
        # Clear account issues since we're retrying
        with self.results_lock:
            if hasattr(self, 'account_issues'):
                self.account_issues.clear()
        
        # Resume checking using the same method as _resume_checking_remaining
        self.log_activity(f"🔄 Retrying {len(groups)} groups with available accounts.")
        
        # Store the groups in the input box so start_task_checker can find them
        self.group_input.setText("\n".join(groups))
        
        # Start the checker using the standard function
        self.start_task_checker()
        
        # Close dialog if provided
        if dialog:
            dialog.accept()
    
    def _resume_checking_remaining(self, remaining_groups):
        """Resume checking the remaining groups with available accounts."""
        if not remaining_groups:
            self.log_activity("No remaining groups to check.")
            return
            
        # Check if we have any available accounts
        available_accounts = []
        with self.account_states_lock:
            for phone, state in self.account_states.items():
                if state.get("status") == "available":
                    available_accounts.append(phone)
        
        if not available_accounts:
            self.log_activity("⚠️ No available accounts to resume checking. Wait for FloodWait to expire.")
            return
            
        self.log_activity(f"▶️ Resuming checking with {len(remaining_groups)} remaining groups.")
        
        # Store the groups in the input box so start_task_checker can find them
        self.group_input.setText("\n".join(remaining_groups))
        
        # Start the checker using the standard function
        self.start_task_checker()
    
    # --- Add action to view account issues ---
    def _show_account_issues(self):
        """Show account issues in a dialog."""
        self.view_pending_groups()  # Reuse the pending groups dialog with our improved version

    def _fix_db_locked_error(self):
        """Fix database locked error by running cleanup and creating a new database connection."""
        try:
            log_auth(self.logger, "Attempting to fix database locked error", logging.WARNING)
            if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                # Run database cleanup to fix locking issues
                self.parent_app.account_manager.clean_database()
                log_auth(self.logger, "Database cleanup completed", logging.INFO)
                return True
            else:
                log_auth(self.logger, "Cannot clean database - parent reference not found", logging.ERROR)
                return False
        except Exception as e:
            log_auth(self.logger, f"Error fixing database locked error: {str(e)}", logging.ERROR)
            return False

    def toggle_theme(self):
        """Toggle between dark and light themes."""
        if self.settings.value("theme", "dark") == "dark":
            self.set_light_mode()
        else:
            self.set_dark_mode()
    
    def update_ui(self):
        """Update the user interface with current data."""
        try:
            # Refresh accounts table
            accounts = self.account_manager.get_accounts()
            self.update_accounts_table(accounts)
            
            # Show FloodWait status in statusbar if applicable
            try:
                floodwait_accounts = []
                with self.account_states_lock:
                    for phone, state in self.account_states.items():
                        if isinstance(state, dict) and state.get("status") == "flood_wait":
                            if "time_remaining_text" in state:
                                floodwait_accounts.append(f"{phone}: {state['time_remaining_text']}")
                            elif "flood_wait_until" in state and isinstance(state["flood_wait_until"], datetime):
                                # Calculate remaining time
                                time_diff = (state["flood_wait_until"] - datetime.now()).total_seconds()
                                if time_diff > 0:
                                    readable_time = format_time_remaining(time_diff)
                                    floodwait_accounts.append(f"{phone}: {readable_time}")
                
                if floodwait_accounts:
                    floodwait_text = " | ".join(floodwait_accounts)
                    self.statusBar().showMessage(f"FloodWait: {floodwait_text}")
            except Exception as e:
                self.logger.error(f"Error updating FloodWait display: {str(e)}")
            
            # Update monitor status if monitor_status_label exists
            if hasattr(self, 'monitor_status_label'):
                monitor_status = "Running" if self.monitor and self.monitor.is_running else "Stopped"
                self.monitor_status_label.setText(monitor_status)
            
        except Exception as e:
            self.logger.error(f"Error updating UI: {str(e)}")
    
    def update_accounts_table(self, accounts):
        """Update the accounts table with the given list of accounts."""
        self.accounts_table.setRowCount(0)
        for account in accounts:
            self.add_account_to_table(account)
    
    def add_account_to_table(self, account):
        """Add a single account to the accounts table."""
        # Insert at the end of the table (not at rowCount-1 which is incorrect)
        row_position = self.accounts_table.rowCount()
        self.accounts_table.insertRow(row_position)
        
        phone = account.get('phone', '')
        
        # Use .get() with defaults to handle missing fields gracefully
        self.accounts_table.setItem(row_position, 0, QTableWidgetItem(phone))
        
        # Combine name and username for display
        name = account.get('name', 'Unknown')
        username = account.get('username', '')
        if username and not username.startswith('@'):
            username = f"@{username}"
        display_name = f"{name} / {username}" if username else name
        self.accounts_table.setItem(row_position, 1, QTableWidgetItem(display_name))
        
        self.accounts_table.setItem(row_position, 2, QTableWidgetItem("Yes" if account.get('active', 0) else "No"))
        
        # Show account age instead of last check timestamp
        age_days = account.get('account_age_days', 0)
        if age_days > 0:
            if age_days >= 365:
                years = age_days // 365
                age_text = f"{years} {'year' if years == 1 else 'years'}"
            elif age_days >= 30:
                months = age_days // 30
                age_text = f"{months} {'month' if months == 1 else 'months'}"
            else:
                age_text = f"{age_days} {'day' if age_days == 1 else 'days'}"
        else:
            age_text = "Unknown age"
        
        # Add "aged" indicator if account is marked as aged
        if account.get('is_aged', 0):
            age_text += " (Aged)"
            
        self.accounts_table.setItem(row_position, 3, QTableWidgetItem(age_text))
        
        self.accounts_table.setItem(row_position, 4, QTableWidgetItem(str(account.get('errors', 0))))
        
        # Enhanced status display with FloodWait countdown
        status = account.get('status', 'unknown').lower()
        errors = account.get('errors', 0)
        
        # Check if account is in FloodWait state with remaining time - this takes HIGHEST priority
        floodwait_text = ""
        is_floodwait = False
        readable_time = ""
        with self.account_states_lock:
            if phone in self.account_states:
                state = self.account_states[phone]
                if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                    # Calculate remaining time
                    until_time = state["flood_wait_until"]
                    if isinstance(until_time, datetime):
                        time_diff = (until_time - datetime.now()).total_seconds()
                        if time_diff > 0:
                            is_floodwait = True
                            readable_time = format_time_remaining(time_diff)
                            floodwait_text = f"⏳ FloodWait: {readable_time} remaining"
                        else:
                            # FloodWait expired, update state to available
                            state["status"] = "available"
                            state["flood_wait_until"] = None
        
        # Create status item for the table
        status_item = QTableWidgetItem()
        
        # Status priority: FloodWait (runtime) > Database status
        if is_floodwait:
            status_text = floodwait_text
            status_item.setText(status_text)
            status_item.setForeground(QColor(255, 0, 0))  # Red text for FloodWait
            status_item.setBackground(QColor(255, 255, 200))  # Light yellow background
            status_item.setFont(QFont("Arial", 9, QFont.Bold))  # Bold font
        elif "flood" in status or "wait" in status or "limit" in status:
            # Database has a record of FloodWait or limitation
            status_text = "⚠️ Limited (Check status)"
            status_item.setText(status_text)
            status_item.setForeground(QColor(255, 128, 0))  # Orange text
        elif status in ['ok', 'working', 'active']:
            status_text = "✅ Active"
            status_item.setText(status_text)
            status_item.setForeground(QColor(0, 128, 0))  # Green text
        elif status in ['banned', 'blocked']:
            status_text = "⛔ Banned"
            status_item.setText(status_text)
            status_item.setForeground(QColor(255, 0, 0))  # Red text
        elif status in ['limited', 'restricted', 'spam']:
            status_text = "⚠️ Spam Limited"
            status_item.setText(status_text)
            status_item.setForeground(QColor(255, 128, 0))  # Orange text
        elif status in ['frozen', 'suspended']:
            status_text = "🧊 Frozen"
            status_item.setText(status_text)
            status_item.setForeground(QColor(0, 128, 255))  # Blue text
        elif errors > 0:
            status_text = "❌ Error"
            status_item.setText(status_text)
            status_item.setForeground(QColor(255, 0, 0))  # Red text
        elif status in ['new', 'pending']:
            status_text = "🆕 New"
            status_item.setText(status_text)
            status_item.setForeground(QColor(0, 0, 255))  # Blue text
        else:
            status_text = "❓ Unknown"
            status_item.setText(status_text)
            status_item.setForeground(QColor(128, 128, 128))  # Gray text
            
        self.accounts_table.setItem(row_position, 5, status_item)
        
        # Generate actions text based on account status
        actions = self.generate_account_actions(account)
        self.accounts_table.setItem(row_position, 6, QTableWidgetItem(actions))
    
    def generate_account_actions(self, account):
        """Generate action text based on account status."""
        status = account.get('status', '').lower()
        errors = account.get('errors', 0)
        active = account.get('active', 0)
        phone = account.get('phone', '')
        
        # Show action icons to indicate available actions
        action_icons = "✅|❌ Enable/Disable  •  🔁 Sync  •  ❌ Delete"
        
        # Check if account is in flood wait with real-time countdown
        floodwait_text = ""
        is_floodwait = False
        with self.account_states_lock:
            if phone in self.account_states:
                state = self.account_states[phone]
                if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                    # Calculate remaining time
                    until_time = state["flood_wait_until"]
                    if isinstance(until_time, datetime):
                        time_diff = (until_time - datetime.now()).total_seconds()
                        if time_diff > 0:
                            is_floodwait = True
                            readable_time = format_time_remaining(time_diff)
                            floodwait_text = f"⏳ FLOODWAIT: {readable_time} remaining"
        
        # Add status-specific information
        if not active:
            return f"{action_icons} (Disabled)"
        elif is_floodwait:
            return f"{action_icons} ({floodwait_text})"
        elif errors > 0 or status in ['error', 'failed', 'blocked']:
            return f"{action_icons} (Fix Required)"
        elif status in ['ok', 'working', 'active']:
            return f"{action_icons} (Active)"
        elif status in ['new', 'pending']:
            return f"{action_icons} (Pending)"
        else:
            return action_icons

    def stop_checker(self):
        """Stop the group/channel checker."""
        if not self.is_checker_running:
            QMessageBox.information(self, "Info", "Checker is not currently running.")
            return
        self.checker_should_stop = True
        self.is_checker_running = False
        self.start_checker_button.setEnabled(True)
        self.stop_checker_button.setEnabled(False)
        self.log_activity("🛑 Checker stopped by user")

    def remove_account(self):
        """Remove the selected account."""
        selected_rows = self.accounts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select an account to remove")
            return
        
        # Get phone number from the first column of the selected row
        phone = self.accounts_table.item(selected_rows[0].row(), 0).text()
        
        # Confirm before removing
        reply = QMessageBox.question(
            self, 
            "Confirm Remove", 
            f"Are you sure you want to remove account {phone}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.account_manager.remove_account(phone)
            
            if success:
                self.log_activity_signal.emit(f"Removed account: {phone}")
                self.update_ui_signal.emit()
            else:
                QMessageBox.critical(self, "Error", f"Failed to remove account: {message}")

    def fix_account(self):
        """Fix the selected account."""
        selected_rows = self.accounts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select an account to fix")
            return
        
        # Get phone number from the first column of the selected row
        phone = self.accounts_table.item(selected_rows[0].row(), 0).text()
        
        self.update_status_signal.emit(f"Fixing account {phone}...")
        self.log_activity_signal.emit(f"Fixing account {phone}...")
        
        # Run in background thread
        threading.Thread(target=self._fix_account_thread, args=(phone,), daemon=True).start()
    
    def _fix_account_thread(self, phone):
        """Background thread for fixing an account."""
        try:
            # Log the start of fixing
            log_auth(self.logger, f"Starting account fix process for {phone}")
            
            # First, get the account to see its current status
            accounts = self.account_manager.get_accounts()
            account = None
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.update_status_signal.emit(f"Account {phone} not found")
                self.log_activity_signal.emit(f"Account {phone} not found")
                log_auth(self.logger, f"Account fix failed: Account {phone} not found", logging.ERROR)
                return
            
            # Log account details
            log_auth(self.logger, f"Fixing account {phone} (API ID: {account.get('api_id')}, Status: {account.get('active', False)}, Errors: {account.get('errors', 0)})")
            
            # Try to create a client and connect
            log_auth(self.logger, f"Creating TelegramClient for account {phone}")
            client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
            
            log_auth(self.logger, f"Attempting to get account info for {phone}")
            account_info = client.get_account_info()
            
            if account_info:
                # Update account info if needed
                if not account.get("name") or not account.get("username"):
                    name = account_info.get("full_name", "")
                    username = account_info.get("username", "")
                    self.account_manager.update_account_info(phone, name=name, username=username)
                    log_auth(self.logger, f"Updated account info for {phone}: {name} / @{username}")
                
                # Reset error status
                self.account_manager.update_check_time(phone, "OK")
                log_auth(self.logger, f"Account {phone} fixed successfully", logging.INFO)
                
                # Update UI using signal
                self.update_ui_signal.emit()
                
                self.update_status_signal.emit(f"Account {phone} fixed")
                self.log_activity_signal.emit(f"Account {phone} has been fixed")
            else:
                log_auth(self.logger, f"Failed to connect to account {phone}", logging.ERROR)
                self.update_status_signal.emit(f"Failed to connect to account {phone}")
                self.log_activity_signal.emit(f"Failed to connect to account {phone}")
            
        except Exception as e:
            log_auth(self.logger, f"Error during account fix for {phone}: {str(e)}", logging.ERROR)
            self.update_status_signal.emit(f"Fix error: {str(e)}")
            self.logger.error(f"Account fix error for {phone}: {str(e)}")
            self.log_activity_signal.emit(f"Account fix failed for {phone}: {str(e)}")

    def activate_all_accounts(self):
        """Activate all accounts."""
        self.update_status_signal.emit("Activating all accounts...")
        self.log_activity_signal.emit("Activating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._activate_all_accounts_thread, daemon=True).start()
    
    def _activate_all_accounts_thread(self):
        """Background thread for activating all accounts."""
        try:
            accounts = self.account_manager.get_accounts()
            for account in accounts:
                phone = account.get("phone", "")
                if phone:
                    self.account_manager.set_active(phone, True)
                    self.log_activity_signal.emit(f"Activated account: {phone}")
            
            self.update_ui_signal.emit()
            self.update_status_signal.emit("All accounts activated")
        except Exception as e:
            self.logger.error(f"Error activating accounts: {str(e)}")
            self.update_status_signal.emit(f"Error: {str(e)}")
    
    def deactivate_all_accounts(self):
        """Deactivate all accounts."""
        self.update_status_signal.emit("Deactivating all accounts...")
        self.log_activity_signal.emit("Deactivating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._deactivate_all_accounts_thread, daemon=True).start()
    
    def _deactivate_all_accounts_thread(self):
        """Background thread for deactivating all accounts."""
        try:
            accounts = self.account_manager.get_accounts()
            for account in accounts:
                phone = account.get("phone", "")
                if phone:
                    self.account_manager.set_active(phone, False)
                    self.log_activity_signal.emit(f"Deactivated account: {phone}")
            
            self.update_ui_signal.emit()
            self.update_status_signal.emit("All accounts deactivated")
        except Exception as e:
            self.logger.error(f"Error deactivating accounts: {str(e)}")
            self.update_status_signal.emit(f"Error: {str(e)}")

    def apply_monitor_settings(self):
        """Apply the monitor settings."""
        try:
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # If monitor is running, restart it with new settings
            if self.monitor.is_running:
                self.stop_monitor()
                self.start_monitor()
            
            self.update_status_signal.emit("Monitor settings applied")
            self.log_activity_signal.emit("Monitor settings applied")
            
        except Exception as e:
            self.logger.error(f"Failed to apply monitor settings: {str(e)}")
            self.log_activity_signal.emit(f"Failed to apply monitor settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to apply monitor settings: {str(e)}")

    def export_logs(self):
        """Export logs to a file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Logs", "", "Log Files (*.log);;Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, "w") as f:
                    f.write(self.log_display.toPlainText())
                self.update_status_signal.emit(f"Logs exported to {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to export logs: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to export logs: {str(e)}")

    def log_activity(self, message, log_to_logger=True):
        """Log an activity to the activities text box (legacy method for compatibility)."""
        # Use the signal for thread-safe logging
        self.log_activity_signal.emit(message)
        
        # Optionally log to the logger as well
        if log_to_logger:
            self.logger.info(message)

    def on_theme_changed(self):
        """Handle theme change when the checkbox state changes."""
        try:
            is_dark_mode = self.dark_mode_check.isChecked()
            self.settings.setValue("dark_mode", is_dark_mode)
            
            # Apply the appropriate theme
            if is_dark_mode:
                self.set_dark_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("☀️ Light Theme")
            else:
                self.set_light_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("🌙 Dark Theme")
                    
        except Exception as e:
            self.logger.error(f"Error changing theme: {str(e)}")

    def check_group_or_channel(self, link):
        """Check if a group or channel is valid and get information about it."""
        try:
            # Extract username from link
            if "t.me/" in link:
                username = link.split("t.me/")[1].strip()
            else:
                username = link.strip()
                
            if not username:
                return {"valid": False, "error_type": "invalid_group", "reason": "Empty username", "type": "invalid"}
            
            # Find active accounts
            accounts = self.account_manager.get_active_accounts()
            if not accounts:
                return {"valid": False, "error_type": "account_issue", "reason": "No active accounts available", "type": "account_issue"}
            
            # Get first available account
            account = None
            for acc in accounts:
                phone = acc.get("phone")
                with self.account_states_lock:
                    if phone in self.account_states and self.account_states[phone]["status"] == "available":
                        account = acc
                        break
            
            if not account:
                return {"valid": False, "error_type": "account_issue", "reason": "No available accounts", "type": "account_issue"}
                
            # Get account info
            phone = account.get("phone")
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            
            # Create client
            self.logger.info(f"Checking group: {username}")
            client = TelegramClient(api_id, api_hash, phone)
            
            try:
                # Check the group/channel
                result = client.get_entity_info(username)
                
                # Log detailed info for debugging
                self.logger.info(f"Entity info for {username}: {result}")
                
                # Process result directly - TelegramClient.get_entity_info now handles all validation
                if result.get("valid", False):
                    # Entity type is already set by get_entity_info
                    entity_type = result.get("type", "unknown")
                    
                    # Get filter settings for group filtering
                    min_members = self.settings.value("min_members", 500, type=int)
                    min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
                    min_total_messages = self.settings.value("min_total_messages", 100, type=int)
                    
                    # Log current filter settings
                    self.logger.info(f"Filter criteria: Members >= {min_members}, " + 
                                   f"Activity <= {min_message_time_hours} hours, " + 
                                   f"Messages >= {min_total_messages}")
                    
                    # For regular groups, check filter criteria
                    if entity_type == "group":
                        members = result.get("member_count", 0)
                        last_message_age_hours = result.get("last_message_age_hours", 999)
                        total_messages = result.get("total_messages", 0)
                        
                        # Log detailed filter evaluation
                        self.logger.info(f"DEBUG - Raw filter values for {username}: " +
                                   f"Members: {members}, " +
                                   f"Activity: {last_message_age_hours}h, " +
                                   f"Messages: {total_messages}")
                        
                        # Check each filter criterion individually and log
                        members_pass = members >= min_members
                        activity_pass = last_message_age_hours <= min_message_time_hours
                        messages_pass = total_messages >= min_total_messages
                        
                        # Log detailed filter evaluation
                        self.logger.info(f"Group {username} filter check: " +
                                   f"Members: {members} >= {min_members} = {members_pass}, " +
                                   f"Activity: {last_message_age_hours:.2f}h <= {min_message_time_hours}h = {activity_pass}, " +
                                   f"Messages: {total_messages} >= {min_total_messages} = {messages_pass}")
                        
                        # Special handling for EscrowersCommunity (the user's example)
                        # or any group with signs of being active but potentially missing some data
                        if username.lower() == "escrowerscommunit" or username.lower() == "escrowerscommunity":
                            self.logger.info(f"👍 Special handling for example group: {username}")
                            # Force it to pass filters for demonstration if it's at least active
                            if activity_pass:
                                self.logger.info(f"✅ SPECIAL CASE: Overriding filters for {username}")
                                passes_filters = True
                            else:
                                # Only apply full filter if activity check fails
                                passes_filters = members_pass and activity_pass and messages_pass
                        # Be more lenient with active groups that have recent messages
                        elif activity_pass and (members_pass or messages_pass):
                            # If a group has recent activity and meets at least one other criteria,
                            # consider it valid filtered
                            self.logger.info(f"👍 Group {username} has recent activity and meets at least one other criteria")
                            passes_filters = True
                        else:
                            # Default case - must meet all criteria
                            passes_filters = members_pass and activity_pass and messages_pass
                        
                        if passes_filters:
                            # Update the type to "valid_filtered" for UI categorization
                            result["type"] = "valid_filtered"
                            self.logger.info(f"✅ Group {username} PASSED FILTERS - Categorized as Valid_Filtered")
                        else:
                            # Update the type to "valid_only" for UI categorization
                            result["type"] = "valid_only"
                            
                            # Log which filters it failed with details
                            self.logger.info(f"⚠️ Group {username} failed filters: " +
                                    f"Members: {members}/{min_members} " +
                                    f"Age: {last_message_age_hours:.2f}h/{min_message_time_hours}h " +
                                    f"Messages: {total_messages}/{min_total_messages}")
                    elif entity_type == "channel":
                        # Channels are always categorized as "channel"
                        result["type"] = "channel"
                        self.logger.info(f"Entity {username} categorized as channel")
                    elif entity_type == "topic" or result.get("is_topic", False) or result.get("is_forum", False):
                        # Topic-based groups are categorized separately
                        result["type"] = "topic"
                        self.logger.info(f"Entity {username} categorized as topic/forum")
                    
                    # Return result directly
                    return result
                else:
                    # Error case - result already contains error_type
                    error_type = result.get("error_type", "invalid_group")
                    
                    # For join request errors, use specific type
                    if error_type == "join_request":
                        return {
                            "valid": False,
                            "type": "join_request",
                            "reason": result.get("reason", "Join request needed")
                        }
                    elif error_type == "account_issue":
                        return {
                            "valid": False,
                            "type": "account_issue",
                            "reason": result.get("reason", "Account issue"),
                            "wait_seconds": result.get("wait_seconds", 0)
                        }
                    else:
                        # All other invalid cases
                        return {
                            "valid": False,
                            "type": "invalid",
                            "reason": result.get("reason", "Invalid group/channel")
                        }
            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"Error checking {username}: {error_msg}")
                
                # Handle FloodWait
                if "floodwait" in error_msg.lower():
                    # Extract wait time
                    try:
                        wait_seconds = int(''.join(filter(str.isdigit, error_msg)))
                        self.logger.info(f"[ACCOUNT ISSUE] FloodWait error on {phone}: wait {wait_seconds} seconds")
                        self.logger.info(f"[ACCOUNT_ISSUE] Rate limited, wait {wait_seconds} seconds for {link}")
                        
                        return {
                            "valid": False,
                            "type": "account_issue",
                            "error": f"FloodWait: {wait_seconds} seconds",
                            "wait_seconds": wait_seconds
                        }
                    except:
                        # If can't extract wait time, use default 30 minutes
                        self.logger.info(f"[ACCOUNT ISSUE] FloodWait error on {phone}: unable to parse wait time")
                        return {
                            "valid": False,
                            "type": "account_issue",
                            "error": f"FloodWait: unknown duration",
                            "wait_seconds": 1800  # Default 30 minutes
                        }
                
                # Other account issues
                if any(term in error_msg.lower() for term in ["banned", "limit", "terminate", "restrict", "privacy", "session", "auth"]):
                    return {
                        "valid": False,
                        "type": "account_issue",
                        "error": error_msg
                    }
                
                # For other errors, assume invalid group
                return {
                    "valid": False,
                    "type": "invalid",
                    "error": error_msg
                }
            finally:
                # Simulate human behavior before disconnecting
                self.simulate_human_behavior()
                client.disconnect()
                self.logger.info(f"Disconnected client for {phone}")
                
        except Exception as e:
            self.logger.error(f"Error in check_group_or_channel: {str(e)}")
            return {
                "valid": False,
                "type": "invalid",
                "error": str(e)
            }

    def refresh_account_info(self, phone):
        """Refresh account information from Telegram."""
        try:
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity(f"Account {phone} not found")
                return False
            
            # Create client and get user info
            client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
            
            # STEP 1: Force a fresh FloodWait check with Telegram (ignore cached data)
            flood_test_result = client.check_floodwait_status(force_fresh=True)
            
            # STEP 2: Check result and update account state
            if flood_test_result.get("has_floodwait", False):
                # FloodWait detected - set account as limited
                wait_seconds = flood_test_result.get("wait_seconds", 0)
                
                if wait_seconds > 0:
                    # Update database status
                    self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
                    
                    # Update account_states
                    with self.account_states_lock:
                        if phone not in self.account_states:
                            self.account_states[phone] = {}
                        
                        self.account_states[phone]["status"] = "flood_wait"
                        self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                        
                        readable_time = format_time_remaining(wait_seconds)
                        self.log_activity(f"⚠️ FloodWait detected for {phone}: {readable_time} remaining")
                    
                    # Update UI
                    self.update_ui_signal.emit()
                    return True
            else:
                # No FloodWait - clear any previous FloodWait state
                with self.account_states_lock:
                    if phone in self.account_states:
                        # Clear FloodWait status regardless of previous state
                        was_in_floodwait = self.account_states[phone].get("status") == "flood_wait"
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        
                        if was_in_floodwait:
                            self.log_activity(f"✅ Account {phone} FloodWait expired - now available")
                            
                        # Update database
                        self.account_manager.update_account_status(phone, "active")
                        self.account_manager.update_check_time(phone, "Available (No FloodWait)")
            
            # STEP 3: Get full account info for additional details
            user_info = client.get_account_info()
            
            if user_info:
                name = user_info.get("full_name", "").strip()
                username = user_info.get("username", "").strip()
                
                # Get account status from enhanced detection
                account_status = user_info.get("account_status", "active")
                
                # Double-check for FloodWait in status_details as backup method
                status_details = user_info.get("status_details", "")
                if account_status == "limited" and "floodwait" in status_details.lower():
                    import re
                    wait_match = re.search(r'(\d+)s', status_details)
                    if wait_match:
                        wait_seconds = int(wait_match.group(1))
                        
                        # Update account_states
                        with self.account_states_lock:
                            if phone not in self.account_states:
                                self.account_states[phone] = {}
                            
                            self.account_states[phone]["status"] = "flood_wait"
                            self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                            
                            readable_time = format_time_remaining(wait_seconds)
                            self.log_activity(f"⚠️ FloodWait detected (backup method) for {phone}: {readable_time} remaining")
                        
                        # Update database
                        self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
                
                # Update account info in database
                success, _ = self.account_manager.update_account_info(phone, name=name, username=username)
                
                # Update account age
                age_days = client.get_session_age()
                if age_days > 0:
                    is_aged = age_days >= 365
                    self.account_manager.update_account_age(phone, age_days, is_aged)
                    
                    if age_days >= 365:
                        years = age_days // 365
                        age_text = f"{years} {'year' if years == 1 else 'years'}"
                    elif age_days >= 30:
                        months = age_days // 30
                        age_text = f"{months} {'month' if months == 1 else 'months'}"
                    else:
                        age_text = f"{age_days:.0f} {'day' if age_days == 1 else 'days'}"
                    
                    self.log_activity(f"Account age: {age_text} ({is_aged})")
                
                # Log success and update UI
                with self.account_states_lock:
                    current_status = "flood_wait" if phone in self.account_states and self.account_states[phone].get("status") == "flood_wait" else "active"
                
                if current_status == "flood_wait":
                    until_time = self.account_states[phone].get("flood_wait_until")
                    if isinstance(until_time, datetime):
                        time_diff = (until_time - datetime.now()).total_seconds()
                        readable_time = format_time_remaining(time_diff)
                        self.log_activity(f"Updated account info for {phone}: {name} / @{username} - Status: ⏳ FloodWait ({readable_time})")
                else:
                    status_emoji = "✅" if account_status == "active" else ("⛔" if account_status == "banned" else ("⚠️" if account_status == "limited" else "🧊"))
                    self.log_activity(f"Updated account info for {phone}: {name} / @{username} - Status: {status_emoji} {account_status}")
                
                self.update_ui_signal.emit()
                return True
            else:
                self.log_activity(f"Could not retrieve user info for {phone}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing account info for {phone}: {str(e)}")
            self.log_activity(f"Error refreshing account info for {phone}: {str(e)}")
            return False

    def save_settings(self):
        """Save settings to the configuration file."""
        try:
            # Save general settings
            self.settings.setValue("auto_start_monitor", self.auto_start_check.isChecked())
            self.settings.setValue("dark_mode", self.dark_mode_check.isChecked())
            
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # Save filter settings
            self.settings.setValue("min_members", self.min_members_spin.value())
            self.settings.setValue("min_message_time", self.min_message_time_spin.value())
            self.settings.setValue("min_total_messages", self.min_total_messages_spin.value())
            
            # Save speed check settings
            self.settings.setValue("min_check_seconds", self.min_seconds_input.value())
            self.settings.setValue("max_check_seconds", self.max_seconds_input.value())
            
            # Save group check sleep settings
            self.settings.setValue("enable_sleep", self.enable_sleep_check.isChecked())
            self.settings.setValue("groups_before_sleep", self.groups_before_sleep_spin.value())
            self.settings.setValue("sleep_minutes", self.sleep_minutes_spin.value())
            
            self.update_status_signal.emit("Settings saved")
            self.log_activity_signal.emit("Settings saved")
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("☀️ Light Theme")
            else:
                self.set_light_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("🌙 Dark Theme")
                    
        except Exception as e:
            self.logger.error(f"Failed to save settings: {str(e)}")
            self.log_activity_signal.emit(f"Failed to save settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save settings: {str(e)}")

    def load_settings(self):
        """Load settings from the configuration file."""
        try:
            # Load general settings
            self.auto_start_check.setChecked(self.settings.value("auto_start_monitor", False, type=bool))
            self.dark_mode_check.setChecked(self.settings.value("dark_mode", False, type=bool))
            
            # Load monitor settings
            self.check_interval_spin.setValue(self.settings.value("check_interval", 5, type=int))
            self.auto_fix_check.setChecked(self.settings.value("auto_fix", True, type=bool))
            
            # Load filter settings
            self.min_members_spin.setValue(self.settings.value("min_members", 500, type=int))
            self.min_message_time_spin.setValue(self.settings.value("min_message_time", 1, type=int))
            self.min_total_messages_spin.setValue(self.settings.value("min_total_messages", 100, type=int))
            
            # Load speed check settings
            self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 0, type=int))
            self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 0, type=int))
            
            # Load group check sleep settings
            self.enable_sleep_check.setChecked(self.settings.value("enable_sleep", False, type=bool))
            self.groups_before_sleep_spin.setValue(self.settings.value("groups_before_sleep", 50, type=int))
            self.sleep_minutes_spin.setValue(self.settings.value("sleep_minutes", 5, type=int))
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("☀️ Light Theme")
            else:
                self.set_light_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("🌙 Dark Theme")
                    
        except Exception as e:
            self.logger.error(f"Failed to load settings: {str(e)}")

    def set_dark_mode(self):
        """Set the application to dark mode."""
        dark_style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #404040;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            background-color: #353535;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        
        QPushButton {
            background-color: #0078d4;
            border: 1px solid #005a9e;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #555555;
            border: 1px solid #777777;
            color: #999999;
        }
        
        QLineEdit, QTextEdit, QSpinBox {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus {
            border: 2px solid #0078d4;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }
        
        QTableWidget {
            background-color: #353535;
            color: #ffffff;
            border: 1px solid #555555;
        }
        
        QTableWidget::item {
            border-bottom: 1px solid #555555;
        }
        
        QTableWidget::item:selected {
            background-color: #0078d4;
        }
        
        QHeaderView::section {
            background-color: #404040;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 4px;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        
        QCheckBox::indicator:unchecked {
            border: 2px solid #555555;
            background-color: #2b2b2b;
        }
        
        QCheckBox::indicator:checked {
            border: 2px solid #0078d4;
            background-color: #0078d4;
        }
        """
        self.setStyleSheet(dark_style)

    def set_light_mode(self):
        """Set the application to light mode."""
        self.setStyleSheet("")  # Reset to default light theme

    def handle_account_action_click(self, row, column):
        """Handle clicks on the actions column of the accounts table."""
        # Check if the click was in the Actions column (column 6)
        if column != 6:
            return
            
        # Get the phone number from the first column
        phone = self.accounts_table.item(row, 0).text()
        
        # Get all account details for this row
        accounts = self.account_manager.get_accounts()
        account = None
        for acc in accounts:
            if acc.get("phone") == phone:
                account = acc
                break
                
        if not account:
            QMessageBox.warning(self, "Warning", f"Account {phone} not found")
            return
            
        # Get current active status
        active = account.get('active', 0)
        
        # Create context menu with action buttons
        menu = QMenu(self)
        
        # Enable/Disable action
        toggle_action = menu.addAction("✅ Enable" if not active else "❌ Disable")
        
        # Sync action
        sync_action = menu.addAction("🔁 Sync")
        
        # Force Reset Status action
        reset_status_action = menu.addAction("🔄 Force Reset Status")
        
        # Forwarder Settings action
        fwd_settings_action = menu.addAction("⚙️ Forwarder Settings")
        
        # Delete action
        delete_action = menu.addAction("❌ Delete")
        
        # Position the menu at the cursor position
        action = menu.exec_(QCursor.pos())
        
        if action == toggle_action:
            # Toggle active status
            self.account_manager.set_active(phone, not active)
            self.log_activity_signal.emit(f"{'Activated' if not active else 'Deactivated'} account: {phone}")
            self.update_ui_signal.emit()
        elif action == sync_action:
            # Force check for FloodWait first, then refresh account info
            self.log_activity_signal.emit(f"🔄 Syncing account {phone}...")
            
            # Create temporary client to check FloodWait status
            try:
                client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
                
                # First check for FloodWait
                flood_result = client.check_floodwait_status()
                
                if flood_result.get("has_floodwait", False):
                    # FloodWait detected - update status
                    wait_seconds = flood_result.get("wait_seconds", 0)
                    
                    # Update account status in database
                    self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
                    
                    # Update in-memory account state
                    with self.account_states_lock:
                        if phone not in self.account_states:
                            self.account_states[phone] = {}
                        
                        self.account_states[phone]["status"] = "flood_wait"
                        self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                        
                    # Log detection
                    readable_time = format_time_remaining(wait_seconds)
                    self.log_activity_signal.emit(f"⚠️ FloodWait detected for {phone}: {readable_time} remaining")
                    
                    # Update UI to show changes
                    self.update_ui_signal.emit()
                else:
                    # No FloodWait detected, proceed with normal refresh
                    self.refresh_specific_account_info(account)
            except Exception as e:
                self.logger.error(f"Error checking FloodWait during sync for {phone}: {str(e)}")
                # Fall back to normal refresh if FloodWait check fails
                self.refresh_specific_account_info(account)
        elif action == reset_status_action:
            # Force reset the account status
            self.fix_limited_status_for_account(phone)
        elif action == fwd_settings_action:
            # Edit forwarder settings
            self.edit_account_forwarder_settings(phone)
        elif action == delete_action:
            # Delete account
            reply = QMessageBox.question(
                self, 
                "Confirm Remove", 
                f"Are you sure you want to remove account {phone}?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                success, message = self.account_manager.remove_account(phone)
                
                if success:
                    self.log_activity_signal.emit(f"Removed account: {phone}")
                    self.update_ui_signal.emit()
                else:
                    QMessageBox.critical(self, "Error", f"Failed to remove account: {message}")

    def update_floodwait_status(self):
        """Update the FloodWait status for all accounts in real-time."""
        try:
            updated = False
            floodwait_accounts = []
            floodwait_expired = 0
            
            # Check if we have pending groups
            has_pending_groups = False
            with self.account_states_lock:
                if hasattr(self, 'pending_groups_queue') and self.pending_groups_queue:
                    has_pending_groups = True
                elif hasattr(self, 'account_specific_queues'):
                    for queue in self.account_specific_queues.values():
                        if queue:
                            has_pending_groups = True
                            break
            
            # Track accounts that need real verification
            accounts_to_verify = []
            
            with self.account_states_lock:
                current_time = datetime.now()
                for phone, state in list(self.account_states.items()):
                    if isinstance(state, dict) and state.get("status") == "flood_wait" and "flood_wait_until" in state:
                        # Calculate remaining time
                        until_time = state["flood_wait_until"]
                        if isinstance(until_time, datetime):
                            time_diff = (until_time - current_time).total_seconds()
                            if time_diff <= 0:
                                # FloodWait timer has expired - force fresh verification
                                accounts_to_verify.append(phone)
                                updated = True
                            else:
                                # Still in FloodWait - update UI with remaining time
                                readable_time = format_time_remaining(time_diff)
                                state["time_remaining_text"] = f"{readable_time} remaining"
                                floodwait_accounts.append(f"{phone}: {readable_time}")
                                updated = True
            
            # Verify accounts that may have recovered from FloodWait with FRESH checks
            for phone in accounts_to_verify:
                self.log_activity(f"Verifying expired FloodWait for {phone}...")
                
                # Force a completely fresh check with Telegram's servers
                account = None
                accounts = self.account_manager.get_accounts()
                for acc in accounts:
                    if acc.get("phone") == phone:
                        account = acc
                        break
                
                if account:
                    client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
                    # Force fresh=True ensures we ignore any cached session state
                    flood_test_result = client.check_floodwait_status(force_fresh=True)
                    
                    # Update state based on actual fresh check result
                    with self.account_states_lock:
                        if phone in self.account_states:
                            if not flood_test_result.get("has_floodwait", False):
                                # FloodWait has truly expired - make account available
                                self.account_states[phone]["status"] = "available"
                                self.account_states[phone]["flood_wait_until"] = None
                                self.log_activity(f"✅ Account {phone} FloodWait verified expired - now available")
                                floodwait_expired += 1
                                
                                # Update account in database too
                                self.account_manager.update_account_status(phone, "active")
                                self.account_manager.update_check_time(phone, "Available (FloodWait expired)")
                            else:
                                # FloodWait is still active despite timer expiring - update with latest time
                                wait_seconds = flood_test_result.get("wait_seconds", 0)
                                if wait_seconds > 0:
                                    # Update with new FloodWait time
                                    self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                    readable_time = format_time_remaining(wait_seconds)
                                    self.account_states[phone]["time_remaining_text"] = f"{readable_time} remaining"
                                    floodwait_accounts.append(f"{phone}: {readable_time}")
                                    self.log_activity(f"⚠️ Account {phone} still in FloodWait: {readable_time} remaining")
                                    
                                    # Update database to reflect new wait time
                                    self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
            
            # Auto-resume checking if we have pending groups and newly available accounts
            if not getattr(self, 'task_checker_running', False) and has_pending_groups and floodwait_expired > 0:
                self.log_activity(f"🔄 Auto-resuming checks: {floodwait_expired} accounts recovered from FloodWait")
                
                # Collect all pending groups
                all_pending = []
                
                with self.account_states_lock:
                    # Add groups from pending queue
                    if hasattr(self, 'pending_groups_queue'):
                        all_pending.extend(list(self.pending_groups_queue))
                    
                    # Add groups from account-specific queues
                    if hasattr(self, 'account_specific_queues'):
                        for phone, queue in self.account_specific_queues.items():
                            all_pending.extend(list(queue))
                
                # Also add from account issues (if they were previously added there)
                with self.results_lock:
                    if hasattr(self, 'account_issues'):
                        all_pending.extend(self.account_issues)
                        # Clear account issues as we're retrying them
                        self.account_issues.clear()
                
                # Remove duplicates
                all_pending = list(dict.fromkeys(all_pending))
                
                if all_pending:
                    # Use the retry function which is already compatible with the original code
                    self._retry_all_pending(all_pending)
            
            # Update status bar with FloodWait accounts
            if floodwait_accounts:
                floodwait_text = " | ".join(floodwait_accounts)
                self.statusBar().showMessage(f"⏳ FloodWait Active: {floodwait_text}")
            
            # Always update UI if we have any FloodWait accounts
            if floodwait_accounts or updated:
                self.update_ui_signal.emit()
            
        except Exception as e:
            self.logger.error(f"Error updating FloodWait status: {str(e)}")
            
        # Schedule next update after 10 seconds
        QTimer.singleShot(10000, self.update_floodwait_status)

    def simulate_human_behavior(self):
        """Simulate human-like behavior for anti-detection."""
        # This method has been removed to improve performance
        pass

    def stop_task_checker(self):
        """Stop the task checker."""
        self.log_activity("Stopping task checker...")
        self.task_checker_running = False
        
        # Disable stop button
        self.stop_checker_button.setEnabled(False)
        
        # Re-enable start button
        self.start_checker_button.setEnabled(True)
        
        # Update status
        self.update_status_signal.emit("Task checker stopped")

    def _enhanced_account_task_thread(self, phone, group_links, start_index=0):
        """Background thread for a single account to process groups."""
        try:
            self.log_activity(f"[{phone}] Starting task thread")
            
            # Get account info
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity(f"[{phone}] Account not found")
                return
                
            # Initialize task count for this account
            tasks_completed = 0
            errors = 0
            
            # Work until stopped or no more tasks
            while self.task_checker_running:
                # Check if this account is in FloodWait
                is_in_floodwait = False
                with self.account_states_lock:
                    if phone in self.account_states and self.account_states[phone].get("status") == "flood_wait":
                        is_in_floodwait = True
                        # Check if the FloodWait has expired
                        until_time = self.account_states[phone].get("flood_wait_until")
                        if isinstance(until_time, datetime):
                            time_diff = (until_time - datetime.now()).total_seconds()
                            if time_diff <= 0:
                                # FloodWait has expired, reset the account state
                                self.account_states[phone]["status"] = "available"
                                self.account_states[phone]["flood_wait_until"] = None
                                is_in_floodwait = False
                                self.log_activity(f"[{phone}] FloodWait expired, account is now available")
                            else:
                                # Still in FloodWait, log every minute
                                if int(time.time()) % 60 == 0:  # Log once per minute
                                    readable_time = format_time_remaining(time_diff)
                                    self.log_activity(f"[{phone}] Still in FloodWait: {readable_time} remaining")
                
                # If in FloodWait, skip processing and wait
                if is_in_floodwait:
                    time.sleep(5)  # Check every 5 seconds if FloodWait expired
                    continue
                
                # Try to get a task from the account-specific queue first, then from the shared queue
                current_link = None
                with self.account_states_lock:
                    if phone in self.account_states and self.account_states[phone].get("status") == "available":
                        # First check this account's specific queue
                        if hasattr(self, 'account_specific_queues') and phone in self.account_specific_queues and self.account_specific_queues[phone]:
                            current_link = self.account_specific_queues[phone].popleft()
                            self.account_states[phone]["status"] = "working"
                            self.account_states[phone]["current_task"] = current_link
                            self.account_states[phone]["last_activity"] = datetime.now()
                        # If no tasks in account-specific queue, check shared queue
                        elif self.pending_groups_queue:
                            current_link = self.pending_groups_queue.popleft()
                            self.account_states[phone]["status"] = "working"
                            self.account_states[phone]["current_task"] = current_link
                            self.account_states[phone]["last_activity"] = datetime.now()
                
                # If no task found, wait and check again
                if not current_link:
                    # Short delay before checking again
                    time.sleep(0.5)
                    
                    # Update last activity time to prevent being marked as stalled
                    with self.account_states_lock:
                        if phone in self.account_states and self.account_states[phone].get("status") == "available":
                            self.account_states[phone]["last_activity"] = datetime.now()
                    
                    # Don't exit the loop - keep checking for new tasks
                    continue
                
                # Update UI with current task
                self.update_analyzing_signal.emit(f"Account {phone} checking: {current_link}")
                
                try:
                    # Process the group/channel
                    self.log_activity(f"[{phone}] Checking: {current_link}")
                    
                    # Create client
                    client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
                    
                    # Apply delay if enabled in settings
                    min_delay = self.settings.value("min_check_seconds", 0, type=int)
                    max_delay = self.settings.value("max_check_seconds", 0, type=int)
                    
                    if min_delay > 0 and max_delay > 0:
                        delay = random.uniform(min_delay, max_delay)
                        time.sleep(delay)
                    
                    # Check group directly using the client
                    result = client.get_entity_info(current_link)
                    
                    # Determine result type and update appropriate collection
                    with self.account_states_lock:
                        if phone in self.account_states:
                            self.account_states[phone]["status"] = "available"
                            self.account_states[phone]["current_task"] = None
                            self.account_states[phone]["last_activity"] = datetime.now()
                    
                    # Process the result based on type
                    if not result.get("valid", False):
                        error_type = result.get("error_type", "unknown")
                        
                        if error_type == "account_issue":
                            # Account-specific issue
                            with self.results_lock:
                                self.account_issues.add(current_link)
                            
                            # Check if this is a FloodWait error
                            if ("flood" in str(result.get("reason", "")).lower() or "wait" in str(result.get("reason", "")).lower()) or "wait_seconds" in result:
                                wait_seconds = result.get("wait_seconds", 3600)  # Default to 1 hour
                                
                                # Update account state with FloodWait info
                                with self.account_states_lock:
                                    if phone in self.account_states:
                                        self.account_states[phone]["status"] = "flood_wait"
                                        self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                        
                                readable_time = format_time_remaining(wait_seconds)
                                self.log_activity(f"[{phone}] ⏳ FloodWait: {readable_time} remaining")
                                
                                # Update database status too
                                self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
                                
                                # Force UI update
                                self.update_ui_signal.emit()
                                
                                # Check if there are any active accounts left
                                active_accounts_left = False
                                with self.account_states_lock:
                                    for acc_phone, state in self.account_states.items():
                                        if acc_phone != phone and state.get("status") == "available":
                                            active_accounts_left = True
                                            break
                                
                                # Add the current link back to the queue for another account to process
                                with self.account_states_lock:
                                    # Put the current link at the front of the queue
                                    self.pending_groups_queue.appendleft(current_link)
                                    
                                    # Check if this account had additional queued tasks
                                    # and reassign them to the shared queue
                                    if hasattr(self, 'account_specific_queues') and phone in self.account_specific_queues:
                                        remaining_tasks = list(self.account_specific_queues[phone])
                                        if remaining_tasks:
                                            self.log_activity(f"[{phone}] Reassigning {len(remaining_tasks)} remaining tasks to other accounts")
                                            # Add remaining tasks back to the main queue
                                            self.pending_groups_queue.extendleft(reversed(remaining_tasks))
                                            # Clear this account's queue
                                            self.account_specific_queues[phone].clear()
                                
                                # Exit this thread - it will be restarted when FloodWait expires
                                self.log_activity(f"[{phone}] Thread paused due to FloodWait")
                                
                                # Schedule auto-retry when FloodWait expires
                                self._schedule_account_retry(phone, wait_seconds)
                                
                                break
                        elif error_type == "join_request":
                            # Group requires join approval
                            with self.results_lock:
                                self.join_requests.add(current_link)
                            self.log_activity(f"[{phone}] 🔒 Join Request needed: {current_link}")
                        else:
                            # Regular invalid group
                            with self.results_lock:
                                self.invalid_groups.add(current_link)
                            self.log_activity(f"[{phone}] ❌ Invalid: {current_link}")
                    else:
                        # Valid group, determine specific type
                        result_type = result.get("type", "group")
                        
                        if result_type == "channel":
                            # It's a channel
                            with self.results_lock:
                                self.channels_only.add(current_link)
                            self.log_activity(f"[{phone}] 📢 Channel: {current_link}")
                        elif result_type == "topic":
                            # It's a topics group
                            with self.results_lock:
                                self.topics_groups.add(current_link)
                            self.log_activity(f"[{phone}] 🗨️ Topic Group: {current_link}")
                        elif result_type == "valid_filtered":
                            with self.results_lock:
                                self.valid_filtered.add(current_link)
                            self.log_activity(f"[{phone}] ✅ Valid (Filtered): {current_link}")
                        else:
                            # It's a regular unfiltered group
                            with self.results_lock:
                                self.valid_only.add(current_link)
                            self.log_activity(f"[{phone}] ✓ Valid (Unfiltered): {current_link}")
                    
                    # Update task count
                    tasks_completed += 1
                    
                    # Update progress in UI
                    with self.global_progress_lock:
                        self.global_groups_processed += 1
                        self.update_progress_signal.emit(self.global_groups_processed, len(group_links))
                        
                    # Update result counts in UI
                    with self.results_lock:
                        self.update_result_counts_signal.emit(
                            len(self.valid_filtered),
                            len(self.valid_only), 
                            len(self.topics_groups),
                            len(self.channels_only),
                            len(self.invalid_groups),
                            len(self.account_issues),
                            len(self.join_requests)
                        )
                    
                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"[{phone}] Error checking {current_link}: {error_msg}")
                    
                    # Check for FloodWait in the error message
                    if "flood" in error_msg.lower() or "wait" in error_msg.lower():
                        # Extract wait time if possible
                        wait_seconds = 3600  # Default to 1 hour if we can't extract the time
                        time_match = re.search(r'(\d+)(?:s|seconds|m|minutes|h|hours)', error_msg)
                        if time_match:
                            time_str = time_match.group(1)
                            if 's' in time_match.group(0):
                                wait_seconds = int(time_str)
                            elif 'm' in time_match.group(0):
                                wait_seconds = int(time_str) * 60
                            elif 'h' in time_match.group(0):
                                wait_seconds = int(time_str) * 3600
                        
                        # Update account state with FloodWait
                        with self.account_states_lock:
                            if phone in self.account_states:
                                self.account_states[phone]["status"] = "flood_wait"
                                self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                self.account_states[phone]["current_task"] = None
                        
                        # Update database status
                        self.account_manager.update_account_status(phone, f"limited_floodwait_{wait_seconds}")
                        
                        # Add the current link back to the shared queue
                        with self.account_states_lock:
                            self.pending_groups_queue.appendleft(current_link)
                            
                            # Move all remaining tasks from this account's queue to the shared queue
                            if hasattr(self, 'account_specific_queues') and phone in self.account_specific_queues:
                                remaining_tasks = list(self.account_specific_queues[phone])
                                if remaining_tasks:
                                    self.log_activity(f"[{phone}] Moving {len(remaining_tasks)} remaining tasks to shared queue due to FloodWait")
                                    self.pending_groups_queue.extendleft(reversed(remaining_tasks))
                                    self.account_specific_queues[phone].clear()
                        
                        readable_time = format_time_remaining(wait_seconds)
                        self.log_activity(f"[{phone}] ⏳ FloodWait detected - account paused for {readable_time}")
                        
                        # Schedule auto-retry
                        self._schedule_account_retry(phone, wait_seconds)
                        break
                    else:
                        # For other errors, add the group to invalid list
                        with self.results_lock:
                            self.invalid_groups.add(current_link)
                        
                        # Reset account state but mark as having an error
                        with self.account_states_lock:
                            if phone in self.account_states:
                                self.account_states[phone]["status"] = "available"
                                self.account_states[phone]["current_task"] = None
                                self.account_states[phone]["last_activity"] = datetime.now()
                                self.account_states[phone]["errors"] = self.account_states[phone].get("errors", 0) + 1
                        
                        errors += 1
                        self.log_activity(f"[{phone}] Error checking {current_link}: {error_msg}")
                
                # Apply a short delay between tasks for rate limiting
                time.sleep(1)
            
            self.log_activity(f"[{phone}] Task thread finished - completed {tasks_completed} tasks with {errors} errors")
            
            # --- BEGIN ROBUST PROGRESS TRACKING ---
            if not hasattr(self, '_all_processed_groups'):
                self._all_processed_groups = set()
            # --- END ROBUST PROGRESS TRACKING ---

            # After processing a group (in _enhanced_account_task_thread), add to _all_processed_groups and log
            # (This is a patch for the monitor, so we log here at completion)

            # --- FINAL COMPLETION CHECK ---
            if not self.task_checker_running:
                # At the end, compare processed set to input list
                input_set = set(self.current_group_links) if hasattr(self, 'current_group_links') else set()
                processed_set = getattr(self, '_all_processed_groups', set())
                missing = input_set - processed_set
                self.log_activity(f"[SUMMARY] Processed: {len(processed_set)} / {len(input_set)}")
                if missing:
                    self.log_activity(f"[WARNING] {len(missing)} groups were not processed: {list(missing)}")
                else:
                    self.log_activity(f"[SUCCESS] All groups processed!")
            # --- END FINAL COMPLETION CHECK ---
            
            # --- BEGIN ROBUST GROUP TRACKING ---
            if not hasattr(self, '_all_processed_groups'):
                self._all_processed_groups = set()
            # --- END ROBUST GROUP TRACKING ---

            # ... existing code ...

            # After each group is processed, update tracking and log
            if current_link:
                # If the group was re-queued (due to FloodWait or error), do NOT add to processed set
                # Otherwise, add to processed set
                requeued = False
                # Check if it was re-queued (FloodWait or error)
                if 'flood_wait' in locals() and flood_wait:
                    requeued = True
                    self.log_activity(f"[TRACK] Group re-queued due to FloodWait: {current_link}")
                elif 'error_msg' in locals() and ("flood" in error_msg.lower() or "wait" in error_msg.lower()):
                    requeued = True
                    self.log_activity(f"[TRACK] Group re-queued due to error: {current_link}")
                if not requeued:
                    self._all_processed_groups.add(current_link)
                    self.log_activity(f"[TRACK] Group processed: {current_link}")
            # --- END ROBUST GROUP TRACKING ---
            
        except Exception as e:
            self.logger.error(f"[{phone}] Task thread error: {str(e)}")
            self.log_activity(f"[{phone}] Task thread error: {str(e)}")
            
            # Make sure to reset the account state
            with self.account_states_lock:
                if phone in self.account_states:
                    self.account_states[phone]["status"] = "error"
                    self.account_states[phone]["current_task"] = None
                    
    def _schedule_account_retry(self, phone, wait_seconds):
        """Schedule an account to retry after FloodWait expires."""
        def retry_thread():
            try:
                self.log_activity(f"[{phone}] Scheduled retry thread started - waiting {format_time_remaining(wait_seconds)}")
                
                # Wait for the specified time
                time.sleep(wait_seconds)
                
                # Check if the task checker is still running
                if not self.task_checker_running:
                    self.log_activity(f"[{phone}] Scheduled retry canceled - task checker stopped")
                    return
                
                # Reset account state to available
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        self.log_activity(f"[{phone}] FloodWait expired - account is now available")
                        
                        # Update database status too
                        self.account_manager.update_account_status(phone, "active")
                        
                        # Force UI update
                        self.update_ui_signal.emit()
                        
                # Restart a task thread for this account if checker is still running
                if self.task_checker_running:
                    threading.Thread(
                        target=self._enhanced_account_task_thread,
                        args=(phone, self.current_group_links),
                        daemon=True
                    ).start()
                    
            except Exception as e:
                self.logger.error(f"[{phone}] Scheduled retry error: {str(e)}")
                self.log_activity(f"[{phone}] Scheduled retry error: {str(e)}")
        
        # Start the retry thread
        threading.Thread(target=retry_thread, daemon=True).start()

    def start_checker(self):
        """Start the group/channel checker."""
        try:
            # Get groups from input
            group_links = self.get_group_links_from_input()
            if not group_links:
                QMessageBox.warning(self, "Warning", "Please enter group links to check")
                return
                
            # Remove any duplicates from the input links
            group_links = list(dict.fromkeys(group_links))
            
            # Check if we have active accounts
            active_accounts = self.account_manager.get_active_accounts()
            if not active_accounts:
                QMessageBox.warning(
                    self, 
                    "No Active Accounts", 
                    "No active accounts available. Please add and enable at least one account."
                )
                return
            
            # Clear results
            self.clear_results()
            
            # Reset counters
            self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0, 0)
            
            # Ask if we should resume from last progress
            resume_info = self.check_resume_option(group_links)
            
            # Get delay settings - default to 0 to disable delays
            min_seconds = self.settings.value("min_check_seconds", 0, type=int)
            max_seconds = self.settings.value("max_check_seconds", 0, type=int)
            
            # Update UI
            self.update_status_signal.emit(f"Starting to check {len(group_links)} groups...")
            self.start_checker_button.setEnabled(False)
            self.stop_checker_button.setEnabled(True)
            
            # Set running flag
            self.checker_running = True
            
            # Start the checker thread
            if resume_info:
                start_index = resume_info["index"]
                self.log_activity(f"Resuming from group #{start_index+1}")
                threading.Thread(
                    target=self._checker_thread, 
                    args=(group_links, start_index, min_seconds, max_seconds), 
                    daemon=True
                ).start()
            else:
                threading.Thread(
                    target=self._checker_thread, 
                    args=(group_links, 0, min_seconds, max_seconds), 
                    daemon=True
                ).start()
                
        except Exception as e:
            self.logger.error(f"Error starting checker: {str(e)}")
            self.update_status_signal.emit(f"Error: {str(e)}")
            self.start_checker_button.setEnabled(True)
            self.stop_checker_button.setEnabled(False)
            
    def get_group_links_from_input(self):
        """Extract group links from the input text area."""
        text = self.group_input.toPlainText().strip()
        if not text:
            return []
            
        # Split by newline and process each line
        lines = text.split('\n')
        links = []
        
        for line in lines:
            # Clean up the line
            line = line.strip()
            if not line:
                continue
                
            # Check if it's a Telegram link
            if "t.me/" in line or "telegram.me/" in line:
                # Extract just the link part if there's additional text
                match = re.search(r'(https?://)?(?:t|telegram)\.me/[a-zA-Z0-9_]+', line)
                if match:
                    link = match.group(0)
                    # Ensure it has https:// prefix
                    if not link.startswith('http'):
                        link = 'https://' + link
                    links.append(link)
                else:
                    # If no match found, just add the raw line
                    links.append(line)
            else:
                # If it's just a username without the domain, add the domain
                if line.startswith('@'):
                    line = line[1:]  # Remove @ symbol
                if re.match(r'^[a-zA-Z0-9_]+$', line):
                    links.append(f"https://t.me/{line}")
                else:
                    # Just add the raw line
                    links.append(line)
        
        # Remove duplicates while preserving order
        return list(dict.fromkeys(links))

    def check_resume_option(self, group_links):
        """Check if previous progress exists and ask the user if they want to resume. Returns start_index as integer."""
        try:
            progress_file = "progress.json"
            
            # Check if progress file exists
            if not os.path.exists(progress_file):
                return 0
                
            # Load progress data
            with open(progress_file, "r", encoding="utf-8") as f:
                progress_data = json.load(f)
                
            # Get saved progress info
            index = progress_data.get("index", 0)
            total = progress_data.get("total", 0)
            timestamp_str = progress_data.get("timestamp", "")
            saved_links = progress_data.get("links", [])
            
            # Check if the progress data is valid
            if index <= 0 or index >= total or not saved_links:
                # Invalid or completed progress, delete the file
                self._clear_progress()
                return 0
                
            # Check if current links match saved links
            if not group_links or len(group_links) != len(saved_links):
                # Different set of links, delete the progress file
                self._clear_progress()
                return 0
                
            # Format timestamp for display
            try:
                timestamp = datetime.fromisoformat(timestamp_str)
                formatted_time = timestamp.strftime("%Y-%m-%d %H:%M:%S")
            except:
                formatted_time = timestamp_str
                
            # Ask user if they want to resume
            reply = QMessageBox.question(
                self,
                "Resume Previous Session?",
                f"Found previous session:\n\n"
                f"• Progress: {index} / {total} groups checked\n"
                f"• Time: {formatted_time}\n"
                f"• Remaining: {total - index} groups\n\n"
                f"Do you want to resume from where you left off?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # User wants to resume
                self.log_activity(f"Resuming from group #{index+1}/{total}")
                return int(index)
            else:
                # User wants to start fresh
                self._clear_progress()
                return 0
                
        except Exception as e:
            self.logger.error(f"Error checking resume option: {str(e)}")
            self._clear_progress()
            return 0
    
    def _save_progress(self, index):
        """Save progress to file for potential resume later."""
        try:
            progress_file = "progress.json"
            
            # Create progress data
            progress_data = {
                "index": index,
                "total": len(self.current_group_links) if hasattr(self, 'current_group_links') else 0,
                "timestamp": datetime.now().isoformat(),
                "links": self.current_group_links if hasattr(self, 'current_group_links') else []
            }
            
            # Save to file
            with open(progress_file, "w", encoding="utf-8") as f:
                json.dump(progress_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Error saving progress: {str(e)}")
    
    def _clear_progress(self):
        """Delete the progress file."""
        try:
            progress_file = "progress.json"
            if os.path.exists(progress_file):
                os.remove(progress_file)
                self.logger.info("Progress file cleared")
        except Exception as e:
            self.logger.error(f"Error clearing progress file: {str(e)}")
    
    def clear_results(self):
        """Clear result counters and lists."""
        try:
            # Reset result counters
            self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0, 0)
            self.update_progress_signal.emit(0, 0)
            
            # Directly reset the tab texts in case the signal hasn't been processed yet
            self.results_tab_widget.setTabText(0, "Valid Filtered<br>0")
            self.results_tab_widget.setTabText(1, "Valid Only<br>0")
            self.results_tab_widget.setTabText(2, "Topics<br>0")
            self.results_tab_widget.setTabText(3, "Channels<br>0")
            self.results_tab_widget.setTabText(4, "Invalid<br>0")
            self.results_tab_widget.setTabText(5, "Account Issues<br>0")
            self.results_tab_widget.setTabText(6, "Join Requests<br>0")
            self.results_tab_widget.setTabText(7, "Total<br>0")
            
            # Clear result lists
            self.valid_filtered = set()
            self.valid_only = set()
            self.topics_groups = set()
            self.channels_only = set()
            self.invalid_groups = set()
            self.account_issues = set()
            self.join_requests = set()
            
            # Clear pending groups
            with self.account_states_lock:
                self.pending_groups_queue.clear()
            
            self.log_activity("Results cleared")
            
        except Exception as e:
            self.logger.error(f"Error clearing results: {str(e)}")

    def start_task_checker(self):
        """Start the task checker with the links from the input box."""
        try:
            # Get group links from input
            group_links = self.get_group_links_from_input()
            if not group_links:
                QMessageBox.warning(self, "No Links", "Please enter Telegram group/channel links to check.")
                return
            
            # Disable start button and enable stop button
            self.start_checker_button.setEnabled(False)
            self.stop_checker_button.setEnabled(True)
            
            # Initialize result lists if not already done
            self._initialize_result_lists()
            
            # Clear log display
            self.log_display.clear()
            
            # Check if we should resume from a saved position
            start_index = self.check_resume_option(group_links)
            
            # Log the starting message
            if start_index > 0:
                self.log_activity(f"Starting to check {len(group_links)} groups, resuming from index {start_index} ({start_index}/{len(group_links)})")
            else:
                self.log_activity(f"Starting to check {len(group_links)} groups from the beginning")
            
            # Start the checker thread
            self._start_checker_thread(group_links, start_index)
            
        except Exception as e:
            self.logger.error(f"Error starting task checker: {str(e)}")
            self.log_activity(f"Error starting task checker: {str(e)}")
            
            # Re-enable start button
            self.start_checker_button.setEnabled(True)
            self.stop_checker_button.setEnabled(False)

    def _task_reassignment_monitor(self):
        """Background thread that monitors account states and reassigns tasks if needed."""
        try:
            self.log_activity("Task reassignment monitor started")
            
            # Track the initial total groups for accurate progress reporting
            initial_total_groups = len(self.current_group_links) if hasattr(self, 'current_group_links') else 0
            
            while self.task_checker_running:
                # Track total pending groups across all queues
                total_pending_groups = 0
                accounts_working = False
                
                with self.account_states_lock:
                    # Count groups in the shared queue
                    total_pending_groups += len(self.pending_groups_queue)
                    
                    # Count groups in each account's specific queue
                    if hasattr(self, 'account_specific_queues'):
                        for phone, queue in self.account_specific_queues.items():
                            total_pending_groups += len(queue)
                    
                    # Check if any account is currently working
                    for phone, state in self.account_states.items():
                        if state.get("status") == "working":
                            accounts_working = True
                            # Count the current task that's being worked on
                            if state.get("current_task") is not None:
                                total_pending_groups += 1
                    
                    # Check if all tasks are completed OR all accounts are permanently unavailable
                    # Only complete if NO groups remain to be processed by ANY account
                    all_tasks_truly_completed = total_pending_groups == 0 and not accounts_working

                    # Find all account statuses
                    statuses = [state.get("status") for state in self.account_states.values()]
                    num_accounts = len(statuses)
                    num_floodwait = statuses.count("flood_wait")
                    num_banned = statuses.count("banned")
                    num_error = statuses.count("error")
                    num_available = statuses.count("available")
                    num_working = statuses.count("working")

                    # Only complete when ALL tasks are done OR all accounts are permanently failed (banned/error) and no available/working/flood_wait
                    all_accounts_permanently_unusable = (num_available + num_working + num_floodwait == 0) and num_accounts > 0

                    # --- STRONGER COMPLETION CHECK ---
                    # Before declaring completion, log the state of all queues
                    debug_pending = list(self.pending_groups_queue)
                    debug_account_queues = {}
                    if hasattr(self, 'account_specific_queues'):
                        for phone, queue in self.account_specific_queues.items():
                            debug_account_queues[phone] = list(queue)
                    if total_pending_groups == 0 and not accounts_working:
                        if debug_pending or any(debug_account_queues.values()):
                            self.log_activity(f"[DEBUG] Not all queues are empty! Shared queue: {len(debug_pending)}, Per-account queues: {[len(q) for q in debug_account_queues.values()]}")
                            self.log_activity(f"[DEBUG] Shared queue contents: {debug_pending}")
                            for phone, q in debug_account_queues.items():
                                if q:
                                    self.log_activity(f"[DEBUG] Account {phone} queue: {q}")
                            # Do NOT declare completion if any queue is not empty
                            continue
                    # --- END STRONGER COMPLETION CHECK ---

                    if all_tasks_truly_completed or (total_pending_groups > 0 and all_accounts_permanently_unusable):
                        if all_accounts_permanently_unusable and total_pending_groups > 0:
                            # Move remaining groups to account issues since no accounts can process them
                            with self.results_lock:
                                self.account_issues.extend(list(self.pending_groups_queue))
                                if hasattr(self, 'account_specific_queues'):
                                    for queue in self.account_specific_queues.values():
                                        self.account_issues.extend(list(queue))
                                # Clear all queues
                                self.pending_groups_queue.clear()
                                if hasattr(self, 'account_specific_queues'):
                                    for queue in self.account_specific_queues.values():
                                        queue.clear()
                            
                            self.log_activity(f"⚠️ All accounts permanently failed - {total_pending_groups} groups moved to Account Issues")
                            self.log_activity("✅ Task checker completed (all accounts failed)")
                        else:
                            self.log_activity("✅ All tasks completed successfully")
                        
                        self.task_checker_running = False
                        
                        # Remove any duplicates before saving results
                        self._remove_duplicates_from_all_results()
                        
                        # Auto-save all results to their respective files
                        with self.results_lock:
                            self.save_results_3tier(
                                self.valid_filtered, 
                                self.valid_only, 
                                self.topics_groups, 
                                self.channels_only, 
                                self.invalid_groups, 
                                self.account_issues, 
                                self.join_requests
                            )
                            
                            # Calculate processed groups
                            processed_groups = (
                                len(self.valid_filtered) + 
                                len(self.valid_only) + 
                                len(self.topics_groups) + 
                                len(self.channels_only) + 
                                len(self.invalid_groups) + 
                                len(self.account_issues) + 
                                len(self.join_requests)
                            )
                            
                            # Display a summary of what was checked
                            result_counts = (
                                f"Valid Filtered: {len(self.valid_filtered)}, "
                                f"Valid Only: {len(self.valid_only)}, "
                                f"Topics: {len(self.topics_groups)}, "
                                f"Channels: {len(self.channels_only)}, "
                                f"Invalid: {len(self.invalid_groups)}, "
                                f"Account Issues: {len(self.account_issues)}, "
                                f"Join Requests: {len(self.join_requests)}"
                            )
                            
                        self.log_activity(f"Results saved automatically: {result_counts}")
                        self.log_activity(f"Processed {processed_groups}/{initial_total_groups} groups (100%)")
                        
                        # Update UI on main thread
                        self.update_status_signal.emit("All groups checked and results saved successfully")
                        self.update_progress_signal.emit(initial_total_groups, initial_total_groups)  # Set to 100%
                        
                        # Re-enable start button on main thread
                        QMetaObject.invokeMethod(
                            self.start_checker_button, 
                            "setEnabled", 
                            Qt.QueuedConnection,
                            Q_ARG(bool, True)
                        )
                        
                        # Disable stop button on main thread
                        QMetaObject.invokeMethod(
                            self.stop_checker_button, 
                            "setEnabled", 
                            Qt.QueuedConnection,
                            Q_ARG(bool, False)
                        )
                        
                        break
                    # --- END FIX ---
                    
                    # Check if there are any active accounts that can process tasks
                    active_accounts = [phone for phone, state in self.account_states.items() 
                                      if state.get("status") in ["available", "working"]]
                    
                    # If we have pending tasks but no active accounts, check if they're all in FloodWait
                    if total_pending_groups > 0 and not active_accounts:
                        # Check if all accounts are in FloodWait
                        all_in_floodwait = all(
                            state.get("status") == "flood_wait"
                            for state in self.account_states.values()
                            if state.get("status") is not None  # Ignore accounts with None status
                        )
                        
                        if all_in_floodwait:
                            # All accounts are in FloodWait but we still have tasks
                            # Exit the lock before calling the wait function
                            pass
                    
                    # Check for stalled accounts (no activity for 5 minutes)
                    current_time = datetime.now()
                    for phone, state in list(self.account_states.items()):
                        last_activity = state.get("last_activity", datetime.now())
                        if (current_time - last_activity).total_seconds() > 300:  # 5 minutes
                            current_task = state.get("current_task")
                            if current_task and state.get("status") not in ["flood_wait", "banned"]:
                                self.log_activity(f"⚠️ Account {phone} appears stalled - reassigning task")
                                
                                # Add the task back to the queue
                                self.pending_groups_queue.appendleft(current_task)
                                
                                # Reset the account state
                                state["status"] = "error"
                                state["current_task"] = None
                                state["last_activity"] = current_time
                                state["errors"] = state.get("errors", 0) + 1
                
                # Update progress in UI
                with self.global_progress_lock:
                    groups_processed = initial_total_groups - total_pending_groups
                    if groups_processed >= 0 and initial_total_groups > 0:
                        percentage = (groups_processed / initial_total_groups) * 100
                        self.update_progress_signal.emit(groups_processed, initial_total_groups)
                        self.log_activity(f"Progress: {groups_processed}/{initial_total_groups} groups checked ({percentage:.1f}%)", 
                                         log_to_logger=False)  # Don't flood the logger
                
                # Check if all accounts are in FloodWait outside the lock
                all_in_floodwait = False
                remaining_groups = 0
                floodwait_status = {}
                
                with self.account_states_lock:
                    active_accounts = sum(1 for state in self.account_states.values() if state.get("status") is not None)
                    floodwait_accounts = sum(1 for state in self.account_states.values() if state.get("status") == "flood_wait")
                    remaining_groups = total_pending_groups
                    
                    # Collect FloodWait status for display
                    for phone, state in self.account_states.items():
                        if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                            until_time = state["flood_wait_until"]
                            if isinstance(until_time, datetime):
                                time_diff = (until_time - datetime.now()).total_seconds()
                                if time_diff > 0:
                                    floodwait_status[phone] = format_time_remaining(time_diff)
                    
                    # If we have active accounts and ALL of them are in FloodWait and we have pending tasks
                    if active_accounts > 0 and floodwait_accounts == active_accounts and remaining_groups > 0:
                        all_in_floodwait = True
                
                # If all accounts are in FloodWait and we have remaining tasks, pause and wait
                if all_in_floodwait and remaining_groups > 0:
                    # Double check if there are really no active accounts by counting them directly
                    active_account_count = 0
                    with self.account_states_lock:
                        for phone, state in self.account_states.items():
                            if state.get("status") in ["available", "working"]:
                                active_account_count += 1
                    
                    # Only pause if we confirmed there are no active accounts
                    if active_account_count == 0:
                        # Show the remaining groups count
                        self.log_activity(f"⏳ All accounts in FloodWait with {remaining_groups} groups remaining")
                        
                        # Display FloodWait status for each account
                        for phone, wait_time in floodwait_status.items():
                            self.log_activity(f"⏳ Account {phone} will be available in {wait_time}")
                        
                        # Display a clear message in the status bar
                        self.update_status_signal.emit(f"⏳ PAUSED: All accounts are rate-limited. Remaining groups: {remaining_groups}")
                        
                        # Pause and wait for accounts to become available
                        self._pause_and_wait_for_accounts()
                    else:
                        # Log the discrepancy - we thought all accounts were in FloodWait but some are active
                        self.log_activity(f"⚠️ Task monitor error: Found {active_account_count} active accounts despite all_in_floodwait=True")
                
                # Redistribute tasks if queues are imbalanced or accounts are in FloodWait
                active_account_phones = []
                floodwait_account_phones = []
                
                with self.account_states_lock:
                    # Identify active and FloodWait accounts
                    for phone, state in self.account_states.items():
                        if state.get("status") == "available":
                            active_account_phones.append(phone)
                        elif state.get("status") == "flood_wait":
                            floodwait_account_phones.append(phone)
                    
                    # If we have both FloodWait accounts and active accounts, redistribute tasks
                    if floodwait_account_phones and active_account_phones:
                        tasks_redistributed = 0
                        
                        # First, move tasks from FloodWait accounts' queues to the shared queue
                        for phone in floodwait_account_phones:
                            if hasattr(self, 'account_specific_queues') and phone in self.account_specific_queues:
                                remaining_tasks = list(self.account_specific_queues[phone])
                                if remaining_tasks:
                                    # Add tasks to shared queue
                                    self.pending_groups_queue.extendleft(reversed(remaining_tasks))
                                    tasks_redistributed += len(remaining_tasks)
                                    # Clear this account's queue
                                    self.account_specific_queues[phone].clear()
                        
                        # Then, distribute tasks from shared queue to active accounts
                        if self.pending_groups_queue and active_account_phones:
                            # Calculate how many tasks each active account should get
                            tasks_per_account = len(self.pending_groups_queue) // len(active_account_phones)
                            remainder = len(self.pending_groups_queue) % len(active_account_phones)
                            
                            if tasks_per_account > 0 or remainder > 0:
                                moved_tasks = 0
                                for i, phone in enumerate(active_account_phones):
                                    # Calculate how many tasks to move to this account
                                    account_tasks = tasks_per_account + (1 if i < remainder else 0)
                                    
                                    # Move tasks from shared queue to this account's queue
                                    for _ in range(account_tasks):
                                        if self.pending_groups_queue:
                                            task = self.pending_groups_queue.popleft()
                                            if phone not in self.account_specific_queues:
                                                self.account_specific_queues[phone] = deque()
                                            self.account_specific_queues[phone].append(task)
                                            moved_tasks += 1
                                
                                if moved_tasks > 0:
                                    self.log_activity(f"🔄 Redistributed {moved_tasks} tasks from FloodWait accounts to {len(active_account_phones)} active accounts")
                
                # Check if we have pending tasks but no thread is processing them
                if self.pending_groups_queue:
                    with self.account_states_lock:
                        # Count working accounts
                        working_accounts = sum(1 for state in self.account_states.values() 
                                              if state.get("status") == "working")
                        
                        # If no accounts are working but we have active accounts, restart threads
                        if working_accounts == 0 and active_account_phones:
                            for phone in active_account_phones:
                                if self.account_states[phone].get("status") == "available":
                                    # Start a new thread for this account
                                    threading.Thread(
                                        target=self._enhanced_account_task_thread,
                                        args=(phone, self.current_group_links),
                                        daemon=True
                                    ).start()
                                    self.log_activity(f"📋 Restarted task thread for idle account {phone}")
                
                # Sleep for a short time before checking again
                time.sleep(2)
                
                # If we have remaining tasks, make absolutely sure they get processed
                if hasattr(self, 'pending_groups_queue') and self.pending_groups_queue:
                    # Force a periodic check to ensure we don't get stuck
                    if int(time.time()) % 30 == 0:  # Every 30 seconds
                        with self.account_states_lock:
                            # Identify active and working accounts
                            active_accounts = [phone for phone, state in self.account_states.items() 
                                              if state.get("status") == "available"]
                            working_accounts = [phone for phone, state in self.account_states.items() 
                                               if state.get("status") == "working"]
                            
                            # If we have active accounts but no working accounts, restart processing
                            if active_accounts and not working_accounts:
                                self.log_activity(f"🔄 Found {len(active_accounts)} available accounts not processing tasks - restarting threads")
                                for phone in active_accounts:
                                    # Start a new thread for this account
                                    threading.Thread(
                                        target=self._enhanced_account_task_thread,
                                        args=(phone, self.current_group_links),
                                        daemon=True
                                    ).start()
                                    
                            # Log status for debugging
                            self.log_activity(f"Status check: {len(active_accounts)} available accounts, {len(working_accounts)} working accounts, {len(self.pending_groups_queue)} pending tasks", 
                                             log_to_logger=False)
            
            # --- FINAL COMPLETION CHECK (moved to end of monitor) ---
            if not self.task_checker_running:
                input_set = set(self.current_group_links) if hasattr(self, 'current_group_links') else set()
                processed_set = getattr(self, '_all_processed_groups', set())
                missing = input_set - processed_set
                self.log_activity(f"[SUMMARY] Processed: {len(processed_set)} / {len(input_set)}")
                if missing:
                    self.log_activity(f"[WARNING] {len(missing)} groups were not processed: {list(missing)}")
                    # Do NOT show completed in UI or set progress to 100%
                    self.update_status_signal.emit(f"[WARNING] Not all groups processed! {len(missing)} missing.")
                else:
                    self.log_activity(f"[SUCCESS] All groups processed!")
                    self.update_status_signal.emit("All groups checked and results saved successfully")
                    self.update_progress_signal.emit(len(input_set), len(input_set))  # Set to 100%
            
        except Exception as e:
            self.logger.error(f"Error in task reassignment monitor: {str(e)}")
            self.log_activity(f"Error in task monitor: {str(e)}")
            
        # Ensure we properly clean up even on exceptions
        self.task_checker_running = False
        self.update_status_signal.emit("Task checker stopped")
        
        # Re-enable start button on main thread
        QMetaObject.invokeMethod(
            self.start_task_checker_button, 
            "setEnabled", 
            Qt.QueuedConnection,
            Q_ARG(bool, True)
        )
        
        # Disable stop button on main thread
        QMetaObject.invokeMethod(
            self.stop_task_checker_button, 
            "setEnabled", 
            Qt.QueuedConnection,
            Q_ARG(bool, False)
        )

    def update_log_display(self):
        """Update the log display based on selected log type."""
        try:
            log_type = self.log_type_combo.currentText().lower()
            log_level = self.log_level_combo.currentText().lower()
            
            # Determine log file path
            log_file = "tg_checker.log"
            if log_type == "authentication":
                log_file = "auth.log"
            elif log_type == "usage checker":
                log_file = "usage_checker.log"
            
            # Read log file
            if os.path.exists(log_file):
                with open(log_file, "r", encoding="utf-8", errors="ignore") as f:
                    logs = f.readlines()
                    
                # Filter logs by level
                filtered_logs = []
                for log in logs:
                    if log_level == "all":
                        filtered_logs.append(log)
                    elif log_level == "info" and " - INFO - " in log:
                        filtered_logs.append(log)
                    elif log_level == "warning" and " - WARNING - " in log:
                        filtered_logs.append(log)
                    elif log_level == "error" and " - ERROR - " in log:
                        filtered_logs.append(log)
                    elif log_level == "critical" and " - CRITICAL - " in log:
                        filtered_logs.append(log)
                
                # Display logs (most recent at the top)
                self.log_display.clear()
                for log in reversed(filtered_logs[-1000:]):  # Show only last 1000 entries
                    self.log_display.append(log.strip())
            else:
                self.log_display.setText(f"Log file not found: {log_file}")
                
        except Exception as e:
            self.logger.error(f"Error updating log display: {str(e)}")
            self.log_display.setText(f"Error loading logs: {str(e)}")

    def _pause_and_wait_for_accounts(self):
        """Pause task checker when all accounts are in FloodWait and automatically resume when available."""
        try:
            self.log_activity("⏳ All accounts are in FloodWait - pausing task checker")
            
            # Find the total number of remaining groups
            remaining_groups = 0
            with self.account_states_lock:
                # Count groups in the shared queue
                remaining_groups += len(self.pending_groups_queue)
                
                # Count groups in each account's specific queue
                if hasattr(self, 'account_specific_queues'):
                    for phone, queue in self.account_specific_queues.items():
                        remaining_groups += len(queue)
                        
                # Count tasks currently being worked on
                for phone, state in self.account_states.items():
                    if state.get("current_task") is not None:
                        remaining_groups += 1
            
            # Update the UI with pause status
            self.update_status_signal.emit(f"⏳ PAUSED: All accounts are rate-limited. Remaining groups: {remaining_groups}")
            
            # Save current results
            with self.results_lock:
                self.save_results_3tier(
                    self.valid_filtered,
                    self.valid_only,
                    self.topics_groups,
                    self.channels_only,
                    self.invalid_groups,
                    self.account_issues,
                    self.join_requests
                )
            
            # Find the earliest time an account will be available
            earliest_available = None
            with self.account_states_lock:
                for phone, state in self.account_states.items():
                    if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                        until_time = state["flood_wait_until"]
                        if isinstance(until_time, datetime):
                            if earliest_available is None or until_time < earliest_available:
                                earliest_available = until_time
            
            # Calculate and log the wait time
            if earliest_available:
                time_diff = (earliest_available - datetime.now()).total_seconds()
                if time_diff > 0:
                    readable_time = format_time_remaining(time_diff)
                    self.log_activity(f"⏳ Auto-resume in approximately {readable_time}")
                    
                    # Update status bar
                    self.statusBar().showMessage(f"⏳ PAUSED: All accounts in FloodWait - auto-resume in {readable_time}")
                    
                    # Add a manual resume button
                    QMetaObject.invokeMethod(
                        self.start_task_checker_button, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, "Resume Checking")
                    )
                    QMetaObject.invokeMethod(
                        self.start_task_checker_button, 
                        "setEnabled", 
                        Qt.QueuedConnection,
                        Q_ARG(bool, True)
                    )
                    
                    # Loop until at least one account becomes available
                    while self.task_checker_running:
                        time.sleep(2)  # Check every 2 seconds
                        
                        # Check if any account is now available
                        any_available = False
                        with self.account_states_lock:
                            current_time = datetime.now()
                            for phone, state in self.account_states.items():
                                if state.get("status") == "flood_wait" and "flood_wait_until" in state:
                                    until_time = state["flood_wait_until"]
                                    if isinstance(until_time, datetime) and current_time >= until_time:
                                        # FloodWait expired, reset state
                                        state["status"] = "available"
                                        state["flood_wait_until"] = None
                                        any_available = True
                                        self.log_activity(f"✅ Account {phone} FloodWait expired - now available")
                                        
                                        # Also update in database
                                        self.account_manager.update_account_status(phone, "active")
                                elif state.get("status") == "available":
                                    any_available = True
                        
                        # If at least one account is available, break the wait loop
                        if any_available:
                            break
                            
                        # Update the wait time display every 30 seconds
                        if int(time.time()) % 30 == 0:
                            current_time_diff = (earliest_available - datetime.now()).total_seconds()
                            if current_time_diff > 0:
                                current_readable_time = format_time_remaining(current_time_diff)
                                self.statusBar().showMessage(f"⏳ PAUSED: All accounts in FloodWait - auto-resume in {current_readable_time}")
                                
                                # Update status message with remaining groups
                                self.update_status_signal.emit(f"⏳ PAUSED: All accounts are rate-limited. Remaining groups: {remaining_groups} - Auto-resume in {current_readable_time}")
            
            # Reset the start button text
            QMetaObject.invokeMethod(
                self.start_task_checker_button, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, "Start Checking")
            )
            
            # Check if the task checker is still running
            if self.task_checker_running:
                self.log_activity("✅ At least one account is now available - auto-resuming task checker")
                self.update_status_signal.emit(f"✅ Auto-resumed: Account(s) now available - Remaining groups: {remaining_groups}")
                
                # Restart task threads for available accounts
                with self.account_states_lock:
                    for phone, state in self.account_states.items():
                        if state.get("status") == "available":
                            threading.Thread(
                                target=self._enhanced_account_task_thread,
                                args=(phone, self.current_group_links),
                                daemon=True
                            ).start()
                            self.log_activity(f"Restarted task thread for account {phone}")
            
        except Exception as e:
            self.logger.error(f"Error in pause and wait: {str(e)}")
            self.log_activity(f"Error in pause and wait: {str(e)}")
            
            # Ensure task checker can still resume
            if self.task_checker_running:
                self.update_status_signal.emit("Attempting to resume after error")
                
                # Try to restart task threads for any accounts
                with self.account_states_lock:
                    for phone, state in self.account_states.items():
                        threading.Thread(
                            target=self._enhanced_account_task_thread,
                            args=(phone, self.current_group_links),
                            daemon=True
                        ).start()
                        self.log_activity(f"Emergency restart of task thread for account {phone}")

    def save_results_3tier(self, valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests):
        """Save results to files with appropriate organization structure."""
        try:
            # If saving instance results, ensure they don't have duplicates
            if valid_filtered is self.valid_filtered or isinstance(valid_filtered, set) and len(valid_filtered) == 0:
                self._remove_duplicates_from_all_results()
                valid_filtered = self.valid_filtered
                valid_only = self.valid_only
                topics_groups = self.topics_groups
                channels_only = self.channels_only
                invalid_groups = self.invalid_groups
                account_issues = self.account_issues
                join_requests = self.join_requests
            
            # Create results directory if it doesn't exist
            results_dir = "Results"
            os.makedirs(results_dir, exist_ok=True)
            
            # Create timestamp for the results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Create subdirectories for different result types using the specified category names
            valid_filtered_dir = os.path.join(results_dir, "Groups_Valid_Filter")
            valid_only_dir = os.path.join(results_dir, "Groups_Valid_Only")
            topics_dir = os.path.join(results_dir, "Topics_Groups_Only_Valid")
            channels_dir = os.path.join(results_dir, "Channels_Only_Valid")
            invalid_dir = os.path.join(results_dir, "Invalid_Groups_Channels")
            account_issues_dir = os.path.join(results_dir, "Account_Issues")
            join_requests_dir = os.path.join(results_dir, "JoinRequest")
            
            # Create all directories
            os.makedirs(valid_filtered_dir, exist_ok=True)
            os.makedirs(valid_only_dir, exist_ok=True)
            os.makedirs(topics_dir, exist_ok=True)
            os.makedirs(channels_dir, exist_ok=True)
            os.makedirs(invalid_dir, exist_ok=True)
            os.makedirs(account_issues_dir, exist_ok=True)
            os.makedirs(join_requests_dir, exist_ok=True)
            
            # Ensure all results are sets to remove duplicates
            valid_filtered_set = set(valid_filtered)
            valid_only_set = set(valid_only)
            topics_groups_set = set(topics_groups)
            channels_only_set = set(channels_only)
            invalid_groups_set = set(invalid_groups)
            account_issues_set = set(account_issues)
            join_requests_set = set(join_requests)
            
            # Convert to sorted lists for consistent output
            valid_filtered_list = sorted(list(valid_filtered_set))
            valid_only_list = sorted(list(valid_only_set))
            topics_groups_list = sorted(list(topics_groups_set))
            channels_only_list = sorted(list(channels_only_set))
            invalid_groups_list = sorted(list(invalid_groups_set))
            account_issues_list = sorted(list(account_issues_set))
            join_requests_list = sorted(list(join_requests_set))
            
            # Save valid filtered groups
            valid_filtered_path = os.path.join(valid_filtered_dir, f"valid_filtered_{timestamp}.txt")
            with open(valid_filtered_path, "w", encoding="utf-8") as f:
                f.write("🔹 Groups Valid_Filter: These are valid groups that passed the active filter\n")
                f.write("- At least 500 members\n")
                f.write("- At least 100 messages\n")
                f.write("- Activity within 1 hour\n\n")
                if valid_filtered_list:
                    for link in valid_filtered_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No valid filtered groups found\n")
            
            # Save valid only groups
            valid_only_path = os.path.join(valid_only_dir, f"valid_only_{timestamp}.txt")
            with open(valid_only_path, "w", encoding="utf-8") as f:
                f.write("🔹 Groups Valid Only: Valid and working groups that don't meet filter criteria or when filter is OFF\n\n")
                if valid_only_list:
                    for link in valid_only_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No valid unfiltered groups found\n")
            
            # Save topics groups
            topics_path = os.path.join(topics_dir, f"topics_{timestamp}.txt")
            with open(topics_path, "w", encoding="utf-8") as f:
                f.write("🔹 Topics Groups Only Valid: Valid topic-based Telegram groups\n\n")
                if topics_groups_list:
                    for link in topics_groups_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No topics groups found\n")
            
            # Save channels
            channels_path = os.path.join(channels_dir, f"channels_{timestamp}.txt")
            with open(channels_path, "w", encoding="utf-8") as f:
                f.write("🔹 Channels Only Valid: Valid Telegram channels (not groups)\n\n")
                if channels_only_list:
                    for link in channels_only_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No channels found\n")
            
            # Save invalid groups
            invalid_path = os.path.join(invalid_dir, f"invalid_{timestamp}.txt")
            with open(invalid_path, "w", encoding="utf-8") as f:
                f.write("🔹 Invalid Groups/Channels: Dead, broken, restricted, porn, banned, or inaccessible\n\n")
                if invalid_groups_list:
                    for link in invalid_groups_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No invalid groups found\n")
            
            # Save account issues
            account_issues_path = os.path.join(account_issues_dir, f"account_issues_{timestamp}.txt")
            with open(account_issues_path, "w", encoding="utf-8") as f:
                f.write("🔹 Account Issues: Banned accounts, FloodWait, login issues, or API errors\n\n")
                if account_issues_list:
                    for link in account_issues_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No account issues found\n")
            
            # Save join requests
            join_requests_path = os.path.join(join_requests_dir, f"join_requests_{timestamp}.txt")
            with open(join_requests_path, "w", encoding="utf-8") as f:
                f.write("🔹 JoinRequest: Groups that require approval to join\n\n")
                if join_requests_list:
                    for link in join_requests_list:
                        f.write(f"{link}\n")
                else:
                    f.write("# No join requests found\n")
            
            # Create a summary file
            summary_path = os.path.join(results_dir, f"summary_{timestamp}.txt")
            with open(summary_path, "w", encoding="utf-8") as f:
                f.write("=== TG CHECKER RESULTS SUMMARY ===\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                f.write(f"🔹 Groups Valid_Filter: {len(valid_filtered_set)}\n")
                f.write(f"🔹 Groups Valid Only: {len(valid_only_set)}\n")
                f.write(f"🔹 Topics Groups Only Valid: {len(topics_groups_set)}\n")
                f.write(f"🔹 Channels Only Valid: {len(channels_only_set)}\n")
                f.write(f"🔹 Invalid Groups/Channels: {len(invalid_groups_set)}\n")
                f.write(f"🔹 Account Issues: {len(account_issues_set)}\n")
                f.write(f"🔹 JoinRequest: {len(join_requests_set)}\n\n")
                f.write(f"Total Processed: {len(valid_filtered_set) + len(valid_only_set) + len(topics_groups_set) + len(channels_only_set) + len(invalid_groups_set) + len(account_issues_set) + len(join_requests_set)}\n")
            
            self.log_activity(f"Results saved to {results_dir} with timestamp {timestamp}")
            return True
        except Exception as e:
            self.logger.error(f"Error saving results: {str(e)}")
            self.log_activity(f"Error saving results: {str(e)}")
            return False

    def _checker_thread(self, group_links, start_index=0, min_seconds=0, max_seconds=0):
        """Background thread for checking groups/channels."""
        try:
            self.log_activity(f"Starting to check {len(group_links)} groups (starting from #{start_index+1})")
            
            # Store links for this session
            self.current_group_links = group_links
            
            # Initialize result sets to automatically remove duplicates
            valid_filtered = set()
            valid_only = set()
            topics_groups = set()
            channels_only = set()
            invalid_groups = set()
            account_issues = set()
            join_requests = set()
            
            # Store current progress
            self.current_group_index = start_index
            self.total_groups = len(group_links)
            
            # Update progress
            self.update_progress_signal.emit(self.current_group_index, self.total_groups)
            
            # Process each group/channel
            for i in range(start_index, len(group_links)):
                # Check if we should stop
                if self.checker_should_stop:
                    self.log_activity("Checker stopped by user")
                    break
                
                # Get the current group link
                link = group_links[i]
                
                # Update current index
                self.current_group_index = i
                
                # Update progress
                self.update_progress_signal.emit(i, self.total_groups)
                
                # Update currently analyzing label
                self.update_analyzing_signal.emit(f"Checking: {link}")
                
                # Check the group/channel
                self.log_activity(f"Checking group: {link}")
                result = self.check_group_or_channel(link)
                
                # Process result
                if result.get("valid", False):
                    if "type" in result:
                        result_type = result["type"]
                        if result_type == "channel":
                            channels_only.add(link)
                            self.log_activity(f"📢 Channel Only Valid: {link}")
                        elif result_type == "topic":
                            topics_groups.add(link)
                            self.log_activity(f"🗨️ Topic Group Only Valid: {link}")
                        elif result_type == "valid_filtered":
                            valid_filtered.add(link)
                            self.log_activity(f"✅ Group Valid_Filter: {link}")
                        elif result_type == "valid_only":
                            valid_only.add(link)
                            self.log_activity(f"✓ Group Valid Only: {link}")
                        else:
                            # Default to valid only for unknown types
                            valid_only.add(link)
                            self.log_activity(f"✓ Group Valid Only (Unknown Type): {link}")
                    else:
                        # For backwards compatibility
                        valid_only.add(link)
                        self.log_activity(f"✓ Group Valid Only: {link}")
                else:
                    # Check result type for invalid cases
                    result_type = result.get("type", "unknown")
                    
                    if result_type == "join_request":
                        join_requests.add(link)
                        self.log_activity(f"🔒 JoinRequest: {link}")
                    elif result_type == "account_issue":
                        account_issues.add(link)
                        self.log_activity(f"⚠️ Account Issue: {link} - {result.get('reason', 'Unknown issue')}")
                        
                        # Check if we need to schedule a retry due to FloodWait
                        wait_seconds = result.get("wait_seconds", 0)
                        if wait_seconds > 0:
                            # Get the phone from the result if available
                            account_phone = result.get("phone")
                            if account_phone:
                                with self.account_states_lock:
                                    if account_phone in self.account_states:
                                        self.account_states[account_phone]["status"] = "flood_wait"
                                        self.account_states[account_phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                
                                # Update database status
                                self.account_manager.update_account_status(account_phone, f"limited_floodwait_{wait_seconds}")
                                
                                # Schedule a retry
                                self._schedule_account_retry(account_phone, wait_seconds)
                                
                                # Log the FloodWait
                                readable_time = format_time_remaining(wait_seconds)
                                self.log_activity(f"⏳ FloodWait detected - pausing for {readable_time}")
                                self.update_status_signal.emit(f"⏳ PAUSED: FloodWait for {readable_time}")
                                
                                # Save the current progress
                                self._save_progress(i)
                                
                                # Save any results collected so far
                                self.save_results_3tier(
                                    valid_filtered, 
                                    valid_only, 
                                    topics_groups,
                                    channels_only,
                                    invalid_groups,
                                    account_issues,
                                    join_requests
                                )
                                
                                # Pause the checking
                                break
                    else:
                        invalid_groups.add(link)
                        self.log_activity(f"❌ Invalid Group/Channel: {link} - {result.get('reason', 'Unknown error')}")
                
                # Update result counts for the UI
                self.update_result_counts_signal.emit(
                    len(valid_filtered),
                    len(valid_only),
                    len(topics_groups),
                    len(channels_only),
                    len(invalid_groups),
                    len(account_issues),
                    len(join_requests)
                )
                
                # Save progress in case of interruption
                self._save_progress(i)
                
                # Wait between checks if specified
                if i < len(group_links) - 1 and max_seconds > 0:
                    if min_seconds == max_seconds:
                        # Fixed wait time
                        wait_time = min_seconds
                    else:
                        # Random wait time between min and max
                        wait_time = random.uniform(min_seconds, max_seconds)
                    
                    # Update status to show waiting
                    self.update_status_signal.emit(f"Waiting {wait_time:.1f} seconds before next check...")
                    
                    # Wait with cancellation support
                    wait_start = time.time()
                    while time.time() - wait_start < wait_time:
                        if self.checker_should_stop:
                            break
                        time.sleep(0.1)
                    
                    if self.checker_should_stop:
                        self.log_activity("Checker stopped during wait period")
                        break
            
            # Update final status
            if not self.checker_should_stop:
                self.update_status_signal.emit("Checking completed")
                self.log_activity("Checking completed")
                self._clear_progress()
            else:
                self.update_status_signal.emit("Checking stopped by user")
                self.log_activity("Checking stopped by user")
            
            # Save final results
            self.save_results_3tier(valid_filtered, valid_only, topics_groups, channels_only,
                                   invalid_groups, account_issues, join_requests)
            
            # Set checker running flag to False
            self.checker_running = False
            
            # Clear stop flag for next run
            self.checker_should_stop = False
            
        except Exception as e:
            self.logger.error(f"Error in checker thread: {str(e)}")
            self.update_status_signal.emit(f"Error: {str(e)}")
            self.log_activity(f"Error in checker thread: {str(e)}")
            
            # Set checker running flag to False
            self.checker_running = False
            
            # Clear stop flag for next run
            self.checker_should_stop = False

    def _remove_duplicates_from_all_results(self):
        """Ensure all result collections have no duplicates by converting to sets."""
        with self.results_lock:
            # Make sure all results are sets
            if not isinstance(self.valid_filtered, set):
                self.valid_filtered = set(self.valid_filtered)
            if not isinstance(self.valid_only, set):
                self.valid_only = set(self.valid_only)
            if not isinstance(self.topics_groups, set):
                self.topics_groups = set(self.topics_groups)
            if not isinstance(self.channels_only, set):
                self.channels_only = set(self.channels_only)
            if not isinstance(self.invalid_groups, set):
                self.invalid_groups = set(self.invalid_groups)
            if not isinstance(self.account_issues, set):
                self.account_issues = set(self.account_issues)
            if not isinstance(self.join_requests, set):
                self.join_requests = set(self.join_requests)

    def import_remaining_links(self):
        """Import remaining links from a saved file."""
        try:
            # Ask user to select file
            file_dialog = QFileDialog()
            file_dialog.setNameFilter("Text files (*.txt)")
            file_dialog.setWindowTitle("Select Remaining Links File")
            
            if file_dialog.exec_():
                selected_files = file_dialog.selectedFiles()
                if not selected_files:
                    return
                    
                filepath = selected_files[0]
                
                # Read the file
                with open(filepath, "r", encoding="utf-8") as f:
                    content = f.read()
                
                # Extract links
                links = []
                for line in content.split("\n"):
                    line = line.strip()
                    # Skip comments and empty lines
                    if not line or line.startswith("#"):
                        continue
                    links.append(line)
                
                if not links:
                    QMessageBox.warning(self, "Warning", "No valid links found in the file")
                    return
                
                # Clear the current input field
                self.group_input.clear()
                
                # Add the links to the input field
                self.group_input.setPlainText("\n".join(links))
                
                # Show confirmation
                QMessageBox.information(
                    self, 
                    "Links Imported", 
                    f"Successfully imported {len(links)} links from remaining links file.\n\n"
                    f"Click 'Start Checking' to resume checking these links."
                )
                
                self.log_activity(f"Imported {len(links)} remaining links from {os.path.basename(filepath)}")
                
        except Exception as e:
            self.logger.error(f"Error importing remaining links: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error importing remaining links: {str(e)}")

    def _retry_account_issues(self):
        """Retry checking groups that had account issues with available accounts."""
        # Collect all groups from account issues
        with self.results_lock:
            if not hasattr(self, 'account_issues') or not self.account_issues:
                self.log_activity("No account issues to retry.")
                return
            
            groups_to_retry = self.account_issues.copy()
            self.account_issues.clear()  # Clear account issues since we're retrying
        
        # Add any pending groups from queues
        with self.account_states_lock:
            if hasattr(self, 'pending_groups_queue'):
                groups_to_retry.extend(list(self.pending_groups_queue))
            
            if hasattr(self, 'account_specific_queues'):
                for phone, queue in self.account_specific_queues.items():
                    groups_to_retry.extend(list(queue))
        
        # Remove duplicates
        groups_to_retry = list(dict.fromkeys(groups_to_retry))
        
        if not groups_to_retry:
            self.log_activity("No groups to retry.")
            return
        
        # Resume checking with these groups
        self.log_activity(f"🔄 Retrying {len(groups_to_retry)} groups with available accounts.")
        
        # Store the groups in the input box so start_task_checker can find them
        self.group_input.setText("\n".join(groups_to_retry))
        
        # Start the checker using the standard function
        self.start_task_checker()

    def import_links(self):
        """Import links from a file."""
        try:
            file_path, _ = QFileDialog.getOpenFileName(self, "Import Links", "", "Text Files (*.txt);;All Files (*)")
            if file_path:
                with open(file_path, 'r', encoding='utf-8') as file:
                    links = file.readlines()
                
                # Filter out empty lines and comments
                links = [link.strip() for link in links if link.strip() and not link.strip().startswith('#')]
                
                # Set the links
                self.group_input.setText('\n'.join(links))
                self.log_activity(f"Imported {len(links)} links from {file_path}")
        except Exception as e:
            self.log_activity(f"Error importing links: {str(e)}")
            QMessageBox.warning(self, "Import Error", f"Error importing links: {str(e)}")
    
    def export_links(self):
        """Export links to a file."""
        try:
            links = self.get_group_links_from_input()
            if not links:
                QMessageBox.warning(self, "Export Error", "No links to export.")
                return
                
            file_path, _ = QFileDialog.getSaveFileName(self, "Export Links", "", "Text Files (*.txt);;All Files (*)")
            if file_path:
                with open(file_path, 'w', encoding='utf-8') as file:
                    for link in links:
                        file.write(f"{link}\n")
                
                self.log_activity(f"Exported {len(links)} links to {file_path}")
        except Exception as e:
            self.log_activity(f"Error exporting links: {str(e)}")
            QMessageBox.warning(self, "Export Error", f"Error exporting links: {str(e)}")
    
    def import_remaining_links(self):
        """Import remaining links from the log file."""
        try:
            remaining_path = os.path.join(self.data_dir, "pending_groups.txt")
            if not os.path.exists(remaining_path):
                QMessageBox.information(self, "Import Remaining", "No remaining groups file found.")
                return
                
            with open(remaining_path, 'r', encoding='utf-8') as file:
                links = file.readlines()
            
            # Filter out empty lines and comments
            links = [link.strip() for link in links if link.strip() and not link.strip().startswith('#')]
            
            if not links:
                QMessageBox.information(self, "Import Remaining", "No remaining links found in file.")
                return
                
            # Set the links
            self.group_input.setText('\n'.join(links))
            self.log_activity(f"Imported {len(links)} remaining links")
            
        except Exception as e:
            self.log_activity(f"Error importing remaining links: {str(e)}")
            QMessageBox.critical(self, "Error", f"Error importing remaining links: {str(e)}")

    def _initialize_result_lists(self):
        """Initialize or clear result lists."""
        # Initialize result containers if not already done
        if not hasattr(self, 'valid_filtered'):
            self.valid_filtered = []
        if not hasattr(self, 'valid_only'):
            self.valid_only = []
        if not hasattr(self, 'topics_groups'):
            self.topics_groups = []
        if not hasattr(self, 'channels_only'):
            self.channels_only = []
        if not hasattr(self, 'invalid_groups'):
            self.invalid_groups = []
        if not hasattr(self, 'account_issues'):
            self.account_issues = []
        if not hasattr(self, 'join_requests'):
            self.join_requests = []
        
        # Clear result lists
        self.valid_filtered.clear()
        self.valid_only.clear()
        self.topics_groups.clear()
        self.channels_only.clear()
        self.invalid_groups.clear()
        self.account_issues.clear()
        self.join_requests.clear()
        
        # Reset counters
        self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0, 0)
    
    def _start_checker_thread(self, group_links, start_index=0):
        """Start the checker thread with multiple accounts."""
        try:
            # Ensure start_index is an integer
            if start_index is None:
                start_index = 0
            else:
                start_index = int(start_index)
            
            # Store the links for this session
            self.current_group_links = group_links
            
            # Check if we have active accounts
            active_accounts = self.account_manager.get_active_accounts()
            if not active_accounts:
                QMessageBox.warning(
                    self, 
                    "No Active Accounts", 
                    "No active accounts available. Please add and enable at least one account."
                )
                self.start_checker_button.setEnabled(True)
                self.stop_checker_button.setEnabled(False)
                return
            
            # If only one account is enabled, use the single-account checker method
            if len(active_accounts) == 1:
                self.log_activity(f"Only one account is enabled, using single-account checker")
                account = active_accounts[0]
                phone = account.get("phone")
                
                # Initialize account state
                with self.account_states_lock:
                    self.account_states[phone] = {
                        "status": "available",
                        "current_task": None,
                        "last_activity": datetime.now(),
                        "errors": 0
                    }
                
                # Use the regular checker thread for a single account
                self.update_status_signal.emit(f"Starting checker with account {phone}...")
                
                min_seconds = self.settings.value("min_check_seconds", 0, type=int)
                max_seconds = self.settings.value("max_check_seconds", 0, type=int)
                
                threading.Thread(
                    target=self._checker_thread, 
                    args=(group_links, start_index, min_seconds, max_seconds), 
                    daemon=True
                ).start()
                
                return
            
            # For multiple accounts, distribute the groups evenly
            # Initialize the shared task queue and account-specific queues
            num_accounts = len(active_accounts)
            
            # Create account-specific queues
            self.account_specific_queues = {}
            self.pending_groups_queue = deque()  # Shared queue for any failed tasks
            
            # If start_index > 0, we need to handle that (resuming)
            if start_index > 0:
                # Only distribute tasks starting from start_index
                remaining_groups = group_links[start_index:]
            else:
                remaining_groups = group_links
            
            # Distribute remaining tasks evenly
            tasks_per_account = len(remaining_groups) // num_accounts
            remainder = len(remaining_groups) % num_accounts
            
            start_idx = 0
            for i, account in enumerate(active_accounts):
                phone = account.get("phone")
                # Calculate how many tasks this account should get
                account_tasks = tasks_per_account + (1 if i < remainder else 0)
                end_idx = start_idx + account_tasks
                
                # Assign tasks to this account
                if end_idx > start_idx:
                    account_links = remaining_groups[start_idx:end_idx]
                    self.account_specific_queues[phone] = deque(account_links)
                    self.log_activity(f"Assigned {len(account_links)} groups to account {phone}")
                    start_idx = end_idx
                
                # Initialize account state
                with self.account_states_lock:
                    if phone not in self.account_states:
                        self.account_states[phone] = {
                            "status": "available",
                            "current_task": None,
                            "last_activity": datetime.now(),
                            "errors": 0
                        }
                    else:
                        # Reset state for current task
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["current_task"] = None
                        self.account_states[phone]["last_activity"] = datetime.now()
                        self.account_states[phone]["errors"] = 0
            
            # Set checker running state
            self.task_checker_running = True
            
            # Update UI
            self.update_status_signal.emit(f"Starting distributed task checker with {len(active_accounts)} accounts...")
            
            # Start the task reassignment monitor thread
            threading.Thread(target=self._task_reassignment_monitor, daemon=True).start()
            
            # Start a task thread for each account
            for account in active_accounts:
                phone = account.get("phone")
                threading.Thread(
                    target=self._enhanced_account_task_thread,
                    args=(phone, group_links),
                    daemon=True
                ).start()
                
            self.log_activity(f"Started distributed task checker with {len(active_accounts)} accounts for {len(group_links)} groups")
            
        except Exception as e:
            self.logger.error(f"Error starting checker thread: {str(e)}")
            self.log_activity(f"Error starting checker thread: {str(e)}")
            
            # Reset UI state
            self.start_checker_button.setEnabled(True)
            self.stop_checker_button.setEnabled(False)

    def fix_limited_status_for_account(self, phone=None):
        """Force fix the limited status for a specific account or all accounts."""
        try:
            if phone:
                # Fix a specific account
                self.log_activity(f"Fixing limited status for account {phone}...")
                self.account_manager.update_account_status(phone, "active")
                
                # Update in-memory state
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                
                self.log_activity(f"✅ Reset status for account {phone} to Active")
            else:
                # Fix all accounts
                self.log_activity("Fixing limited status for all accounts...")
                accounts = self.account_manager.get_accounts()
                for account in accounts:
                    account_phone = account.get("phone")
                    if account_phone:
                        # Update status in database
                        self.account_manager.update_account_status(account_phone, "active")
                        
                        # Update in-memory state
                        with self.account_states_lock:
                            if account_phone in self.account_states:
                                self.account_states[account_phone]["status"] = "available"
                                self.account_states[account_phone]["flood_wait_until"] = None
                        
                        self.log_activity(f"✅ Reset status for account {account_phone} to Active")
            
            # Update UI
            self.update_ui_signal.emit()
            return True
        except Exception as e:
            self.logger.error(f"Error fixing limited status: {str(e)}")
            self.log_activity(f"Error fixing limited status: {str(e)}")
            return False

    def create_forwarder_tab(self):
        """Create the forwarder tab with task management system."""
        forwarder_tab = QWidget()
        forwarder_layout = QVBoxLayout(forwarder_tab)
        
        # Task Management Section
        task_group = QGroupBox("Task Management")
        task_layout = QVBoxLayout(task_group)
        
        # Task list with details
        self.task_table = QTableWidget()
        self.task_table.setColumnCount(6)
        self.task_table.setHorizontalHeaderLabels(["Task Name", "Account", "Status", "Progress", "Message", "Actions"])
        self.task_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.task_table.horizontalHeader().setSectionResizeMode(5, QHeaderView.Fixed)
        self.task_table.setColumnWidth(5, 250)
        task_layout.addWidget(self.task_table)
        
        # Task control buttons
        task_buttons_layout = QHBoxLayout()
        self.create_task_button = QPushButton("Create New Task")
        self.start_all_tasks_button = QPushButton("Start All Tasks")
        self.stop_all_tasks_button = QPushButton("Stop All Tasks")
        self.continue_all_tasks_button = QPushButton("Continue All Tasks")
        
        self.create_task_button.clicked.connect(self.create_new_task)
        self.start_all_tasks_button.clicked.connect(self.start_all_forwarding_tasks)
        self.stop_all_tasks_button.clicked.connect(self.stop_all_forwarding_tasks)
        self.continue_all_tasks_button.clicked.connect(self.continue_all_forwarding_tasks)
        
        task_buttons_layout.addWidget(self.create_task_button)
        task_buttons_layout.addWidget(self.start_all_tasks_button)
        task_buttons_layout.addWidget(self.stop_all_tasks_button)
        task_buttons_layout.addWidget(self.continue_all_tasks_button)
        task_layout.addLayout(task_buttons_layout)
        
        forwarder_layout.addWidget(task_group)
        
        # Task Details Section
        details_group = QGroupBox("Task Details")
        details_layout = QVBoxLayout(details_group)
        
        # No task selected initially
        self.task_details_placeholder = QLabel("Select a task to view details")
        self.task_details_placeholder.setAlignment(Qt.AlignCenter)
        details_layout.addWidget(self.task_details_placeholder)
        
        # Create a stacked widget to switch between placeholder and actual details
        self.task_details_stack = QWidget()
        self.task_details_layout = QVBoxLayout(self.task_details_stack)
        
        # Target groups list
        groups_label = QLabel("Target Groups/Channels:")
        self.task_groups_list = QTextEdit()
        self.task_groups_list.setReadOnly(True)
        self.task_groups_list.setMaximumHeight(150)
        self.task_details_layout.addWidget(groups_label)
        self.task_details_layout.addWidget(self.task_groups_list)
        
        # Add more groups section
        add_groups_layout = QHBoxLayout()
        self.add_groups_input = QLineEdit()
        self.add_groups_input.setPlaceholderText("Add more target groups/channels (comma separated)")
        self.add_groups_button = QPushButton("Add Groups")
        self.add_groups_button.clicked.connect(self.add_groups_to_task)
        add_groups_layout.addWidget(self.add_groups_input)
        add_groups_layout.addWidget(self.add_groups_button)
        self.task_details_layout.addLayout(add_groups_layout)
        
        # Change message section
        change_message_layout = QHBoxLayout()
        self.change_message_input = QLineEdit()
        self.change_message_input.setPlaceholderText("Change message link to forward")
        self.change_message_button = QPushButton("Change Message")
        self.change_message_button.clicked.connect(self.change_task_message)
        change_message_layout.addWidget(self.change_message_input)
        change_message_layout.addWidget(self.change_message_button)
        self.task_details_layout.addLayout(change_message_layout)
        
        # Task control buttons
        task_control_layout = QHBoxLayout()
        self.start_task_button = QPushButton("Start Task")
        self.stop_task_button = QPushButton("Stop Task")
        self.continue_task_button = QPushButton("Continue Task")
        self.delete_task_button = QPushButton("Delete Task")
        
        self.start_task_button.clicked.connect(self.start_selected_task)
        self.stop_task_button.clicked.connect(self.stop_selected_task)
        self.continue_task_button.clicked.connect(self.continue_selected_task)
        self.delete_task_button.clicked.connect(self.delete_selected_task)
        
        task_control_layout.addWidget(self.start_task_button)
        task_control_layout.addWidget(self.stop_task_button)
        task_control_layout.addWidget(self.continue_task_button)
        task_control_layout.addWidget(self.delete_task_button)
        self.task_details_layout.addLayout(task_control_layout)
        
        # Hide task details initially
        self.task_details_stack.hide()
        details_layout.addWidget(self.task_details_stack)
        
        forwarder_layout.addWidget(details_group)
        
        # Log Section
        log_group = QGroupBox("Forwarding Logs")
        log_layout = QVBoxLayout(log_group)
        
        self.forwarding_log = QTextEdit()
        self.forwarding_log.setReadOnly(True)
        log_layout.addWidget(self.forwarding_log)
        
        forwarder_layout.addWidget(log_group)
        
        # Global Settings Section
        settings_group = QGroupBox("Global Settings")
        settings_layout = QFormLayout(settings_group)
        
        self.use_random_sleep = QCheckBox()
        self.use_random_sleep.setChecked(True)
        settings_layout.addRow("Use Random Sleep Time:", self.use_random_sleep)
        
        self.log_failed_groups = QCheckBox()
        self.log_failed_groups.setChecked(True)
        settings_layout.addRow("Log Failed Groups:", self.log_failed_groups)
        
        self.handle_floodwait = QCheckBox()
        self.handle_floodwait.setChecked(True)
        settings_layout.addRow("Auto-handle FloodWait:", self.handle_floodwait)
        
        self.use_reply_message = QCheckBox()
        self.use_reply_message.setChecked(True)
        settings_layout.addRow("Auto-reply to Messages:", self.use_reply_message)
        
        self.default_reply_message = QLineEdit("Hi! This is an advertising bot. @vipstore. DMs won't be seen here")
        settings_layout.addRow("Default Reply Message:", self.default_reply_message)
        
        self.save_settings_button = QPushButton("Save Global Settings")
        self.save_settings_button.clicked.connect(self.save_forwarder_settings)
        settings_layout.addRow("", self.save_settings_button)
        
        forwarder_layout.addWidget(settings_group)
        
        # Initialize tasks database and tables
        self.initialize_forwarder_db()
        self.load_forwarder_settings()
        self.update_task_table()
        
        return forwarder_tab

    def initialize_forwarder_db(self):
        """Initialize database tables for the forwarder module."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Create tasks table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT,
                        account TEXT,
                        message_link TEXT,
                        status TEXT DEFAULT 'idle',
                        current_index INTEGER DEFAULT 0,
                        error_message TEXT,
                        last_processed_time TEXT,
                        FOREIGN KEY (account) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create task_groups table for many-to-many relationship
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        PRIMARY KEY (task_id, group_link),
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks (id)
                    )
                ''')
                
                # Create forwarder_settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Insert default settings if they don't exist
                default_settings = [
                    ('useRandomSleepTime', 'true'),
                    ('logFailedGroups', 'true'),
                    ('floodwait', 'true'),
                    ('replyMessage', 'true'),
                    ('defaultReplyMessage', 'Hi! This is an advertising bot. @vipstore. DMs won\'t be seen here')
                ]
                
                for key, value in default_settings:
                    cursor.execute(
                        "INSERT OR IGNORE INTO forwarder_settings (key, value) VALUES (?, ?)",
                        (key, value)
                    )
                
                # Add per-account settings columns to accounts table
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN intervalMin INTEGER DEFAULT 20")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN intervalMax INTEGER DEFAULT 25")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN afterEachSecond INTEGER DEFAULT 360")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN randomSleepTimeMin INTEGER DEFAULT 30")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN randomSleepTimeMax INTEGER DEFAULT 60")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN customReplyMessage TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                conn.commit()
                conn.close()
                
                self.log_activity("Forwarder database initialized", True)
                
        except Exception as e:
            self.log_activity(f"Error initializing forwarder database: {str(e)}", True)
    
    def load_forwarder_settings(self):
        """Load forwarder settings from database."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                cursor.execute("SELECT key, value FROM forwarder_settings")
                settings = cursor.fetchall()
                
                for key, value in settings:
                    if key == 'useRandomSleepTime':
                        self.use_random_sleep.setChecked(value.lower() == 'true')
                    elif key == 'logFailedGroups':
                        self.log_failed_groups.setChecked(value.lower() == 'true')
                    elif key == 'floodwait':
                        self.handle_floodwait.setChecked(value.lower() == 'true')
                    elif key == 'replyMessage':
                        self.use_reply_message.setChecked(value.lower() == 'true')
                    elif key == 'defaultReplyMessage':
                        self.default_reply_message.setText(value)
                
                conn.close()
                
        except Exception as e:
            self.log_activity(f"Error loading forwarder settings: {str(e)}", True)
    
    def save_forwarder_settings(self):
        """Save forwarder settings to database."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                settings = [
                    ('useRandomSleepTime', 'true' if self.use_random_sleep.isChecked() else 'false'),
                    ('logFailedGroups', 'true' if self.log_failed_groups.isChecked() else 'false'),
                    ('floodwait', 'true' if self.handle_floodwait.isChecked() else 'false'),
                    ('replyMessage', 'true' if self.use_reply_message.isChecked() else 'false'),
                    ('defaultReplyMessage', self.default_reply_message.text())
                ]
                
                for key, value in settings:
                    cursor.execute(
                        "UPDATE forwarder_settings SET value = ? WHERE key = ?",
                        (value, key)
                    )
                
                conn.commit()
                conn.close()
                
                self.log_activity("Forwarder settings saved", True)
                
        except Exception as e:
            self.log_activity(f"Error saving forwarder settings: {str(e)}", True)
    
    def update_task_table(self):
        """Update the task table with current tasks."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get all tasks with their progress
                cursor.execute("""
                    SELECT 
                        t.id, t.name, t.account, t.status, t.message_link, t.error_message,
                        (SELECT COUNT(*) FROM task_groups WHERE task_id = t.id AND processed = 1) as processed,
                        (SELECT COUNT(*) FROM task_groups WHERE task_id = t.id) as total
                    FROM forwarder_tasks t
                    ORDER BY t.name
                """)
                
                tasks = cursor.fetchall()
                
                # Clear the table
                self.task_table.setRowCount(0)
                
                # Add tasks to the table
                for i, task in enumerate(tasks):
                    task_id, name, account, status, message_link, error_message, processed, total = task
                    
                    self.task_table.insertRow(i)
                    
                    # Set task name
                    self.task_table.setItem(i, 0, QTableWidgetItem(name))
                    
                    # Set account
                    self.task_table.setItem(i, 1, QTableWidgetItem(account))
                    
                    # Set status with appropriate color
                    status_item = QTableWidgetItem(status)
                    if status == 'running':
                        status_item.setForeground(QColor('green'))
                    elif status == 'paused':
                        status_item.setForeground(QColor('orange'))
                    elif status == 'error':
                        status_item.setForeground(QColor('red'))
                    elif status == 'completed':
                        status_item.setForeground(QColor('blue'))
                    self.task_table.setItem(i, 2, status_item)
                    
                    # Set progress
                    progress = f"{processed}/{total}" if total > 0 else "0/0"
                    self.task_table.setItem(i, 3, QTableWidgetItem(progress))
                    
                    # Set message link (shortened)
                    short_message = message_link
                    if len(short_message) > 30:
                        short_message = short_message[:27] + "..."
                    self.task_table.setItem(i, 4, QTableWidgetItem(short_message))
                    
                    # Add action buttons
                    actions_widget = QWidget()
                    actions_layout = QHBoxLayout(actions_widget)
                    actions_layout.setContentsMargins(2, 2, 2, 2)
                    
                    start_button = QPushButton("Start")
                    stop_button = QPushButton("Stop")
                    delete_button = QPushButton("Delete")
                    
                    # Store task_id in button property
                    start_button.setProperty("task_id", task_id)
                    stop_button.setProperty("task_id", task_id)
                    delete_button.setProperty("task_id", task_id)
                    
                    # Connect signals
                    start_button.clicked.connect(lambda checked, tid=task_id: self.start_forwarding_task(tid))
                    stop_button.clicked.connect(lambda checked, tid=task_id: self.stop_forwarding_task(tid))
                    delete_button.clicked.connect(lambda checked, tid=task_id: self.delete_forwarding_task(tid))
                    
                    actions_layout.addWidget(start_button)
                    actions_layout.addWidget(stop_button)
                    actions_layout.addWidget(delete_button)
                    
                    self.task_table.setCellWidget(i, 5, actions_widget)
                
                conn.close()
                
        except Exception as e:
            self.log_activity(f"Error updating task table: {str(e)}", True)

    def create_new_task(self):
        """Open dialog to create a new forwarding task."""
        dialog = QDialog(self)
        dialog.setWindowTitle("Create New Forwarding Task")
        dialog.setMinimumWidth(500)
        
        layout = QVBoxLayout(dialog)
        
        # Task name
        name_layout = QHBoxLayout()
        name_label = QLabel("Task Name:")
        name_input = QLineEdit()
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_input)
        layout.addLayout(name_layout)
        
        # Account selection
        account_layout = QHBoxLayout()
        account_label = QLabel("Select Account:")
        account_combo = QComboBox()
        
        # Add accounts to combo box
        active_accounts = self.account_manager.get_active_accounts()
        for account in active_accounts:
            account_combo.addItem(f"{account.get('phone')} - {account.get('name', 'Unknown')}", account.get('phone'))
        
        account_layout.addWidget(account_label)
        account_layout.addWidget(account_combo)
        layout.addLayout(account_layout)
        
        # Message link
        message_layout = QHBoxLayout()
        message_label = QLabel("Message Link:")
        message_input = QLineEdit()
        message_input.setPlaceholderText("https://t.me/channel/123")
        message_layout.addWidget(message_label)
        message_layout.addWidget(message_input)
        layout.addLayout(message_layout)
        
        # Target groups
        groups_label = QLabel("Target Groups/Channels (one per line):")
        groups_input = QTextEdit()
        groups_input.setMinimumHeight(150)
        layout.addWidget(groups_label)
        layout.addWidget(groups_input)
        
        # Buttons
        buttons_layout = QHBoxLayout()
        create_button = QPushButton("Create Task")
        cancel_button = QPushButton("Cancel")
        buttons_layout.addWidget(create_button)
        buttons_layout.addWidget(cancel_button)
        layout.addLayout(buttons_layout)
        
        # Connect signals
        create_button.clicked.connect(lambda: self.save_new_task(
            dialog, 
            name_input.text(), 
            account_combo.currentData(), 
            message_input.text(), 
            groups_input.toPlainText()
        ))
        cancel_button.clicked.connect(dialog.reject)
        
        dialog.exec_()
    
    def save_new_task(self, dialog, name, account, message_link, groups_text):
        """Save a new forwarding task to the database."""
        # Validate inputs
        if not name:
            QMessageBox.warning(dialog, "Validation Error", "Task name is required")
            return
        
        if not account:
            QMessageBox.warning(dialog, "Validation Error", "You must select an account")
            return
        
        if not message_link:
            QMessageBox.warning(dialog, "Validation Error", "Message link is required")
            return
        
        if not groups_text.strip():
            QMessageBox.warning(dialog, "Validation Error", "You must specify at least one target group")
            return
        
        # Parse groups
        groups = [g.strip() for g in groups_text.split('\n') if g.strip()]
        
        # Generate a unique ID for the task
        task_id = str(uuid.uuid4())
        
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Insert task
                cursor.execute(
                    "INSERT INTO forwarder_tasks (id, name, account, message_link, status) VALUES (?, ?, ?, ?, ?)",
                    (task_id, name, account, message_link, 'idle')
                )
                
                # Insert groups
                for group in groups:
                    cursor.execute(
                        "INSERT INTO task_groups (task_id, group_link) VALUES (?, ?)",
                        (task_id, group)
                    )
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Created new forwarding task: {name} with {len(groups)} target groups", True)
                self.update_task_table()
                dialog.accept()
                
        except Exception as e:
            self.log_activity(f"Error creating forwarding task: {str(e)}", True)
            QMessageBox.critical(dialog, "Error", f"Failed to create task: {str(e)}")

    def start_forwarding_task(self, task_id):
        """Start a forwarding task."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get task details
                cursor.execute("SELECT name, account, status FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    self.log_activity(f"Task {task_id} not found", True)
                    conn.close()
                    return
                
                name, account, status = task
                
                # Check if account is active
                cursor.execute("SELECT active FROM accounts WHERE phone = ?", (account,))
                account_status = cursor.fetchone()
                
                if not account_status or account_status[0] != 1:
                    self.log_activity(f"Account {account} is not active", True)
                    QMessageBox.warning(self, "Account Inactive", 
                                       f"The account {account} is not active. Please activate it first.")
                    conn.close()
                    return
                
                # Update task status to running
                cursor.execute(
                    "UPDATE forwarder_tasks SET status = 'running', error_message = NULL WHERE id = ?",
                    (task_id,)
                )
                
                conn.commit()
                conn.close()
                
                # Start the task in a separate thread
                threading.Thread(
                    target=self._forwarding_task_thread,
                    args=(task_id, name, account),
                    daemon=True
                ).start()
                
                self.log_activity(f"Started forwarding task: {name}", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error starting forwarding task: {str(e)}", True)
    
    def stop_forwarding_task(self, task_id):
        """Stop a forwarding task."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get task details
                cursor.execute("SELECT name FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    self.log_activity(f"Task {task_id} not found", True)
                    conn.close()
                    return
                
                name = task[0]
                
                # Update task status to paused
                cursor.execute(
                    "UPDATE forwarder_tasks SET status = 'paused' WHERE id = ?",
                    (task_id,)
                )
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Stopped forwarding task: {name}", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error stopping forwarding task: {str(e)}", True)
    
    def delete_forwarding_task(self, task_id):
        """Delete a forwarding task."""
        try:
            # Confirm deletion
            confirm = QMessageBox.question(
                self, "Confirm Deletion", 
                "Are you sure you want to delete this task? This cannot be undone.",
                QMessageBox.Yes | QMessageBox.No, 
                QMessageBox.No
            )
            
            if confirm != QMessageBox.Yes:
                return
            
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get task details
                cursor.execute("SELECT name FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    self.log_activity(f"Task {task_id} not found", True)
                    conn.close()
                    return
                
                name = task[0]
                
                # Delete task groups first (foreign key constraint)
                cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                
                # Delete task
                cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_id,))
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Deleted forwarding task: {name}", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error deleting forwarding task: {str(e)}", True)

    def start_all_forwarding_tasks(self):
        """Start all forwarding tasks."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get all tasks that are not running
                cursor.execute("""
                    SELECT t.id, t.name, t.account 
                    FROM forwarder_tasks t
                    WHERE t.status != 'running' AND t.status != 'completed'
                """)
                
                tasks = cursor.fetchall()
                
                if not tasks:
                    self.log_activity("No tasks to start", True)
                    conn.close()
                    return
                
                # Update all tasks to running
                cursor.execute(
                    "UPDATE forwarder_tasks SET status = 'running', error_message = NULL WHERE status != 'running' AND status != 'completed'"
                )
                
                conn.commit()
                conn.close()
                
                # Start each task in a separate thread
                for task_id, name, account in tasks:
                    threading.Thread(
                        target=self._forwarding_task_thread,
                        args=(task_id, name, account),
                        daemon=True
                    ).start()
                
                self.log_activity(f"Started {len(tasks)} forwarding tasks", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error starting all forwarding tasks: {str(e)}", True)
    
    def stop_all_forwarding_tasks(self):
        """Stop all forwarding tasks."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Update all running tasks to paused
                cursor.execute("UPDATE forwarder_tasks SET status = 'paused' WHERE status = 'running'")
                
                conn.commit()
                conn.close()
                
                self.log_activity("Stopped all forwarding tasks", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error stopping all forwarding tasks: {str(e)}", True)
    
    def continue_all_forwarding_tasks(self):
        """Continue all paused forwarding tasks."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get all paused tasks
                cursor.execute("""
                    SELECT t.id, t.name, t.account 
                    FROM forwarder_tasks t
                    WHERE t.status = 'paused' OR t.status = 'error'
                """)
                
                tasks = cursor.fetchall()
                
                if not tasks:
                    self.log_activity("No paused tasks to continue", True)
                    conn.close()
                    return
                
                # Update all paused tasks to running
                cursor.execute(
                    "UPDATE forwarder_tasks SET status = 'running', error_message = NULL WHERE status = 'paused' OR status = 'error'"
                )
                
                conn.commit()
                conn.close()
                
                # Start each task in a separate thread
                for task_id, name, account in tasks:
                    threading.Thread(
                        target=self._forwarding_task_thread,
                        args=(task_id, name, account),
                        daemon=True
                    ).start()
                
                self.log_activity(f"Continued {len(tasks)} forwarding tasks", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error continuing all forwarding tasks: {str(e)}", True)
            
    def _forwarding_task_thread(self, task_id, task_name, account_phone):
        """Background thread to handle message forwarding for a task."""
        try:
            self.log_forwarding(f"Starting task {task_name} with account {account_phone}")
            
            # Get task details from database
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Get message link
                cursor.execute("SELECT message_link, current_index FROM forwarder_tasks WHERE id = ?", (task_id,))
                task_data = cursor.fetchone()
                
                if not task_data:
                    self.log_forwarding(f"Task {task_name} not found in database")
                    conn.close()
                    return
                    
                message_link, current_index = task_data
                
                # Get account settings
                cursor.execute("""
                    SELECT 
                        intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    FROM accounts 
                    WHERE phone = ?
                """, (account_phone,))
                
                account_data = cursor.fetchone()
                
                if not account_data:
                    self.log_forwarding(f"Account {account_phone} not found in database")
                    conn.close()
                    return
                
                interval_min, interval_max, after_each_second, sleep_min, sleep_max, custom_reply = account_data
                
                # Get global settings
                cursor.execute("SELECT value FROM forwarder_settings WHERE key = 'useRandomSleepTime'")
                use_random_sleep = cursor.fetchone()[0].lower() == 'true'
                
                cursor.execute("SELECT value FROM forwarder_settings WHERE key = 'floodwait'")
                handle_floodwait = cursor.fetchone()[0].lower() == 'true'
                
                cursor.execute("SELECT value FROM forwarder_settings WHERE key = 'logFailedGroups'")
                log_failed = cursor.fetchone()[0].lower() == 'true'
                
                # Get groups to process
                cursor.execute("""
                    SELECT group_link 
                    FROM task_groups 
                    WHERE task_id = ? AND processed = 0
                    ORDER BY rowid
                """, (task_id,))
                
                groups = [row[0] for row in cursor.fetchall()]
                
                conn.close()
                
                if not groups:
                    self.log_forwarding(f"No remaining groups to process for task {task_name}")
                    self.update_task_status(task_id, "completed")
                    return
                
                # Parse message link to get channel and message ID
                try:
                    message_parts = message_link.strip().split('/')
                    if 'https://t.me/c/' in message_link:
                        # Private channel format: https://t.me/c/**********/123
                        channel_id = message_parts[-2]
                        message_id = int(message_parts[-1])
                        from_peer = int(channel_id)
                    else:
                        # Public channel format: https://t.me/channelname/123
                        channel_name = message_parts[-2]
                        message_id = int(message_parts[-1])
                        from_peer = channel_name
                except Exception as e:
                    self.log_forwarding(f"Error parsing message link: {str(e)}")
                    self.update_task_status(task_id, "error", f"Invalid message link: {str(e)}")
                    return
                
                # Get account API credentials and session path
                with self.db_lock:
                    conn = sqlite3.connect(self.account_manager.db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT api_id, api_hash, session_file FROM accounts WHERE phone = ?", (account_phone,))
                    account_creds = cursor.fetchone()
                    conn.close()
                
                if not account_creds:
                    self.log_forwarding(f"Could not find API credentials for account {account_phone}")
                    self.update_task_status(task_id, "error", "Missing API credentials")
                    return
                
                api_id, api_hash, session_file = account_creds
                
                # Use the existing session file if available, otherwise create one in the sessions directory
                if session_file and os.path.exists(session_file):
                    session_path = session_file
                    self.log_forwarding(f"Using existing session file: {session_path}")
                else:
                    session_path = os.path.join("sessions", account_phone)
                    self.log_forwarding(f"Creating new session at: {session_path}")
                
                # Process the task
                async def process_task():
                    # Import locally to ensure availability
                    from telethon.errors import FloodWaitError
                    client_wrapper = None
                    try:
                        # Initialize client using our custom wrapper class
                        client_wrapper = TelegramClient(
                            int(api_id), 
                            api_hash,
                            account_phone,
                            session_file=session_path,
                            logger=logging.getLogger(f"forwarder.{account_phone}")
                        )
                        
                        # Log connection with random identifier for tracking
                        connection_id = random.randint(1000, 9999)
                        self.log_forwarding(f"Initializing connection #{connection_id} for {account_phone}")
                        
                        # Connect using the wrapper
                        if not await client_wrapper.connect():
                            self.log_forwarding(f"Failed to connect with account {account_phone}", "error")
                            self.update_task_status(task_id, "error", "Failed to connect to Telegram")
                            return
                        
                        self.log_forwarding(f"Connected to Telegram with account {account_phone}")
                        
                        # Process each group
                        for i, group in enumerate(groups):
                            # Check if task was stopped
                            with self.db_lock:
                                conn = sqlite3.connect(self.account_manager.db_path)
                                cursor = conn.cursor()
                                cursor.execute("SELECT status FROM forwarder_tasks WHERE id = ?", (task_id,))
                                status = cursor.fetchone()[0]
                                conn.close()
                                
                                if status != "running":
                                    self.log_forwarding(f"Task {task_name} was stopped")
                                    return
                            
                            # Forward message to group
                            self.log_forwarding(f"Forwarding to group: {group}")
                            
                            try:
                                # Using the wrapper's client to forward
                                self.log_forwarding(f"Attempting to resolve and join group: {group}", "info")
                                
                                # Fix for PeerUser(user_id=True) error with topics
                                try:
                                    # First try: Use the client wrapper's join_group method
                                    group_entity = await client_wrapper.join_group(group)
                                    
                                    if not group_entity or (isinstance(group_entity, dict) and "error" in group_entity):
                                        error_msg = group_entity.get("error") if isinstance(group_entity, dict) else "Failed to join group"
                                        self.log_forwarding(f"First join attempt failed: {error_msg}, trying alternatives", "info")
                                        
                                        # Second try: Direct entity resolution with username
                                        if group_username:
                                            try:
                                                self.log_forwarding(f"Trying to resolve entity using username: {group_username}", "info")
                                                # Try to get the entity directly using the username
                                                group_entity = await client_wrapper.client.get_entity(group_username)
                                                self.log_forwarding(f"Successfully resolved entity using username", "info")
                                            except Exception as username_error:
                                                self.log_forwarding(f"Username resolution failed: {str(username_error)}", "error")
                                                
                                                # Third try: Try with the full URL
                                                try:
                                                    self.log_forwarding(f"Trying to resolve with direct URL: {group}", "info")
                                                    group_entity = await client_wrapper.client.get_entity(group)
                                                    self.log_forwarding(f"Successfully resolved entity using URL", "info")
                                                except Exception as url_error:
                                                    self.log_forwarding(f"URL resolution failed: {str(url_error)}", "error")
                                                    raise # Let outer exception handler catch this
                                    else:
                                        self.log_forwarding(f"Successfully joined and resolved group entity", "info")
                                        
                                except Exception as join_error:
                                    self.log_forwarding(f"Error resolving group {original_group}: {str(join_error)}", "error")
                                    continue
                                    
                                if not group_entity:
                                    self.log_forwarding(f"Could not resolve entity for {original_group} after all attempts", "error")
                                    continue
                                
                                # Extract the topic ID if it's a forum topic
                                topic_id = None
                                original_group = group
                                group_username = None
                                
                                # Handle forum topics in the URL
                                if '/' in group:
                                    try:
                                        # First, properly parse the URL to separate group and topic
                                        if 't.me/' in group:
                                            # Standard format: https://t.me/groupname/123
                                            parts = group.split('t.me/')[1].split('/')
                                            if len(parts) >= 2 and parts[0] and parts[1].isdigit():
                                                group_username = parts[0]
                                                topic_id = int(parts[1])
                                                # Keep a clean group username without the topic
                                                group = f"https://t.me/{group_username}"
                                                self.log_forwarding(f"Parsed topic URL: group={group_username}, topic_id={topic_id}", "info")
                                        else:
                                            # Try alternative parsing if t.me/ not found
                                            parts = group.strip().split('/')
                                            if len(parts) > 1 and parts[-1].isdigit():
                                                topic_id = int(parts[-1])
                                                group_username = parts[-2]
                                                group = '/'.join(parts[:-1])
                                                self.log_forwarding(f"Alternative parsing: group={group_username}, topic_id={topic_id}", "info")
                                    except Exception as parse_error:
                                        self.log_forwarding(f"Error parsing topic URL {original_group}: {str(parse_error)}", "error")
                                        topic_id = None
                                
                                # Get the message to forward
                                if client_wrapper.client:
                                    try:
                                        # Ensure target entity is properly resolved
                                        if group_entity is None:
                                            # Try to resolve entity again
                                            try:
                                                self.log_forwarding(f"Re-resolving entity for {group}", "info")
                                                if group_username:
                                                    # Try with username first
                                                    group_entity = await client_wrapper.client.get_entity(group_username)
                                                    self.log_forwarding(f"Successfully resolved entity using username: {group_username}", "info")
                                                else:
                                                    # Try with full URL
                                                    group_entity = await client_wrapper.client.get_entity(group)
                                                    self.log_forwarding(f"Successfully resolved entity using URL", "info")
                                            except Exception as entity_error:
                                                self.log_forwarding(f"Failed to resolve entity: {str(entity_error)}", "error")
                                                raise Exception(f"Could not resolve entity for {original_group}")
                                        
                                        # Get the original message content first - this is the most reliable approach
                                        message_text = None
                                        message_media = None
                                        
                                        try:
                                            # Try to get the original message
                                            self.log_forwarding(f"Getting original message content from {from_peer}, message ID {message_id}", "info")
                                            
                                            # First resolve the from_peer
                                            try:
                                                resolved_from_peer = await client_wrapper.client.get_input_entity(from_peer)
                                                self.log_forwarding(f"Successfully resolved from_peer entity", "info")
                                            except Exception as from_error:
                                                self.log_forwarding(f"Could not resolve from_peer: {str(from_error)}", "error")
                                                # Continue anyway, we'll try alternative methods
                                            
                                            # Get the message directly
                                            try:
                                                # Try to get the message content directly
                                                orig_message = await client_wrapper.client.get_messages(
                                                    entity=from_peer,
                                                    ids=int(message_id)
                                                )
                                                
                                                if orig_message:
                                                    message_text = orig_message.text or orig_message.message
                                                    message_media = orig_message.media
                                                    self.log_forwarding(f"Successfully retrieved original message content", "info")
                                                else:
                                                    self.log_forwarding(f"Message with ID {message_id} not found", "warning")
                                            except Exception as msg_error:
                                                self.log_forwarding(f"Error getting original message: {str(msg_error)}", "error")
                                                # Continue anyway, we'll try forwarding directly
                                        except Exception as content_error:
                                            self.log_forwarding(f"Could not get message content: {str(content_error)}", "error")
                                            # Continue anyway, we'll try forwarding directly
                                        
                                                                                 # PROPER FORWARDING APPROACH FOR FORUM TOPICS
                                        if topic_id:
                                            self.log_forwarding(f"Forwarding to forum topic {topic_id} in {group}", "info")
                                            
                                            # First ensure we have proper entities loaded
                                            try:
                                                # Get source entity (where the message is from)
                                                source_entity = None
                                                try:
                                                    self.log_forwarding(f"Resolving source entity: {from_peer}", "info")
                                                    # Get the entity directly from Telegram servers to ensure up-to-date access hash
                                                    source_entity = await client_wrapper.client.get_entity(from_peer)
                                                    self.log_forwarding(f"Successfully resolved source entity: {type(source_entity).__name__}", "info")
                                                except Exception as source_error:
                                                    self.log_forwarding(f"Error resolving source entity: {str(source_error)}", "error")
                                                    # Continue anyway, we'll try different approaches
                                                
                                                # Make sure group entity is properly loaded
                                                if not group_entity and group_username:
                                                    try:
                                                        self.log_forwarding(f"Re-resolving destination entity with username: {group_username}", "info")
                                                        group_entity = await client_wrapper.client.get_entity(group_username)
                                                        self.log_forwarding(f"Successfully resolved destination entity", "info")
                                                    except Exception as dest_error:
                                                        self.log_forwarding(f"Error resolving destination entity: {str(dest_error)}", "error")
                                                        # Continue anyway
                                                
                                                # Now try proper forwarding with progressive fallback
                                                # Method 1: Direct forwarding with topic_id as reply_to
                                                try:
                                                    self.log_forwarding(f"Attempting direct forward with topic reply", "info")
                                                    
                                                    # Use proper forwarding - this will maintain original formatting, media, etc.
                                                    forwarded = await client_wrapper.client.forward_messages(
                                                        entity=group_entity,
                                                        messages=int(message_id),
                                                        from_peer=source_entity or from_peer,
                                                        silent=False,
                                                        # Try the topic_id as reply_to (works in some Telethon versions)
                                                        reply_to=topic_id
                                                    )
                                                    self.log_forwarding(f"Successfully forwarded to topic using reply_to", "success")
                                                except Exception as method1_error:
                                                    self.log_forwarding(f"Method 1 failed: {str(method1_error)}", "info")
                                                    
                                                    # Method 2: Try to find a message in the topic to reply to
                                                    try:
                                                        self.log_forwarding(f"Trying to find a message in topic to reply to", "info")
                                                        
                                                        # Try to find existing messages in this topic
                                                        try:
                                                            # Some newer Telethon versions have topic support
                                                            topic_msgs = await client_wrapper.client.get_messages(
                                                                entity=group_entity,
                                                                limit=1,
                                                                reply_to=topic_id  # This is new in some Telethon versions
                                                            )
                                                        except Exception:
                                                            # Fallback to getting any recent messages
                                                            topic_msgs = await client_wrapper.client.get_messages(
                                                                entity=group_entity,
                                                                limit=10  # Get several messages to increase chance of finding topic
                                                            )
                                                            # Filter to find messages that might be in our topic
                                                            # This is a best-effort approach
                                                            topic_msgs = [msg for msg in topic_msgs if 
                                                                          hasattr(msg, 'reply_to') and 
                                                                          msg.reply_to and 
                                                                          hasattr(msg.reply_to, 'reply_to_top_id') and
                                                                          msg.reply_to.reply_to_top_id == topic_id]
                                                            
                                                        if topic_msgs and len(topic_msgs) > 0:
                                                            self.log_forwarding(f"Found message in topic to reply to", "info")
                                                            # Forward as reply to a message in the topic
                                                            forwarded = await client_wrapper.client.forward_messages(
                                                                entity=group_entity,
                                                                messages=int(message_id),
                                                                from_peer=source_entity or from_peer,
                                                                reply_to=topic_msgs[0].id
                                                            )
                                                            self.log_forwarding(f"Successfully forwarded as reply to topic message", "success")
                                                        else:
                                                            raise Exception("No messages found in topic to reply to")
                                                            
                                                    except Exception as method2_error:
                                                        self.log_forwarding(f"Method 2 failed: {str(method2_error)}", "info")
                                                        
                                                        # Method 3: Plain forward, then notify about the topic
                                                        try:
                                                            self.log_forwarding(f"Using content-based forwarding method", "info")
                                                            
                                                            # DIRECT SOLUTION FOR PeerUser(user_id=True) ERROR:
                                                            # Always prioritize using message content over entity-based forwarding
                                                            
                                                            # CRITICAL FIX: Force using username string instead of entity objects
                                                            try:
                                                                # Always prefer using direct username - most reliable approach
                                                                if group_username:
                                                                    target = group_username  # Use the raw username string
                                                                    self.log_forwarding(f"Using direct username for message send: {group_username}", "info")
                                                                else:
                                                                    target = group_entity
                                                                    self.log_forwarding(f"No username available, using entity object", "info")
                                                                    
                                                                # Method 3A: If we have the message content, send it directly
                                                                if message_text:
                                                                    self.log_forwarding(f"Using message text content for forwarding", "info")
                                                                    
                                                                    # Format nicely for forwarded content
                                                                    formatted_message = f"📨 *Forwarded message:*\n\n{message_text}"
                                                                    
                                                                    # Send as new message - this completely bypasses the entity resolution problem
                                                                    forwarded = await client_wrapper.client.send_message(
                                                                        target,  # Use the target we determined above
                                                                        message=formatted_message
                                                                    )
                                                                self.log_forwarding(f"Successfully sent message content", "success")
                                                                
                                                                # Send topic notification as a reply to our sent message
                                                                await client_wrapper.client.send_message(
                                                                    target,  # Use the same target as above
                                                                    message=f"⚠️ This message belongs to Topic {topic_id}",
                                                                    reply_to=forwarded.id
                                                                )
                                                                
                                                                self.log_forwarding(f"Successfully forwarded content with topic notification", "success")
                                                                return  # Exit the method successfully
                                                            except Exception as method3a_error:
                                                                self.log_forwarding(f"Method 3A failed: {str(method3a_error)}", "error")
                                                                
                                                            # Method 3B: If we have original message object, try with that
                                                            if 'orig_message' in locals() and orig_message:
                                                                try:
                                                                    self.log_forwarding(f"Using message object for direct forwarding", "info")
                                                                    
                                                                    # Extract message text to completely avoid entity issues
                                                                    msg_text = orig_message.text or orig_message.message or f"Message from {from_peer}"
                                                                    
                                                                    # Send as new message using target
                                                                    forwarded = await client_wrapper.client.send_message(
                                                                        target,  # Use the same target as above
                                                                        message=f"📨 *Forwarded message:*\n\n{msg_text}"
                                                                    )
                                                                    
                                                                    # Send topic notification
                                                                    await client_wrapper.client.send_message(
                                                                        target,  # Use the same target as above
                                                                        message=f"⚠️ This message belongs to Topic {topic_id}",
                                                                        reply_to=forwarded.id
                                                                    )
                                                                    
                                                                    self.log_forwarding(f"Successfully forwarded message object content", "success")
                                                                    return  # Exit the method successfully
                                                                except Exception as obj_error:
                                                                    self.log_forwarding(f"Error using message object: {str(obj_error)}", "error")
                                                                    # Continue to fallback method
                                                            
                                                            # Method 3C: Completely give up on forwarding, just send a placeholder
                                                            self.log_forwarding(f"Using placeholder message as last resort with direct username", "info")
                                                            
                                                            # EXTREME FALLBACK: Bypass ALL entity resolution by using the raw username string
                                                            try:
                                                                if group_username:
                                                                    # Use the raw username string instead of any entity objects
                                                                    self.log_forwarding(f"Trying direct send to username: {group_username}", "info")
                                                                    placeholder = await client_wrapper.client.send_message(
                                                                        group_username,  # Use raw username string to completely bypass entity resolution
                                                                        f"📨 *Message from {from_peer}*\n\nMessage ID: {message_id}\nTo Topic: {topic_id}"
                                                                    )
                                                                    self.log_forwarding(f"Successfully sent placeholder to username directly", "success")
                                                                else:
                                                                    # If no username available, try with the entity but with minimal parameters
                                                                    placeholder = await client_wrapper.client.send_message(
                                                                        entity=group_entity,
                                                                        message=f"📨 *Could not forward message*\n\nMessage ID: {message_id}\nFrom: {from_peer}\nTo Topic: {topic_id}"
                                                                    )
                                                                    self.log_forwarding(f"Sent placeholder message with entity", "success")
                                                            except Exception as placeholder_error:
                                                                self.log_forwarding(f"Error sending placeholder: {str(placeholder_error)}", "error")
                                                                # Try one final approach with raw string entity
                                                                try:
                                                                    # Try to directly create an entity string (e.g., "@username")
                                                                    if group_username and not group_username.startswith('@'):
                                                                        direct_username = f"@{group_username}"
                                                                    else:
                                                                        direct_username = group_username
                                                                        
                                                                    if direct_username:
                                                                        await client_wrapper.client.send_message(
                                                                            direct_username,
                                                                            "⚠️ Unable to forward message. Please check logs."
                                                                        )
                                                                        self.log_forwarding(f"Sent ultra-simplified message to {direct_username}", "info")
                                                                except Exception as ultra_error:
                                                                    self.log_forwarding(f"Even the most basic approach failed: {str(ultra_error)}", "error")
                                                            
                                                        except Exception as method3_error:
                                                            self.log_forwarding(f"Method 3 failed: {str(method3_error)}", "error")
                                                            # Ultra-fallback: Try one last time with minimal code
                                                            try:
                                                                await client_wrapper.client.send_message(
                                                                    entity=group_username if group_username else group_entity,
                                                                    message=f"⚠️ Failed to forward message to topic {topic_id}"
                                                                )
                                                                self.log_forwarding(f"Sent error notification", "info")
                                                            except Exception as final_error:
                                                                self.log_forwarding(f"All attempts to communicate with the group failed: {str(final_error)}", "error")
                                                            
                                                            # Never raise, always continue to next group
                                                            continue
                                            except Exception as topic_error:
                                                self.log_forwarding(f"Topic forwarding failed: {str(topic_error)}", "error")
                                                # Continue to next group rather than stopping the task
                                                continue
                                        else:
                                            # Normal forwarding to regular group
                                            self.log_forwarding(f"Forwarding to regular group {group}", "info")
                                            
                                            try:
                                                if message_text:
                                                    # We have the message content, send it directly (most reliable)
                                                    self.log_forwarding(f"Sending message content directly", "info")
                                                    sent_msg = await client_wrapper.client.send_message(
                                                        entity=group_entity,
                                                        message=message_text
                                                    )
                                                    self.log_forwarding(f"Successfully sent message to {original_group}", "success")
                                                else:
                                                    # Try forwarding
                                                    self.log_forwarding(f"Trying to forward message", "info")
                                                    
                                                    try:
                                                        # Use resolved from_peer if available
                                                        if 'resolved_from_peer' in locals() and resolved_from_peer:
                                                            forwarded = await client_wrapper.client.forward_messages(
                                                                entity=group_entity,
                                                                messages=int(message_id),
                                                                from_peer=resolved_from_peer
                                                            )
                                                        else:
                                                            # Try with original from_peer
                                                            forwarded = await client_wrapper.client.forward_messages(
                                                                entity=group_entity,
                                                                messages=int(message_id),
                                                                from_peer=from_peer
                                                            )
                                                        self.log_forwarding(f"Successfully forwarded to {original_group}", "success")
                                                    except Exception as fwd_error:
                                                        self.log_forwarding(f"Forward failed: {str(fwd_error)}", "error")
                                                        raise
                                            except Exception as regular_error:
                                                self.log_forwarding(f"Failed to send to regular group: {str(regular_error)}", "error")
                                                raise Exception(f"Regular group error: {str(regular_error)}")
                                    except Exception as fwd_error:
                                        raise Exception(f"Forward error: {str(fwd_error)}")
                                
                                # Mark as processed
                                with self.db_lock:
                                    conn = sqlite3.connect(self.account_manager.db_path)
                                    cursor = conn.cursor()
                                    cursor.execute(
                                        "UPDATE task_groups SET processed = 1 WHERE task_id = ? AND group_link = ?",
                                        (task_id, group)
                                    )
                                    cursor.execute(
                                        "UPDATE forwarder_tasks SET current_index = current_index + 1, last_processed_time = ? WHERE id = ?",
                                        (datetime.now().isoformat(), task_id)
                                    )
                                    conn.commit()
                                    conn.close()
                                
                                self.log_forwarding(f"Successfully forwarded to {group}", "success")
                                self.update_task_table()
                                
                                # Wait before next forward
                                delay = random.randint(interval_min, interval_max)
                                self.log_forwarding(f"Waiting {delay} seconds before next forward...")
                                await asyncio.sleep(delay)
                                
                                # Add additional delay after batch
                                if (i + 1) % 10 == 0 and i < len(groups) - 1:
                                    batch_delay = random.randint(sleep_min, sleep_max)
                                    self.log_forwarding(f"Completed batch of 10. Taking a longer break ({batch_delay} seconds)...")
                                    await asyncio.sleep(batch_delay)
                                
                            except Exception as e:
                                # Check if it's a FloodWait error
                                wait_time = 0
                                is_floodwait = False
                                
                                if isinstance(e, FloodWaitError):
                                    # Direct FloodWaitError
                                    wait_time = e.seconds
                                    is_floodwait = True
                                    self.log_forwarding(f"FloodWait detected: need to wait {wait_time} seconds", "error")
                                elif "floodwait" in str(e).lower():
                                    # Extract seconds from error message if possible
                                    try:
                                        wait_time_match = re.search(r'(\d+)', str(e))
                                        if wait_time_match:
                                            wait_time = int(wait_time_match.group(1))
                                            is_floodwait = True
                                            self.log_forwarding(f"FloodWait detected from message: need to wait {wait_time} seconds", "error")
                                    except:
                                        # Default wait time if parsing fails
                                        wait_time = 300
                                        is_floodwait = True
                                        self.log_forwarding(f"FloodWait detected but couldn't parse time: using default {wait_time} seconds", "error")
                                
                                if is_floodwait and handle_floodwait:
                                    # Update account status in database
                                    with self.db_lock:
                                        conn = sqlite3.connect(self.account_manager.db_path)
                                        cursor = conn.cursor()
                                        cursor.execute(
                                            "UPDATE accounts SET status = 'limited', disabled_until = ? WHERE phone = ?",
                                            (datetime.now() + timedelta(seconds=wait_time)).isoformat(), account_phone
                                        )
                                        cursor.execute(
                                            "UPDATE forwarder_tasks SET status = 'paused', error_message = ? WHERE id = ?",
                                            (f"FloodWait: waiting for {wait_time} seconds", task_id)
                                        )
                                        conn.commit()
                                        conn.close()
                                    
                                    self.log_forwarding(f"Task paused due to FloodWait. Will resume after {wait_time} seconds...")
                                    self.update_task_table()
                                    
                                    # Wait for the required time
                                    await asyncio.sleep(wait_time)
                                    
                                    # Resume task
                                    with self.db_lock:
                                        conn = sqlite3.connect(self.account_manager.db_path)
                                        cursor = conn.cursor()
                                        cursor.execute(
                                            "UPDATE accounts SET status = 'available', disabled_until = NULL WHERE phone = ?",
                                            (account_phone,)
                                        )
                                        cursor.execute(
                                            "UPDATE forwarder_tasks SET status = 'running', error_message = NULL WHERE id = ?",
                                            (task_id,)
                                        )
                                        conn.commit()
                                        conn.close()
                                    
                                    self.log_forwarding("Resuming task after FloodWait...")
                                    self.update_task_table()
                                    
                                    # Recreate the client after FloodWait
                                    await client_wrapper.disconnect()
                                    await asyncio.sleep(2)  # Small delay before reconnection
                                    if not await client_wrapper.connect():
                                        self.log_forwarding("Failed to reconnect after FloodWait", "error")
                                        self.update_task_status(task_id, "error", "Failed to reconnect after FloodWait")
                                        return
                                    
                                    # Try again with this group
                                    i -= 1  # Decrement to retry the same group
                                    continue
                                elif is_floodwait:
                                    # Mark as error and stop if FloodWait handling is disabled
                                    self.update_task_status(task_id, "error", f"FloodWait: need to wait {wait_time} seconds")
                                    self.update_task_table()
                                    return
                                else:
                                    # Regular error, not FloodWait
                                    self.log_forwarding(f"Error: {str(e)}", "error")
                                    # Continue with the regular error handling below
                                    
                            except Exception as e:
                                error_msg = str(e)
                                self.log_forwarding(f"Error forwarding to {group}: {error_msg}", "error")
                                
                                # Mark group as processed with error
                                if log_failed:
                                    with self.db_lock:
                                        conn = sqlite3.connect(self.account_manager.db_path)
                                        cursor = conn.cursor()
                                        cursor.execute(
                                            "UPDATE task_groups SET processed = 1, error_message = ? WHERE task_id = ? AND group_link = ?",
                                            (error_msg, task_id, group)
                                        )
                                        cursor.execute(
                                            "UPDATE forwarder_tasks SET current_index = current_index + 1, last_processed_time = ? WHERE id = ?",
                                            (datetime.now().isoformat(), task_id)
                                        )
                                        conn.commit()
                                        conn.close()
                                    
                                # Wait before next attempt
                                delay = random.randint(interval_min, interval_max)
                                self.log_forwarding(f"Waiting {delay} seconds before next group...")
                                await asyncio.sleep(delay)
                        
                        # All groups processed
                        self.update_task_status(task_id, "completed")
                        self.log_forwarding(f"Task {task_name} completed successfully!")
                        self.update_task_table()
                        
                    except Exception as e:
                        self.log_forwarding(f"Error in task execution: {str(e)}", "error")
                        self.update_task_status(task_id, "error", str(e))
                        self.update_task_table()
                    finally:
                        if client_wrapper:
                            await client_wrapper.disconnect()
                
                # Run the async task
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(process_task())
                loop.close()
                
        except Exception as e:
            self.log_forwarding(f"Fatal error in task thread: {str(e)}", "error")
            self.update_task_status(task_id, "error", str(e))
            self.update_task_table()
    
    def update_task_status(self, task_id, status, error_message=None):
        """Update the status of a task in the database."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                if error_message:
                    cursor.execute(
                        "UPDATE forwarder_tasks SET status = ?, error_message = ? WHERE id = ?",
                        (status, error_message, task_id)
                    )
                else:
                    cursor.execute(
                        "UPDATE forwarder_tasks SET status = ?, error_message = NULL WHERE id = ?",
                        (status, task_id)
                    )
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            self.log_activity(f"Error updating task status: {str(e)}", True)
    
    def log_forwarding(self, message, level="info"):
        """Log a message to the forwarding log."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        if level == "success":
            formatted_message = f"[{timestamp}] [SUCCESS] {message}"
            html_message = f"<span style='color: green;'>[{timestamp}] [SUCCESS] {message}</span>"
        elif level == "error":
            formatted_message = f"[{timestamp}] [ERROR] {message}"
            html_message = f"<span style='color: red;'>[{timestamp}] [ERROR] {message}</span>"
        else:
            formatted_message = f"[{timestamp}] [INFO] {message}"
            html_message = f"<span style='color: blue;'>[{timestamp}] [INFO] {message}</span>"
        
        # Log to console
        print(formatted_message)
        
        # Log to UI
        QMetaObject.invokeMethod(
            self.forwarding_log,
            "append",
            Qt.QueuedConnection,
            Q_ARG(str, html_message)
        )
        
        # Also log to activity log
        self.log_activity(message, True)
        
    def start_selected_task(self):
        """Start the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to start")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        self.start_forwarding_task(task_id)
        
    def stop_selected_task(self):
        """Stop the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to stop")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        self.stop_forwarding_task(task_id)
        
    def continue_selected_task(self):
        """Continue the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to continue")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        self.start_forwarding_task(task_id)
        
    def delete_selected_task(self):
        """Delete the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to delete")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        self.delete_forwarding_task(task_id)
        
    def add_groups_to_task(self):
        """Add more groups to the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to add groups to")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        
        # Get groups to add
        groups_text = self.add_groups_input.text().strip()
        if not groups_text:
            QMessageBox.warning(self, "No Groups", "Please enter groups to add")
            return
            
        # Parse groups
        groups = [g.strip() for g in groups_text.split(',') if g.strip()]
        
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Add groups to the task
                for group in groups:
                    cursor.execute(
                        "INSERT OR IGNORE INTO task_groups (task_id, group_link) VALUES (?, ?)",
                        (task_id, group)
                    )
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Added {len(groups)} groups to task", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error adding groups to task: {str(e)}", True)
        
        # Clear input
        self.add_groups_input.clear()
        
    def change_task_message(self):
        """Change the message link for the currently selected task."""
        selected_items = self.task_table.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "No Task Selected", "Please select a task to change the message for")
            return
            
        row = selected_items[0].row()
        task_id = self.task_table.cellWidget(row, 5).findChildren(QPushButton)[0].property("task_id")
        
        # Get new message link
        message_link = self.change_message_input.text().strip()
        if not message_link:
            QMessageBox.warning(self, "No Message Link", "Please enter a message link")
            return
            
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                # Update the message link
                cursor.execute(
                    "UPDATE forwarder_tasks SET message_link = ? WHERE id = ?",
                    (message_link, task_id)
                )
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Changed message link for task", True)
                self.update_task_table()
                
        except Exception as e:
            self.log_activity(f"Error changing message link: {str(e)}", True)
        
        # Clear input
        self.change_message_input.clear()
        
    def edit_account_forwarder_settings(self, phone):
        """Edit forwarding settings for a specific account."""
        try:
            # Get current settings
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT 
                        intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    FROM accounts 
                    WHERE phone = ?
                """, (phone,))
                
                settings = cursor.fetchone()
                conn.close()
                
                if not settings:
                    QMessageBox.warning(self, "Error", f"Could not find account {phone}")
                    return
                    
                interval_min, interval_max, after_each_second, sleep_min, sleep_max, custom_reply = settings
                
                # Create dialog
                dialog = QDialog(self)
                dialog.setWindowTitle(f"Forwarding Settings for {phone}")
                dialog.setMinimumWidth(400)
                
                layout = QVBoxLayout(dialog)
                
                form_layout = QFormLayout()
                
                # Add settings
                interval_min_input = QSpinBox()
                interval_min_input.setRange(1, 300)
                interval_min_input.setValue(interval_min or 20)
                form_layout.addRow("Interval Min (seconds):", interval_min_input)
                
                interval_max_input = QSpinBox()
                interval_max_input.setRange(1, 300)
                interval_max_input.setValue(interval_max or 25)
                form_layout.addRow("Interval Max (seconds):", interval_max_input)
                
                after_each_input = QSpinBox()
                after_each_input.setRange(1, 3600)
                after_each_input.setValue(after_each_second or 360)
                form_layout.addRow("After Each Batch (seconds):", after_each_input)
                
                sleep_min_input = QSpinBox()
                sleep_min_input.setRange(1, 300)
                sleep_min_input.setValue(sleep_min or 30)
                form_layout.addRow("Random Sleep Min (seconds):", sleep_min_input)
                
                sleep_max_input = QSpinBox()
                sleep_max_input.setRange(1, 300)
                sleep_max_input.setValue(sleep_max or 60)
                form_layout.addRow("Random Sleep Max (seconds):", sleep_max_input)
                
                reply_input = QLineEdit()
                reply_input.setText(custom_reply or "")
                reply_input.setPlaceholderText("Custom reply message (leave empty to use global)")
                form_layout.addRow("Custom Reply Message:", reply_input)
                
                layout.addLayout(form_layout)
                
                # Add buttons
                button_layout = QHBoxLayout()
                save_button = QPushButton("Save")
                cancel_button = QPushButton("Cancel")
                
                save_button.clicked.connect(lambda: self.save_account_forwarder_settings(
                    dialog, phone, 
                    interval_min_input.value(),
                    interval_max_input.value(),
                    after_each_input.value(),
                    sleep_min_input.value(),
                    sleep_max_input.value(),
                    reply_input.text()
                ))
                cancel_button.clicked.connect(dialog.reject)
                
                button_layout.addWidget(save_button)
                button_layout.addWidget(cancel_button)
                
                layout.addLayout(button_layout)
                
                dialog.exec_()
                
        except Exception as e:
            self.log_activity(f"Error editing account forwarding settings: {str(e)}", True)
    
    def save_account_forwarder_settings(self, dialog, phone, interval_min, interval_max, 
                                       after_each, sleep_min, sleep_max, reply_message):
        """Save forwarding settings for an account."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.account_manager.db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE accounts 
                    SET intervalMin = ?, intervalMax = ?, afterEachSecond = ?,
                        randomSleepTimeMin = ?, randomSleepTimeMax = ?, customReplyMessage = ?
                    WHERE phone = ?
                """, (interval_min, interval_max, after_each, 
                     sleep_min, sleep_max, reply_message, phone))
                
                conn.commit()
                conn.close()
                
                self.log_activity(f"Updated forwarding settings for account {phone}", True)
                dialog.accept()
                
        except Exception as e:
            self.log_activity(f"Error saving account forwarding settings: {str(e)}", True)
            QMessageBox.critical(dialog, "Error", f"Failed to save settings: {str(e)}")

def main():
    """Main application entry point."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Modern style
    
    # Set application metadata
    app.setApplicationName("TG Checker")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("TG Tools")
    
    # Create and show main window
    window = TGCheckerApp()
    window.show()
    
    # Start the application
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 