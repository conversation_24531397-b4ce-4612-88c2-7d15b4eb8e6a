#!/usr/bin/env python3
"""
<PERSON>ript to fix the indentation of button creation code in main.py
"""

def main():
    # Read the original file
    with open('main.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    fixed_lines = []
    
    # Find and fix the indentation issue
    for line in lines:
        # Check for the specific pattern with incorrect indentation
        if "                # Create buttons with specific colors as requested" in line:
            # Fix the indentation
            fixed_line = "                # Create buttons with specific colors as requested\n"
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    # Write the updated content
    with open('main.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print("Button formatting has been fixed.")

if __name__ == "__main__":
    main() 