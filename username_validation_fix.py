"""
USERNAME VALIDATION FIX for TG Checker
Fixes: "No user has X as username" errors

This validates usernames/links before attempting to join them,
preventing unnecessary errors and improving success rates.
"""

import re
import asyncio
import logging
from typing import List, Tuple, Optional, Dict, Any
from telethon import TelegramClient
from telethon.errors import UsernameNotOccupiedError, UsernameInvalidError, FloodWaitError


class UsernameValidator:
    """
    CRITICAL FIX: Validates usernames and group links before attempting joins.
    
    This prevents "No user has X as username" errors by:
    1. Pre-validating username format
    2. Checking if username exists on Telegram
    3. Filtering out invalid/deleted groups
    4. Providing clean, valid group lists
    """
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.validation_cache = {}  # Cache validation results
        self.cache_timeout = 3600  # 1 hour cache
        
    def validate_username_format(self, username: str) -> <PERSON><PERSON>[bool, str, str]:
        """
        Validate username format.
        Returns: (is_valid, cleaned_username, error_message)
        """
        if not username or not username.strip():
            return False, "", "Empty username"
        
        username = username.strip()
        
        # Remove @ if present
        if username.startswith('@'):
            username = username[1:]
        
        # Extract username from t.me links
        if 't.me/' in username:
            match = re.search(r't\.me/([^/?]+)', username)
            if match:
                username = match.group(1)
            else:
                return False, "", "Invalid t.me link format"
        
        # Remove query parameters and other URL parts
        username = username.split('?')[0].split('/')[0]
        
        # Check username format
        if not re.match(r'^[a-zA-Z0-9_]+$', username):
            return False, "", f"Invalid username format: {username}"
        
        # Check length (Telegram usernames: 5-32 characters)
        if len(username) < 5:
            return False, "", f"Username too short (minimum 5 characters): {username}"
        
        if len(username) > 32:
            return False, "", f"Username too long (maximum 32 characters): {username}"
        
        # Check if starts with number (not allowed for usernames)
        if username[0].isdigit():
            return False, "", f"Username cannot start with number: {username}"
        
        return True, username, ""
    
    async def check_username_exists(self, client: TelegramClient, username: str) -> Tuple[bool, str, Optional[Dict]]:
        """
        Check if username exists on Telegram.
        Returns: (exists, error_message, entity_info)
        """
        try:
            # Check cache first
            cache_key = f"{username}"
            if cache_key in self.validation_cache:
                cached_time, cached_result = self.validation_cache[cache_key]
                if asyncio.get_event_loop().time() - cached_time < self.cache_timeout:
                    return cached_result
            
            # Try to get the entity
            entity = await client.get_entity(f"@{username}")
            
            # Extract entity information
            entity_info = {
                'id': entity.id,
                'title': getattr(entity, 'title', username),
                'username': getattr(entity, 'username', username),
                'is_channel': hasattr(entity, 'broadcast'),
                'is_group': hasattr(entity, 'megagroup') or (hasattr(entity, 'chat') and not hasattr(entity, 'broadcast')),
                'member_count': getattr(entity, 'participants_count', 0),
                'verified': getattr(entity, 'verified', False)
            }
            
            # Cache successful result
            result = (True, "", entity_info)
            self.validation_cache[cache_key] = (asyncio.get_event_loop().time(), result)
            
            return result
            
        except UsernameNotOccupiedError:
            error_msg = f"Username '@{username}' does not exist or is not available"
            result = (False, error_msg, None)
            self.validation_cache[cache_key] = (asyncio.get_event_loop().time(), result)
            return result
            
        except UsernameInvalidError:
            error_msg = f"Username '@{username}' is invalid"
            result = (False, error_msg, None)
            self.validation_cache[cache_key] = (asyncio.get_event_loop().time(), result)
            return result
            
        except FloodWaitError as e:
            error_msg = f"Flood wait {e.seconds}s while checking username '@{username}'"
            return False, error_msg, None
            
        except Exception as e:
            error_msg = f"Error checking username '@{username}': {str(e)}"
            return False, error_msg, None
    
    async def validate_group_list(self, client: TelegramClient, group_links: List[str]) -> Dict[str, Any]:
        """
        Validate a list of group links/usernames.
        Returns comprehensive validation results.
        """
        results = {
            'valid_groups': [],
            'invalid_groups': [],
            'format_errors': [],
            'not_found_errors': [],
            'flood_wait_errors': [],
            'other_errors': [],
            'summary': {}
        }
        
        self.logger.info(f"🔍 Validating {len(group_links)} group links...")
        
        for i, group_link in enumerate(group_links):
            self.logger.info(f"Validating {i+1}/{len(group_links)}: {group_link}")
            
            # Step 1: Validate format
            is_valid_format, cleaned_username, format_error = self.validate_username_format(group_link)
            
            if not is_valid_format:
                results['format_errors'].append({
                    'original': group_link,
                    'error': format_error
                })
                results['invalid_groups'].append(group_link)
                self.logger.warning(f"❌ Format error: {group_link} - {format_error}")
                continue
            
            # Step 2: Check if username exists
            try:
                exists, error_msg, entity_info = await self.check_username_exists(client, cleaned_username)
                
                if exists:
                    results['valid_groups'].append({
                        'original': group_link,
                        'username': cleaned_username,
                        'formatted_link': f"@{cleaned_username}",
                        'entity_info': entity_info
                    })
                    self.logger.info(f"✅ Valid: @{cleaned_username} - {entity_info.get('title', 'Unknown')}")
                    
                else:
                    if "does not exist" in error_msg or "not available" in error_msg:
                        results['not_found_errors'].append({
                            'original': group_link,
                            'username': cleaned_username,
                            'error': error_msg
                        })
                    elif "flood wait" in error_msg.lower():
                        results['flood_wait_errors'].append({
                            'original': group_link,
                            'username': cleaned_username,
                            'error': error_msg
                        })
                    else:
                        results['other_errors'].append({
                            'original': group_link,
                            'username': cleaned_username,
                            'error': error_msg
                        })
                    
                    results['invalid_groups'].append(group_link)
                    self.logger.warning(f"❌ Invalid: @{cleaned_username} - {error_msg}")
                
                # Add small delay to avoid rate limiting
                await asyncio.sleep(1)
                
            except Exception as e:
                error_msg = f"Validation error: {str(e)}"
                results['other_errors'].append({
                    'original': group_link,
                    'username': cleaned_username,
                    'error': error_msg
                })
                results['invalid_groups'].append(group_link)
                self.logger.error(f"❌ Error: {group_link} - {error_msg}")
        
        # Generate summary
        results['summary'] = {
            'total_checked': len(group_links),
            'valid_count': len(results['valid_groups']),
            'invalid_count': len(results['invalid_groups']),
            'format_errors_count': len(results['format_errors']),
            'not_found_count': len(results['not_found_errors']),
            'flood_wait_count': len(results['flood_wait_errors']),
            'other_errors_count': len(results['other_errors']),
            'success_rate': (len(results['valid_groups']) / len(group_links) * 100) if group_links else 0
        }
        
        self.logger.info(f"📊 Validation complete: {results['summary']['valid_count']}/{results['summary']['total_checked']} valid ({results['summary']['success_rate']:.1f}%)")
        
        return results
    
    def get_clean_group_list(self, validation_results: Dict[str, Any]) -> List[str]:
        """Get a clean list of valid group links for joining."""
        valid_groups = validation_results.get('valid_groups', [])
        return [group['formatted_link'] for group in valid_groups]
    
    def generate_validation_report(self, validation_results: Dict[str, Any]) -> str:
        """Generate a detailed validation report."""
        summary = validation_results['summary']
        
        report_lines = [
            "🔍 GROUP VALIDATION REPORT",
            "=" * 40,
            f"📊 Total Groups Checked: {summary['total_checked']}",
            f"✅ Valid Groups: {summary['valid_count']}",
            f"❌ Invalid Groups: {summary['invalid_count']}",
            f"📈 Success Rate: {summary['success_rate']:.1f}%",
            "",
            "📋 BREAKDOWN:",
            f"  • Format Errors: {summary['format_errors_count']}",
            f"  • Not Found: {summary['not_found_count']}",
            f"  • Flood Wait: {summary['flood_wait_count']}",
            f"  • Other Errors: {summary['other_errors_count']}",
            ""
        ]
        
        # Add details for each error type
        if validation_results['format_errors']:
            report_lines.extend([
                "❌ FORMAT ERRORS:",
                *[f"  • {error['original']} - {error['error']}" for error in validation_results['format_errors'][:5]],
                f"  ... and {len(validation_results['format_errors']) - 5} more" if len(validation_results['format_errors']) > 5 else "",
                ""
            ])
        
        if validation_results['not_found_errors']:
            report_lines.extend([
                "❌ NOT FOUND (These groups don't exist):",
                *[f"  • {error['original']} - @{error['username']} does not exist" for error in validation_results['not_found_errors'][:5]],
                f"  ... and {len(validation_results['not_found_errors']) - 5} more" if len(validation_results['not_found_errors']) > 5 else "",
                ""
            ])
        
        if validation_results['valid_groups']:
            report_lines.extend([
                "✅ VALID GROUPS (Ready for joining):",
                *[f"  • {group['formatted_link']} - {group['entity_info'].get('title', 'Unknown')}" for group in validation_results['valid_groups'][:10]],
                f"  ... and {len(validation_results['valid_groups']) - 10} more" if len(validation_results['valid_groups']) > 10 else "",
                ""
            ])
        
        return "\n".join(line for line in report_lines if line is not None)


# INTEGRATION FUNCTIONS FOR MAIN.PY

async def validate_and_clean_group_list(client: TelegramClient, group_links: List[str], logger=None) -> Tuple[List[str], str]:
    """
    CRITICAL FIX: Validate and clean group list before joining.
    
    This prevents "No user has X as username" errors by filtering out invalid groups.
    
    Returns: (clean_group_list, validation_report)
    """
    validator = UsernameValidator(logger)
    
    # Validate all groups
    validation_results = await validator.validate_group_list(client, group_links)
    
    # Get clean list of valid groups
    clean_groups = validator.get_clean_group_list(validation_results)
    
    # Generate report
    report = validator.generate_validation_report(validation_results)
    
    return clean_groups, report

def fix_money_vaults_error(group_links: List[str]) -> List[str]:
    """
    QUICK FIX: Remove the problematic @money_vaults and other invalid entries.
    """
    # Known invalid usernames to remove
    invalid_usernames = [
        'money_vaults',
        'MoneyVaults', 
        'moneyvaults'
    ]
    
    cleaned_links = []
    removed_count = 0
    
    for link in group_links:
        # Extract username from link
        username = link.strip().replace('@', '').replace('t.me/', '').split('?')[0].split('/')[0]
        
        if username.lower() not in [inv.lower() for inv in invalid_usernames]:
            cleaned_links.append(link)
        else:
            removed_count += 1
            print(f"🗑️  Removed invalid group: {link}")
    
    if removed_count > 0:
        print(f"✅ Cleaned group list: Removed {removed_count} invalid entries")
    
    return cleaned_links

async def smart_group_validation(main_app, task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    SMART FIX: Validate group list before starting join task.
    """
    try:
        # Get group links from task
        import json
        group_links = task_data.get('group_links', [])
        if isinstance(group_links, str):
            group_links = json.loads(group_links)
        
        # Get Telegram client
        client = await main_app._get_joining_client(task_data['account_phone'])
        if not client:
            return task_data  # Return unchanged if no client
        
        # Validate groups
        clean_groups, report = await validate_and_clean_group_list(client, group_links, main_app.logger)
        
        # Update task with clean group list
        task_data['group_links'] = json.dumps(clean_groups)
        task_data['total_groups'] = len(clean_groups)
        
        # Log validation report
        if hasattr(main_app, 'log_joining_message'):
            main_app.log_joining_message('info', task_data['id'], 
                f"Group validation complete: {len(clean_groups)}/{len(group_links)} valid groups")
        
        print(report)
        
        return task_data
        
    except Exception as e:
        if hasattr(main_app, 'logger'):
            main_app.logger.error(f"Group validation failed: {e}")
        return task_data  # Return unchanged on error


if __name__ == "__main__":
    # Test the validator
    validator = UsernameValidator()
    
    # Test format validation
    test_usernames = [
        "@money_vaults",  # The problematic one
        "https://t.me/telegram",
        "valid_username",
        "123invalid",  # Invalid (starts with number)
        "ab",  # Invalid (too short)
        ""  # Invalid (empty)
    ]
    
    print("🔍 Testing username format validation:")
    for username in test_usernames:
        is_valid, cleaned, error = validator.validate_username_format(username)
        status = "✅" if is_valid else "❌"
        print(f"{status} {username} -> {cleaned} ({error if error else 'Valid'})")
    
    print("\n✅ Username Validator test completed!") 