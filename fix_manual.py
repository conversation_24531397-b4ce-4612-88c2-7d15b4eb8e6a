import os
import sys
import shutil

def fix_manual():
    """Fix problematic code sections manually."""
    # Create a new file with all fixes
    input_file = "main.py"
    output_file = "main_manual_fixed.py"
    
    # Create a backup
    backup_file = f"{input_file}.manual_bak"
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.readlines()
    
    # Fix line 576 - indentation after if statement
    for i in range(570, 580):
        if i < len(content) and "if self.settings.value(\"auto_start_monitor\"" in content[i]:
            # Fix indentation of the next line
            indent = ' ' * (content[i].find('if'))
            content[i+1] = indent + "    self.start_monitor()\n"
            print(f"Fixed line {i+1}")
    
    # Fix lines 2676-2677 - for and if statement indentation
    for i in range(2670, 2680):
        if i < len(content) and "for acc in accounts:" in content[i]:
            # Fix indentation of the next lines
            indent = ' ' * (content[i].find('for'))
            content[i+1] = indent + "    if acc.get(\"phone\") == phone:\n"
            content[i+2] = indent + "        account = acc\n"
            print(f"Fixed lines {i+1}-{i+2}")
    
    # Fix lines 3013-3014 - else statement and indentation
    for i in range(3010, 3020):
        if i < len(content) and "else:" in content[i] and not content[i].startswith(' '):
            # Get proper indentation from surrounding code
            for j in range(max(0, i-10), i):
                if content[j].strip().startswith('if '):
                    indent = ' ' * (content[j].find('if') - 4)  # Get indentation of the if statement
                    # Fix the else and following line
                    content[i] = indent + "            else:\n"
                    content[i+1] = indent + "                self.log_activity(f\"Could not retrieve user info for {phone}\")\n"
                    print(f"Fixed lines {i+1}-{i+2}")
                    break
    
    # Fix line 3062 - try without except
    for i in range(3060, 3070):
        if i < len(content) and content[i].strip() == "try:":
            # Look for an except
            has_except = False
            for j in range(i+1, min(i+10, len(content))):
                if "except" in content[j]:
                    has_except = True
                    break
            
            if not has_except:
                # Get indentation
                indent = ' ' * (content[i].find('try'))
                # Find where to insert except
                insert_pos = i+1
                while insert_pos < len(content) and content[insert_pos].strip():
                    insert_pos += 1
                
                # Insert except block
                content.insert(insert_pos, indent + "except Exception as e:\n")
                content.insert(insert_pos+1, indent + "    self.log_activity(f\"Error: {str(e)}\")\n")
                print(f"Fixed line {i+1} (added except block)")
    
    # Fix lines 3073-3074 - unexpected indentation
    for i in range(3070, 3080):
        if i < len(content) and "self.logger.error" in content[i] and not content[i].startswith(' '):
            # Get indentation from surrounding code
            indent = "            "  # Default to reasonable indentation
            content[i] = indent + content[i].lstrip()
            print(f"Fixed line {i+1}")
    
    # Fix line 3086 - missing colon
    for i in range(3080, 3090):
        if i < len(content) and "else" in content[i] and not content[i].strip().endswith(':'):
            content[i] = content[i].rstrip() + ":\n"
            print(f"Fixed line {i+1}")
    
    # Save the fixed content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(content)
    
    print(f"Fixed all issues and saved to {output_file}")
    return output_file

if __name__ == "__main__":
    output_file = fix_manual()
    
    # Create batch file
    batch_content = """@echo off
echo Running TG Checker with all issues fixed manually...
python main_manual_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_manual_fixed.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Jarandni programi chakkrawi dawsti...
echo Hamu kishakan charesar kran.
python main_manual_fixed.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Bo zanini ziatr sairi faily log bka.
    pause
)
pause
"""
    
    with open("run_manual_fixed_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print(f"Created batch files to run the fixed application")
    print(f"You can now run: python {output_file}")
    print(f"Or use the batch files: run_manual_fixed.bat or run_manual_fixed_kurdish.bat") 