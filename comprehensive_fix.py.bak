import re
import os
import sys
import ast
import traceback

def check_syntax(filename):
    """Check Python file for syntax errors."""
    print(f"Checking syntax in {filename}...")
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            content = file.read()
        ast.parse(content)
        return []
    except SyntaxError as e:
        return [(e.lineno, str(e))]
    except Exception as e:
        return [(0, f"Error parsing file: {str(e)}")]

def check_indentation(filename):
    """Check file for consistent indentation."""
    print(f"Checking indentation in {filename}...")
    issues = []
    with open(filename, 'r', encoding='utf-8') as file:
        lines = file.readlines()
    
    # Check for mixed tabs and spaces
    has_tabs = any('\t' in line for line in lines)
    has_spaces = any('    ' in line for line in lines)
    if has_tabs and has_spaces:
        issues.append((0, "Mixed tabs and spaces for indentation"))
    
    # Check for consistent indentation patterns
    for i, line in enumerate(lines):
        stripped = line.lstrip()
        if not stripped or stripped.startswith('#'):
            continue
        
        indent = len(line) - len(stripped)
        if indent > 0 and indent % 4 != 0:
            issues.append((i+1, f"Inconsistent indentation: {indent} spaces"))
    
    return issues

def check_if_expressions(filename):
    """Check for incomplete if expressions."""
    print(f"Checking conditional expressions in {filename}...")
    issues = []
    with open(filename, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Look for potential incomplete if expressions
    pattern = r'(\w+\s*=\s*\w+\.get\([^)]+\)\s+if\s+\w+)(?!\s+else)'
    for match in re.finditer(pattern, content):
        line_number = content[:match.start()].count('\n') + 1
        issues.append((line_number, f"Possible incomplete if expression: {match.group(0)}"))
    
    # Look for malformed if expressions
    pattern = r'if\s+\w+\s+else\s+\d+\w+'  # Matches patterns like "if task else 0ask"
    for match in re.finditer(pattern, content):
        line_number = content[:match.start()].count('\n') + 1
        issues.append((line_number, f"Malformed if-else expression: {match.group(0)}"))
    
    return issues

def check_try_blocks(filename):
    """Check for proper try-except blocks."""
    print(f"Checking try-except blocks in {filename}...")
    issues = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        in_try = False
        try_line = 0
        has_except = False
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('try:'):
                in_try = True
                try_line = i + 1
                has_except = False
            elif in_try and stripped.startswith('except'):
                has_except = True
            elif in_try and stripped.startswith('finally:'):
                if not has_except:
                    issues.append((try_line, "Try block with finally but no except clause"))
            elif (in_try and not has_except and 
                  (stripped.startswith('def ') or stripped.startswith('class ') or i == len(lines) - 1)):
                issues.append((try_line, "Try block without except clause"))
                in_try = False
    except Exception as e:
        issues.append((0, f"Error checking try blocks: {str(e)}"))
    
    return issues

def check_for_loops(filename):
    """Check for proper indentation after for loops."""
    print(f"Checking for loops in {filename}...")
    issues = []
    
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            lines = file.readlines()
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('for ') and stripped.endswith(':'):
                # Check next non-empty line for proper indentation
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    j += 1
                
                if j < len(lines):
                    current_indent = len(line) - len(line.lstrip())
                    next_indent = len(lines[j]) - len(lines[j].lstrip())
                    if next_indent <= current_indent:
                        issues.append((i+1, f"For loop without properly indented block"))
    except Exception as e:
        issues.append((0, f"Error checking for loops: {str(e)}"))
    
    return issues

def fix_issues(filename, issues):
    """Fix identified issues in the file."""
    if not issues:
        print(f"No issues to fix in {filename}")
        return
    
    print(f"Fixing {len(issues)} issues in {filename}...")
    
    # Make a backup of the original file
    backup_file = f"{filename}.bak"
    os.system(f'copy "{filename}" "{backup_file}"')
    
    with open(filename, 'r', encoding='utf-8') as file:
        content = file.read()
    
    # Fix incomplete if expressions
    content = re.sub(r'(\w+\s*=\s*\w+\.get\([^)]+\)\s+if\s+\w+)(?!\s+else)', r'\1 else 0', content)
    
    # Fix malformed if expressions like "if task else 0"
    content = re.sub(r'if\s+(\w+)\s+else\s+0\w+\s+else\s+0', r'if \1 else 0', content)
    
    # Fix any remaining "ask else 0" fragments
    content = content.replace("0", "0")
    content = content.replace("0", "0")
    
    # Write fixed content back to file
    with open(filename, 'w', encoding='utf-8') as file:
        file.write(content)
    
    print(f"Fixed issues in {filename}")

def fix_all_syntax_issues():
    """Fix all syntax issues in Python files."""
    print("Starting comprehensive code inspection and fixes...")
    
    # Get list of Python files
    python_files = [file for file in os.listdir() if file.endswith('.py')]
    
    for filename in python_files:
        all_issues = []
        
        # Collect issues from different checks
        all_issues.extend(check_syntax(filename))
        all_issues.extend(check_indentation(filename))
        all_issues.extend(check_if_expressions(filename))
        all_issues.extend(check_try_blocks(filename))
        all_issues.extend(check_for_loops(filename))
        
        # Sort issues by line number
        all_issues.sort(key=lambda x: x[0])
        
        # Print issues
        if all_issues:
            print(f"\nIssues in {filename}:")
            for line, issue in all_issues:
                print(f"  Line {line}: {issue}")
            
            # Fix issues
            fix_issues(filename, all_issues)
        else:
            print(f"\nNo issues found in {filename}")

def test_application():
    """Test the application to ensure it runs without errors."""
    print("\nTesting application startup...")
    try:
        # Run the application in the background
        os.system("start /B python main.py")
        print("Application started successfully")
        return True
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        return False

if __name__ == "__main__":
    fix_all_syntax_issues()
    test_application() 