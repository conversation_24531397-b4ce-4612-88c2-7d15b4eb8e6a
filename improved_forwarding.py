import logging
import asyncio
import re
from telethon.errors import (
    <PERSON>Wait<PERSON>rro<PERSON>, ChatAdminRequiredError, ChannelPrivateError,
    UserBannedInChannelError, PeerIdInvalidError, MessageIdInvalidError
)
from telethon.tl.types import In<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Input<PERSON>eerUser, Input<PERSON>eer<PERSON>hat
from telethon.tl.functions.channels import JoinChannelRe<PERSON>, GetFullChannelRequest
from telethon.tl.functions.messages import GetHistoryRequest

class ImprovedForwarder:
    """Enhanced message forwarding functionality for Telegram."""
    
    def __init__(self, client, logger=None):
        """
        Initialize with a Telethon client.
        
        Args:
            client: A connected Telethon client
            logger: Optional logger
        """
        self.client = client
        self.logger = logger or logging.getLogger('improved_forwarder')
    
    async def resolve_entity_properly(self, entity_str):
        """
        Properly resolve an entity with multiple fallback mechanisms.
        
        Args:
            entity_str: Username, URL, or entity ID string
            
        Returns:
            Resolved entity or None if resolution fails
        """
        if not entity_str:
            return None
            
        # Remove common prefixes
        if entity_str.startswith('https://t.me/'):
            entity_str = entity_str.split('https://t.me/')[1]
        elif entity_str.startswith('@'):
            entity_str = entity_str[1:]
            
        # Split by slashes to handle topics
        parts = entity_str.split('/')
        username = parts[0]
        topic_id = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else None
        
        # Try multiple resolution methods
        entity = None
        try:
            # Method 1: Direct resolution
            try:
                self.logger.info(f"Attempting direct entity resolution for: {username}")
                entity = await self.client.get_entity(username)
                self.logger.info(f"Successfully resolved entity type: {type(entity).__name__}")
                return entity
            except Exception as e:
                self.logger.info(f"Direct resolution failed: {str(e)}, trying alternatives")
                
            # Method 2: Get via recent messages
            try:
                self.logger.info(f"Trying to resolve entity through messages")
                messages = await self.client.get_messages(username, limit=1)
                if messages and len(messages) > 0:
                    entity = await self.client.get_entity(messages[0].peer_id)
                    self.logger.info(f"Successfully resolved entity through messages")
                    return entity
            except Exception as e:
                self.logger.info(f"Message-based resolution failed: {str(e)}")
                
            # Method 3: Join channel first
            try:
                self.logger.info(f"Trying to join channel first")
                await self.client(JoinChannelRequest(username))
                entity = await self.client.get_entity(username)
                self.logger.info(f"Successfully joined and resolved entity")
                return entity
            except Exception as e:
                self.logger.info(f"Join-based resolution failed: {str(e)}")
                
            # If username is numeric, try as chat ID
            if username.isdigit():
                try:
                    chat_id = int(username)
                    self.logger.info(f"Trying numeric ID: {chat_id}")
                    entity = await self.client.get_entity(chat_id)
                    self.logger.info(f"Successfully resolved numeric entity")
                    return entity
                except Exception as e:
                    self.logger.info(f"Numeric ID resolution failed: {str(e)}")
                    
            return None
                
        except Exception as e:
            self.logger.error(f"Entity resolution failed: {str(e)}")
            return None
    
    async def get_message_by_id(self, source, message_id):
        """
        Get a specific message by ID with proper error handling.
        
        Args:
            source: Source entity (channel, chat, etc.)
            message_id: Message ID to retrieve
            
        Returns:
            Message object or None if retrieval fails
        """
        try:
            # Try to resolve the source entity first
            source_entity = await self.resolve_entity_properly(source)
            
            if not source_entity:
                self.logger.error(f"Could not resolve source entity: {source}")
                return None
                
            # Get the message
            message = await self.client.get_messages(source_entity, ids=int(message_id))
            
            if not message:
                self.logger.error(f"Message {message_id} not found in {source}")
                return None
                
            return message
            
        except Exception as e:
            self.logger.error(f"Error getting message: {str(e)}")
            return None
    
    async def forward_message(self, source, message_id, destination, topic_id=None):
        """
        Forward a message using the most reliable method.
        
        Args:
            source: Source entity (channel, chat, username)
            message_id: Message ID to forward
            destination: Destination entity (channel, chat, username)
            topic_id: Optional forum topic ID
            
        Returns:
            dict with status and result information
        """
        try:
            # Step 1: Resolve the source entity
            source_entity = await self.resolve_entity_properly(source)
            if not source_entity:
                return {"success": False, "error": "Could not resolve source entity"}
                
            # Step 2: Resolve the destination entity
            dest_entity = await self.resolve_entity_properly(destination)
            if not dest_entity:
                return {"success": False, "error": "Could not resolve destination entity"}
                
            # Step 3: Get the message
            message = await self.get_message_by_id(source_entity, message_id)
            if not message:
                return {"success": False, "error": f"Could not find message {message_id}"}
                
            # Step 4: Forward the message
            self.logger.info(f"Attempting to forward message {message_id} from {source} to {destination}")
            
            # Try multiple methods in sequence
            forwarded = None
            error = None
            
            # Method 1: Direct forwarding - most preserves the forwarded appearance
            try:
                # If we have a topic, try to use it as reply_to
                if topic_id:
                    try:
                        forwarded = await self.client.forward_messages(
                            entity=dest_entity,
                            messages=message,
                            from_peer=source_entity,
                            reply_to=topic_id
                        )
                    except Exception as e:
                        # If reply_to fails, try getting a message in the topic
                        self.logger.info(f"Forward with topic_id as reply_to failed: {str(e)}")
                        error = str(e)
                        
                        # Get messages in this topic
                        try:
                            topic_msgs = await self.client.get_messages(
                                entity=dest_entity,
                                limit=1,
                                # Some versions of Telethon support reply_to for topics
                                reply_to=topic_id if hasattr(self.client, 'get_messages') and 'reply_to' in self.client.get_messages.__code__.co_varnames else None
                            )
                            
                            if topic_msgs and len(topic_msgs) > 0:
                                forwarded = await self.client.forward_messages(
                                    entity=dest_entity,
                                    messages=message,
                                    from_peer=source_entity,
                                    reply_to=topic_msgs[0].id
                                )
                        except Exception as topic_msg_error:
                            self.logger.info(f"Failed to get topic messages: {str(topic_msg_error)}")
                            # Fall through to next method
                else:
                    # Regular forwarding
                    forwarded = await self.client.forward_messages(
                        entity=dest_entity,
                        messages=message,
                        from_peer=source_entity
                    )
                    
                if forwarded:
                    self.logger.info(f"Successfully forwarded message using native forwarding")
                    return {"success": True, "method": "native_forward", "message": forwarded}
                    
            except Exception as e:
                error = str(e)
                self.logger.info(f"Native forwarding failed: {error}")
                # Fall through to next method
                
            # Method 2: Send message with content
            if not forwarded:
                try:
                    # Extract content from original message
                    text = message.text or message.message or ""
                    
                    # Format as forwarded
                    formatted_text = f"📨 Forwarded from {source}:\n\n{text}"
                    
                    # Send as new message
                    sent = await self.client.send_message(
                        entity=dest_entity,
                        message=formatted_text,
                        file=message.media if hasattr(message, 'media') else None,
                        reply_to=topic_id if topic_id else None
                    )
                    
                    if sent:
                        self.logger.info(f"Successfully sent message content as new message")
                        return {"success": True, "method": "content_send", "message": sent}
                        
                except Exception as e:
                    error = f"{error}, then content sending failed: {str(e)}"
                    self.logger.info(f"Content sending failed: {str(e)}")
                    # Fall through to final method
            
            # Method 3: Minimal text message
            if not forwarded:
                try:
                    # Send minimal text
                    sent = await self.client.send_message(
                        entity=destination,  # Use raw destination as last resort
                        message=f"⚠️ Forwarded message from {source}, ID: {message_id}\n\nUnable to forward content directly."
                    )
                    
                    if sent:
                        self.logger.info(f"Sent minimal text fallback")
                        return {"success": True, "method": "minimal_fallback", "message": sent}
                        
                except Exception as e:
                    error = f"{error}, then minimal fallback failed: {str(e)}"
                    self.logger.error(f"All forwarding methods failed: {error}")
                    
            return {"success": False, "error": error or "Unknown error in forwarding"}
            
        except Exception as e:
            self.logger.error(f"Error in forward_message: {str(e)}")
            return {"success": False, "error": str(e)} 