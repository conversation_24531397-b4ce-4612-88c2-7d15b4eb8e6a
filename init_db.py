"""
Database initialization script for TG Checker.
This script creates the initial database structure and adds test accounts if needed.
"""

import os
import sqlite3
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def init_database(db_path="tg_checker.db", add_test_accounts=False):
    """Initialize the database with required tables."""
    try:
        # Check if database already exists
        db_exists = os.path.exists(db_path)
        
        # Connect to the database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Create accounts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                phone TEXT PRIMARY KEY,
                api_id TEXT,
                api_hash TEXT,
                session_file TEXT,
                active INTEGER DEFAULT 0,
                status TEXT DEFAULT 'unknown',
                last_check TEXT,
                errors INTEGER DEFAULT 0,
                notes TEXT
            )
        ''')
        
        # Create errors table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS errors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT,
                error_type TEXT,
                error_message TEXT,
                timestamp TEXT,
                resolved INTEGER DEFAULT 0,
                FOREIGN KEY (phone) REFERENCES accounts (phone)
            )
        ''')
        
        # Create settings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT
            )
        ''')
        
        # Initialize default settings if the database is new
        if not db_exists:
            # Default settings
            default_settings = [
                ("check_interval", "300", "Account check interval in seconds"),
                ("auto_fix", "1", "Automatically fix account issues (0=off, 1=on)"),
                ("auto_start_monitor", "1", "Start monitor automatically on startup (0=off, 1=on)"),
                ("minimize_to_tray", "1", "Minimize to system tray (0=off, 1=on)"),
                ("dark_mode", "0", "Use dark mode (0=off, 1=on)"),
                ("log_level", "INFO", "Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)")
            ]
            
            cursor.executemany(
                "INSERT OR REPLACE INTO settings (key, value, description) VALUES (?, ?, ?)",
                default_settings
            )
            
            logger.info("Initialized default settings")
        
        # Add test accounts if requested and database is new
        if add_test_accounts and not db_exists:
            test_accounts = [
                ("+**********", "12345", "abcdef**********abcdef", "sessions/+**********", 1, "active", 
                 datetime.now().isoformat(), 0, "Test account 1"),
                ("+**********", "67890", "fedcba**********fedcba", "sessions/+**********", 0, "inactive", 
                 datetime.now().isoformat(), 0, "Test account 2")
            ]
            
            cursor.executemany(
                "INSERT OR REPLACE INTO accounts (phone, api_id, api_hash, session_file, active, status, last_check, errors, notes) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
                test_accounts
            )
            
            logger.info("Added test accounts")
        
        # Commit changes and close connection
        conn.commit()
        conn.close()
        
        logger.info(f"Database initialized successfully at: {db_path}")
        return True
        
    except Exception as e:
        logger.error(f"Database initialization error: {str(e)}")
        return False

if __name__ == "__main__":
    # Initialize the database with test accounts when run directly
    init_database(add_test_accounts=True)
    print("Database initialization complete!") 