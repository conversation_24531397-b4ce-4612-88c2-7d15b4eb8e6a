[2025-05-21 11:26:43] INFO: Connect button clicked for account +************
[2025-05-21 11:26:43] INFO: Connecting to Telegram for account +************
[2025-05-21 11:26:43] INFO: Sending code request for account +************
[2025-05-21 11:26:48] INFO: Code request successful for account +************
[2025-05-21 11:26:48] INFO: Verification code requested for account +************
[2025-05-21 11:27:02] INFO: Verification code submitted for account +************
[2025-05-21 11:27:02] INFO: Verifying code for account +************
[2025-05-21 11:30:19] INFO: Connect button clicked for account +************
[2025-05-21 11:30:19] INFO: Connecting to Telegram for account +************
[2025-05-21 11:30:19] INFO: Sending code request for account +************
[2025-05-21 11:30:20] INFO: Code request successful for account +************
[2025-05-21 11:30:20] INFO: Verification code requested for account +************
[2025-05-21 11:31:15] INFO: Verification code submitted for account +************
[2025-05-21 11:31:15] INFO: Verifying code for account +************
[2025-05-21 11:49:33] INFO: Connect button clicked for account +************
[2025-05-21 11:49:33] INFO: Connecting to Telegram for account +************
[2025-05-21 11:49:33] INFO: Sending code request for account +************
[2025-05-21 11:49:34] INFO: Code request successful for account +************
[2025-05-21 11:49:34] INFO: Verification code requested for account +************
[2025-05-21 11:50:47] INFO: Verification code submitted for account +************
[2025-05-21 11:50:47] INFO: Verifying code for account +************
[2025-05-21 11:57:40] INFO: Connect button clicked for account +************
[2025-05-21 11:57:40] INFO: Connecting to Telegram for account +************
[2025-05-21 11:57:40] INFO: Sending code request for account +************
[2025-05-21 11:57:45] INFO: Code request successful for account +************
[2025-05-21 11:57:45] INFO: Verification code requested for account +************
[2025-05-21 11:57:56] INFO: Verification code submitted for account +************
[2025-05-21 11:57:56] INFO: Verifying code for account +************
[2025-05-21 11:57:56] ERROR: Exception during code verification for account +************: 'accountdialog' object has no attribute 'update_verification_status'
[2025-05-21 11:57:56] ERROR: Code verification error for account +************: 'AccountDialog' object has no attribute 'update_verification_status'
[2025-05-21 12:00:05] INFO: Connect button clicked for account +************
[2025-05-21 12:00:05] INFO: Connecting to Telegram for account +************
[2025-05-21 12:00:05] INFO: Sending code request for account +************
[2025-05-21 12:00:06] INFO: Code request successful for account +************
[2025-05-21 12:00:06] INFO: Verification code requested for account +************
[2025-05-21 12:00:19] INFO: Verification code submitted for account +************
[2025-05-21 12:00:19] INFO: Verifying code for account +************
[2025-05-21 12:00:49] ERROR: Code verification timed out for account +************
[2025-05-21 12:00:49] INFO: Session cleaned after timeout for +************
[2025-05-21 12:06:23] INFO: Connect button clicked for account +************
[2025-05-21 12:06:23] INFO: Connecting to Telegram for account +************
[2025-05-21 12:06:23] INFO: Sending code request for account +************
[2025-05-21 12:06:24] INFO: Code request successful for account +************
[2025-05-21 12:06:24] INFO: Verification code requested for account +************
[2025-05-21 12:06:35] INFO: Verification code submitted for account +************
[2025-05-21 12:06:35] INFO: Verifying code for account +************
[2025-05-21 12:07:05] ERROR: Code verification timed out for account +************
[2025-05-21 12:07:05] INFO: Session cleaned after timeout for +************
[2025-05-21 12:15:54] INFO: Connect button clicked for account +************
[2025-05-21 12:15:54] INFO: Connecting to Telegram for account +************
[2025-05-21 12:15:54] INFO: Sending code request for account +************
[2025-05-21 12:15:59] INFO: Code request successful for account +************
[2025-05-21 12:15:59] INFO: Verification code requested for account +************
[2025-05-21 12:16:16] INFO: Verification code submitted for account +************
[2025-05-21 12:16:16] INFO: Verifying code for account +************
[2025-05-21 12:16:46] ERROR: Code verification timed out for account +************
[2025-05-21 12:16:46] INFO: Session cleaned after timeout for +************
[2025-05-21 16:40:21] INFO: Connect button clicked for account +************
[2025-05-21 16:40:21] INFO: Connecting to Telegram for account +************
[2025-05-21 16:40:21] INFO: Sending code request for account +************
[2025-05-21 16:40:26] INFO: Code request successful for account +************
[2025-05-21 16:40:26] INFO: Verification code requested for account +************
[2025-05-21 16:40:48] INFO: Verification code submitted for account +************
[2025-05-21 16:40:48] INFO: Verifying code for account +************
[2025-05-22 21:13:09] INFO: Connect button clicked for account ************
[2025-05-22 21:13:09] INFO: Connecting to Telegram for account ************
[2025-05-22 21:13:09] INFO: Sending code request for account ************
[2025-05-22 21:13:15] INFO: Code request successful for account ************
[2025-05-22 21:13:15] INFO: Verification code requested for account ************
[2025-05-22 21:13:49] INFO: Verification code submitted for account ************
[2025-05-22 21:13:49] INFO: Verifying code for account ************
[2025-05-22 21:21:37] INFO: Connect button clicked for account ************
[2025-05-22 21:21:37] INFO: Connecting to Telegram for account ************
[2025-05-22 21:21:37] INFO: Sending code request for account ************
[2025-05-22 21:21:43] INFO: Code request successful for account ************
[2025-05-22 21:21:43] INFO: Verification code requested for account ************
[2025-05-22 21:22:04] INFO: Verification code submitted for account ************
[2025-05-22 21:22:04] INFO: Verifying code for account ************
[2025-05-22 21:26:20] INFO: Connect button clicked for account +************
[2025-05-22 21:26:20] INFO: Connecting to Telegram for account +************
[2025-05-22 21:26:20] INFO: Sending code request for account +************
[2025-05-22 21:26:25] INFO: Code request successful for account +************
[2025-05-22 21:26:25] INFO: Verification code requested for account +************
[2025-05-22 21:26:37] INFO: Verification code submitted for account +************
[2025-05-22 21:26:37] INFO: Verifying code for account +************
[2025-05-22 21:32:43] INFO: Connect button clicked for account ************
[2025-05-22 21:32:43] INFO: Connecting to Telegram for account ************
[2025-05-22 21:32:43] INFO: Sending code request for account ************
[2025-05-22 21:32:48] INFO: Code request successful for account ************
[2025-05-22 21:32:48] INFO: Verification code requested for account ************
[2025-05-22 21:32:58] INFO: Verification code submitted for account ************
[2025-05-22 21:39:22] INFO: Connect button clicked for account ************
[2025-05-22 21:39:22] INFO: Connecting to Telegram for account ************
[2025-05-22 21:39:22] INFO: Sending code request for account ************
[2025-05-22 21:39:23] INFO: Code request successful for account ************
[2025-05-22 21:39:23] INFO: Verification code requested for account ************
[2025-05-22 21:39:34] INFO: Verification code submitted for account ************
[2025-05-22 21:43:22] INFO: Connect button clicked for account ************
[2025-05-22 21:43:22] INFO: Connecting to Telegram for account ************
[2025-05-22 21:43:22] INFO: Sending code request for account ************
[2025-05-22 21:43:27] INFO: Code request successful for account ************
[2025-05-22 21:43:27] INFO: Verification code requested for account ************
[2025-05-22 21:43:36] INFO: Verification code submitted for account ************
[2025-05-22 21:52:38] INFO: Connect button clicked for account ************
[2025-05-22 21:52:38] INFO: Connecting to Telegram for account ************
[2025-05-22 21:52:38] INFO: Sending code request for account ************
[2025-05-22 21:52:43] INFO: Code request successful for account ************
[2025-05-22 21:52:43] INFO: Verification code requested for account ************
[2025-05-22 21:53:25] INFO: Verification code submitted for account ************
[2025-05-22 21:53:27] INFO: 2FA password required for account ************
[2025-05-22 21:53:35] INFO: 2FA password submitted for account ************
[2025-05-22 21:53:36] INFO: Successfully added account ************ (Admin / @)
[2025-05-23 18:35:34] INFO: Connect button clicked for account +*************
[2025-05-23 18:35:34] INFO: Connecting to Telegram for account +*************
[2025-05-23 18:35:34] INFO: Sending code request for account +*************
[2025-05-23 18:35:39] INFO: Code request successful for account +*************
[2025-05-23 18:35:39] INFO: Verification code requested for account +*************
[2025-05-23 18:35:53] INFO: Verification code submitted for account +*************
[2025-05-23 18:35:55] INFO: 2FA password required for account +*************
[2025-05-23 18:36:07] INFO: 2FA password submitted for account +*************
[2025-05-23 18:36:10] INFO: Successfully added account +************* with info: FaceBluff 2 / @FaceBluf
[2025-05-23 21:07:56] INFO: Starting account fix process for ************
[2025-05-23 21:07:56] INFO: Fixing account ************ (API ID: ********, Status: 1, Errors: 0)
[2025-05-23 21:07:56] INFO: Creating TelegramClient for account ************
[2025-05-23 21:07:56] INFO: Attempting to get account info for ************
[2025-05-23 21:07:56] INFO: Updated account info for ************: Admin 2 / @
[2025-05-23 21:07:56] INFO: Account ************ fixed successfully
[2025-05-23 21:08:04] INFO: Starting account fix process for +*************
[2025-05-23 21:08:04] INFO: Fixing account +************* (API ID: ********, Status: 1, Errors: 0)
[2025-05-23 21:08:04] INFO: Creating TelegramClient for account +*************
[2025-05-23 21:08:04] INFO: Attempting to get account info for +*************
[2025-05-23 21:08:05] INFO: Account +************* fixed successfully
[2025-05-24 09:41:11] INFO: Starting account fix process for ************
[2025-05-24 09:41:11] INFO: Fixing account ************ (API ID: ********, Status: 1, Errors: 0)
[2025-05-24 09:41:11] INFO: Creating TelegramClient for account ************
[2025-05-24 09:41:11] INFO: Attempting to get account info for ************
[2025-05-24 09:41:12] INFO: Updated account info for ************: Admin 2 / @
[2025-05-24 09:41:12] INFO: Account ************ fixed successfully
[2025-05-24 09:41:12] INFO: Starting account fix process for +*************
[2025-05-24 09:41:12] INFO: Fixing account +************* (API ID: ********, Status: 1, Errors: 0)
[2025-05-24 09:41:12] INFO: Creating TelegramClient for account +*************
[2025-05-24 09:41:12] INFO: Attempting to get account info for +*************
[2025-05-24 09:41:13] INFO: Account +************* fixed successfully
[2025-05-26 07:46:30] INFO: Starting account fix process for ************
[2025-05-26 07:46:30] INFO: Fixing account ************ (API ID: ********, Status: 1, Errors: 0)
[2025-05-26 07:46:30] INFO: Creating TelegramClient for account ************
[2025-05-26 07:46:30] INFO: Attempting to get account info for ************
[2025-05-26 07:46:32] INFO: Starting account fix process for ************
[2025-05-26 07:46:32] INFO: Fixing account ************ (API ID: ********, Status: 1, Errors: 0)
[2025-05-26 07:46:32] INFO: Creating TelegramClient for account ************
[2025-05-26 07:46:32] INFO: Attempting to get account info for ************
[2025-05-26 07:46:32] INFO: Updated account info for ************: Admin 2 / @
[2025-05-26 07:46:32] INFO: Account ************ fixed successfully
[2025-05-26 07:46:33] INFO: Updated account info for ************: Admin 2 / @
[2025-05-26 07:46:33] INFO: Account ************ fixed successfully
[2025-05-28 14:29:06] INFO: Starting account fix process for ************
[2025-05-28 14:29:06] INFO: Fixing account ************ (API ID: ********, Status: 1, Errors: 0)
[2025-05-28 14:29:06] INFO: Creating TelegramClient for account ************
[2025-05-28 14:29:06] INFO: Attempting to get account info for ************
[2025-05-28 14:29:07] INFO: Updated account info for ************: Admin 2 / @
[2025-05-28 14:29:07] INFO: Account ************ fixed successfully
[2025-05-30 19:31:41] INFO: Starting account fix process for +***********
[2025-05-30 19:31:41] INFO: Fixing account +*********** (API ID: 0, Status: 1, Errors: 0)
[2025-05-30 19:31:41] INFO: Creating TelegramClient for account +***********
[2025-05-30 19:31:41] INFO: Attempting to get account info for +***********
[2025-05-30 19:31:42] INFO: Updated account info for +***********:  / @
[2025-05-30 19:31:42] INFO: Account +*********** fixed successfully
[2025-05-31 15:13:06] INFO: Successfully imported session for +*********** (Hazelfomichev Malis / @KhscZg)
[2025-05-31 15:19:39] INFO: Starting complete account deletion for +***********
[2025-05-31 15:19:39] INFO: Removed session file: sessions/***********.session
[2025-05-31 15:19:39] ERROR: Error deleting account +***********: cannot unpack non-iterable bool object
[2025-05-31 15:19:50] INFO: Successfully imported session for +*********** (Hazelfomichev Malis / @KhscZg)
[2025-05-31 17:30:47] INFO: Starting account fix process for +***********
[2025-05-31 17:30:47] INFO: Fixing account +*********** (API ID: session_placeholder, Status: 0, Errors: 0)
[2025-05-31 17:30:47] INFO: Creating TelegramClient for account +***********
[2025-05-31 17:30:47] INFO: Attempting to get account info for +***********
[2025-05-31 17:30:48] INFO: Account +*********** fixed successfully
[2025-05-31 19:08:12] INFO: Successfully imported session for +************ ( / @)
[2025-05-31 19:08:31] INFO: Successfully imported session for +************ ( / @)
[2025-05-31 19:09:01] INFO: Successfully imported session for +*********** (Frkyg Taild / @Biochemical_Delat)
[2025-05-31 19:09:14] INFO: Successfully imported session for +*********** (Donirthe Hon / @fnbksy)
[2025-05-31 19:09:22] INFO: Successfully imported session for +************* ( / @)
[2025-06-01 05:03:37] INFO: Starting complete account deletion for +*************
[2025-06-01 05:03:37] INFO: Removed session file: sessions/*************.session
[2025-06-01 05:03:37] INFO: Account +************* deleted successfully from database
[2025-06-01 05:03:39] INFO: Starting complete account deletion for +************
[2025-06-01 05:03:39] INFO: Removed session file: sessions/************.session
[2025-06-01 05:03:39] INFO: Account +************ deleted successfully from database
[2025-06-01 05:03:41] INFO: Starting complete account deletion for +************
[2025-06-01 05:03:41] INFO: Removed session file: sessions/************.session
[2025-06-01 05:03:41] INFO: Account +************ deleted successfully from database
[2025-06-01 05:29:11] INFO: Starting complete account deletion for +***********
[2025-06-01 05:29:11] INFO: Removed session file: sessions/***********.session
[2025-06-01 05:29:11] INFO: Account +*********** deleted successfully from database
[2025-06-01 08:59:20] INFO: Starting complete account deletion for +***********
[2025-06-01 08:59:20] INFO: Account +*********** deleted successfully from database
[2025-06-01 08:59:23] INFO: Starting complete account deletion for +***********
[2025-06-01 08:59:23] INFO: Account +*********** deleted successfully from database
[2025-06-01 12:37:10] INFO: Connect button clicked for account ***********
[2025-06-01 12:37:10] INFO: Connecting to Telegram for account ***********
[2025-06-01 12:37:10] INFO: Sending code request for account ***********
[2025-06-01 12:37:16] INFO: Code request successful for account ***********
[2025-06-01 12:37:16] INFO: Verification code requested for account ***********
[2025-06-01 12:37:33] INFO: Verification code submitted for account ***********
[2025-06-01 12:37:35] INFO: 2FA password required for account ***********
[2025-06-01 12:37:40] INFO: 2FA password submitted for account ***********
[2025-06-01 12:43:04] INFO: Connect button clicked for account ***********
[2025-06-01 12:43:04] INFO: Connecting to Telegram for account ***********
[2025-06-01 12:43:04] INFO: Sending code request for account ***********
[2025-06-01 12:43:05] INFO: Code request successful for account ***********
[2025-06-01 12:43:05] INFO: Verification code requested for account ***********
[2025-06-01 12:43:15] INFO: Verification code submitted for account ***********
[2025-06-01 12:43:20] ERROR: Login error: Connection error: database is locked
[2025-06-01 12:43:45] INFO: Verification code submitted for account ***********
[2025-06-01 12:43:51] ERROR: Login error: Connection error: database is locked
[2025-06-01 17:54:29] INFO: Connect button clicked for account ***********
[2025-06-01 17:54:29] INFO: Connecting to Telegram for account ***********
[2025-06-01 17:54:29] INFO: Sending code request for account ***********
[2025-06-01 17:54:31] INFO: Code request successful for account ***********
[2025-06-01 17:54:31] INFO: Verification code requested for account ***********
[2025-06-01 17:55:31] INFO: Verification code submitted for account ***********
[2025-06-01 17:55:36] ERROR: Login error: Connection error: database is locked
[2025-06-01 18:02:13] INFO: Connect button clicked for account ***********
[2025-06-01 18:02:13] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:02:13] INFO: Sending code request for account ***********
[2025-06-01 18:02:14] INFO: Code request successful for account ***********
[2025-06-01 18:02:14] INFO: Verification code requested for account ***********
[2025-06-01 18:03:25] INFO: Reset button clicked - cleaning up login session
[2025-06-01 18:03:25] INFO: Client connection cleaned up
[2025-06-01 18:03:25] INFO: Forcing database cleanup to unlock any locked connections
[2025-06-01 18:03:26] INFO: Database cleanup completed successfully
[2025-06-01 18:03:26] INFO: Login reset completed successfully - ready for new attempt
[2025-06-01 18:03:31] INFO: Connect button clicked for account ***********
[2025-06-01 18:03:31] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:03:31] INFO: Sending code request for account ***********
[2025-06-01 18:03:36] ERROR: Code request failed for account ***********: database is locked
[2025-06-01 18:03:36] INFO: Starting emergency database cleanup and unlock
[2025-06-01 18:03:37] INFO: Database cleanup attempt 1 successful
[2025-06-01 18:03:37] INFO: Database unlock completed successfully
[2025-06-01 18:03:37] INFO: Database unlocked successfully after connection error
[2025-06-01 18:03:42] INFO: Dialog closing - starting comprehensive cleanup
[2025-06-01 18:03:42] INFO: Log timer stopped
[2025-06-01 18:03:42] INFO: Client connection cleaned up
[2025-06-01 18:03:43] INFO: Database cleanup completed on dialog close
[2025-06-01 18:03:43] INFO: Dialog cleanup completed successfully
[2025-06-01 18:04:06] INFO: Connect button clicked for account ***********
[2025-06-01 18:04:06] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:04:06] INFO: Sending code request for account ***********
[2025-06-01 18:04:11] ERROR: Code request failed for account ***********: database is locked
[2025-06-01 18:04:11] INFO: Starting emergency database cleanup and unlock
[2025-06-01 18:04:12] INFO: Database cleanup attempt 1 successful
[2025-06-01 18:04:13] INFO: Database unlock completed successfully
[2025-06-01 18:04:13] INFO: Database unlocked successfully after connection error
[2025-06-01 18:04:17] INFO: Reset button clicked - cleaning up login session
[2025-06-01 18:04:17] INFO: Client connection cleaned up
[2025-06-01 18:04:17] INFO: Forcing database cleanup to unlock any locked connections
[2025-06-01 18:04:18] INFO: Database cleanup completed successfully
[2025-06-01 18:04:18] INFO: Login reset completed successfully - ready for new attempt
[2025-06-01 18:04:22] INFO: Connect button clicked for account ***********
[2025-06-01 18:04:22] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:04:22] INFO: Sending code request for account ***********
[2025-06-01 18:04:27] ERROR: Code request failed for account ***********: database is locked
[2025-06-01 18:04:27] INFO: Starting emergency database cleanup and unlock
[2025-06-01 18:04:28] INFO: Database cleanup attempt 1 successful
[2025-06-01 18:04:29] INFO: Database unlock completed successfully
[2025-06-01 18:04:29] INFO: Database unlocked successfully after connection error
[2025-06-01 18:05:17] INFO: Dialog closing - starting comprehensive cleanup
[2025-06-01 18:05:17] INFO: Log timer stopped
[2025-06-01 18:05:17] INFO: Client connection cleaned up
[2025-06-01 18:05:18] INFO: Database cleanup completed on dialog close
[2025-06-01 18:05:18] INFO: Dialog cleanup completed successfully
[2025-06-01 18:09:26] INFO: Connect button clicked for account +***********
[2025-06-01 18:09:26] INFO: Connecting to Telegram for account +***********
[2025-06-01 18:09:26] INFO: Sending code request for account +***********
[2025-06-01 18:09:27] INFO: Code request successful for account +***********
[2025-06-01 18:09:27] INFO: Verification code requested for account +***********
[2025-06-01 18:09:49] INFO: 🔄 RESET: Starting complete login reset and session cleanup
[2025-06-01 18:09:49] INFO: 🔄 RESET: Cleaning up Telegram client connections
[2025-06-01 18:09:49] INFO: 🔄 RESET: Telegram client force cleanup completed
[2025-06-01 18:09:49] WARNING: 🔄 RESET: Could not remove session sessions/***********.session: [WinError 32] The process cannot access the file because it is being used by another process: 'sessions/***********.session'
[2025-06-01 18:09:49] INFO: 🔄 RESET: Starting comprehensive database cleanup
[2025-06-01 18:09:50] INFO: 🔄 RESET: Database cleanup attempt 1 successful
[2025-06-01 18:09:50] INFO: 🔄 RESET: Internal state variables cleared
[2025-06-01 18:09:50] INFO: ✅ RESET COMPLETE: Full session reset successful - ready for completely fresh login attempt
[2025-06-01 18:10:00] INFO: Connect button clicked for account ***********
[2025-06-01 18:10:00] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:10:00] INFO: Sending code request for account ***********
[2025-06-01 18:10:05] ERROR: Code request failed for account ***********: database is locked
[2025-06-01 18:10:05] INFO: 🔧 DB CLEANUP: Starting emergency database cleanup and unlock
[2025-06-01 18:10:06] INFO: 🔧 DB CLEANUP: Attempt 1 successful
[2025-06-01 18:10:06] INFO: ✅ DB CLEANUP: Database unlock completed successfully
[2025-06-01 18:10:06] INFO: Database unlocked successfully after connection error
[2025-06-01 18:19:24] INFO: Connect button clicked for account ***********
[2025-06-01 18:19:24] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:19:24] INFO: Using default session path: sessions/***********
[2025-06-01 18:19:24] INFO: Sending code request for account ***********
[2025-06-01 18:19:29] ERROR: Code request failed for account ***********: database is locked
[2025-06-01 18:19:29] INFO: 🔧 DB CLEANUP: Starting emergency database cleanup and unlock
[2025-06-01 18:19:30] INFO: 🔧 DB CLEANUP: Attempt 1 successful
[2025-06-01 18:19:31] INFO: ✅ DB CLEANUP: Database unlock completed successfully
[2025-06-01 18:19:31] INFO: Database unlocked successfully after connection error
[2025-06-01 18:19:36] INFO: 🔄 RESET: Starting complete login reset and session cleanup
[2025-06-01 18:19:36] INFO: 🔄 RESET: Cleaning up Telegram client connections
[2025-06-01 18:19:40] INFO: 🔄 RESET: Telegram client force cleanup completed
[2025-06-01 18:19:40] WARNING: 🔄 RESET: Could not remove session sessions/***********.session: [WinError 32] The process cannot access the file because it is being used by another process: 'sessions/***********.session'
[2025-06-01 18:19:40] WARNING: 🔄 RESET: Could not remove session sessions/***********.session: [WinError 32] The process cannot access the file because it is being used by another process: 'sessions/***********.session'
[2025-06-01 18:19:40] INFO: 🔄 RESET: Created fresh session path: sessions/***********_fresh_9411d145
[2025-06-01 18:19:41] INFO: 🔄 RESET: Starting comprehensive database cleanup
[2025-06-01 18:19:42] INFO: 🔄 RESET: Database cleanup attempt 1 successful
[2025-06-01 18:19:42] INFO: 🔄 RESET: Internal state variables cleared
[2025-06-01 18:19:42] INFO: ✅ RESET COMPLETE: Full session reset successful - ready for completely fresh login attempt
[2025-06-01 18:19:46] INFO: Connect button clicked for account ***********
[2025-06-01 18:19:46] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:19:46] INFO: Using fresh session path: sessions/***********_fresh_9411d145
[2025-06-01 18:19:46] INFO: Sending code request for account ***********
[2025-06-01 18:19:52] INFO: Code request successful for account ***********
[2025-06-01 18:19:52] INFO: Verification code requested for account ***********
[2025-06-01 18:20:04] INFO: Verification code submitted for account ***********
[2025-06-01 18:20:09] ERROR: ❌ LOGIN ERROR: Connection error: database is locked
[2025-06-01 18:20:09] INFO: 🔧 AUTO-RECOVERY: Database lock detected, triggering automatic reset
[2025-06-01 18:20:09] INFO: 🔧 DB CLEANUP: Starting emergency database cleanup and unlock
[2025-06-01 18:20:10] INFO: 🔧 DB CLEANUP: Attempt 1 successful
[2025-06-01 18:20:11] INFO: ✅ DB CLEANUP: Database unlock completed successfully
[2025-06-01 18:20:11] INFO: ✅ AUTO-RECOVERY: Database unlocked successfully after connection error
[2025-06-01 18:20:15] INFO: 🔄 RESET: Starting complete login reset and session cleanup
[2025-06-01 18:20:15] INFO: 🔄 RESET: Terminating login worker
[2025-06-01 18:20:15] INFO: 🔄 RESET: Login worker terminated and deleted
[2025-06-01 18:20:15] INFO: 🔄 RESET: Cleaning up Telegram client connections
[2025-06-01 18:20:19] INFO: 🔄 RESET: Telegram client force cleanup completed
[2025-06-01 18:20:19] WARNING: 🔄 RESET: Could not remove session sessions/***********.session: [WinError 32] The process cannot access the file because it is being used by another process: 'sessions/***********.session'
[2025-06-01 18:20:19] WARNING: 🔄 RESET: Could not remove session sessions/***********.session: [WinError 32] The process cannot access the file because it is being used by another process: 'sessions/***********.session'
[2025-06-01 18:20:19] INFO: 🔄 RESET: Created fresh session path: sessions/***********_fresh_3fd9147c
[2025-06-01 18:20:20] INFO: 🔄 RESET: Starting comprehensive database cleanup
[2025-06-01 18:20:21] INFO: 🔄 RESET: Database cleanup attempt 1 successful
[2025-06-01 18:20:21] INFO: 🔄 RESET: Internal state variables cleared
[2025-06-01 18:20:21] INFO: ✅ RESET COMPLETE: Full session reset successful - ready for completely fresh login attempt
[2025-06-01 18:20:23] INFO: Connect button clicked for account ***********
[2025-06-01 18:20:23] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:20:23] INFO: Using fresh session path: sessions/***********_fresh_3fd9147c
[2025-06-01 18:20:23] INFO: Sending code request for account ***********
[2025-06-01 18:20:29] INFO: Code request successful for account ***********
[2025-06-01 18:21:56] INFO: Dialog closing - starting comprehensive cleanup
[2025-06-01 18:21:56] INFO: Log timer stopped
[2025-06-01 18:21:56] INFO: Client connection cleaned up
[2025-06-01 18:21:57] INFO: Database cleanup completed on dialog close
[2025-06-01 18:21:57] INFO: Dialog cleanup completed successfully
[2025-06-01 18:30:15] INFO: Connect button clicked for account ***********
[2025-06-01 18:30:15] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:30:15] INFO: Connect button clicked for account ***********
[2025-06-01 18:30:15] INFO: Using fresh session path: sessions/***********_fresh_22639f3e
[2025-06-01 18:30:15] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:30:15] INFO: Using fresh session path: sessions/***********_fresh_67766d9d
[2025-06-01 18:30:16] INFO: Database cleaned automatically before connection
[2025-06-01 18:30:16] INFO: Sending code request for account ***********
[2025-06-01 18:30:16] INFO: Database cleaned automatically before connection
[2025-06-01 18:30:16] INFO: Sending code request for account ***********
[2025-06-01 18:30:22] INFO: Code request successful for account ***********
[2025-06-01 18:30:22] INFO: Verification code requested for account ***********
[2025-06-01 18:30:22] INFO: Code request successful for account ***********
[2025-06-01 18:30:22] INFO: Verification code requested for account ***********
[2025-06-01 18:30:40] INFO: Verification code submitted for account ***********
[2025-06-01 18:30:40] INFO: Verification code submitted for account ***********
[2025-06-01 18:30:42] INFO: Successfully added account *********** with info: Aryan 1 / @Tguhqworld
[2025-06-01 18:55:25] INFO: Connect button clicked for account ***********
[2025-06-01 18:55:25] INFO: Connecting to Telegram for account ***********
[2025-06-01 18:55:25] INFO: Using fresh session path: sessions/***********_fresh_04693389
[2025-06-01 18:55:26] INFO: Database cleaned automatically before connection
[2025-06-01 18:55:26] INFO: Sending code request for account ***********
[2025-06-01 18:55:32] INFO: Code request successful for account ***********
[2025-06-01 18:55:32] INFO: Verification code requested for account ***********
[2025-06-01 19:00:20] INFO: Verification code submitted for account ***********
[2025-06-01 19:00:20] INFO: Verification code submitted for account ***********
[2025-06-01 19:00:21] INFO: Successfully added account *********** with info: Aryan 1 / @Tguhqworld
[2025-06-01 19:42:07] INFO: Connect button clicked for account ************
[2025-06-01 19:42:07] INFO: Connecting to Telegram for account ************
[2025-06-01 19:42:07] INFO: Using fresh session path: sessions/************_fresh_5c6d4d91
[2025-06-01 19:42:08] INFO: Database cleaned automatically before connection
[2025-06-01 19:42:08] INFO: Sending code request for account ************
[2025-06-01 19:42:13] INFO: Code request successful for account ************
[2025-06-01 19:42:13] INFO: Verification code requested for account ************
[2025-06-01 19:42:27] INFO: Verification code submitted for account ************
[2025-06-01 19:42:27] INFO: Using session path for verification: sessions/************_fresh_5c6d4d91
[2025-06-01 19:42:27] INFO: Verification code submitted for account ************
[2025-06-01 19:42:27] INFO: Using session path for verification: sessions/************_fresh_5c6d4d91
[2025-06-01 19:42:28] INFO: 2FA password required for account ************
[2025-06-01 19:42:31] INFO: 2FA password required for account ************
[2025-06-01 19:42:39] INFO: 2FA password submitted for account ************
[2025-06-01 19:42:39] INFO: Using session path for 2FA verification: sessions/************_fresh_5c6d4d91
[2025-06-01 19:42:39] INFO: Verifying 2FA password for account ************
[2025-06-01 19:43:26] INFO: Connect button clicked for account ************
[2025-06-01 19:43:26] INFO: Connecting to Telegram for account ************
[2025-06-01 19:43:26] INFO: Using fresh session path: sessions/************_fresh_e25eb447
[2025-06-01 19:43:27] INFO: Database cleaned automatically before connection
[2025-06-01 19:43:27] INFO: Sending code request for account ************
[2025-06-01 19:43:32] INFO: Code request successful for account ************
[2025-06-01 19:43:32] INFO: Verification code requested for account ************
[2025-06-01 19:43:45] INFO: Verification code submitted for account ************
[2025-06-01 19:43:45] INFO: Using session path for verification: sessions/************_fresh_e25eb447
[2025-06-01 19:43:45] INFO: Verification code submitted for account ************
[2025-06-01 19:43:45] INFO: Using session path for verification: sessions/************_fresh_e25eb447
[2025-06-01 19:43:46] INFO: 2FA password required for account ************
[2025-06-01 19:43:50] INFO: 2FA password required for account ************
[2025-06-01 19:44:00] INFO: 2FA password submitted for account ************
[2025-06-01 19:44:00] INFO: Using session path for 2FA verification: sessions/************_fresh_e25eb447
[2025-06-01 19:44:00] INFO: 2FA password submitted for account ************
[2025-06-01 19:44:00] INFO: Verifying 2FA password for account ************
[2025-06-01 19:44:00] INFO: Using session path for 2FA verification: sessions/************_fresh_e25eb447
[2025-06-01 19:44:00] INFO: Verifying 2FA password for account ************
[2025-06-01 19:44:01] INFO: Login successful for ************
[2025-06-01 19:44:01] INFO: Temporary session path: sessions/************_fresh_e25eb447
[2025-06-01 19:44:01] INFO: Final session path will be: sessions/************
[2025-06-01 19:44:01] INFO: Session file exists: sessions/************_fresh_e25eb447.session (28672 bytes)
[2025-06-01 19:44:01] INFO: Copied session from sessions/************_fresh_e25eb447.session to sessions/************.session
[2025-06-01 19:44:01] INFO: Session successfully saved to sessions/************.session
[2025-06-01 19:44:01] INFO: Successfully added account ************ with info: Admin 2 / @
[2025-06-01 19:44:33] INFO: Connect button clicked for account *************
[2025-06-01 19:44:33] INFO: Connecting to Telegram for account *************
[2025-06-01 19:44:33] INFO: Using fresh session path: sessions/*************_fresh_dd22abe9
[2025-06-01 19:44:34] INFO: Database cleaned automatically before connection
[2025-06-01 19:44:34] INFO: Sending code request for account *************
[2025-06-01 19:44:40] INFO: Code request successful for account *************
[2025-06-01 19:44:40] INFO: Verification code requested for account *************
[2025-06-01 19:44:57] INFO: Verification code submitted for account *************
[2025-06-01 19:44:57] INFO: Using session path for verification: sessions/*************_fresh_dd22abe9
[2025-06-01 19:44:57] INFO: Verification code submitted for account *************
[2025-06-01 19:44:57] INFO: Using session path for verification: sessions/*************_fresh_dd22abe9
[2025-06-01 19:44:59] INFO: 2FA password required for account *************
[2025-06-01 19:45:02] INFO: 2FA password required for account *************
[2025-06-01 19:45:11] INFO: 2FA password submitted for account *************
[2025-06-01 19:45:11] INFO: Using session path for 2FA verification: sessions/*************_fresh_dd22abe9
[2025-06-01 19:45:11] INFO: 2FA password submitted for account *************
[2025-06-01 19:45:11] INFO: Verifying 2FA password for account *************
[2025-06-01 19:45:11] INFO: Using session path for 2FA verification: sessions/*************_fresh_dd22abe9
[2025-06-01 19:45:11] INFO: Verifying 2FA password for account *************
[2025-06-01 19:45:14] INFO: Login successful for *************
[2025-06-01 19:45:14] INFO: Temporary session path: sessions/*************_fresh_dd22abe9
[2025-06-01 19:45:14] INFO: Final session path will be: sessions/*************
[2025-06-01 19:45:14] INFO: Session file exists: sessions/*************_fresh_dd22abe9.session (28672 bytes)
[2025-06-01 19:45:14] INFO: Copied session from sessions/*************_fresh_dd22abe9.session to sessions/*************.session
[2025-06-01 19:45:14] INFO: Session successfully saved to sessions/*************.session
[2025-06-01 19:45:14] INFO: Successfully added account ************* with info: FaceBluff 3 / @FaceBluf
[2025-06-01 19:45:36] INFO: Connect button clicked for account ***********
[2025-06-01 19:45:36] INFO: Connecting to Telegram for account ***********
[2025-06-01 19:45:36] INFO: Using fresh session path: sessions/***********_fresh_92b813b8
[2025-06-01 19:45:37] INFO: Database cleaned automatically before connection
[2025-06-01 19:45:37] INFO: Sending code request for account ***********
[2025-06-01 19:45:42] INFO: Code request successful for account ***********
[2025-06-01 19:45:42] INFO: Verification code requested for account ***********
[2025-06-01 19:45:52] INFO: Verification code submitted for account ***********
[2025-06-01 19:45:52] INFO: Using session path for verification: sessions/***********_fresh_92b813b8
[2025-06-01 19:45:52] INFO: Verification code submitted for account ***********
[2025-06-01 19:45:52] INFO: Using session path for verification: sessions/***********_fresh_92b813b8
[2025-06-01 19:45:53] INFO: 2FA password required for account ***********
[2025-06-01 19:45:53] INFO: 2FA password required for account ***********
[2025-06-01 19:46:01] INFO: 2FA password submitted for account ***********
[2025-06-01 19:46:01] INFO: Using session path for 2FA verification: sessions/***********_fresh_92b813b8
[2025-06-01 19:46:01] INFO: 2FA password submitted for account ***********
[2025-06-01 19:46:01] INFO: Verifying 2FA password for account ***********
[2025-06-01 19:46:01] INFO: Using session path for 2FA verification: sessions/***********_fresh_92b813b8
[2025-06-01 19:46:01] INFO: Verifying 2FA password for account ***********
[2025-06-01 19:46:02] INFO: Login successful for ***********
[2025-06-01 19:46:02] INFO: Temporary session path: sessions/***********_fresh_92b813b8
[2025-06-01 19:46:02] INFO: Final session path will be: sessions/***********
[2025-06-01 19:46:02] INFO: Session file exists: sessions/***********_fresh_92b813b8.session (28672 bytes)
[2025-06-01 19:46:02] INFO: Copied session from sessions/***********_fresh_92b813b8.session to sessions/***********.session
[2025-06-01 19:46:02] INFO: Session successfully saved to sessions/***********.session
[2025-06-01 19:46:02] INFO: Successfully added account *********** with info: AD 4 / @vipxlegit
[2025-06-01 19:46:21] INFO: Connect button clicked for account ************
[2025-06-01 19:46:21] INFO: Connecting to Telegram for account ************
[2025-06-01 19:46:21] INFO: Using fresh session path: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:22] INFO: Database cleaned automatically before connection
[2025-06-01 19:46:22] INFO: Sending code request for account ************
[2025-06-01 19:46:26] INFO: Code request successful for account ************
[2025-06-01 19:46:26] INFO: Verification code requested for account ************
[2025-06-01 19:46:35] INFO: Verification code submitted for account ************
[2025-06-01 19:46:35] INFO: Using session path for verification: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:35] INFO: Verification code submitted for account ************
[2025-06-01 19:46:35] INFO: Using session path for verification: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:36] INFO: 2FA password required for account ************
[2025-06-01 19:46:39] INFO: 2FA password required for account ************
[2025-06-01 19:46:45] INFO: 2FA password submitted for account ************
[2025-06-01 19:46:45] INFO: Using session path for 2FA verification: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:45] INFO: 2FA password submitted for account ************
[2025-06-01 19:46:45] INFO: Verifying 2FA password for account ************
[2025-06-01 19:46:45] INFO: Using session path for 2FA verification: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:45] INFO: Verifying 2FA password for account ************
[2025-06-01 19:46:46] INFO: Login successful for ************
[2025-06-01 19:46:46] INFO: Temporary session path: sessions/************_fresh_0faaddfb
[2025-06-01 19:46:46] INFO: Final session path will be: sessions/************
[2025-06-01 19:46:46] INFO: Session file exists: sessions/************_fresh_0faaddfb.session (28672 bytes)
[2025-06-01 19:46:46] INFO: Copied session from sessions/************_fresh_0faaddfb.session to sessions/************.session
[2025-06-01 19:46:46] INFO: Session successfully saved to sessions/************.session
[2025-06-01 19:46:46] INFO: Successfully added account ************ with info: AD 5 / @vipxworld
[2025-07-24 21:30:45] INFO: Starting account fix process for ***********
[2025-07-24 21:30:45] INFO: Fixing account *********** (API ID: ********, Status: 1, Errors: 0)
[2025-07-24 21:30:45] INFO: Creating TelegramClient for account ***********
[2025-07-24 21:30:45] INFO: Attempting to get account info for ***********
[2025-07-24 21:30:47] INFO: Account *********** fixed successfully
