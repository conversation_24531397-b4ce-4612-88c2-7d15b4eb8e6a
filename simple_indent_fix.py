#!/usr/bin/env python3
"""
EMERGENCY FIX: Resolves indentation error in main.py
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_indentation():
    """Fix the indentation error around line 13509."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # Find the problematic line
        problem_line_num = None
        for i, line in enumerate(lines):
            if "def stop_joining_task(self, task_id):" in line:
                problem_line_num = i
                break
        
        if problem_line_num is None:
            print("⚠️ Could not find the problematic function")
            return False
        
        print(f"Found problematic line at line {problem_line_num + 1}")
        
        # Check the indentation of surrounding functions
        prev_function_line = None
        for i in range(problem_line_num - 1, 0, -1):
            if "def " in lines[i] and ":" in lines[i]:
                prev_function_line = i
                break
        
        if prev_function_line is None:
            print("⚠️ Could not find a previous function for reference")
            return False
        
        # Get the indentation of the previous function
        prev_indent = len(lines[prev_function_line]) - len(lines[prev_function_line].lstrip())
        print(f"Previous function at line {prev_function_line + 1} has {prev_indent} spaces of indentation")
        
        # Fix the indentation
        current_line = lines[problem_line_num]
        current_indent = len(current_line) - len(current_line.lstrip())
        
        if current_indent != prev_indent:
            lines[problem_line_num] = " " * prev_indent + current_line.lstrip()
            print(f"Changed indentation from {current_indent} to {prev_indent} spaces")
            
            # Write the fixed content back
            with open("main.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            print(f"✅ Fixed indentation of line {problem_line_num + 1}")
            return True
        else:
            print("⚠️ Indentation already matches the previous function")
            return False
    except Exception as e:
        print(f"❌ Error fixing indentation: {str(e)}")
        return False

def main():
    print("🚨 EMERGENCY FIX: Resolving indentation error in main.py...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fixed = fix_indentation()
    
    if fixed:
        print("\n✅ Emergency fix applied successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fix was applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 