
# Add this function after reset_forwarder_task_progress function
def reset_joining_task_progress(self, task_id):
    """Reset joining task progress to allow re-running or new task creation."""
    try:
        conn = sqlite3.connect(self.joining_db_path, timeout=30)
        cursor = conn.cursor()
        
        query = """UPDATE joining_tasks SET 
                   current_index = 0, 
                   successful_joins = 0, 
                   failed_joins = 0,
                   updated_at = ?
                   WHERE id = ?"""
        
        cursor.execute(query, [datetime.now().isoformat(), task_id])
        conn.commit()
        conn.close()
        
        # Update local cache
        if task_id in self.joining_tasks:
            self.joining_tasks[task_id]['current_index'] = 0
            self.joining_tasks[task_id]['successful_joins'] = 0
            self.joining_tasks[task_id]['failed_joins'] = 0
        
        self.logger.info(f"Joining task {task_id} progress reset successfully")
        
    except Exception as e:
        self.logger.error(f"Failed to reset joining task progress {task_id}: {str(e)}")
        raise

# Modify the start_joining_task function to include these lines after checking task existence:
# # ALWAYS reset task progress when manually starting (allows re-running any task)
# self.logger.info(f"Resetting joining task progress for manual restart: {task_id}")
# self.log_joining_message("info", task_id, "🔄 Resetting progress counters for clean start")
# self.reset_joining_task_progress(task_id)
#            
# # Refresh task data after reset
# self.refresh_joining_tasks()
