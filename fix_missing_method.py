#!/usr/bin/env python3
"""
🚨 FIX MISSING METHOD IMMEDIATELY
================================

This script adds the missing _refresh_joining_tasks_background method
to fix the AttributeError.
"""

def fix_missing_method():
    """Add the missing background refresh method."""
    try:
        # Read main.py
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("🔧 Adding missing _refresh_joining_tasks_background method...")
        
        # Check if method already exists
        if "_refresh_joining_tasks_background" in content:
            print("   ✅ Method already exists")
            return True
        
        # Find the refresh_joining_tasks method and add the background version right after it
        refresh_method_pattern = r"(    def refresh_joining_tasks\(self\):.*?)(    def [^_])"
        
        background_method = '''
    def _refresh_joining_tasks_background(self):
        """Non-blocking version of refresh_joining_tasks."""
        def background_refresh():
            try:
                conn = sqlite3.connect(self.joining_db_path, timeout=30)
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM joining_tasks ORDER BY created_at DESC")
                tasks = cursor.fetchall()
                
                conn.close()
                
                # Convert to dictionary format
                task_dict = {}
                for task in tasks:
                    task_dict[task[0]] = {
                        'id': task[0],
                        'name': task[1],
                        'account_phone': task[2],
                        'group_links': task[3],
                        'status': task[4],
                        'current_index': task[5],
                        'total_groups': task[6],
                        'successful_joins': task[7],
                        'failed_joins': task[8],
                        'created_at': task[9],
                        'updated_at': task[10],
                        'settings': task[11],
                        'shareable_folder_name': task[12] if len(task) > 12 else '',
                        'shareable_folder_enabled': task[13] if len(task) > 13 else 0
                    }
                
                # Update UI in main thread using QTimer
                def update_ui():
                    try:
                        self.joining_tasks = task_dict
                        self.update_joining_tasks_table(list(task_dict.values()))
                    except Exception as e:
                        print(f"UI update error: {e}")
                
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, update_ui)
                
            except Exception as e:
                print(f"Background refresh error: {e}")
        
        # Start in background thread
        import threading
        threading.Thread(target=background_refresh, daemon=True, name="RefreshBg").start()

'''
        
        import re
        
        def add_background_method(match):
            original_method = match.group(1)
            next_method = match.group(2)
            return original_method + background_method + next_method
        
        # Apply the replacement
        new_content = re.sub(refresh_method_pattern, add_background_method, content, flags=re.DOTALL)
        
        if new_content != content:
            # Write the updated content
            with open("main.py", "w", encoding="utf-8") as f:
                f.write(new_content)
            print("   ✅ Added _refresh_joining_tasks_background method")
            return True
        else:
            # If pattern didn't match, add at the end of the class
            class_end = content.rfind("# ============================================================================")
            if class_end > 0:
                content = content[:class_end] + background_method + "\n" + content[class_end:]
                with open("main.py", "w", encoding="utf-8") as f:
                    f.write(content)
                print("   ✅ Added method at end of class")
                return True
            else:
                print("   ❌ Could not find insertion point")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Fix the missing method immediately."""
    print("🚨 FIXING MISSING METHOD")
    print("=" * 30)
    
    if fix_missing_method():
        print("\n✅ METHOD FIXED!")
        print("🚀 Your TG Checker should now work without errors")
    else:
        print("\n❌ Fix failed")

if __name__ == "__main__":
    main() 