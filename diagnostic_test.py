#!/usr/bin/env python3
"""
🔧 Diagnostic Test for TG Checker Classification
Tests real activity times without artificial modifications
"""

import asyncio
import logging
from tg_client import TelegramClient
import os

# Test groups 
TEST_GROUPS = [
    "https://t.me/hyipinformer_com",      # Expected: Groups_Valid_Only (should fail filters)
    "https://t.me/islamic_hacker_army",   # Expected: Groups_Valid_Only (should fail filters)
    "https://t.me/imperiamarket",         # Expected: Groups_Valid_Filter (should pass filters)
    "https://t.me/infocoindogroup",       # Expected: Groups_Valid_Filter (should pass filters)
]

async def check_real_activity():
    """Check real activity times for test groups."""
    print("🔧 DIAGNOSTIC: Real Activity Times")
    print("=" * 50)
    
    # Get account info
    account_manager_file = 'account_manager.json'
    if not os.path.exists(account_manager_file):
        print("❌ account_manager.json not found")
        return
    
    import json
    with open(account_manager_file, 'r') as f:
        accounts = json.load(f)
    
    if not accounts:
        print("❌ No accounts found")
        return
    
    account = accounts[0]
    
    # Create client
    session_path = f"sessions/{account.get('phone', 'unknown')}.session"
    
    client = TelegramClient(
        session=session_path,
        api_id=account.get("api_id"),
        api_hash=account.get("api_hash"),
        system_version="4.16.30-vxCUSTOM"
    )
    
    try:
        print("🔐 Connecting to Telegram...")
        await client.connect()
        
        for group_link in TEST_GROUPS:
            print(f"\n🔍 Checking: {group_link}")
            
            # Get raw entity info
            try:
                username = group_link.replace("https://t.me/", "").replace("@", "")
                entity = await client.get_entity(username)
                
                # Get recent messages to check real activity
                messages = await client.get_messages(entity, limit=10)
                
                if messages and messages[0] and messages[0].date:
                    from datetime import datetime
                    now = datetime.now()
                    msg_time = messages[0].date
                    
                    # Handle timezone
                    if msg_time.tzinfo is not None:
                        msg_time = msg_time.replace(tzinfo=None)
                    
                    # Calculate real time difference
                    time_diff_hours = (now - msg_time).total_seconds() / 3600
                    
                    # Get member count
                    member_count = getattr(entity, 'participants_count', 0)
                    total_messages = len(messages)
                    
                    print(f"   📊 Members: {member_count}")
                    print(f"   📝 Recent Messages: {total_messages}")
                    print(f"   🕒 Real Last Activity: {time_diff_hours:.2f} hours ago")
                    print(f"   📅 Last Message Date: {messages[0].date}")
                    
                    # Check filter criteria (default: 500 members, 1 hour, 100 messages)
                    passes_filters = (
                        member_count >= 500 and 
                        time_diff_hours <= 1 and 
                        total_messages >= 100
                    )
                    
                    print(f"   🎯 Passes Filters: {passes_filters}")
                    if passes_filters:
                        print(f"   📂 Should go to: Groups_Valid_Filter")
                    else:
                        print(f"   📂 Should go to: Groups_Valid_Only")
                        print(f"      └─ Reasons: ", end="")
                        reasons = []
                        if member_count < 500:
                            reasons.append(f"members({member_count}<500)")
                        if time_diff_hours > 1:
                            reasons.append(f"activity({time_diff_hours:.1f}h>1h)")
                        if total_messages < 100:
                            reasons.append(f"messages({total_messages}<100)")
                        print(", ".join(reasons))
                
                else:
                    print(f"   ❌ No messages found")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
    finally:
        await client.disconnect()

if __name__ == "__main__":
    asyncio.run(check_real_activity()) 