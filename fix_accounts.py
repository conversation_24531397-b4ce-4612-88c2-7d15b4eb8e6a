#!/usr/bin/env python3
import sqlite3
import os

db_path = "tg_checker.db"

def fix_accounts():
    if not os.path.exists(db_path):
        print("Database not found!")
        return
        
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    print("🔧 Fixing account storage issues...")
    print("=" * 60)
    
    # 1. Remove accounts with invalid session credentials (API ID = 0 or "session_import")
    cursor.execute("""
        SELECT phone, api_id, api_hash, account_type 
        FROM accounts 
        WHERE api_id = '0' OR api_id = 'session_import' OR api_hash = 'session_import'
    """)
    
    invalid_accounts = cursor.fetchall()
    
    if invalid_accounts:
        print(f"🗑️ Found {len(invalid_accounts)} accounts with invalid session credentials:")
        for phone, api_id, api_hash, account_type in invalid_accounts:
            print(f"  - {phone} (API ID: {api_id}, Type: {account_type})")
        
        # Delete them
        cursor.execute("""
            DELETE FROM accounts 
            WHERE api_id = '0' OR api_id = 'session_import' OR api_hash = 'session_import'
        """)
        print(f"✅ Removed {len(invalid_accounts)} invalid accounts")
    
    # 2. Remove duplicate phone numbers (keep the one with valid credentials)
    cursor.execute("""
        SELECT phone, COUNT(*) as count 
        FROM accounts 
        GROUP BY phone 
        HAVING COUNT(*) > 1
    """)
    
    duplicates = cursor.fetchall()
    
    if duplicates:
        print(f"\n🔄 Found {len(duplicates)} phone numbers with duplicates:")
        for phone, count in duplicates:
            print(f"  - {phone} ({count} entries)")
            
            # Get all entries for this phone
            cursor.execute("""
                SELECT rowid, phone, api_id, api_hash, account_type, status 
                FROM accounts 
                WHERE phone = ? OR phone = ?
            """, (phone, phone.replace('+', '')))
            entries = cursor.fetchall()
            
            # Keep the best entry (prefer normal type with valid API credentials)
            best_entry = None
            entries_to_delete = []
            
            for entry in entries:
                rowid, ph, api_id, api_hash, acc_type, status = entry
                
                # Score entries (higher = better)
                score = 0
                if acc_type == "normal":
                    score += 10
                if api_id and api_id not in ['0', 'session_import']:
                    score += 5
                if api_hash and api_hash not in ['session_import']:
                    score += 5
                if status == "active":
                    score += 3
                
                if best_entry is None or score > best_entry[1]:
                    if best_entry:
                        entries_to_delete.append(best_entry[0])
                    best_entry = (entry, score)
                else:
                    entries_to_delete.append(entry)
            
            # Delete duplicate entries
            for entry_to_delete in entries_to_delete:
                if isinstance(entry_to_delete, tuple):
                    cursor.execute("DELETE FROM accounts WHERE rowid = ?", (entry_to_delete[0],))
                else:
                    cursor.execute("DELETE FROM accounts WHERE rowid = ?", (entry_to_delete[0],))
            
            print(f"    ✅ Kept best entry, removed {len(entries_to_delete)} duplicates")
    
    # 3. Remove accounts with status 'error' that have been failing for a while
    cursor.execute("""
        SELECT phone, status, errors 
        FROM accounts 
        WHERE status = 'error' AND errors > 3
    """)
    
    error_accounts = cursor.fetchall()
    
    if error_accounts:
        print(f"\n❌ Found {len(error_accounts)} accounts with persistent errors:")
        for phone, status, errors in error_accounts:
            print(f"  - {phone} (Status: {status}, Errors: {errors})")
        
        # Ask user if they want to remove these
        response = input("\n🤔 Remove accounts with persistent errors? (y/N): ").strip().lower()
        if response == 'y':
            cursor.execute("DELETE FROM accounts WHERE status = 'error' AND errors > 3")
            print(f"✅ Removed {len(error_accounts)} error accounts")
        else:
            print("⏭️ Skipped removing error accounts")
    
    # 4. Update account_type for any remaining accounts
    cursor.execute("""
        UPDATE accounts 
        SET account_type = 'normal' 
        WHERE account_type IS NULL OR account_type = ''
    """)
    
    conn.commit()
    
    # 5. Show final state
    cursor.execute("SELECT phone, api_id, account_type, status, active FROM accounts")
    final_accounts = cursor.fetchall()
    
    print(f"\n📊 Final account state ({len(final_accounts)} accounts):")
    print("-" * 60)
    for phone, api_id, acc_type, status, active in final_accounts:
        active_str = "✅" if active else "❌"
        print(f"{active_str} {phone:<15} | Type: {acc_type:<8} | Status: {status:<8} | API ID: {api_id}")
    
    conn.close()
    print(f"\n🎉 Account cleanup completed!")

if __name__ == "__main__":
    fix_accounts() 