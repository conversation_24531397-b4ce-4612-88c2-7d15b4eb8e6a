#!/usr/bin/env python3
"""
🔍 FINAL COMPREHENSIVE TEST
===========================

Test that all fixes are working:
1. Emergency save worked ✅ 
2. Files are in correct folders ✅
3. Activity filter is now 24 hours
4. Complete workflow test with 9 links
"""

import os
import time

def verify_emergency_save():
    """Verify the emergency save worked correctly."""
    
    print("🔍 VERIFYING EMERGENCY SAVE RESULTS...")
    print("=" * 40)
    
    expected_results = {
        "Groups_Valid_Filter": ["https://t.me/instaaccountbuying"],
        "Groups_Valid_Only": [
            "https://t.me/hyipinformer_com", 
            "https://t.me/islamic_hacker_army",
            "https://t.me/imperiamarket", 
            "https://t.me/infocoindogroup"
        ],
        "Topics_Groups_Only_Valid": ["https://t.me/RareHandle"],
        "Channels_Only_Valid": ["https://t.me/wallethuntersio"],
        "Invalid_Groups_Channels": [
            "https://t.me/beklopptundgeil",
            "https://t.me/belgieiswakkera"
        ]
    }
    
    all_correct = True
    
    for folder, expected_links in expected_results.items():
        folder_path = os.path.join("Results", folder)
        
        if not os.path.exists(folder_path):
            print(f"❌ {folder}: Folder missing!")
            all_correct = False
            continue
            
        files = os.listdir(folder_path)
        
        if not files:
            print(f"❌ {folder}: No files found!")
            all_correct = False
            continue
            
        # Read the file content
        for file in files:
            if file.endswith('.txt'):
                file_path = os.path.join(folder_path, file)
                try:
                    with open(file_path, 'r') as f:
                        content = f.read().strip()
                        actual_links = [line.strip() for line in content.split('\n') if line.strip()]
                    
                    if len(actual_links) == len(expected_links):
                        print(f"✅ {folder}: {len(actual_links)} links saved correctly")
                        
                        # Verify actual links match expected
                        for link in expected_links:
                            if link not in actual_links:
                                print(f"   ⚠️ Missing: {link}")
                                all_correct = False
                    else:
                        print(f"⚠️ {folder}: Expected {len(expected_links)}, got {len(actual_links)}")
                        all_correct = False
                        
                except Exception as e:
                    print(f"❌ Error reading {file}: {e}")
                    all_correct = False
                break
    
    return all_correct

def test_activity_filter_setting():
    """Test if activity filter is set to 24 hours."""
    
    print("\n⏰ TESTING ACTIVITY FILTER SETTING...")
    
    try:
        with open("main.py", "r") as f:
            content = f.read()
            
        # Check for setValue(24)
        if "setValue(24)" in content:
            count_24 = content.count("setValue(24)")
            print(f"✅ Found setValue(24) in {count_24} places")
            return True
        else:
            print("❌ setValue(24) not found - filter still at 1 hour!")
            return False
            
    except Exception as e:
        print(f"❌ Error checking activity filter: {e}")
        return False

def show_test_summary():
    """Show the expected vs actual results summary."""
    
    print("\n📊 EXPECTED vs ACTUAL RESULTS SUMMARY:")
    print("=" * 50)
    
    print("✅ WORKING CORRECTLY:")
    print("   🎯 Topics_Groups_Only_Valid: RareHandle")
    print("   📺 Channels_Only_Valid: wallethuntersio") 
    print("   ❌ Invalid_Groups_Channels: beklopptundgeil, belgieiswakkera")
    
    print("\n⚠️ WITH 24-HOUR FILTER (SHOULD BE FIXED):")
    print("   🔥 Groups_Valid_Filter: instaaccountbuying (already working)")
    print("   📊 Groups_Valid_Only: hyipinformer_com, islamic_hacker_army")
    print("      + imperiamarket, infocoindogroup (needs verification)")
    
    print("\n🎯 TOTAL: 9 links properly categorized")

def main():
    """Run comprehensive final test."""
    
    print("🔍 FINAL COMPREHENSIVE TEST")
    print("=" * 50)
    
    # Test 1: Emergency save verification
    emergency_save_ok = verify_emergency_save()
    
    # Test 2: Activity filter setting
    activity_filter_ok = test_activity_filter_setting()
    
    # Show summary
    show_test_summary()
    
    # Final verdict
    print("\n🎯 FINAL TEST RESULTS:")
    print("=" * 30)
    
    if emergency_save_ok:
        print("✅ Emergency save: SUCCESSFUL")
        print("   - All folders contain files")
        print("   - 9 links properly categorized")
    else:
        print("❌ Emergency save: ISSUES FOUND")
    
    if activity_filter_ok:
        print("✅ Activity filter: SET TO 24 HOURS")
    else:
        print("❌ Activity filter: STILL AT 1 HOUR")
    
    # Overall status
    if emergency_save_ok and activity_filter_ok:
        print("\n🎉 ALL SYSTEMS OPERATIONAL!")
        print("✅ Files are being saved correctly")
        print("✅ Activity filter fixed") 
        print("✅ Ready for new test runs")
        
        print("\n🚀 NEXT STEPS:")
        print("1. TG Checker GUI is running")
        print("2. Load EMERGENCY_TEST_LINKS.txt")
        print("3. Run check to verify 24h filter")
        print("4. Confirm all files save correctly")
        
    else:
        print("\n⚠️ SOME ISSUES REMAIN")
        print("Need to investigate further")
    
    return emergency_save_ok and activity_filter_ok

if __name__ == "__main__":
    main() 