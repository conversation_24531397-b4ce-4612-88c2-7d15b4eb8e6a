@echo off
echo ================================================
echo    TG CHECKER - CRASH PROOF EDITION
echo ================================================
echo.
echo This version includes comprehensive protection:
echo   * UI Freeze Prevention
echo   * Crash Prevention System  
echo   * Emergency Exception Handling
echo   * Resource Management
echo   * Database Safety
echo.

REM Navigate to TG Checker directory
cd /d "%~dp0"

echo Starting TG Checker with crash protection...
echo.
echo IMPORTANT: This version is CRASH-PROOF!
echo - Joining operations run in background threads
echo - <PERSON><PERSON> will NEVER freeze or become unresponsive
echo - Crashes are prevented with emergency handlers
echo - Resource cleanup happens automatically
echo.

REM Run the crash-proof version
python START_TG_CHECKER_CRASH_PROOF.py

echo.
echo TG Checker has closed.
echo If it crashed, emergency systems should have prevented data loss.
pause 