@echo off
echo ========================================
echo    TG Checker with Emergency UI Fix
echo ========================================
echo.
echo This will run TG Checker with emergency UI freeze protection.
echo The joining operations will now run in background threads!
echo.

REM Navigate to TG Checker directory
cd /d "%~dp0"

echo Starting TG Checker...
echo.
echo IMPORTANT: The emergency UI freeze fix is now active!
echo - Joining operations run in background threads
echo - UI will remain responsive during operations
echo - No more "Not Responding" freeze issues
echo.

REM Run TG Checker normally - the fix is applied in main.py
python main.py

echo.
echo TG Checker has closed.
pause 