#!/usr/bin/env python3
"""
🚨 IMMEDIATE FIX: Continue Button Patch Error
============================================

This script fixes the "name 'apply_continue_button_patch' is not defined" error
by removing the early function calls and adding the proper call at the end.
"""

import re

def fix_main_file():
    """Fix the main.py file by removing early calls and adding proper call."""
    
    try:
        # Read the main file
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("📁 Reading main.py...")
        
        # Remove all early calls to apply_continue_button_patch that happen before the function is defined
        print("🧹 Removing early function calls...")
        
        # Pattern to find and remove the early calls
        patterns_to_remove = [
            r"\s*# Apply continue button thread fix\s*\n\s*apply_continue_button_patch\(self\)\s*\n",
            r"\s*apply_continue_button_patch\(self\)\s*\n",
        ]
        
        for pattern in patterns_to_remove:
            content = re.sub(pattern, "\n", content, flags=re.MULTILINE)
        
        # Find the end of the TGCheckerApp.__init__ method and add the call there
        print("🎯 Finding TGCheckerApp.__init__ method...")
        
        # Look for the end of the __init__ method (right before the next method definition)
        init_pattern = r"(class TGCheckerApp.*?def __init__\(self\):.*?)(def [^_])"
        
        def add_patch_call_at_end(match):
            init_part = match.group(1)
            next_method = match.group(2)
            
            # Add the patch call before the next method, but only if it's not already there
            if "apply_continue_button_patch(self)" not in init_part:
                # Find the last substantive line in the init method
                lines = init_part.split('\n')
                
                # Insert the call before the last few lines (usually empty lines)
                insert_index = len(lines) - 1
                while insert_index > 0 and lines[insert_index].strip() == "":
                    insert_index -= 1
                
                # Insert the patch call
                patch_lines = [
                    "",
                    "        # Apply continue button thread fix (moved to end)",
                    "        try:",
                    "            apply_continue_button_patch(self)",
                    "            print('✅ Continue button thread fix applied')",
                    "        except Exception as e:",
                    "            print(f'⚠️ Continue button fix failed: {e}')",
                    ""
                ]
                
                # Insert the patch lines
                for i, line in enumerate(patch_lines):
                    lines.insert(insert_index + 1 + i, line)
                
                init_part = '\n'.join(lines)
            
            return init_part + next_method
        
        # Apply the pattern
        content = re.sub(init_pattern, add_patch_call_at_end, content, flags=re.DOTALL)
        
        # Write the fixed content
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ Fixed main.py successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing main.py: {e}")
        return False

def verify_fix():
    """Verify the fix was applied correctly."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check that the function is defined
        has_function = "def apply_continue_button_patch" in content
        
        # Check that early calls are removed (no calls before line 10000)
        lines = content.split('\n')
        early_calls = 0
        function_definition_line = -1
        
        for i, line in enumerate(lines):
            if "def apply_continue_button_patch" in line:
                function_definition_line = i
                break
            elif "apply_continue_button_patch(self)" in line:
                early_calls += 1
        
        # Check that there's a proper call after the function definition
        proper_call_exists = False
        if function_definition_line > 0:
            for i in range(function_definition_line):
                if "apply_continue_button_patch(self)" in lines[i]:
                    # This is a call before the definition, which we want to check for
                    pass
            
            # Look for calls in the init method that come with try/except
            init_section = content[content.find("def __init__(self):"):content.find("def __init__(self):") + 10000]
            if "apply_continue_button_patch(self)" in init_section and "try:" in init_section:
                proper_call_exists = True
        
        print("\n🔍 Verification Results:")
        print(f"   ✅ Function definition exists: {has_function}")
        print(f"   ✅ Early calls removed: {early_calls == 0}")
        print(f"   ✅ Proper call in init: {proper_call_exists}")
        
        if has_function and early_calls == 0:
            print("\n🎉 Fix applied successfully!")
            print("   Your TG Checker should now start without errors.")
            return True
        else:
            print("\n⚠️ Some issues remain.")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

def main():
    """Main function."""
    print("🚨 IMMEDIATE FIX: Continue Button Patch Error")
    print("=" * 50)
    print()
    
    if fix_main_file():
        if verify_fix():
            print("\n🚀 READY TO TEST!")
            print("=" * 20)
            print("Try running your TG Checker now:")
            print("python main.py")
        else:
            print("\n⚠️ Fix may not be complete. Please check manually.")
    else:
        print("\n❌ Fix failed. Please check the error above.")

if __name__ == "__main__":
    main() 