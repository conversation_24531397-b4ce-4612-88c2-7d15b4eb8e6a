#!/usr/bin/env python3
"""
Fix indentation issues in main.py by directly modifying the problematic method
"""

import os
import shutil

def fix_line_by_line():
    """Fix indentation issues in main.py by directly modifying the problematic method"""
    # Create a backup of the original file
    input_file = "main.py"
    backup_file = f"{input_file}.bak_direct"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the problematic method
    target_line_num = None
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            target_line_num = i
            print(f"Found auto_refresh_missing_account_info method at line {i+1}")
            break
    
    if target_line_num is None:
        print("Error: Could not find the problematic method")
        return False
    
    # Check if we need to modify this line
    current_line = lines[target_line_num]
    current_indent = len(current_line) - len(current_line.lstrip())
    
    # Force indentation to be exactly 4 spaces (standard for class methods)
    new_line = "    " + current_line.lstrip()
    lines[target_line_num] = new_line
    
    # Find the end of the method
    start_line = target_line_num
    end_line = start_line + 1
    
    while end_line < len(lines):
        # Skip empty lines
        if not lines[end_line].strip():
            end_line += 1
            continue
        
        # If we find a line that starts with 'def' and has the same or less indentation,
        # we've reached the next method
        line_stripped = lines[end_line].lstrip()
        if line_stripped.startswith("def ") and len(lines[end_line]) - len(line_stripped) <= 4:
            break
        
        end_line += 1
    
    print(f"Method body spans from line {start_line+1} to {end_line}")
    
    # Fix indentation for all lines in the method body
    for i in range(start_line + 1, end_line):
        if not lines[i].strip():  # Skip empty lines
            continue
        
        # All method body lines should be indented at least 8 spaces (4 for class + 4 for method body)
        body_line = lines[i].lstrip()
        lines[i] = "        " + body_line
    
    # Write the fixed content back
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("Fixed indentation of the auto_refresh_missing_account_info method")
    print("You can now run: python main.py")
    
    return True

if __name__ == "__main__":
    fix_line_by_line() 