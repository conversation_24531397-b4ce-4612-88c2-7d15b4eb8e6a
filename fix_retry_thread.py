#!/usr/bin/env python3
"""
Script to fix the _schedule_account_retry method in main.py
This resolves issues with indentation errors and improves
the account retry mechanism with better error handling
"""

import re

def main():
    # Read the current main.py file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the current _schedule_account_retry method
    method_pattern = re.compile(
        r'def _schedule_account_retry\(self, phone, wait_seconds\):.*?def update_log_display',
        re.DOTALL
    )
    
    match = method_pattern.search(content)
    if not match:
        print("Could not find _schedule_account_retry method in main.py")
        return
    
    old_method = match.group(0)
    
    # Replace with the corrected implementation
    correct_method = """def _schedule_account_retry(self, phone, wait_seconds):
        \"\"\"Schedule an account retry after FloodWait or error recovery period with improved error handling.\"\"\"
        retry_type = "FloodWait" if wait_seconds >= 300 else "Error Recovery"
        self.log_activity_signal.emit(f"⏰ RETRY SCHEDULED: Account {phone} {retry_type} - will retry in {wait_seconds}s ({wait_seconds//60}m {wait_seconds%60}s)")
        
        def retry_thread():
            try:
                # Use the actual wait time, with minimum safety margins
                if wait_seconds >= 300:  # FloodWait
                    actual_wait = max(wait_seconds, 300)  # Minimum 5 minutes for safety
                else:  # Error recovery
                    actual_wait = max(wait_seconds, 60)   # Minimum 1 minute for error recovery
                
                # Log countdown every minute for longer waits
                if actual_wait > 60:
                    remaining_time = actual_wait
                    while remaining_time > 0 and not self.task_checker_should_stop:
                        if remaining_time % 60 == 0:  # Log every minute
                            minutes = remaining_time // 60
                            self.log_activity_signal.emit(f"⏰ Account {phone} retry in {minutes} minutes")
                        time.sleep(30)  # Check every 30 seconds
                        remaining_time -= 30
                else:
                    time.sleep(actual_wait)
                
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Retry cancelled for account {phone}")
                    return
                
                # Re-enable account
                with self.account_states_lock:
                    if phone in self.account_states:
                        old_status = self.account_states[phone]["status"]
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        
                        self.log_activity_signal.emit(f"🔄 RETRY: Account {phone} recovered from {old_status} → now available")
                
                # Update account status in database
                self.account_manager.update_check_time(phone, "OK - Recovered")
                
                self.log_activity_signal.emit(f"✅ RECOVERY COMPLETE: Account {phone} is ready for new tasks")
            except Exception as e:
                self.logger.error(f"Retry thread error for {phone}: {str(e)}")
                self.log_activity_signal.emit(f"❌ Retry error for account {phone}: {str(e)}")
                
                # Mark as failed if recovery fails
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "failed"
        
        # Start retry thread
        threading.Thread(target=retry_thread, daemon=True).start()
    
    def update_log_display"""
    
    # Replace the old method with the new one
    new_content = content.replace(old_method, correct_method)
    
    # Write the updated content back to main.py
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("Successfully fixed _schedule_account_retry method in main.py")

if __name__ == "__main__":
    main() 