import asyncio
import sqlite3
import json
import time
import random
import logging
from datetime import datetime, timedelta
from contextlib import contextmanager
from PyQt5.QtCore import QThread, pyqtSignal
from PyQt5.QtWidgets import QApplication
from telethon import TelegramClient
from telethon.errors import FloodWaitError, ChatAdminRequiredError, ChatWriteForbiddenError, UserBannedInChannelError, SlowModeWaitError
from telethon.tl.types import Channel, Chat, User


class TelegramForwarder(QThread):
    """
    High-performance Telegram message forwarder with anti-detection features.
    """
    
    # Qt signals for UI updates
    task_started = pyqtSignal(str)  # task_id
    task_stopped = pyqtSignal(str)  # task_id
    task_completed = pyqtSignal(str)  # task_id
    message_forwarded = pyqtSignal(str, str, str)  # task_id, target, status
    account_paused = pyqtSignal(str, str, int)  # phone, reason, wait_time
    account_resumed = pyqtSignal(str)  # phone
    log_message = pyqtSignal(str, str, str)  # level, task_id, message
    progress_updated = pyqtSignal(str, int, int)  # task_id, current, total
    
    def __init__(self, database_path="forwarder.db", logger=None):
        super().__init__()
        self.database_path = database_path
        self.logger = logger or logging.getLogger(__name__)
        
        # Task management
        self.active_tasks = {}
        self.running = False
        self.should_stop = False
        
        # Account states and flood protection
        self.account_states = {}
        self.account_clients = {}
        self.paused_accounts = {}
        
        # Anti-detection settings
        self.anti_detection_enabled = True
        self.randomize_timing = True
        self.use_smart_delays = True
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize the forwarder database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Forwarder tasks table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT NOT NULL,
                        account_phone TEXT NOT NULL,
                        message_link TEXT,
                        message_link_2 TEXT,
                        target_groups TEXT,
                        target_groups_2 TEXT,
                        status TEXT DEFAULT 'stopped',
                        current_index INTEGER DEFAULT 0,
                        current_index_2 INTEGER DEFAULT 0,
                        total_messages INTEGER DEFAULT 0,
                        successful_forwards INTEGER DEFAULT 0,
                        failed_forwards INTEGER DEFAULT 0,
                        created_at TEXT,
                        updated_at TEXT,
                        settings TEXT
                    )
                ''')
                
                # Account forwarder settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS account_forwarder_settings (
                        phone TEXT PRIMARY KEY,
                        interval_min INTEGER DEFAULT 20,
                        interval_max INTEGER DEFAULT 25,
                        after_each_second INTEGER DEFAULT 360,
                        random_sleep_time_min INTEGER DEFAULT 30,
                        random_sleep_time_max INTEGER DEFAULT 60,
                        reply_message TEXT,
                        is_active INTEGER DEFAULT 1,
                        last_used TEXT,
                        flood_wait_until TEXT,
                        settings TEXT
                    )
                ''')
                
                # Global forwarder settings
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS global_forwarder_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Forwarding logs
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarding_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        task_id TEXT,
                        account_phone TEXT,
                        target_group TEXT,
                        message_link TEXT,
                        status TEXT,
                        error_message TEXT,
                        timestamp TEXT
                    )
                ''')
                
                conn.commit()
                self.logger.info("Forwarder database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize forwarder database: {str(e)}")
            raise
    
    @contextmanager
    def _get_db_connection(self):
        """Get database connection with proper error handling."""
        conn = None
        try:
            conn = sqlite3.connect(self.database_path, timeout=30)
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA busy_timeout=30000")
            yield conn
        finally:
            if conn:
                conn.close()
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            task_id = f"task_{int(time.time())}"
            now = datetime.now().isoformat()
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO forwarder_tasks 
                    (id, name, account_phone, message_link, message_link_2, 
                     target_groups, target_groups_2, created_at, updated_at, settings)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_id,
                    task_data.get('name', f'Task {task_id}'),
                    task_data.get('account_phone'),
                    task_data.get('message_link'),
                    task_data.get('message_link_2'),
                    json.dumps(task_data.get('target_groups', [])),
                    json.dumps(task_data.get('target_groups_2', [])),
                    now,
                    now,
                    json.dumps(task_data.get('settings', {}))
                ))
                conn.commit()
            
            self.logger.info(f"Created forwarding task: {task_id}")
            return task_id
            
        except Exception as e:
            self.logger.error(f"Failed to create task: {str(e)}")
            return None
    
    def get_tasks(self):
        """Get all forwarding tasks."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM forwarder_tasks ORDER BY created_at DESC')
                
                tasks = []
                for row in cursor.fetchall():
                    task = {
                        'id': row[0],
                        'name': row[1],
                        'account_phone': row[2],
                        'message_link': row[3],
                        'message_link_2': row[4],
                        'target_groups': json.loads(row[5] or '[]'),
                        'target_groups_2': json.loads(row[6] or '[]'),
                        'status': row[7],
                        'current_index': row[8],
                        'current_index_2': row[9],
                        'total_messages': row[10],
                        'successful_forwards': row[11],
                        'failed_forwards': row[12],
                        'created_at': row[13],
                        'updated_at': row[14],
                        'settings': json.loads(row[15] or '{}')
                    }
                    tasks.append(task)
                
                return tasks
                
        except Exception as e:
            self.logger.error(f"Failed to get tasks: {str(e)}")
            return []
    
    def update_task_status(self, task_id, status, **kwargs):
        """Update task status and other fields."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                update_fields = ['status = ?']
                values = [status]
                
                if 'current_index' in kwargs:
                    update_fields.append('current_index = ?')
                    values.append(kwargs['current_index'])
                
                if 'current_index_2' in kwargs:
                    update_fields.append('current_index_2 = ?')
                    values.append(kwargs['current_index_2'])
                
                if 'successful_forwards' in kwargs:
                    update_fields.append('successful_forwards = ?')
                    values.append(kwargs['successful_forwards'])
                
                if 'failed_forwards' in kwargs:
                    update_fields.append('failed_forwards = ?')
                    values.append(kwargs['failed_forwards'])
                
                update_fields.append('updated_at = ?')
                values.append(datetime.now().isoformat())
                values.append(task_id)
                
                query = f"UPDATE forwarder_tasks SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, values)
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to update task status: {str(e)}")
    
    def get_account_settings(self, phone):
        """Get account-specific forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    'SELECT * FROM account_forwarder_settings WHERE phone = ?',
                    (phone,)
                )
                row = cursor.fetchone()
                
                if row:
                    return {
                        'phone': row[0],
                        'interval_min': row[1],
                        'interval_max': row[2],
                        'after_each_second': row[3],
                        'random_sleep_time_min': row[4],
                        'random_sleep_time_max': row[5],
                        'reply_message': row[6],
                        'is_active': bool(row[7]),
                        'last_used': row[8],
                        'flood_wait_until': row[9],
                        'settings': json.loads(row[10] or '{}')
                    }
                else:
                    # Return default settings
                    return {
                        'phone': phone,
                        'interval_min': 20,
                        'interval_max': 25,
                        'after_each_second': 360,
                        'random_sleep_time_min': 30,
                        'random_sleep_time_max': 60,
                        'reply_message': "Hi! This is an advertising bot. @vipstore. DMs won't be seen here",
                        'is_active': True,
                        'last_used': None,
                        'flood_wait_until': None,
                        'settings': {}
                    }
                    
        except Exception as e:
            self.logger.error(f"Failed to get account settings: {str(e)}")
            return None
    
    def update_account_settings(self, phone, settings):
        """Update account-specific forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO account_forwarder_settings
                    (phone, interval_min, interval_max, after_each_second,
                     random_sleep_time_min, random_sleep_time_max, reply_message,
                     is_active, last_used, flood_wait_until, settings)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    phone,
                    settings.get('interval_min', 20),
                    settings.get('interval_max', 25),
                    settings.get('after_each_second', 360),
                    settings.get('random_sleep_time_min', 30),
                    settings.get('random_sleep_time_max', 60),
                    settings.get('reply_message', ''),
                    1 if settings.get('is_active', True) else 0,
                    settings.get('last_used'),
                    settings.get('flood_wait_until'),
                    json.dumps(settings.get('settings', {}))
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to update account settings: {str(e)}")
    
    def get_global_settings(self):
        """Get global forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT key, value FROM global_forwarder_settings')
                
                settings = {}
                for row in cursor.fetchall():
                    try:
                        settings[row[0]] = json.loads(row[1])
                    except:
                        settings[row[0]] = row[1]
                
                # Set defaults if not found
                defaults = {
                    'use_random_sleep_time': True,
                    'log_failed_groups': True,
                    'flood_wait_enabled': True,
                    'reply_message_enabled': True,
                    'global_reply_message': "Hi! This is an advertising bot. @vipstore. DMs won't be seen here"
                }
                
                for key, value in defaults.items():
                    if key not in settings:
                        settings[key] = value
                
                return settings
                
        except Exception as e:
            self.logger.error(f"Failed to get global settings: {str(e)}")
            return {}
    
    def update_global_settings(self, settings):
        """Update global forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                for key, value in settings.items():
                    cursor.execute('''
                        INSERT OR REPLACE INTO global_forwarder_settings (key, value)
                        VALUES (?, ?)
                    ''', (key, json.dumps(value)))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to update global settings: {str(e)}")
    
    async def start_task(self, task_id):
        """Start a specific forwarding task."""
        try:
            tasks = self.get_tasks()
            task = next((t for t in tasks if t['id'] == task_id), None)
            
            if not task:
                self.log_message.emit('error', task_id, f"Task {task_id} not found")
                return False
            
            self.update_task_status(task_id, 'running')
            self.active_tasks[task_id] = task
            self.task_started.emit(task_id)
            
            # Start forwarding in background
            asyncio.create_task(self._run_task(task))
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start task {task_id}: {str(e)}")
            self.log_message.emit('error', task_id, f"Failed to start: {str(e)}")
            return False
    
    async def stop_task(self, task_id):
        """Stop a specific forwarding task."""
        try:
            if task_id in self.active_tasks:
                self.update_task_status(task_id, 'stopped')
                del self.active_tasks[task_id]
                self.task_stopped.emit(task_id)
                self.log_message.emit('info', task_id, "Task stopped")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to stop task {task_id}: {str(e)}")
            return False
    
    async def _run_task(self, task):
        """Run a forwarding task."""
        task_id = task['id']
        phone = task['account_phone']
        
        try:
            # Get account settings
            account_settings = self.get_account_settings(phone)
            if not account_settings or not account_settings['is_active']:
                self.log_message.emit('error', task_id, f"Account {phone} is not active")
                return
            
            # Check if account is paused due to flood wait
            if self._is_account_paused(phone):
                self.log_message.emit('warning', task_id, f"Account {phone} is paused due to flood wait")
                return
            
            # Initialize Telegram client
            client = await self._get_client(phone)
            if not client:
                self.log_message.emit('error', task_id, f"Failed to connect account {phone}")
                return
            
            # Process message links and target groups
            await self._process_task_forwarding(task, client, account_settings)
            
        except Exception as e:
            self.logger.error(f"Error running task {task_id}: {str(e)}")
            self.log_message.emit('error', task_id, f"Task error: {str(e)}")
            self.update_task_status(task_id, 'error')
        finally:
            # Clean up
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            self.task_completed.emit(task_id)
    
    async def _process_task_forwarding(self, task, client, account_settings):
        """Process forwarding for a task."""
        task_id = task['id']
        
        # Process first message link and targets
        if task['message_link'] and task['target_groups']:
            await self._forward_to_targets(
                task_id, client, task['message_link'], 
                task['target_groups'], task['current_index'],
                account_settings, 'current_index'
            )
        
        # Process second message link and targets
        if task['message_link_2'] and task['target_groups_2']:
            await self._forward_to_targets(
                task_id, client, task['message_link_2'], 
                task['target_groups_2'], task['current_index_2'],
                account_settings, 'current_index_2'
            )
    
    async def _forward_to_targets(self, task_id, client, message_link, targets, start_index, account_settings, index_field):
        """Forward message to target groups."""
        phone = account_settings['phone']
        successful = 0
        failed = 0
        
        try:
            # Parse message link to get source and message ID
            source_entity, message_id = await self._parse_message_link(client, message_link)
            if not source_entity or message_id is None:
                self.log_message.emit('error', task_id, f"Invalid message link: {message_link}")
                return
            
            for i, target in enumerate(targets[start_index:], start_index):
                try:
                    # Check if task should stop
                    if task_id not in self.active_tasks:
                        break
                    
                    # Check if account is paused
                    if self._is_account_paused(phone):
                        self.account_paused.emit(phone, "Flood wait", 0)
                        break
                    
                    # Apply anti-detection delays
                    if i > start_index:
                        await self._apply_smart_delay(account_settings)
                    
                    # Forward message
                    success = await self._forward_message(
                        client, source_entity, message_id, target, account_settings
                    )
                    
                    if success:
                        successful += 1
                        self.message_forwarded.emit(task_id, target, "success")
                        self.log_message.emit('success', task_id, f"✅ Forwarded to {target}")
                    else:
                        failed += 1
                        self.message_forwarded.emit(task_id, target, "failed")
                        self.log_message.emit('error', task_id, f"❌ Failed to forward to {target}")
                    
                    # Update progress
                    self.update_task_status(task_id, 'running', **{
                        index_field: i + 1,
                        'successful_forwards': successful,
                        'failed_forwards': failed
                    })
                    
                    self.progress_updated.emit(task_id, i + 1, len(targets))
                    
                    # Log the forwarding attempt
                    self._log_forwarding_attempt(task_id, phone, target, message_link, 
                                               "success" if success else "failed", 
                                               "" if success else "Forward failed")
                
                except Exception as e:
                    failed += 1
                    error_msg = str(e)
                    self.log_message.emit('error', task_id, f"❌ Error forwarding to {target}: {error_msg}")
                    self._log_forwarding_attempt(task_id, phone, target, message_link, "error", error_msg)
                    
                    # Handle specific errors
                    if isinstance(e, FloodWaitError):
                        await self._handle_flood_wait(phone, e.seconds)
                        break
                    
        except Exception as e:
            self.logger.error(f"Error in forward_to_targets: {str(e)}")
            self.log_message.emit('error', task_id, f"Forwarding error: {str(e)}")
    
    async def _parse_message_link(self, client, message_link):
        """Parse a Telegram message link to get source entity and message ID."""
        try:
            # Handle different link formats
            if "t.me/" in message_link:
                # Remove protocol if present
                url = message_link.strip()
                if url.startswith("http://") or url.startswith("https://"):
                    url = url.split("://", 1)[1]
                
                # Handle t.me/username/message_id format
                if url.startswith("t.me/"):
                    parts = url.split('/')
                    if len(parts) >= 3:
                        username = parts[1]  # Channel/group username
                        message_id = int(parts[2])  # Message ID
                        
                        # Try different entity resolution methods
                        entity = None
                        
                        try:
                            # Try with @username format first
                            if not username.startswith('@'):
                                username_with_at = '@' + username
                                entity = await client.get_entity(username_with_at)
                            else:
                                entity = await client.get_entity(username)
                        except Exception:
                            try:
                                # Try without @ if that failed
                                clean_username = username.lstrip('@')
                                entity = await client.get_entity(clean_username)
                            except Exception:
                                # Try resolving as channel ID if it's numeric
                                if clean_username.isdigit():
                                    entity = await client.get_entity(int(clean_username))
                                else:
                                    raise Exception(f"Could not resolve entity for username: {username}")
                        
                        if entity:
                            # Improved message verification
                            try:
                                # Try to get the specific message
                                message = await client.get_messages(entity, ids=message_id)
                                
                                # Handle different message validation scenarios
                                if message:
                                    # Check if it's a list (single message returns list with one item)
                                    if isinstance(message, list):
                                        if len(message) > 0 and message[0] is not None:
                                            # Message exists and is accessible
                                            return entity, message_id
                                        else:
                                            # Message might exist but not accessible, try alternative verification
                                            pass
                                    else:
                                        # Single message object
                                        if message and hasattr(message, 'id') and message.id == message_id:
                                            # Message exists and is accessible
                                            return entity, message_id
                                        else:
                                            # Message might exist but not accessible, try alternative verification
                                            pass
                                
                                # Alternative verification: Try to get recent messages to check if channel is accessible
                                try:
                                    recent_messages = await client.get_messages(entity, limit=1)
                                    if recent_messages:
                                        # Channel is accessible, assume message exists (might be deleted or restricted)
                                        # This is a valid scenario for forwarding
                                        self.logger.info(f"Channel {username} is accessible, assuming message {message_id} exists")
                                        return entity, message_id
                                except Exception:
                                    pass
                                
                                # Final fallback: If we can access the entity, assume the message link is valid
                                # The actual forwarding will reveal if the message truly doesn't exist
                                self.logger.info(f"Entity {username} resolved successfully, assuming message {message_id} is valid")
                                return entity, message_id
                                
                            except Exception as e:
                                error_str = str(e).lower()
                                
                                # Handle specific error types that don't necessarily mean the message is invalid
                                if any(keyword in error_str for keyword in [
                                    'timeout', 'network', 'connection', 'flood', 'rate limit'
                                ]):
                                    # Network/rate limiting issues - assume message is valid
                                    self.logger.warning(f"Network issue accessing message {message_id}, assuming valid: {str(e)}")
                                    return entity, message_id
                                elif 'message_id_invalid' in error_str:
                                    # Message truly doesn't exist
                                    raise Exception(f"Message {message_id} does not exist")
                                else:
                                    # For other errors, assume message might be valid but restricted
                                    self.logger.warning(f"Message verification inconclusive for {message_id}, assuming valid: {str(e)}")
                                    return entity, message_id
                        else:
                            raise Exception("Could not resolve entity")
            
            return None, None
            
        except Exception as e:
            self.logger.error(f"Failed to parse message link {message_link}: {str(e)}")
            return None, None
    
    async def _forward_message(self, client, source_entity, message_id, target, account_settings):
        """Forward a specific message to a target."""
        try:
            # Get target entity
            target_entity = await client.get_entity(target)
            
            # Forward the message using Telegram's native forward function
            await client.forward_messages(
                entity=target_entity,
                messages=message_id,
                from_peer=source_entity
            )
            
            # Send reply message if enabled
            if account_settings.get('reply_message') and self.get_global_settings().get('reply_message_enabled', True):
                await asyncio.sleep(random.uniform(2, 5))  # Small delay before reply
                await client.send_message(target_entity, account_settings['reply_message'])
            
            return True
            
        except FloodWaitError as e:
            await self._handle_flood_wait(account_settings['phone'], e.seconds)
            raise
        except (ChatAdminRequiredError, ChatWriteForbiddenError, UserBannedInChannelError) as e:
            self.logger.warning(f"Access denied for {target}: {str(e)}")
            return False
        except Exception as e:
            self.logger.error(f"Failed to forward to {target}: {str(e)}")
            return False
    
    async def _apply_smart_delay(self, account_settings):
        """Apply intelligent delays for anti-detection."""
        try:
            # Random interval between messages
            min_interval = account_settings.get('interval_min', 20)
            max_interval = account_settings.get('interval_max', 25)
            interval = random.uniform(min_interval, max_interval)
            
            await asyncio.sleep(interval)
            
            # Additional random sleep if enabled
            global_settings = self.get_global_settings()
            if global_settings.get('use_random_sleep_time', True):
                min_sleep = account_settings.get('random_sleep_time_min', 30)
                max_sleep = account_settings.get('random_sleep_time_max', 60)
                
                # Random chance to apply additional sleep
                if random.random() < 0.3:  # 30% chance
                    additional_sleep = random.uniform(min_sleep, max_sleep)
                    await asyncio.sleep(additional_sleep)
            
            # Long pause after each batch
            after_each = account_settings.get('after_each_second', 360)
            if random.random() < 0.1:  # 10% chance for long pause
                await asyncio.sleep(after_each)
                
        except Exception as e:
            self.logger.error(f"Error in smart delay: {str(e)}")
    
    async def _handle_flood_wait(self, phone, wait_seconds):
        """Handle FloodWait errors."""
        try:
            wait_until = datetime.now() + timedelta(seconds=wait_seconds)
            
            # Update account settings with flood wait info
            settings = self.get_account_settings(phone)
            settings['flood_wait_until'] = wait_until.isoformat()
            self.update_account_settings(phone, settings)
            
            # Emit signals
            self.account_paused.emit(phone, f"FloodWait {wait_seconds}s", wait_seconds)
            self.log_message.emit('warning', '', f"Account {phone} paused due to FloodWait ({wait_seconds}s)")
            
            # Auto-resume after wait period
            asyncio.create_task(self._auto_resume_account(phone, wait_seconds))
            
        except Exception as e:
            self.logger.error(f"Error handling flood wait: {str(e)}")
    
    async def _auto_resume_account(self, phone, wait_seconds):
        """Auto-resume account after flood wait."""
        try:
            await asyncio.sleep(wait_seconds + 5)  # Add small buffer
            
            # Clear flood wait status
            settings = self.get_account_settings(phone)
            settings['flood_wait_until'] = None
            self.update_account_settings(phone, settings)
            
            self.account_resumed.emit(phone)
            self.log_message.emit('info', '', f"Account {phone} resumed after flood wait")
            
        except Exception as e:
            self.logger.error(f"Error auto-resuming account: {str(e)}")
    
    def _is_account_paused(self, phone):
        """Check if account is currently paused due to flood wait."""
        try:
            settings = self.get_account_settings(phone)
            flood_wait_until = settings.get('flood_wait_until')
            
            if flood_wait_until:
                wait_time = datetime.fromisoformat(flood_wait_until)
                return datetime.now() < wait_time
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking account pause status: {str(e)}")
            return False
    
    async def _get_client(self, phone):
        """Get or create Telegram client for account."""
        try:
            if phone in self.account_clients:
                return self.account_clients[phone]
            
            # Import account manager to get credentials
            from account_manager import AccountManager
            account_manager = AccountManager()
            account = account_manager.get_account(phone)
            
            if not account:
                self.logger.error(f"Account {phone} not found")
                return None
            
            # Create client
            session_file = f'sessions/{phone}'
            client = TelegramClient(session_file, account['api_id'], account['api_hash'])
            
            # Connect with timeout
            await asyncio.wait_for(client.connect(), timeout=15)
            
            if not await client.is_user_authorized():
                self.logger.error(f"Account {phone} is not authorized")
                return None
            
            self.account_clients[phone] = client
            return client
            
        except Exception as e:
            self.logger.error(f"Failed to get client for {phone}: {str(e)}")
            return None
    
    def _log_forwarding_attempt(self, task_id, phone, target, message_link, status, error_message=""):
        """Log forwarding attempt to database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO forwarding_logs 
                    (task_id, account_phone, target_group, message_link, status, error_message, timestamp)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    task_id, phone, target, message_link, status, 
                    error_message, datetime.now().isoformat()
                ))
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Failed to log forwarding attempt: {str(e)}")
    
    async def start_all_tasks(self):
        """Start all stopped tasks."""
        tasks = self.get_tasks()
        started_count = 0
        
        for task in tasks:
            if task['status'] == 'stopped':
                success = await self.start_task(task['id'])
                if success:
                    started_count += 1
        
        self.log_message.emit('info', '', f"Started {started_count} tasks")
        return started_count
    
    async def stop_all_tasks(self):
        """Stop all running tasks."""
        stopped_count = 0
        
        for task_id in list(self.active_tasks.keys()):
            success = await self.stop_task(task_id)
            if success:
                stopped_count += 1
        
        self.log_message.emit('info', '', f"Stopped {stopped_count} tasks")
        return stopped_count
    
    async def continue_all_tasks(self):
        """Continue all paused tasks from where they stopped."""
        return await self.start_all_tasks()
    
    def delete_task(self, task_id):
        """Delete a task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM forwarder_tasks WHERE id = ?', (task_id,))
                cursor.execute('DELETE FROM forwarding_logs WHERE task_id = ?', (task_id,))
                conn.commit()
            
            # Remove from active tasks if running
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete task: {str(e)}")
            return False
    
    def get_forwarding_logs(self, task_id=None, limit=100):
        """Get forwarding logs."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                if task_id:
                    cursor.execute('''
                        SELECT * FROM forwarding_logs 
                        WHERE task_id = ? 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    ''', (task_id, limit))
                else:
                    cursor.execute('''
                        SELECT * FROM forwarding_logs 
                        ORDER BY timestamp DESC 
                        LIMIT ?
                    ''', (limit,))
                
                logs = []
                for row in cursor.fetchall():
                    logs.append({
                        'id': row[0],
                        'task_id': row[1],
                        'account_phone': row[2],
                        'target_group': row[3],
                        'message_link': row[4],
                        'status': row[5],
                        'error_message': row[6],
                        'timestamp': row[7]
                    })
                
                return logs
                
        except Exception as e:
            self.logger.error(f"Failed to get forwarding logs: {str(e)}")
            return []
    
    async def cleanup(self):
        """Clean up resources."""
        try:
            # Stop all tasks
            await self.stop_all_tasks()
            
            # Disconnect all clients
            for client in self.account_clients.values():
                try:
                    await client.disconnect()
                except:
                    pass
            
            self.account_clients.clear()
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {str(e)}")


# Global forwarder instance
forwarder_instance = None

def get_forwarder_instance(database_path="forwarder.db", logger=None):
    """Get or create global forwarder instance."""
    global forwarder_instance
    if forwarder_instance is None:
        forwarder_instance = TelegramForwarder(database_path, logger)
    return forwarder_instance 