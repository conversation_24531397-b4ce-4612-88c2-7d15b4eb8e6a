#!/usr/bin/env python3
"""
Fix indentation error in main.py and apply Speed Check Time settings
"""

import os
import re
import shutil
import time

def fix_and_apply():
    """Fix the indentation error in main.py and apply the Speed Check settings"""
    # First, create a backup of the original file
    input_file = "main.py"
    backup_file = f"{input_file}.bak_combined"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.readlines()
    
    # 1. Fix indentation issues
    print("Fixing indentation issues...")
    
    # Find the indentation error around line 659
    # Looking for the auto_refresh_missing_account_info method
    fixed_indentation = False
    
    for i in range(650, 670):
        if i < len(content) and "def auto_refresh_missing_account_info" in content[i]:
            # Check the indentation of this line
            current_indent = len(content[i]) - len(content[i].lstrip())
            
            # Find the class definition to determine the correct indentation
            class_indent = 0
            for j in range(i, 0, -1):
                if "class TGCheckerApp" in content[j]:
                    # Class methods should be indented 4 spaces from class definition
                    class_indent = len(content[j]) - len(content[j].lstrip())
                    break
            
            # Calculate the correct indentation for class methods
            correct_indent = class_indent + 4
            
            # If current indentation is incorrect, fix it
            if current_indent != correct_indent:
                print(f"Found indentation error at line {i+1}")
                
                # Determine how many lines are part of this method
                method_end = i + 1
                while method_end < len(content):
                    next_line = content[method_end].strip()
                    if not next_line:  # Empty line
                        method_end += 1
                        continue
                    
                    next_indent = len(content[method_end]) - len(content[method_end].lstrip())
                    if next_indent <= class_indent:  # We've reached the next method or end of class
                        break
                    
                    method_end += 1
                
                # Fix the indentation for all lines in the method
                for k in range(i, method_end):
                    if content[k].strip():  # Skip empty lines
                        line_content = content[k].lstrip()
                        # Determine the relative indentation
                        if k == i:  # Method definition line
                            content[k] = ' ' * correct_indent + line_content
                        else:
                            # Maintain the relative indentation within the method
                            relative_indent = len(content[k]) - len(content[k].lstrip())
                            if relative_indent > current_indent:
                                # This is an indented line inside the method
                                content[k] = ' ' * (correct_indent + (relative_indent - current_indent)) + line_content
                            else:
                                # This is at the same level as the method definition
                                content[k] = ' ' * correct_indent + line_content
                
                fixed_indentation = True
                print(f"Fixed indentation for method at lines {i+1}-{method_end}")
                break
    
    # 2. Fix the syntax error at line 4013 (missing except/finally block)
    print("Fixing syntax error at line 4013...")
    
    # Find the try block without a corresponding except
    try_line = None
    for i in range(4013, 3900, -1):
        if i < len(content) and "try:" in content[i]:
            try_line = i
            break
    
    if try_line is not None:
        print(f"Found 'try:' at line {try_line+1}")
        
        # Check if there's an except block for this try
        try_indent = len(content[try_line]) - len(content[try_line].lstrip())
        has_except = False
        
        for i in range(try_line+1, min(4020, len(content))):
            line_strip = content[i].strip()
            if not line_strip:
                continue
                
            line_indent = len(content[i]) - len(content[i].lstrip())
            
            # If we hit a line with less or equal indentation to the try, but not an except/finally,
            # then we've found the end of the try block without an except
            if line_indent <= try_indent:
                if line_strip.startswith(("except", "finally")):
                    has_except = True
                break
        
        if not has_except:
            print(f"The try block at line {try_line+1} is missing an except clause")
            
            # Find where the try block ends
            try_end = try_line + 1
            while try_end < len(content):
                if not content[try_end].strip():
                    try_end += 1
                    continue
                    
                curr_indent = len(content[try_end]) - len(content[try_end].lstrip())
                if curr_indent <= try_indent and content[try_end].strip():
                    break
                    
                try_end += 1
            
            # Insert an except block at the end of the try block
            indent_str = ' ' * try_indent
            content.insert(try_end, f"{indent_str}except Exception as e:\n")
            content.insert(try_end+1, f"{indent_str}    self.logger.error(f\"Error: {{str(e)}}\")\n")
            
            print(f"Added except block after the try block at line {try_end+1}")
            
    else:
        print("Could not find the try block related to the syntax error")
    
    # 3. Now add the Speed Check Time settings
    print("Adding Speed Check Time settings...")
    
    content_str = ''.join(content)
    
    # Check if the setting already exists
    if "Speed Check Time Per 1 Group" in content_str:
        print("Speed Check Time settings already exist")
    else:
        # Look for the settings tab creation code
        settings_tab_match = re.search(r'def create_settings_tab\(self\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content_str)
        if not settings_tab_match:
            print("Could not find create_settings_tab method")
        else:
            settings_tab_code = settings_tab_match.group(0)
            
            # Find a good insertion point
            insertion_point = None
            if "# Filter settings" in settings_tab_code:
                insertion_point = settings_tab_code.find("# Filter settings")
            elif "# Auto-check settings" in settings_tab_code:
                insertion_point = settings_tab_code.find("# Auto-check settings")
            elif "# SpamBox settings" in settings_tab_code:
                insertion_point = settings_tab_code.find("# SpamBox settings")
            
            if insertion_point:
                # Create the new settings group
                new_settings_code = """
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(1)
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(1)
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
        
"""
                
                # Insert our new settings group
                modified_settings_tab = settings_tab_code[:insertion_point] + new_settings_code + settings_tab_code[insertion_point:]
                content_str = content_str.replace(settings_tab_code, modified_settings_tab)
                
                # Add method to update min/max range
                if "def update_speed_check_range" not in content_str:
                    update_method = """
    def update_speed_check_range(self):
        # Ensure min seconds <= max seconds
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
        
"""
                    # Find a good place to add this method - after create_settings_tab
                    if "def create_settings_tab" in content_str:
                        end_of_settings_tab = content_str.find("def ", content_str.find("def create_settings_tab") + 20)
                        if end_of_settings_tab != -1:
                            content_str = content_str[:end_of_settings_tab] + update_method + content_str[end_of_settings_tab:]
                
                # Add save settings code
                save_settings_method = re.search(r'def save_settings\(self\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content_str)
                if save_settings_method:
                    save_settings_code = save_settings_method.group(0)
                    
                    # Find where to insert our settings saving code
                    if "QMessageBox.information" in save_settings_code:
                        insert_point = save_settings_code.find("QMessageBox.information")
                        save_speed_settings = """
            # Save Speed Check Time settings
            self.settings.setValue("min_check_seconds", self.min_seconds_input.value())
            self.settings.setValue("max_check_seconds", self.max_seconds_input.value())
            
"""
                        # Insert the save code
                        modified_save = save_settings_code[:insert_point] + save_speed_settings + save_settings_code[insert_point:]
                        content_str = content_str.replace(save_settings_code, modified_save)
                
                # Modify group checking logic to add random delay
                # Find the _checker_thread method
                checker_thread_match = re.search(r'def _checker_thread\(self, [^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content_str)
                if checker_thread_match:
                    checker_code = checker_thread_match.group(0)
                    
                    # Look for where each group is checked - usually in a loop
                    if "for i, link in enumerate(group_links):" in checker_code:
                        # Add the random delay before each group check
                        delay_code = """
                # Add random delay to simulate human-like timing
                min_seconds = self.settings.value("min_check_seconds", 2, type=int)
                max_seconds = self.settings.value("max_check_seconds", 5, type=int)
                
                # Ensure min <= max
                if min_seconds > max_seconds:
                    min_seconds, max_seconds = max_seconds, min_seconds
                
                # Only add delay after the first group
                if i > 0:
                    delay = random.uniform(min_seconds, max_seconds)
                    self.log_activity_signal.emit(f"⏱️ Waiting {delay:.1f}s before checking next group...")
                    time.sleep(delay)
                """
                        
                        # Insert after the for loop but before the actual check
                        for_line_start = checker_code.find("for i, link in enumerate(group_links):")
                        for_line_end = checker_code.find("\n", for_line_start) + 1
                        
                        # Find the line after the loop start
                        next_line = checker_code[for_line_end:].lstrip()
                        next_line_indent = len(next_line) - len(next_line.lstrip())
                        
                        # Adjust indent for our delay code
                        indent = " " * (next_line_indent + 4)  # Add 4 spaces for being inside the loop
                        delay_code_indented = "\n".join([indent + line for line in delay_code.strip().split("\n")])
                        
                        # Insert the delay code after the for loop starts
                        modified_checker = checker_code[:for_line_end] + delay_code_indented + "\n" + checker_code[for_line_end:]
                        content_str = content_str.replace(checker_code, modified_checker)
                
                # Also add delay to _enhanced_account_task_thread for multi-account checking
                enhanced_task_match = re.search(r'def _enhanced_account_task_thread\(self, [^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+', content_str)
                if enhanced_task_match:
                    enhanced_task_code = enhanced_task_match.group(0)
                    
                    # Look for where each group is checked - usually in a loop
                    if "for i, group_link in enumerate(group_links):" in enhanced_task_code:
                        # Add the random delay before each group check
                        delay_code = """
                # Add random delay to simulate human-like timing
                min_seconds = self.settings.value("min_check_seconds", 2, type=int)
                max_seconds = self.settings.value("max_check_seconds", 5, type=int)
                
                # Ensure min <= max
                if min_seconds > max_seconds:
                    min_seconds, max_seconds = max_seconds, min_seconds
                
                # Only add delay after the first group
                if i > 0:
                    delay = random.uniform(min_seconds, max_seconds)
                    self.log_activity_signal.emit(f"⏱️ Account {phone}: Waiting {delay:.1f}s before checking next group...")
                    time.sleep(delay)
                """
                        
                        # Insert after the for loop but before the actual check
                        for_line_start = enhanced_task_code.find("for i, group_link in enumerate(group_links):")
                        for_line_end = enhanced_task_code.find("\n", for_line_start) + 1
                        
                        # Find the line after the loop start
                        next_line = enhanced_task_code[for_line_end:].lstrip()
                        next_line_indent = len(next_line) - len(next_line.lstrip())
                        
                        # Adjust indent for our delay code
                        indent = " " * (next_line_indent + 4)  # Add 4 spaces for being inside the loop
                        delay_code_indented = "\n".join([indent + line for line in delay_code.strip().split("\n")])
                        
                        # Insert the delay code after the for loop starts
                        modified_enhanced = enhanced_task_code[:for_line_end] + delay_code_indented + "\n" + enhanced_task_code[for_line_end:]
                        content_str = content_str.replace(enhanced_task_code, modified_enhanced)
                
                # Make sure we have the necessary imports
                if "import random" not in content_str:
                    # Find the imports section at the beginning of the file
                    import_match = re.search(r'import [^\n]+', content_str)
                    if import_match:
                        import_line = import_match.group(0)
                        content_str = content_str.replace(import_line, import_line + "\nimport random")
                
                print("Successfully added Speed Check Time settings")
                
                # Convert content_str back to list of lines
                content = content_str.splitlines(True)
    
    # Write the fixed content back to the file
    with open("main_fixed_combined.py", 'w', encoding='utf-8') as f:
        f.writelines(content)
    
    print("\nFixed indentation issues, syntax errors, and added Speed Check Time settings.")
    print("The updated file is saved as: main_fixed_combined.py")
    
    # Create batch files to run the fixed version
    with open("run_fixed_combined.bat", "w") as f:
        f.write("""@echo off
echo Running TG Checker with fixed code and Speed Check Settings...
python main_fixed_combined.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_fixed_combined.bat")
    
    # Create Kurdish version
    with open("run_fixed_combined_kurdish.bat", "w") as f:
        f.write("""@echo off
echo TG Checker - Barnama chakkrawa ba Speed Check...
python main_fixed_combined.py
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_fixed_combined_kurdish.bat")
    
    # Ask if the user wants to replace the original file
    print("\nDo you want to replace the original main.py with the fixed version?")
    print("Run 'python main_fixed_combined.py' first to test if it works properly.")
    print("If it works fine, you can replace main.py by running: copy main_fixed_combined.py main.py")
    
    return True

if __name__ == "__main__":
    fix_and_apply() 