#!/usr/bin/env python3
"""
🔍 COMPREHENSIVE CLASSIFICATION DIAGNOSTIC
Analyzes exact filter values and classification decisions for test groups
"""

import asyncio
import logging
import json
import os
from tg_client import TelegramClient

# Test groups with expected classifications
TEST_CASES = {
    "https://t.me/hyipinformer_com": "Groups_Valid_Only",      # Should fail filters
    "https://t.me/islamic_hacker_army": "Groups_Valid_Only",   # Should fail filters  
    "https://t.me/imperiamarket": "Groups_Valid_Filter",       # Should pass filters
    "https://t.me/infocoindogroup": "Groups_Valid_Filter",     # Should pass filters
}

# Default filter criteria from main.py
DEFAULT_FILTERS = {
    "min_members": 500,
    "min_message_time_hours": 1,  # <= 1 hour for recent activity
    "min_total_messages": 100
}

async def diagnostic_check():
    """Run comprehensive diagnostic on classification logic."""
    print("🔍 COMPREHENSIVE CLASSIFICATION DIAGNOSTIC")
    print("=" * 60)
    
    # Load account
    try:
        with open('account_manager.json', 'r') as f:
            accounts = json.load(f)
        account = accounts[0]
        print(f"✅ Using account: {account.get('phone', 'unknown')}")
    except Exception as e:
        print(f"❌ Failed to load account: {e}")
        return
    
    # Create client
    session_path = f"sessions/{account.get('phone', 'unknown')}.session"
    client = TelegramClient(
        session=session_path,
        api_id=account.get("api_id"),
        api_hash=account.get("api_hash"),
        system_version="4.16.30-vxCUSTOM"
    )
    
    print(f"\n📋 Filter Criteria:")
    print(f"   • Min Members: {DEFAULT_FILTERS['min_members']}")
    print(f"   • Max Activity Age: {DEFAULT_FILTERS['min_message_time_hours']} hours")
    print(f"   • Min Total Messages: {DEFAULT_FILTERS['min_total_messages']}")
    
    print(f"\n🎯 Expected Classifications:")
    for link, expected in TEST_CASES.items():
        username = link.replace("https://t.me/", "")
        print(f"   • {username} → {expected}")
    
    print(f"\n" + "=" * 60)
    print(f"🔍 DETAILED ANALYSIS")
    print(f"=" * 60)
    
    try:
        await client.connect()
        print("✅ Connected to Telegram")
        
        for link, expected_folder in TEST_CASES.items():
            username = link.replace("https://t.me/", "")
            print(f"\n📊 ANALYZING: {username}")
            print(f"   Expected: {expected_folder}")
            print("-" * 50)
            
            try:
                # Get entity info using our enhanced client
                result = await client.get_entity_info(link)
                
                if not result.get("valid", False):
                    print(f"   ❌ Invalid: {result.get('error', 'Unknown error')}")
                    print(f"   🎯 Actual: Invalid_Groups_Channels")
                    continue
                
                # Extract key metrics
                entity_type = result.get("type", "unknown")
                member_count = result.get("member_count", 0)
                last_activity = result.get("last_message_age_hours", 999)
                total_messages = result.get("total_messages", 0)
                
                print(f"   📈 Metrics:")
                print(f"      Type: {entity_type}")
                print(f"      Members: {member_count}")
                print(f"      Last Activity: {last_activity:.2f} hours ago")
                print(f"      Total Messages: {total_messages}")
                
                # Determine classification based on type first
                if entity_type == "channel":
                    actual_folder = "Channels_Only_Valid"
                elif entity_type == "topic":
                    actual_folder = "Topics_Groups_Only_Valid"
                elif entity_type == "group":
                    # Check filters for groups
                    print(f"   🔍 Filter Check:")
                    
                    member_pass = member_count >= DEFAULT_FILTERS["min_members"]
                    activity_pass = last_activity <= DEFAULT_FILTERS["min_message_time_hours"]
                    message_pass = total_messages >= DEFAULT_FILTERS["min_total_messages"]
                    
                    print(f"      Members: {member_count} >= {DEFAULT_FILTERS['min_members']} = {member_pass}")
                    print(f"      Activity: {last_activity:.2f} <= {DEFAULT_FILTERS['min_message_time_hours']} = {activity_pass}")
                    print(f"      Messages: {total_messages} >= {DEFAULT_FILTERS['min_total_messages']} = {message_pass}")
                    
                    passes_filters = member_pass and activity_pass and message_pass
                    print(f"      PASSES ALL FILTERS: {passes_filters}")
                    
                    if passes_filters:
                        actual_folder = "Groups_Valid_Filter"
                    else:
                        actual_folder = "Groups_Valid_Only"
                        failed_criteria = []
                        if not member_pass:
                            failed_criteria.append(f"members({member_count}<{DEFAULT_FILTERS['min_members']})")
                        if not activity_pass:
                            failed_criteria.append(f"activity({last_activity:.1f}h>{DEFAULT_FILTERS['min_message_time_hours']}h)")
                        if not message_pass:
                            failed_criteria.append(f"messages({total_messages}<{DEFAULT_FILTERS['min_total_messages']})")
                        print(f"      Failed: {', '.join(failed_criteria)}")
                else:
                    actual_folder = "Invalid_Groups_Channels"
                
                print(f"   🎯 Actual: {actual_folder}")
                
                # Check if classification is correct
                if actual_folder == expected_folder:
                    print(f"   ✅ CORRECT CLASSIFICATION")
                else:
                    print(f"   ❌ WRONG CLASSIFICATION")
                    print(f"      Expected: {expected_folder}")
                    print(f"      Actual: {actual_folder}")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {username}: {e}")
                print(f"   🎯 Actual: Invalid_Groups_Channels")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")
    finally:
        await client.disconnect()
        print(f"\n✅ Diagnostic complete")

if __name__ == "__main__":
    asyncio.run(diagnostic_check()) 