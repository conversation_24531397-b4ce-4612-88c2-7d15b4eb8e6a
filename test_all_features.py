import sqlite3
import os
import sys
import json
import time
from datetime import datetime, timedelta

def test_database_connections():
    """Test all database connections"""
    print("\n=== Testing Database Connections ===")
    
    # Test main database
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Main database tables: {', '.join([t[0] for t in tables])}")
        conn.close()
        print("✓ Main database connection successful")
    except Exception as e:
        print(f"✗ Main database error: {str(e)}")
    
    # Test forwarder database
    try:
        conn = sqlite3.connect('data/forwarder.db')
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"Forwarder database tables: {', '.join([t[0] for t in tables])}")
        conn.close()
        print("✓ Forwarder database connection successful")
    except Exception as e:
        print(f"✗ Forwarder database error: {str(e)}")

def test_account_management():
    """Test account management functionality"""
    print("\n=== Testing Account Management ===")
    
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # Check accounts table
        cursor.execute("SELECT COUNT(*) FROM accounts")
        account_count = cursor.fetchone()[0]
        print(f"Total accounts in database: {account_count}")
        
        # Check account details
        cursor.execute("SELECT phone, session_file, is_activated, error_count FROM accounts LIMIT 5")
        accounts = cursor.fetchall()
        for i, account in enumerate(accounts):
            print(f"Account {i+1}: Phone: {account[0]}, Session: {account[1]}, Activated: {account[2]}, Errors: {account[3]}")
        
        conn.close()
        print("✓ Account management check successful")
    except Exception as e:
        print(f"✗ Account management error: {str(e)}")

def test_task_creation():
    """Test task creation functionality"""
    print("\n=== Testing Task Creation ===")
    
    try:
        # Test forwarder tasks
        conn = sqlite3.connect('data/forwarder.db')
        cursor = conn.cursor()
        
        # Check tasks table structure
        cursor.execute("PRAGMA table_info(forwarder_tasks)")
        columns = cursor.fetchall()
        print(f"Forwarder tasks table has {len(columns)} columns")
        
        # Check existing tasks
        cursor.execute("SELECT id, name, source_group, target_groups, status FROM forwarder_tasks")
        tasks = cursor.fetchall()
        print(f"Total forwarder tasks: {len(tasks)}")
        for i, task in enumerate(tasks[:3]):  # Show first 3 tasks
            print(f"Task {i+1}: ID: {task[0]}, Name: {task[1]}, Source: {task[2]}, Targets: {task[3]}, Status: {task[4]}")
        
        conn.close()
        print("✓ Task creation check successful")
    except Exception as e:
        print(f"✗ Task creation error: {str(e)}")

def test_log_functionality():
    """Test logging functionality"""
    print("\n=== Testing Logging Functionality ===")
    
    try:
        # Check if log files exist
        log_files = [f for f in os.listdir('logs') if f.endswith('.log')]
        print(f"Found {len(log_files)} log files")
        
        # Check most recent log file content
        if log_files:
            log_files.sort(key=lambda x: os.path.getmtime(os.path.join('logs', x)), reverse=True)
            most_recent = log_files[0]
            print(f"Most recent log file: {most_recent}")
            
            with open(os.path.join('logs', most_recent), 'r', encoding='utf-8') as f:
                last_lines = f.readlines()[-10:]  # Get last 10 lines
                print("Last 10 log entries:")
                for line in last_lines:
                    print(f"  {line.strip()}")
        
        print("✓ Logging functionality check successful")
    except Exception as e:
        print(f"✗ Logging functionality error: {str(e)}")

def test_forwarding_functionality():
    """Test forwarding functionality"""
    print("\n=== Testing Forwarding Functionality ===")
    
    try:
        conn = sqlite3.connect('data/forwarder.db')
        cursor = conn.cursor()
        
        # Test if required tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND (name='forwarder_tasks' OR name='forwarder_settings')")
        tables = cursor.fetchall()
        required_tables = {'forwarder_tasks', 'forwarder_settings'}
        found_tables = {t[0] for t in tables}
        
        if required_tables.issubset(found_tables):
            print(f"✓ Required tables exist: {', '.join(required_tables)}")
        else:
            missing = required_tables - found_tables
            print(f"✗ Missing tables: {', '.join(missing)}")
        
        # Check forwarding settings
        try:
            cursor.execute("SELECT key, value FROM forwarder_settings")
            settings = {row[0]: row[1] for row in cursor.fetchall()}
            print(f"Forwarding settings: {json.dumps(settings, indent=2)}")
        except sqlite3.OperationalError:
            print("✗ Could not query forwarder_settings table")
        
        conn.close()
        print("✓ Forwarding functionality check successful")
    except Exception as e:
        print(f"✗ Forwarding functionality error: {str(e)}")

def run_comprehensive_tests():
    """Run all tests to check the application's functionality"""
    print("=== Starting Comprehensive Application Tests ===")
    print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Run all tests
    test_database_connections()
    test_account_management()
    test_task_creation()
    test_log_functionality()
    test_forwarding_functionality()
    
    print("\n=== Test Summary ===")
    print("All tests completed. Check above for any error messages.")
    print(f"Test finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_comprehensive_tests() 