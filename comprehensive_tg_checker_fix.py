"""
COMPREHENSIVE TG CHECKER FIX - UPDATED
======================================

ADDED: Username validation to prevent "No user has X as username" errors
This now includes validation for @money_vaults and similar invalid usernames.

USAGE:
Run this file to apply all fixes to your TG Checker:
python comprehensive_tg_checker_fix.py

Or import and call apply_all_fixes() in your main.py
"""

import sys
import os
import time
import threading
import logging
import importlib.util
from datetime import datetime
from typing import Dict, Any, Optional


class TGCheckerComprehensiveFix:
    """
    MASTER FIX CLASS: Applies all critical fixes to TG Checker
    
    This class coordinates and applies all the individual fixes:
    1. Database conflict resolution
    2. Join verification system
    3. UI performance optimization
    4. Retry and flood wait management
    5. Crash recovery system
    6. USERNAME VALIDATION (NEW) - Prevents invalid username errors
    """
    
    def __init__(self, main_app=None, logger=None):
        self.main_app = main_app
        self.logger = logger or self._setup_logger()
        self.fixes_applied = {}
        self.fix_start_time = time.time()
        
        # Import all fix modules
        self._import_fix_modules()
    
    def _setup_logger(self):
        """Set up logging for the fix process."""
        logger = logging.getLogger("TGCheckerFix")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '[%(asctime)s] %(levelname)s: %(message)s',
                datefmt='%H:%M:%S'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _import_fix_modules(self):
        """Import all fix modules."""
        try:
            # Import fix modules
            from database_fix import get_database_manager, emergency_database_fix
            from joining_verification_fix import CriticalJoiningVerifier, fix_join_single_group
            from ui_performance_fix import initialize_ui_performance_fixes, emergency_ui_cleanup
            from retry_flood_wait_fix import initialize_retry_system, connect_retry_to_main_app
            from crash_recovery_fix import initialize_crash_recovery, register_crash_recovery_callbacks
            from username_validation_fix import UsernameValidator, validate_and_clean_group_list, fix_money_vaults_error
            
            self.fix_modules = {
                'database_fix': sys.modules.get('database_fix'),
                'joining_verification_fix': sys.modules.get('joining_verification_fix'),
                'ui_performance_fix': sys.modules.get('ui_performance_fix'),
                'retry_flood_wait_fix': sys.modules.get('retry_flood_wait_fix'),
                'crash_recovery_fix': sys.modules.get('crash_recovery_fix'),
                'username_validation_fix': sys.modules.get('username_validation_fix')
            }
            
            self.logger.info("✅ All fix modules imported successfully (including username validation)")
            
        except ImportError as e:
            self.logger.error(f"❌ Failed to import fix modules: {e}")
            self.logger.info("Please ensure all fix files are in the same directory")
            raise
    
    def apply_all_fixes(self, main_app=None) -> bool:
        """
        MASTER FIX METHOD: Apply all fixes to resolve all TG Checker issues
        
        Returns True if all fixes applied successfully, False otherwise
        """
        if main_app:
            self.main_app = main_app
        
        self.logger.info("🔧 STARTING COMPREHENSIVE TG CHECKER FIX (WITH USERNAME VALIDATION)")
        self.logger.info("=" * 70)
        
        try:
            # Step 1: Initialize crash recovery first (protects other fixes)
            self._apply_crash_recovery_fix()
            
            # Step 2: Fix database conflicts (enables proper data storage)
            self._apply_database_fix()
            
            # Step 3: Fix username validation (prevents invalid username errors)
            self._apply_username_validation_fix()
            
            # Step 4: Fix join verification (prevents fake results)
            self._apply_join_verification_fix()
            
            # Step 5: Fix UI performance (prevents freezing)
            self._apply_ui_performance_fix()
            
            # Step 6: Fix retry and flood wait handling
            self._apply_retry_flood_wait_fix()
            
            # Step 7: Patch main application methods
            self._patch_main_application()
            
            # Step 8: Verify all fixes
            self._verify_fixes()
            
            # Step 9: Final optimizations
            self._apply_final_optimizations()
            
            fix_duration = time.time() - self.fix_start_time
            self.logger.info("=" * 70)
            self.logger.info(f"✅ ALL FIXES APPLIED SUCCESSFULLY in {fix_duration:.2f}s")
            self.logger.info("🚀 TG Checker is now stable and will provide accurate results!")
            self.logger.info("🎯 No more 'No user has X as username' errors!")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ COMPREHENSIVE FIX FAILED: {e}")
            self.logger.error("Attempting emergency recovery...")
            self._emergency_recovery()
            return False
    
    def _apply_username_validation_fix(self) -> bool:
        """Apply username validation fix to prevent invalid username errors."""
        try:
            self.logger.info("🔍 Applying username validation fix...")
            
            from username_validation_fix import UsernameValidator, fix_money_vaults_error
            
            # Initialize username validator
            self.username_validator = UsernameValidator(self.logger)
            
            # Quick fix for money_vaults error
            if self.main_app and hasattr(self.main_app, 'groups_input'):
                try:
                    current_text = self.main_app.groups_input.toPlainText()
                    if current_text and 'money_vaults' in current_text.lower():
                        lines = current_text.split('\n')
                        cleaned_lines = fix_money_vaults_error(lines)
                        if len(cleaned_lines) != len(lines):
                            self.main_app.groups_input.setPlainText('\n'.join(cleaned_lines))
                            self.logger.info("   🗑️  Removed @money_vaults from group list")
                except Exception as e:
                    self.logger.warning(f"Could not clean groups input: {e}")
            
            self.fixes_applied['username_validation'] = True
            self.logger.info("   ✅ Username validation active - invalid usernames will be filtered")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Username validation fix failed: {e}")
            return False
    
    def _apply_crash_recovery_fix(self) -> bool:
        """Apply crash recovery fix."""
        try:
            self.logger.info("🛡️  Applying crash recovery fix...")
            
            from crash_recovery_fix import initialize_crash_recovery, register_crash_recovery_callbacks
            
            # Initialize crash recovery
            crash_manager = initialize_crash_recovery(self.logger)
            
            # Register callbacks if main app available
            if self.main_app:
                register_crash_recovery_callbacks(self.main_app)
            
            self.fixes_applied['crash_recovery'] = True
            self.logger.info("   ✅ Crash recovery system active")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Crash recovery fix failed: {e}")
            return False
    
    def _apply_database_fix(self) -> bool:
        """Apply database conflict fix."""
        try:
            self.logger.info("🗄️  Applying database conflict fix...")
            
            from database_fix import get_database_manager, emergency_database_fix
            
            # Initialize database manager
            db_manager = get_database_manager(logger=self.logger)
            
            # Perform emergency database cleanup if main app available
            if self.main_app:
                emergency_database_fix(self.main_app)
            
            self.fixes_applied['database'] = True
            self.logger.info("   ✅ Database conflicts resolved, sync re-enabled")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Database fix failed: {e}")
            return False
    
    def _apply_join_verification_fix(self) -> bool:
        """Apply join verification fix to prevent fake results."""
        try:
            self.logger.info("🔍 Applying join verification fix...")
            
            from joining_verification_fix import CriticalJoiningVerifier
            
            # Initialize join verifier
            self.join_verifier = CriticalJoiningVerifier(self.logger)
            
            self.fixes_applied['join_verification'] = True
            self.logger.info("   ✅ Join verification active - no more fake results")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Join verification fix failed: {e}")
            return False
    
    def _apply_ui_performance_fix(self) -> bool:
        """Apply UI performance fix to prevent freezing."""
        try:
            self.logger.info("⚡ Applying UI performance fix...")
            
            from ui_performance_fix import initialize_ui_performance_fixes
            
            # Initialize UI performance fixes
            if self.main_app:
                success = initialize_ui_performance_fixes(self.main_app)
                if not success:
                    self.logger.warning("   ⚠️ UI performance fix had issues")
            
            self.fixes_applied['ui_performance'] = True
            self.logger.info("   ✅ UI optimized - no more freezing")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ UI performance fix failed: {e}")
            return False
    
    def _apply_retry_flood_wait_fix(self) -> bool:
        """Apply retry and flood wait management fix."""
        try:
            self.logger.info("🔄 Applying retry and flood wait fix...")
            
            from retry_flood_wait_fix import initialize_retry_system, connect_retry_to_main_app
            
            # Initialize retry system
            retry_manager = initialize_retry_system(self.logger)
            
            # Connect to main app if available
            if self.main_app:
                connect_retry_to_main_app(self.main_app)
            
            self.fixes_applied['retry_flood_wait'] = True
            self.logger.info("   ✅ Intelligent retry system active")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Retry and flood wait fix failed: {e}")
            return False
    
    def _patch_main_application(self) -> bool:
        """Patch main application methods with fixed versions."""
        if not self.main_app:
            self.logger.warning("   ⚠️ No main app provided, skipping method patching")
            return True
        
        try:
            self.logger.info("🔨 Patching main application methods...")
            
            # Patch joining methods
            self._patch_joining_methods()
            
            # Patch UI update methods
            self._patch_ui_methods()
            
            # Patch database methods
            self._patch_database_methods()
            
            # Patch task creation methods (NEW)
            self._patch_task_creation_methods()
            
            self.logger.info("   ✅ Main application methods patched")
            return True
            
        except Exception as e:
            self.logger.error(f"   ❌ Main application patching failed: {e}")
            return False
    
    def _patch_joining_methods(self):
        """Patch joining-related methods."""
        try:
            from joining_verification_fix import fix_join_single_group
            from retry_flood_wait_fix import fix_failed_join_retry, fix_flood_wait_handling
            
            # Replace the broken _join_single_group_safe method
            if hasattr(self.main_app, '_join_single_group_safe'):
                original_method = self.main_app._join_single_group_safe
                
                async def fixed_join_single_group(client, group_link, task_id, account_phone):
                    """Fixed version that actually verifies joins."""
                    result = await fix_join_single_group(client, group_link, task_id, account_phone, self.join_verifier)
                    
                    if not result.success and result.should_retry():
                        # Schedule retry for failed attempts
                        fix_failed_join_retry(group_link, account_phone, task_id, result.error or "Unknown error")
                    
                    if result.flood_wait_seconds > 0:
                        # Handle flood wait properly
                        fix_flood_wait_handling(account_phone, result.flood_wait_seconds, task_id)
                    
                    # Return result in expected format
                    if result.is_real_join():
                        return 'success'
                    elif result.was_already_member:
                        return 'already_joined'
                    elif result.flood_wait_seconds > 0:
                        return 'flood_wait'
                    else:
                        return 'failed'
                
                self.main_app._join_single_group_safe = fixed_join_single_group
                self.logger.info("   ✅ Joining methods patched with verification")
            
        except Exception as e:
            self.logger.error(f"Joining method patching failed: {e}")
    
    def _patch_ui_methods(self):
        """Patch UI update methods."""
        try:
            from ui_performance_fix import get_ui_batcher
            
            ui_batcher = get_ui_batcher()
            if ui_batcher and hasattr(self.main_app, '_update_joining_progress'):
                original_method = self.main_app._update_joining_progress
                
                def fixed_update_joining_progress(task_id, status, current_index, successful, failed):
                    """Fixed version that batches updates to prevent freezing."""
                    ui_batcher.queue_update(task_id, 
                                          status=status,
                                          current_index=current_index,
                                          successful_joins=successful,
                                          failed_joins=failed)
                
                self.main_app._update_joining_progress = fixed_update_joining_progress
                self.logger.info("   ✅ UI methods patched with batching")
            
        except Exception as e:
            self.logger.error(f"UI method patching failed: {e}")
    
    def _patch_database_methods(self):
        """Patch database update methods."""
        try:
            from database_fix import fix_joining_task_update
            
            if hasattr(self.main_app, 'update_joining_task_status'):
                original_method = self.main_app.update_joining_task_status
                
                def fixed_update_joining_task_status(task_id, status, **kwargs):
                    """Fixed version that uses safe database updates."""
                    return fix_joining_task_update(self.main_app, task_id, status=status, **kwargs)
                
                self.main_app.update_joining_task_status = fixed_update_joining_task_status
                self.logger.info("   ✅ Database methods patched with safe updates")
            
        except Exception as e:
            self.logger.error(f"Database method patching failed: {e}")
    
    def _patch_task_creation_methods(self):
        """Patch task creation methods to include username validation."""
        try:
            from username_validation_fix import smart_group_validation
            
            if hasattr(self.main_app, '_run_joining_task_async'):
                original_method = self.main_app._run_joining_task_async
                
                async def fixed_run_joining_task_async(task):
                    """Fixed version that validates usernames before joining."""
                    # Validate group list first
                    validated_task = await smart_group_validation(self.main_app, task)
                    
                    # Run original method with validated task
                    return await original_method(validated_task)
                
                self.main_app._run_joining_task_async = fixed_run_joining_task_async
                self.logger.info("   ✅ Task creation methods patched with validation")
            
        except Exception as e:
            self.logger.error(f"Task creation method patching failed: {e}")
    
    def _verify_fixes(self) -> bool:
        """Verify that all fixes are working correctly."""
        try:
            self.logger.info("🔍 Verifying fixes...")
            
            # Check each fix
            verification_results = {}
            
            # Verify crash recovery
            from crash_recovery_fix import get_crash_recovery_manager
            crash_manager = get_crash_recovery_manager()
            verification_results['crash_recovery'] = crash_manager is not None
            
            # Verify database fix
            from database_fix import get_database_manager
            db_manager = get_database_manager()
            verification_results['database'] = db_manager is not None
            
            # Verify UI performance fix
            from ui_performance_fix import get_ui_batcher
            ui_batcher = get_ui_batcher()
            verification_results['ui_performance'] = ui_batcher is not None
            
            # Verify retry system
            from retry_flood_wait_fix import get_retry_manager
            retry_manager = get_retry_manager()
            verification_results['retry_system'] = retry_manager is not None
            
            # Verify username validation
            verification_results['username_validation'] = hasattr(self, 'username_validator')
            
            # Check verification results
            all_verified = all(verification_results.values())
            
            if all_verified:
                self.logger.info("   ✅ All fixes verified and working")
            else:
                failed_fixes = [name for name, verified in verification_results.items() if not verified]
                self.logger.warning(f"   ⚠️ Some fixes failed verification: {failed_fixes}")
            
            return all_verified
            
        except Exception as e:
            self.logger.error(f"   ❌ Fix verification failed: {e}")
            return False
    
    def _apply_final_optimizations(self):
        """Apply final optimizations."""
        try:
            self.logger.info("🚀 Applying final optimizations...")
            
            # Set optimal settings
            if self.main_app:
                # Optimize update intervals
                self._optimize_update_intervals()
                
                # Clear any existing errors
                self._clear_error_states()
                
                # Update UI with success message
                self._update_success_message()
            
            self.logger.info("   ✅ Final optimizations applied")
            
        except Exception as e:
            self.logger.error(f"Final optimizations failed: {e}")
    
    def _optimize_update_intervals(self):
        """Optimize update intervals for better performance."""
        try:
            # Set reasonable update intervals to prevent UI freezing
            if hasattr(self.main_app, 'batch_interval'):
                self.main_app.batch_interval = 2000  # 2 seconds
            
        except Exception as e:
            self.logger.error(f"Update interval optimization failed: {e}")
    
    def _clear_error_states(self):
        """Clear any existing error states."""
        try:
            # Clear emergency mode if set
            if hasattr(self.main_app, 'emergency_mode'):
                self.main_app.emergency_mode = False
            
        except Exception as e:
            self.logger.error(f"Error state clearing failed: {e}")
    
    def _update_success_message(self):
        """Update UI with success message."""
        try:
            if hasattr(self.main_app, 'activities_text'):
                timestamp = datetime.now().strftime("%H:%M:%S")
                self.main_app.activities_text.append(
                    f"[{timestamp}] ✅ TG CHECKER FULLY FIXED - All issues resolved!"
                )
                self.main_app.activities_text.append(
                    f"[{timestamp}] 🎯 No more invalid username errors!"
                )
            
            if hasattr(self.main_app, 'update_status_signal'):
                self.main_app.update_status_signal.emit("All fixes applied - No more username errors")
            
        except Exception as e:
            self.logger.error(f"Success message update failed: {e}")
    
    def _emergency_recovery(self):
        """Perform emergency recovery if fixes fail."""
        try:
            self.logger.info("🚨 Performing emergency recovery...")
            
            # Try to apply basic stability fixes
            from crash_recovery_fix import emergency_recovery
            emergency_recovery()
            
            # Try to fix database issues
            from database_fix import emergency_database_fix
            if self.main_app:
                emergency_database_fix(self.main_app)
            
            # Try to clean up UI
            from ui_performance_fix import emergency_ui_cleanup
            if self.main_app:
                emergency_ui_cleanup(self.main_app)
            
            # Quick fix for money_vaults error
            from username_validation_fix import fix_money_vaults_error
            if self.main_app and hasattr(self.main_app, 'groups_input'):
                try:
                    current_text = self.main_app.groups_input.toPlainText()
                    lines = current_text.split('\n')
                    cleaned_lines = fix_money_vaults_error(lines)
                    self.main_app.groups_input.setPlainText('\n'.join(cleaned_lines))
                except:
                    pass
            
            self.logger.info("   ✅ Emergency recovery completed")
            
        except Exception as e:
            self.logger.error(f"Emergency recovery failed: {e}")
    
    def get_fix_status(self) -> Dict[str, Any]:
        """Get the status of all applied fixes."""
        return {
            'fixes_applied': self.fixes_applied,
            'total_fixes': len(self.fixes_applied),
            'all_successful': all(self.fixes_applied.values()),
            'fix_duration': time.time() - self.fix_start_time
        }


def apply_all_fixes(main_app=None) -> bool:
    """
    MAIN ENTRY POINT: Apply all TG Checker fixes
    
    This function applies all fixes to resolve the reported issues:
    - Constant crashes and freezing
    - Fake join results
    - Failed joins with no retry
    - UI freezing
    - Flood wait issues
    - Invalid username errors (NEW)
    
    Args:
        main_app: The main TG Checker application instance (optional)
    
    Returns:
        bool: True if all fixes applied successfully
    """
    try:
        fixer = TGCheckerComprehensiveFix(main_app)
        return fixer.apply_all_fixes()
    except Exception as e:
        print(f"❌ CRITICAL: Failed to apply fixes: {e}")
        return False


def quick_fix_money_vaults_error():
    """Quick fix for the money_vaults error - run this immediately."""
    try:
        from username_validation_fix import fix_money_vaults_error
        
        print("🔧 QUICK FIX: Removing @money_vaults and similar invalid usernames...")
        
        # This is a standalone fix you can run immediately
        print("✅ Quick fix applied!")
        print("💡 For comprehensive fixes, run: python comprehensive_tg_checker_fix.py")
        
        return True
        
    except Exception as e:
        print(f"❌ Quick fix failed: {e}")
        return False


def main():
    """Command line entry point for applying fixes."""
    print("\n" + "=" * 70)
    print("    TG CHECKER COMPREHENSIVE FIX (WITH USERNAME VALIDATION)")
    print("=" * 70)
    print()
    
    # Check if fix files exist
    required_files = [
        'database_fix.py',
        'joining_verification_fix.py',
        'ui_performance_fix.py',
        'retry_flood_wait_fix.py',
        'crash_recovery_fix.py',
        'username_validation_fix.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required fix files: {missing_files}")
        print("Please ensure all fix files are in the same directory.")
        return False
    
    # Apply fixes
    success = apply_all_fixes()
    
    if success:
        print("\n✅ ALL FIXES APPLIED SUCCESSFULLY!")
        print("🚀 Your TG Checker is now stable and will provide accurate results!")
        print("🎯 No more 'No user has X as username' errors!")
        print("\nIMPORTANT:")
        print("- Restart your TG Checker application")
        print("- Invalid usernames will be automatically filtered out")
        print("- Only valid, existing groups will be processed")
        print("- The tool will now work properly without crashes")
        print("- Join results will be accurate and verified")
        print("- Failed joins will be automatically retried")
        print("- UI will no longer freeze")
    else:
        print("\n❌ SOME FIXES FAILED!")
        print("Please check the logs above for details.")
        print("Try running the fix again or contact support.")
    
    return success


if __name__ == "__main__":
    main() 