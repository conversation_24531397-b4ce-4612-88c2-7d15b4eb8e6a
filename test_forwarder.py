#!/usr/bin/env python3
"""
Test script for the Telegram Forwarder functionality
"""

import sys
import sqlite3
import json
from datetime import datetime

def test_forwarder_database():
    """Test forwarder database initialization."""
    try:
        print("🔍 Testing forwarder database...")
        
        # Connect to database
        conn = sqlite3.connect("forwarder.db", timeout=30)
        cursor = conn.cursor()
        
        # Check if tables exist
        tables = [
            'forwarder_tasks',
            'account_forwarder_settings', 
            'global_forwarder_settings',
            'forwarding_logs'
        ]
        
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ Table '{table}' exists")
            else:
                print(f"❌ Table '{table}' missing")
                return False
        
        # Test inserting a sample task
        task_data = {
            'id': 'test_task_001',
            'name': 'Test Forwarding Task',
            'account_phone': '+**********',
            'message_link': 'https://t.me/testchannel/123',
            'message_link_2': '',
            'target_groups': json.dumps(['@testgroup1', '@testgroup2']),
            'target_groups_2': json.dumps([]),
            'status': 'stopped',
            'current_index': 0,
            'current_index_2': 0,
            'total_messages': 0,
            'successful_forwards': 0,
            'failed_forwards': 0,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'settings': json.dumps({
                'interval_min': 20,
                'interval_max': 25,
                'after_each_second': 360,
                'random_sleep_time_min': 30,
                'random_sleep_time_max': 60,
                'reply_message': 'Test message'
            })
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO forwarder_tasks 
            (id, name, account_phone, message_link, message_link_2, 
             target_groups, target_groups_2, status, current_index, current_index_2,
             total_messages, successful_forwards, failed_forwards, 
             created_at, updated_at, settings)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', tuple(task_data.values()))
        
        conn.commit()
        print("✅ Successfully inserted test task")
        
        # Verify task was inserted
        cursor.execute("SELECT COUNT(*) FROM forwarder_tasks WHERE id = ?", (task_data['id'],))
        count = cursor.fetchone()[0]
        
        if count > 0:
            print("✅ Test task verified in database")
        else:
            print("❌ Test task not found in database")
            return False
        
        # Clean up test data
        cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_data['id'],))
        conn.commit()
        print("✅ Test data cleaned up")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {str(e)}")
        return False

def test_forwarder_settings():
    """Test forwarder settings functionality."""
    try:
        print("\n🔍 Testing forwarder settings...")
        
        conn = sqlite3.connect("forwarder.db", timeout=30)
        cursor = conn.cursor()
        
        # Test account settings
        test_phone = "+**********"
        test_settings = {
            'phone': test_phone,
            'interval_min': 20,
            'interval_max': 25,
            'after_each_second': 360,
            'random_sleep_time_min': 30,
            'random_sleep_time_max': 60,
            'reply_message': 'Test reply message',
            'is_active': 1,
            'settings': json.dumps({'test': 'value'})
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO account_forwarder_settings
            (phone, interval_min, interval_max, after_each_second,
             random_sleep_time_min, random_sleep_time_max, reply_message,
             is_active, settings)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_settings['phone'],
            test_settings['interval_min'],
            test_settings['interval_max'],
            test_settings['after_each_second'],
            test_settings['random_sleep_time_min'],
            test_settings['random_sleep_time_max'],
            test_settings['reply_message'],
            test_settings['is_active'],
            test_settings['settings']
        ))
        
        conn.commit()
        print("✅ Account settings saved successfully")
        
        # Test global settings
        global_settings = {
            'use_random_sleep_time': True,
            'log_failed_groups': True,
            'flood_wait_enabled': True,
            'reply_message_enabled': True,
            'global_reply_message': 'Global test message'
        }
        
        for key, value in global_settings.items():
            cursor.execute('''
                INSERT OR REPLACE INTO global_forwarder_settings (key, value)
                VALUES (?, ?)
            ''', (key, json.dumps(value)))
        
        conn.commit()
        print("✅ Global settings saved successfully")
        
        # Verify settings
        cursor.execute("SELECT * FROM account_forwarder_settings WHERE phone = ?", (test_phone,))
        account_row = cursor.fetchone()
        
        if account_row:
            print("✅ Account settings retrieved successfully")
        else:
            print("❌ Account settings not found")
            return False
        
        cursor.execute("SELECT COUNT(*) FROM global_forwarder_settings")
        global_count = cursor.fetchone()[0]
        
        if global_count >= len(global_settings):
            print("✅ Global settings verified")
        else:
            print("❌ Global settings incomplete")
            return False
        
        # Clean up
        cursor.execute("DELETE FROM account_forwarder_settings WHERE phone = ?", (test_phone,))
        cursor.execute("DELETE FROM global_forwarder_settings")
        conn.commit()
        print("✅ Settings test data cleaned up")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Settings test failed: {str(e)}")
        return False

def test_forwarder_logs():
    """Test forwarder logging functionality."""
    try:
        print("\n🔍 Testing forwarder logs...")
        
        conn = sqlite3.connect("forwarder.db", timeout=30)
        cursor = conn.cursor()
        
        # Insert test log entries
        test_logs = [
            {
                'task_id': 'test_task_001',
                'account_phone': '+**********',
                'target_group': '@testgroup1',
                'message_link': 'https://t.me/testchannel/123',
                'status': 'success',
                'error_message': '',
                'timestamp': datetime.now().isoformat()
            },
            {
                'task_id': 'test_task_001',
                'account_phone': '+**********', 
                'target_group': '@testgroup2',
                'message_link': 'https://t.me/testchannel/123',
                'status': 'failed',
                'error_message': 'Test error message',
                'timestamp': datetime.now().isoformat()
            }
        ]
        
        for log_entry in test_logs:
            cursor.execute('''
                INSERT INTO forwarding_logs 
                (task_id, account_phone, target_group, message_link, status, error_message, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', tuple(log_entry.values()))
        
        conn.commit()
        print("✅ Test log entries inserted")
        
        # Verify logs
        cursor.execute("SELECT COUNT(*) FROM forwarding_logs WHERE task_id = ?", ('test_task_001',))
        log_count = cursor.fetchone()[0]
        
        if log_count == len(test_logs):
            print("✅ Log entries verified")
        else:
            print(f"❌ Expected {len(test_logs)} log entries, found {log_count}")
            return False
        
        # Test log retrieval
        cursor.execute('''
            SELECT * FROM forwarding_logs 
            WHERE task_id = ? 
            ORDER BY timestamp DESC
        ''', ('test_task_001',))
        
        retrieved_logs = cursor.fetchall()
        
        if len(retrieved_logs) == len(test_logs):
            print("✅ Log retrieval successful")
        else:
            print("❌ Log retrieval failed")
            return False
        
        # Clean up
        cursor.execute("DELETE FROM forwarding_logs WHERE task_id = ?", ('test_task_001',))
        conn.commit()
        print("✅ Log test data cleaned up")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Logs test failed: {str(e)}")
        return False

def main():
    """Run all forwarder tests."""
    print("🚀 Starting Telegram Forwarder Tests")
    print("=" * 50)
    
    tests = [
        ("Database Initialization", test_forwarder_database),
        ("Settings Management", test_forwarder_settings),
        ("Logging System", test_forwarder_logs)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests PASSED! Forwarder system is ready.")
        return True
    else:
        print("⚠️  Some tests FAILED. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 