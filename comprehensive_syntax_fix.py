#!/usr/bin/env python3
"""
Comprehensive fix for all 914 syntax errors in main.py.
Fixes all broken f-string patterns that were introduced by the comprehensive checker fix.
"""

import re
import os

def fix_all_914_errors():
    """Fix all 914 syntax errors in main.py."""
    
    print("🔧 Fixing all 914 syntax errors in main.py...")
    
    # Create backup first
    if os.path.exists("main.py"):
        with open("main.py", "r", encoding="utf-8") as f:
            backup_content = f.read()
        with open("main.py.backup_before_914_fix", "w", encoding="utf-8") as f:
            f.write(backup_content)
        print("✅ Created backup: main.py.backup_before_914_fix")
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix all broken f-string patterns systematically
    print("🔧 Fixing broken f-string patterns...")
    
    # Pattern 1: f.write(f"{link} (missing \n") - most common
    content = re.sub(
        r'f\.write\(f"\{link\}\s*$',
        r'f.write(f"{link}\n")',
        content,
        flags=re.MULTILINE
    )
    
    # Pattern 2: f.write(f"{link}\n (missing closing ")
    content = re.sub(
        r'f\.write\(f"\{link\}\\n\s*$',
        r'f.write(f"{link}\n")',
        content,
        flags=re.MULTILINE
    )
    
    # Pattern 3: f.write(f"{link}\n (missing closing ")
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\\n\s*$',
        r'f.write(f"{link}\n")',
        content,
        flags=re.MULTILINE
    )
    
    # Pattern 4: Fix line-by-line to ensure we catch all variations
    lines = content.split('\n')
    fixed_lines = []
    fixes_applied = 0
    
    for i, line in enumerate(lines):
        original_line = line
        
        # Check for all variations of broken f.write patterns
        if 'f.write(f"{link}' in line and not (line.strip().endswith('")') or line.strip().endswith('"))')):
            
            # Case 1: f.write(f"{link} (completely missing ending)
            if re.search(r'f\.write\(f"\{link\}\s*$', line):
                line = re.sub(r'f\.write\(f"\{link\}.*', r'f.write(f"{link}\n")', line)
                fixes_applied += 1
            
            # Case 2: f.write(f"{link}\n (missing closing quote)
            elif re.search(r'f\.write\(f"\{link\}\\n\s*$', line):
                line = re.sub(r'f\.write\(f"\{link\}\\n.*', r'f.write(f"{link}\n")', line)
                fixes_applied += 1
            
            # Case 3: f.write(f"{link}\n (missing closing quote, literal \n)
            elif re.search(r'f\.write\(f"\{link\}\s*\\n\s*$', line):
                line = re.sub(r'f\.write\(f"\{link\}.*', r'f.write(f"{link}\n")', line)
                fixes_applied += 1
            
            # Case 4: Any other malformed f.write(f"{link} pattern
            elif 'f.write(f"{link}' in line:
                line = re.sub(r'f\.write\(f"\{link\}.*', r'f.write(f"{link}\n")', line)
                fixes_applied += 1
        
        # Also check for other broken f-string patterns
        elif 'f.write(f"' in line and '\\n' not in line and not line.strip().endswith('")'):
            # This might be another broken f-string pattern
            if 'f.write(f"' in line and '{' in line:
                # Try to fix generic broken f-string patterns
                line = re.sub(r'f\.write\(f"[^"]*$', r'f.write(f"{link}\n")', line)
                fixes_applied += 1
        
        if line != original_line:
            print(f"  Line {i+1}: Fixed broken f-string")
        
        fixed_lines.append(line)
    
    # Join the lines back
    content = '\n'.join(fixed_lines)
    
    # Final comprehensive cleanup for any remaining patterns
    print("🔧 Applying final cleanup...")
    
    # Fix any remaining broken patterns with multiple approaches
    patterns_to_fix = [
        (r'f\.write\(f"\{link\}[^"]*\n\s*$', r'f.write(f"{link}\n")'),
        (r'f\.write\(f"\{link\}[^"]*$', r'f.write(f"{link}\n")'),
        (r'f\.write\(f"\{[^}]+\}[^"]*$', r'f.write(f"{link}\n")'),
        (r'f\.write\(f"[^"]*\{link\}[^"]*$', r'f.write(f"{link}\n")'),
    ]
    
    for pattern, replacement in patterns_to_fix:
        old_content = content
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        if content != old_content:
            print(f"  Applied pattern fix: {pattern}")
    
    # Fix any remaining quotes issues
    content = re.sub(r'f\.write\(f"\{link\}\\n"\s*$', r'f.write(f"{link}\n")', content, flags=re.MULTILINE)
    
    # Write the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"✅ Applied {fixes_applied} fixes to broken f-string patterns")
    print("✅ All 914 syntax errors should now be fixed!")
    
    return True

def verify_fix():
    """Verify that the fix worked by compiling the file."""
    
    print("🔍 Verifying fix by compiling main.py...")
    
    try:
        import py_compile
        py_compile.compile("main.py", doraise=True)
        print("✅ main.py compiles successfully - all syntax errors fixed!")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Compilation failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    try:
        success = fix_all_914_errors()
        if success:
            if verify_fix():
                print("\n🎉 SUCCESS: All 914 syntax errors have been fixed!")
                print("✅ main.py now compiles without errors")
                print("🔄 You can now run the TG Checker application")
            else:
                print("\n⚠️ Some errors may remain - please check the compilation output")
        else:
            print("\n❌ Fix failed!")
    except Exception as e:
        print(f"\n❌ Error during fix: {e}")
        import traceback
        traceback.print_exc() 