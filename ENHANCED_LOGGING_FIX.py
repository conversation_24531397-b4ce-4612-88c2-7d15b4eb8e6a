#!/usr/bin/env python3
"""
🚨 ENHANCED LOGGING FIX
Adds comprehensive logging and verification to ensure no groups are silently skipped.

ENHANCEMENTS:
1. Log all group statuses (skipped, failed, joined, valid, invalid)
2. Comprehensive analysis logging for each group
3. Verification that all groups are processed and saved
4. Enhanced error classification
"""

def apply_logging_fix():
    """Apply enhanced logging fixes to main.py"""
    
    # Create backup first
    import shutil
    import time
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    backup_name = f"main.py.backup_before_logging_fix_{timestamp}"
    shutil.copy2("main.py", backup_name)
    print(f"✅ Created backup: {backup_name}")
    
    # Read the current file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    print("🔧 Applying enhanced logging fixes...")
    
    # ========================================
    # FIX 1: Enhanced invalid group processing
    # ========================================
    
    # Find and replace the invalid group processing section
    old_invalid_processing = '''                    # Handle based on error type if invalid
                    if not result["valid"]:
                        error_type = result.get("error_type", "invalid_group")

                        if error_type == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] INVALID: {link} → InvalidGroups_Channels.txt")
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            wait_seconds = result.get("wait_seconds", 0)
                            if wait_seconds > 0:
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: FloodWait → Paused {wait_seconds//60} mins → Saved to AccountIssue.txt")
                            else:
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: {result['reason']} → Saved to AccountIssue.txt")
                        elif error_type == "join_request":
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] JOIN REQUEST: {link} → Saved to JoinRequest.txt")'''
    
    new_invalid_processing = '''                    # Enhanced processing with comprehensive logging
                    if not result["valid"]:
                        error_type = result.get("error_type", "invalid_group")
                        reason = result.get("reason", "Unknown error")
                        
                        # COMPREHENSIVE LOGGING - Log every decision
                        self.log_activity_signal.emit(f"📋 GROUP #{actual_group_number} ANALYSIS: {link}")
                        self.log_activity_signal.emit(f"   ├── Status: INVALID ❌")
                        self.log_activity_signal.emit(f"   ├── Error Type: {error_type}")
                        self.log_activity_signal.emit(f"   ├── Reason: {reason}")
                        
                        if error_type == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: InvalidGroups_Channels.txt")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ❌ INVALID: {link} → InvalidGroups_Channels.txt")
                            
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            wait_seconds = result.get("wait_seconds", 0)
                            if wait_seconds > 0:
                                self.log_activity_signal.emit(f"   └── Saved to: AccountIssue.txt (FloodWait: {wait_seconds//60}m)")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ⏰ FLOOD WAIT: {link} → AccountIssue.txt")
                            else:
                                self.log_activity_signal.emit(f"   └── Saved to: AccountIssue.txt")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 🚫 ACCOUNT ISSUE: {link} → AccountIssue.txt")
                                
                        elif error_type == "join_request":
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: JoinRequest.txt")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 📝 JOIN REQUEST: {link} → JoinRequest.txt")
                        
                        else:
                            # Unknown error type - classify as invalid group
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: InvalidGroups_Channels.txt (Unknown error)")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ❓ UNKNOWN ERROR: {link} → InvalidGroups_Channels.txt")'''

    if old_invalid_processing in content:
        content = content.replace(old_invalid_processing, new_invalid_processing)
        print("✅ FIXED: Enhanced invalid group processing with comprehensive logging")
    else:
        print("⚠️ WARNING: Could not find invalid group processing section")

    # ========================================
    # FIX 2: Enhanced valid group processing
    # ========================================
    
    # Find and enhance valid group processing
    old_valid_start = '''                    # Link is valid, categorize based on type and filters
                    if result["type"] == "channel":'''
    
    new_valid_start = '''                    # COMPREHENSIVE LOGGING - Valid group analysis
                    self.log_activity_signal.emit(f"📋 GROUP #{actual_group_number} ANALYSIS: {link}")
                    self.log_activity_signal.emit(f"   ├── Status: VALID ✅")
                    self.log_activity_signal.emit(f"   ├── Type: {result['type']}")
                    self.log_activity_signal.emit(f"   ├── Members: {result.get('member_count', 0)}")
                    self.log_activity_signal.emit(f"   ├── Messages: {result.get('total_messages', 0)}")
                    self.log_activity_signal.emit(f"   ├── Last Activity: {result.get('last_message_age_hours', 999):.1f}h ago")
                    
                    # Link is valid, categorize based on type and filters
                    if result["type"] == "channel":'''
    
    if old_valid_start in content:
        content = content.replace(old_valid_start, new_valid_start)
        print("✅ FIXED: Enhanced valid group processing with detailed logging")
    else:
        print("⚠️ WARNING: Could not find valid group processing section")

    # ========================================
    # FIX 3: Add verification at the end of processing
    # ========================================
    
    # Find the final results section and enhance it
    old_final = '''            # Final results and saving
            self.log_activity_signal.emit(f"✅ COMPLETED: Checked {len(group_links)} groups")'''
    
    new_final = '''            # VERIFICATION: Ensure all groups were processed and saved
            total_processed = (len(valid_filtered) + len(valid_only) + len(topics_groups) + 
                             len(channels_only) + len(invalid_groups) + len(account_issues) + len(join_requests))
            
            self.log_activity_signal.emit(f"🔍 VERIFICATION: Processing complete")
            self.log_activity_signal.emit(f"   ├── Groups to check: {len(group_links)}")
            self.log_activity_signal.emit(f"   ├── Groups processed: {total_processed}")
            self.log_activity_signal.emit(f"   ├── Valid (filtered): {len(valid_filtered)}")
            self.log_activity_signal.emit(f"   ├── Valid (only): {len(valid_only)}")
            self.log_activity_signal.emit(f"   ├── Topics: {len(topics_groups)}")
            self.log_activity_signal.emit(f"   ├── Channels: {len(channels_only)}")
            self.log_activity_signal.emit(f"   ├── Invalid: {len(invalid_groups)}")
            self.log_activity_signal.emit(f"   ├── Account Issues: {len(account_issues)}")
            self.log_activity_signal.emit(f"   └── Join Requests: {len(join_requests)}")
            
            if total_processed != len(group_links):
                missing = len(group_links) - total_processed
                self.log_activity_signal.emit(f"🚨 WARNING: {missing} groups not accounted for!")
                self.log_activity_signal.emit(f"📋 MISSING GROUPS ANALYSIS:")
                processed_links = set(valid_filtered + valid_only + topics_groups + channels_only + invalid_groups + account_issues + join_requests)
                for i, link in enumerate(group_links):
                    if link not in processed_links:
                        self.log_activity_signal.emit(f"   Missing #{i+1}: {link}")
            else:
                self.log_activity_signal.emit(f"✅ VERIFICATION PASSED: All {len(group_links)} groups processed")

            # Final results and saving
            self.log_activity_signal.emit(f"✅ COMPLETED: Checked {len(group_links)} groups")'''
    
    if old_final in content:
        content = content.replace(old_final, new_final)
        print("✅ FIXED: Added comprehensive verification system")
    else:
        print("⚠️ WARNING: Could not find final results section")

    # ========================================
    # FIX 4: Enhanced categorization logging
    # ========================================
    
    # Find and enhance each categorization section
    categorizations = [
        ('valid_filtered.append(link)', 'self.log_activity_signal.emit(f"   └── Saved to: Groups_Valid_Filter")'),
        ('valid_only.append(link)', 'self.log_activity_signal.emit(f"   └── Saved to: Groups_Valid_Only")'),  
        ('topics_groups.append(link)', 'self.log_activity_signal.emit(f"   └── Saved to: Topics_Groups_Only_Valid")'),
        ('channels_only.append(link)', 'self.log_activity_signal.emit(f"   └── Saved to: Channels_Only_Valid")')
    ]
    
    for append_line, log_line in categorizations:
        if append_line in content:
            # Add logging after each append
            content = content.replace(append_line, f'{append_line}\n                            {log_line}')
    
    print("✅ FIXED: Enhanced categorization logging")

    # Write the enhanced file
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ ENHANCED LOGGING FIXES APPLIED SUCCESSFULLY!")
    print("\n🎯 LOGGING ENHANCEMENTS:")
    print("  ✅ Comprehensive group analysis for each group")
    print("  ✅ Detailed logging of all decisions")
    print("  ✅ Verification system to prevent missing groups")
    print("  ✅ Enhanced categorization logging")
    print("  ✅ Missing group detection and reporting")
    print("\n🚀 The checker will now:")
    print("  • Log every group's full analysis")
    print("  • Show exactly where each group is saved")
    print("  • Verify no groups are missing")
    print("  • Report any unaccounted groups")
    print("  • Provide complete transparency")
    
    return True

if __name__ == "__main__":
    apply_logging_fix() 