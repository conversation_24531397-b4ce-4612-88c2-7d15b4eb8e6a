#!/usr/bin/env python3
"""
Indentation Fixer for main.py
"""

import os
import shutil
from datetime import datetime

def backup_file():
    """Create a backup of the main.py file."""
    backup_name = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("main.py", backup_name)
    print(f"Created backup: {backup_name}")
    return backup_name

def fix_indentation():
    """Fix the indentation of the stop_joining_task function."""
    print("Fixing indentation of stop_joining_task function...")
    
    # Create a backup
    backup_file = backup_file()
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Find the problematic function
    start_marker = "def stop_joining_task(self, task_id):"
    if start_marker not in content:
        print("Could not find the stop_joining_task function.")
        return False
    
    # Create the properly indented function
    properly_indented_function = """    def stop_joining_task(self, task_id):
        \"\"\"Stop a specific joining task with high-performance system.\"\"\"
        try:
            if hasattr(self, 'active_hp_tasks') and task_id in self.active_hp_tasks:
                self.active_hp_tasks[task_id]['stop_requested'] = True
                self.logger.info(f"Stop requested for joining task: {task_id}")
                return True
            else:
                self.logger.warning(f"Cannot stop task {task_id}: Task not found in active tasks")
                return False
        except Exception as e:
            self.logger.error(f"Error stopping joining task {task_id}: {str(e)}")
            return False"""
    
    # Find the start and end of the function to replace
    start_pos = content.find(start_marker)
    if start_pos == -1:
        print("Could not find the function start position.")
        return False
    
    # Find the end of the function (next def at the same indentation level)
    lines = content.split('\n')
    function_start_line = None
    for i, line in enumerate(lines):
        if start_marker in line:
            function_start_line = i
            break
    
    if function_start_line is None:
        print("Could not find the function start line.")
        return False
    
    # Find the end of the function
    function_end_line = None
    for i in range(function_start_line + 1, len(lines)):
        line = lines[i].strip()
        if line.startswith("def ") and lines[i].startswith("    def "):
            function_end_line = i
            break
    
    if function_end_line is None:
        print("Could not find the function end line, using a default span.")
        function_end_line = function_start_line + 15  # Assume function is about 15 lines
    
    # Replace the function
    new_lines = lines[:function_start_line] + properly_indented_function.split('\n') + lines[function_end_line:]
    new_content = '\n'.join(new_lines)
    
    # Write the fixed content back to the file
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(new_content)
    
    print(f"Fixed indentation of stop_joining_task function")
    return True

# Avoid name conflict with the function
def create_backup():
    return backup_file()

if __name__ == "__main__":
    print("🔧 Indentation Fixer for main.py")
    print("--------------------------------")
    
    if fix_indentation():
        print("\n✅ Fix applied successfully!")
        print("You can now run the application with: python main.py")
    else:
        print("\n❌ Failed to apply the fix.") 