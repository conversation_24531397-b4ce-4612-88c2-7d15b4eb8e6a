"""
Create a simple icon for the TG Checker application.
This script generates a basic icon file that can be used by the application.
"""

from PIL import Image, ImageDraw
import os

def create_icon(output_path="icon.png", size=(256, 256)):
    """Create a simple icon for the application."""
    # Create a blank image with a white background
    img = Image.new('RGB', size, color=(255, 255, 255))
    draw = ImageDraw.Draw(img)
    
    # Draw a blue rectangle as background
    draw.rectangle(
        ((20, 20), (size[0]-20, size[1]-20)), 
        fill=(65, 105, 225), 
        outline=(30, 64, 175), 
        width=5
    )
    
    # Draw text in the center
    text_color = (255, 255, 255)
    
    # Draw "TG" text
    draw.text(
        (size[0]//2 - 50, size[1]//2 - 60),
        "TG", 
        fill=text_color, 
        font_size=100
    )
    
    # Draw "PY" text
    draw.text(
        (size[0]//2 - 20, size[1]//2 + 20),
        "PY", 
        fill=text_color,
        font_size=40
    )
    
    # Save the image
    img.save(output_path)
    print(f"Icon created and saved to {output_path}")
    return output_path

if __name__ == "__main__":
    create_icon() 