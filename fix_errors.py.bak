#!/usr/bin/env python
"""
This script fixes errors in the main.py Telegram checker application.
"""

import os
import re
import shutil
from datetime import datetime

def create_backup(file_path):
    """Create a backup of the file."""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        return False
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"Error creating backup: {str(e)}")
        return False

def fix_log_activity_method(content):
    """Fix the _log_activity_to_ui method to check for attribute existence."""
    pattern1 = r'def _log_activity_to_ui\(self, message\):.*?try:.*?timestamp = datetime.now\(\).strftime\("%Y-%m-%d %H:%M:%S"\).*?self\.log_display\.append\(f"\[{timestamp}\] {message}"\).*?# Also log to the logger.*?self\.logger\.info\(message\).*?except Exception as e:'
    
    replacement = '''def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if hasattr(self, 'log_display') and self.log_display is not None:
                self.log_display.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            if hasattr(self, 'logger') and self.logger is not None:
                self.logger.info(message)
            
        except Exception as e:'''
    
    # Use re.DOTALL to match across lines
    return re.sub(pattern1, replacement, content, flags=re.DOTALL)

def fix_db_path_error(content):
    """Fix the initialize_forwarder_db method to handle missing db_path attribute."""
    pattern = r'def initialize_forwarder_db\(self\):.*?try:.*?conn = sqlite3.connect\(self\.account_manager\.db_path\)'
    
    replacement = '''def initialize_forwarder_db(self):
        """Initialize the forwarder database."""
        try:
            # Set a default db_path if account_manager doesn't have one
            if not hasattr(self, 'account_manager') or not hasattr(self.account_manager, 'db_path'):
                if hasattr(self, 'db_path'):
                    db_path = self.db_path
                else:
                    db_path = "tg_checker.db"
                    # Set the db_path attribute for future use
                    self.db_path = db_path
            else:
                db_path = self.account_manager.db_path
                
            conn = sqlite3.connect(db_path)'''
    
    return re.sub(pattern, replacement, content, flags=re.DOTALL)

def fix_threading_issues(content):
    """Add QVector<int> registration to fix Qt threading issues."""
    pattern = r'if __name__ == "__main__":'
    
    replacement = '''# Register QVector<int> for cross-thread signals
def register_qt_types():
    from PyQt5.QtCore import QMetaType, QVector
    from PyQt5.QtCore import qRegisterMetaType
    try:
        qRegisterMetaType('QVector<int>')
    except Exception as e:
        print(f"Failed to register QVector<int>: {str(e)}")

if __name__ == "__main__":
    # Register required types for Qt threading
    register_qt_types()'''
    
    return re.sub(pattern, replacement, content)

def apply_fixes(file_path):
    """Apply all fixes to the file."""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply fixes
        content = fix_log_activity_method(content)
        content = fix_db_path_error(content)
        content = fix_threading_issues(content)
        
        # Write the fixed content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Successfully applied fixes to {file_path}")
        return True
    
    except Exception as e:
        print(f"Error applying fixes: {str(e)}")
        return False

if __name__ == "__main__":
    file_path = "main.py"
    
    # Create a backup first
    if create_backup(file_path):
        # Apply fixes
        apply_fixes(file_path)
    else:
        print("Aborting fix operation due to backup failure") 