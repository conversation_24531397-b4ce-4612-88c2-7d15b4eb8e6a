#!/usr/bin/env python3
"""
Test Script for Qt Fixes in TG Checker Application

This script tests the Qt fixes to ensure the forwarder logging and 
thread-safe operations work properly without generating Qt errors.
"""

import sys
import time
import threading
from PyQt5.QtWidgets import QApplication, QMainWindow, QTextEdit, QVBoxLayout, QWidget, QPushButton
from PyQt5.QtCore import Qt, QMetaObject, Q_ARG, pyqtSlot
from PyQt5.QtGui import QTextCursor

class TestMainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Qt Fix Test - TG Checker")
        self.setGeometry(100, 100, 600, 400)
        
        # Create central widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # Create text area (similar to forwarder_logs)
        self.forwarder_logs = QTextEdit()
        self.forwarder_logs.setReadOnly(True)
        layout.addWidget(self.forwarder_logs)
        
        # Create test button
        test_button = QPushButton("Test Qt Threading Fix")
        test_button.clicked.connect(self.test_threading)
        layout.addWidget(test_button)
        
        print("✅ Qt Test Window initialized successfully")
    
    @pyqtSlot(str, str, str)
    def log_forwarder_message(self, level, task_id, message):
        """Test the fixed log_forwarder_message method."""
        try:
            timestamp = time.strftime("%H:%M:%S")
            
            # Format message with color coding
            if level == "error":
                log_text = f"<span style='color: #ff4444;'>[{timestamp}] ❌ {message}</span><br>"
            elif level == "warning":
                log_text = f"<span style='color: #ffaa00;'>[{timestamp}] ⚠️ {message}</span><br>"
            elif level == "success":
                log_text = f"<span style='color: #44ff44;'>[{timestamp}] ✅ {message}</span><br>"
            else:
                log_text = f"<span style='color: #ffffff;'>[{timestamp}] ℹ️ {message}</span><br>"
            
            # Thread-safe UI update using the FIXED method
            if hasattr(self, 'forwarder_logs') and self.forwarder_logs:
                print(f"📝 Attempting thread-safe log update: {message[:50]}...")
                
                # This is the FIXED version with Q_ARG
                QMetaObject.invokeMethod(
                    self.forwarder_logs,
                    "append",
                    Qt.QueuedConnection,
                    Q_ARG(str, log_text.replace('<br>', ''))
                )
                
                print("✅ Thread-safe log update successful!")
                
                # Safe cursor management
                try:
                    document = self.forwarder_logs.document()
                    if document and document.blockCount() > 100:
                        cursor = self.forwarder_logs.textCursor()
                        if cursor and not cursor.isNull():
                            # Safely move to start
                            cursor.movePosition(QTextCursor.Start)
                            
                            # Calculate safe removal count  
                            blocks_to_remove = min(document.blockCount() - 100, document.blockCount() - 1)
                            if blocks_to_remove > 0:
                                # Move down safely with bounds checking
                                for _ in range(blocks_to_remove):
                                    if not cursor.movePosition(QTextCursor.Down, QTextCursor.KeepAnchor):
                                        break
                                
                                # Remove selected text if we have a valid selection
                                if cursor.hasSelection():
                                    cursor.removeSelectedText()
                    
                    # Safe auto-scroll to bottom
                    cursor = self.forwarder_logs.textCursor()
                    if cursor and not cursor.isNull():
                        cursor.movePosition(QTextCursor.End)
                        self.forwarder_logs.setTextCursor(cursor)
                        
                except Exception as cursor_error:
                    print(f"ℹ️  Cursor operation handled gracefully: {str(cursor_error)}")
                    pass
            
        except Exception as e:
            print(f"❌ Error in log_forwarder_message: {str(e)}")
            raise
    
    def test_threading(self):
        """Test the Qt threading fixes with multiple threads."""
        print("\n🧪 Starting Qt Threading Test...")
        print("-" * 50)
        
        # Test 1: Direct call (main thread)
        print("🔬 Test 1: Direct call from main thread")
        self.log_forwarder_message("info", "test_001", "Direct call test - should work")
        
        # Test 2: Single background thread
        print("🔬 Test 2: Single background thread")
        def single_thread_test():
            time.sleep(0.1)
            self.log_forwarder_message("success", "test_002", "Single thread test - should work")
        
        thread1 = threading.Thread(target=single_thread_test, daemon=True)
        thread1.start()
        
        # Test 3: Multiple background threads (stress test)
        print("🔬 Test 3: Multiple background threads (stress test)")
        def multi_thread_test(thread_id):
            for i in range(3):
                time.sleep(0.05 * thread_id)
                self.log_forwarder_message(
                    "info" if i % 2 == 0 else "warning", 
                    f"test_{thread_id:03d}", 
                    f"Multi-thread test {thread_id}-{i} - should work"
                )
        
        # Start 5 threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=multi_thread_test, args=(i+1,), daemon=True)
            threads.append(thread)
            thread.start()
        
        # Test 4: Rapid-fire test
        print("🔬 Test 4: Rapid-fire logging test")
        def rapid_fire_test():
            for i in range(10):
                self.log_forwarder_message("info", f"rapid_{i:02d}", f"Rapid message {i}")
                time.sleep(0.01)
        
        rapid_thread = threading.Thread(target=rapid_fire_test, daemon=True)
        rapid_thread.start()
        
        print("✅ All threading tests started!")
        print("📊 Watch the log area for results...")
        print("💡 If no Qt errors appear in console, the fix is working!")

def main():
    """Run the Qt fix test"""
    print("🚀 Qt Fix Test for TG Checker")
    print("=" * 50)
    
    # Initialize Qt Application
    app = QApplication(sys.argv)
    
    try:
        # Create and show test window
        window = TestMainWindow()
        window.show()
        
        print("🎯 Test Instructions:")
        print("1. Click 'Test Qt Threading Fix' button")
        print("2. Watch for any Qt errors in the console")
        print("3. Check that log messages appear in the text area")
        print("4. No Qt errors = Fix successful! ✅")
        print("\n👀 Monitoring for Qt errors...")
        
        # Run the application
        sys.exit(app.exec_())
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        return False

if __name__ == "__main__":
    main() 