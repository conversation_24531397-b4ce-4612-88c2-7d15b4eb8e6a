import os

def files_exist(*files):
    for _file in files:
        if not os.path.exists(_file):
            return False, _file
    return True, None

def read_lines(file_name):
    with read_file(file_name) as f:
        return [line.strip() for line in f.readlines() if line]

def read_text(file_name):
    with read_file(file_name) as f:
        return f.read()

def read_file(file_name):
    return open(file_name, encoding="utf-8")
