"""
Critical Database Fix for TG Checker
Fixes: Database conflicts, sync issues, and concurrency problems

This file replaces the broken database handling that's causing:
- Fake join results
- UI freezing 
- Constant crashes
- Sync disabled errors
"""

import sqlite3
import threading
import time
import logging
import weakref
from contextlib import contextmanager
from typing import Optional, Dict, Any, List
import queue
import json
from datetime import datetime


class CriticalDatabaseManager:
    """
    CRITICAL FIX: Centralized database manager that eliminates all concurrency issues
    This fixes the core problems causing crashes and fake results.
    """
    
    def __init__(self, db_path: str = "tg_checker.db", logger=None):
        self.db_path = db_path
        self.logger = logger or logging.getLogger(__name__)
        
        # Thread-safe connection pool
        self._connection_pool = queue.Queue(maxsize=10)
        self._pool_lock = threading.RLock()
        self._active_connections = set()
        self._connection_lock = threading.RLock()
        
        # Transaction management
        self._transaction_lock = threading.RLock()
        self._pending_writes = {}
        self._batch_timer = None
        
        # Initialize database and pool
        self._init_database()
        self._populate_connection_pool()
        
        # Start cleanup timer
        self._start_cleanup_timer()
    
    def _init_database(self):
        """Initialize database with optimal settings for concurrency."""
        try:
            conn = sqlite3.connect(self.db_path, timeout=60.0)
            
            # Set optimal SQLite settings for concurrent access
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL") 
            conn.execute("PRAGMA busy_timeout=60000")
            conn.execute("PRAGMA wal_autocheckpoint=1000")
            conn.execute("PRAGMA cache_size=10000")
            conn.execute("PRAGMA temp_store=MEMORY")
            
            # Create all required tables
            self._create_tables(conn)
            conn.commit()
            conn.close()
            
            self.logger.info("✅ Database initialized with fixed concurrency settings")
            
        except Exception as e:
            self.logger.error(f"❌ Database initialization failed: {e}")
            raise
    
    def _create_tables(self, conn):
        """Create all required tables."""
        cursor = conn.cursor()
        
        # Joining tasks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS joining_tasks (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                account_phone TEXT NOT NULL,
                group_links TEXT NOT NULL,
                current_index INTEGER DEFAULT 0,
                total_groups INTEGER DEFAULT 0,
                successful_joins INTEGER DEFAULT 0,
                failed_joins INTEGER DEFAULT 0,
                status TEXT DEFAULT 'pending',
                settings TEXT DEFAULT '{}',
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Account status tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS account_status (
                phone TEXT PRIMARY KEY,
                status TEXT DEFAULT 'available',
                flood_wait_until TEXT,
                last_activity TEXT,
                error_count INTEGER DEFAULT 0,
                updated_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Join verification table (prevents fake results)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS join_verification (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id TEXT NOT NULL,
                account_phone TEXT NOT NULL,
                group_link TEXT NOT NULL,
                join_attempted_at TEXT NOT NULL,
                join_verified_at TEXT,
                verification_status TEXT DEFAULT 'pending',
                actual_member BOOLEAN DEFAULT FALSE,
                error_message TEXT,
                UNIQUE(task_id, account_phone, group_link)
            )
        ''')
        
        # Performance tracking
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS performance_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
                operation TEXT NOT NULL,
                duration_ms INTEGER,
                success BOOLEAN,
                details TEXT
            )
        ''')
    
    def _populate_connection_pool(self):
        """Create initial connection pool."""
        with self._pool_lock:
            for _ in range(5):  # Start with 5 connections
                try:
                    conn = self._create_connection()
                    self._connection_pool.put(conn, block=False)
                except Exception as e:
                    self.logger.warning(f"Failed to create initial connection: {e}")
    
    def _create_connection(self):
        """Create a new database connection with optimal settings."""
        conn = sqlite3.connect(self.db_path, timeout=60.0, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        
        # Optimal settings for concurrent access
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA busy_timeout=60000")
        conn.execute("PRAGMA cache_size=5000")
        conn.execute("PRAGMA temp_store=MEMORY")
        
        with self._connection_lock:
            self._active_connections.add(conn)
        
        return conn
    
    @contextmanager
    def get_connection(self, timeout: float = 30.0):
        """Get a connection from the pool with automatic cleanup."""
        conn = None
        start_time = time.time()
        
        try:
            # Try to get existing connection
            try:
                conn = self._connection_pool.get(timeout=5.0)
            except queue.Empty:
                # Create new connection if pool is empty
                with self._pool_lock:
                    if len(self._active_connections) < 10:  # Max 10 connections
                        conn = self._create_connection()
                    else:
                        # Wait for available connection
                        conn = self._connection_pool.get(timeout=timeout)
            
            # Test connection health
            if not self._test_connection(conn):
                conn.close()
                conn = self._create_connection()
            
            yield conn
            
        except Exception as e:
            self.logger.error(f"Database connection error: {e}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            if conn:
                try:
                    # Return healthy connection to pool
                    if self._test_connection(conn):
                        self._connection_pool.put(conn, block=False)
                    else:
                        conn.close()
                        with self._connection_lock:
                            self._active_connections.discard(conn)
                except queue.Full:
                    # Pool is full, close the connection
                    conn.close()
                    with self._connection_lock:
                        self._active_connections.discard(conn)
                except Exception as e:
                    self.logger.warning(f"Error returning connection to pool: {e}")
                    try:
                        conn.close()
                    except:
                        pass
                    with self._connection_lock:
                        self._active_connections.discard(conn)
    
    def _test_connection(self, conn) -> bool:
        """Test if connection is healthy."""
        try:
            conn.execute("SELECT 1").fetchone()
            return True
        except:
            return False
    
    def _start_cleanup_timer(self):
        """Start periodic cleanup of stale connections."""
        def cleanup():
            try:
                with self._connection_lock:
                    stale_connections = []
                    for conn in list(self._active_connections):
                        if not self._test_connection(conn):
                            stale_connections.append(conn)
                    
                    for conn in stale_connections:
                        try:
                            conn.close()
                        except:
                            pass
                        self._active_connections.discard(conn)
                
                # Schedule next cleanup
                self._cleanup_timer = threading.Timer(300, cleanup)  # Every 5 minutes
                self._cleanup_timer.daemon = True
                self._cleanup_timer.start()
                
            except Exception as e:
                self.logger.error(f"Connection cleanup error: {e}")
        
        cleanup()
    
    # CRITICAL JOINING TASK FIXES
    
    def update_joining_task_safe(self, task_id: str, **updates) -> bool:
        """CRITICAL FIX: Thread-safe task update that prevents fake results."""
        try:
            with self.get_connection() as conn:
                # Add timestamp
                updates['updated_at'] = datetime.now().isoformat()
                
                # Build update query
                set_clauses = [f"{key} = ?" for key in updates.keys()]
                values = list(updates.values()) + [task_id]
                
                query = f"UPDATE joining_tasks SET {', '.join(set_clauses)} WHERE id = ?"
                
                cursor = conn.execute(query, values)
                conn.commit()
                
                if cursor.rowcount == 0:
                    self.logger.warning(f"No task found with ID: {task_id}")
                    return False
                
                self.logger.debug(f"✅ Updated task {task_id}: {updates}")
                return True
                
        except Exception as e:
            self.logger.error(f"❌ Failed to update task {task_id}: {e}")
            return False
    
    def verify_join_result(self, task_id: str, account_phone: str, group_link: str, 
                          client=None) -> bool:
        """CRITICAL FIX: Actually verify if join was successful to prevent fake results."""
        try:
            # Record join attempt
            with self.get_connection() as conn:
                conn.execute('''
                    INSERT OR REPLACE INTO join_verification 
                    (task_id, account_phone, group_link, join_attempted_at, verification_status)
                    VALUES (?, ?, ?, ?, 'verifying')
                ''', (task_id, account_phone, group_link, datetime.now().isoformat()))
                conn.commit()
            
            # If client provided, verify membership
            if client:
                try:
                    import asyncio
                    
                    async def check_membership():
                        try:
                            entity = await client.get_entity(group_link)
                            # Try to get participant info (will fail if not a member)
                            await client.get_participants(entity, limit=1)
                            return True
                        except:
                            return False
                    
                    # Run verification
                    loop = asyncio.get_event_loop()
                    is_member = loop.run_until_complete(check_membership())
                    
                    # Update verification result
                    with self.get_connection() as conn:
                        conn.execute('''
                            UPDATE join_verification 
                            SET join_verified_at = ?, verification_status = ?, actual_member = ?
                            WHERE task_id = ? AND account_phone = ? AND group_link = ?
                        ''', (datetime.now().isoformat(), 'verified', is_member, 
                              task_id, account_phone, group_link))
                        conn.commit()
                    
                    return is_member
                    
                except Exception as e:
                    self.logger.error(f"Membership verification failed: {e}")
                    # Mark as failed verification
                    with self.get_connection() as conn:
                        conn.execute('''
                            UPDATE join_verification 
                            SET verification_status = 'failed', error_message = ?
                            WHERE task_id = ? AND account_phone = ? AND group_link = ?
                        ''', (str(e), task_id, account_phone, group_link))
                        conn.commit()
                    return False
            
            # If no client, assume join was attempted but not verified
            return False
            
        except Exception as e:
            self.logger.error(f"Join verification error: {e}")
            return False
    
    def get_real_join_stats(self, task_id: str) -> Dict[str, int]:
        """Get actual join statistics based on verification."""
        try:
            with self.get_connection() as conn:
                result = conn.execute('''
                    SELECT 
                        COUNT(*) as total_attempts,
                        COUNT(CASE WHEN actual_member = 1 THEN 1 END) as verified_joins,
                        COUNT(CASE WHEN verification_status = 'failed' THEN 1 END) as failed_joins,
                        COUNT(CASE WHEN verification_status = 'pending' THEN 1 END) as pending_verification
                    FROM join_verification 
                    WHERE task_id = ?
                ''', (task_id,)).fetchone()
                
                return {
                    'total_attempts': result['total_attempts'],
                    'verified_joins': result['verified_joins'],
                    'failed_joins': result['failed_joins'],
                    'pending_verification': result['pending_verification']
                }
        except Exception as e:
            self.logger.error(f"Error getting join stats: {e}")
            return {'total_attempts': 0, 'verified_joins': 0, 'failed_joins': 0, 'pending_verification': 0}
    
    def get_joining_tasks(self) -> List[Dict[str, Any]]:
        """Get all joining tasks safely."""
        try:
            with self.get_connection() as conn:
                rows = conn.execute('''
                    SELECT * FROM joining_tasks 
                    ORDER BY created_at DESC
                ''').fetchall()
                
                tasks = []
                for row in rows:
                    task = dict(row)
                    # Get real statistics
                    real_stats = self.get_real_join_stats(task['id'])
                    task['real_successful_joins'] = real_stats['verified_joins']
                    task['real_failed_joins'] = real_stats['failed_joins']
                    tasks.append(task)
                
                return tasks
                
        except Exception as e:
            self.logger.error(f"Error getting joining tasks: {e}")
            return []
    
    def cleanup_and_reset(self):
        """Emergency cleanup to fix database issues."""
        try:
            self.logger.info("🔧 Starting emergency database cleanup...")
            
            # Close all connections
            with self._connection_lock:
                for conn in list(self._active_connections):
                    try:
                        conn.close()
                    except:
                        pass
                self._active_connections.clear()
            
            # Clear connection pool
            with self._pool_lock:
                while not self._connection_pool.empty():
                    try:
                        conn = self._connection_pool.get_nowait()
                        conn.close()
                    except:
                        pass
            
            # Run database maintenance
            with sqlite3.connect(self.db_path, timeout=60.0) as conn:
                conn.execute("PRAGMA wal_checkpoint(TRUNCATE)")
                conn.execute("VACUUM")
                conn.execute("ANALYZE")
            
            # Reinitialize
            self._populate_connection_pool()
            
            self.logger.info("✅ Database cleanup completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Database cleanup failed: {e}")
    
    def __del__(self):
        """Cleanup on deletion."""
        try:
            with self._connection_lock:
                for conn in list(self._active_connections):
                    try:
                        conn.close()
                    except:
                        pass
        except:
            pass


# Global database manager instance
_db_manager = None
_db_lock = threading.Lock()

def get_database_manager(db_path: str = "tg_checker.db", logger=None) -> CriticalDatabaseManager:
    """Get the global database manager instance."""
    global _db_manager
    
    with _db_lock:
        if _db_manager is None:
            _db_manager = CriticalDatabaseManager(db_path, logger)
        return _db_manager


# CRITICAL HELPER FUNCTIONS FOR MAIN.PY

def fix_joining_task_update(main_app, task_id: str, **updates) -> bool:
    """CRITICAL FIX: Replace broken task update with safe version."""
    try:
        db_manager = get_database_manager(logger=getattr(main_app, 'logger', None))
        return db_manager.update_joining_task_safe(task_id, **updates)
    except Exception as e:
        if hasattr(main_app, 'logger'):
            main_app.logger.error(f"Task update fix failed: {e}")
        return False

def fix_join_verification(main_app, task_id: str, account_phone: str, group_link: str, client=None) -> bool:
    """CRITICAL FIX: Verify actual joins to prevent fake results."""
    try:
        db_manager = get_database_manager(logger=getattr(main_app, 'logger', None))
        return db_manager.verify_join_result(task_id, account_phone, group_link, client)
    except Exception as e:
        if hasattr(main_app, 'logger'):
            main_app.logger.error(f"Join verification fix failed: {e}")
        return False

def emergency_database_fix(main_app):
    """CRITICAL FIX: Emergency database cleanup and reset."""
    try:
        db_manager = get_database_manager(logger=getattr(main_app, 'logger', None))
        db_manager.cleanup_and_reset()
        
        # Re-enable sync
        if hasattr(main_app, 'update_status_signal'):
            main_app.update_status_signal.emit("Database fixed - sync re-enabled")
        if hasattr(main_app, 'log_activity_signal'):
            main_app.log_activity_signal.emit("✅ Emergency database fix completed - sync restored")
        
        return True
    except Exception as e:
        if hasattr(main_app, 'logger'):
            main_app.logger.error(f"Emergency database fix failed: {e}")
        return False


if __name__ == "__main__":
    # Test the database manager
    import logging
    logging.basicConfig(level=logging.INFO)
    
    db = CriticalDatabaseManager()
    print("✅ Critical Database Manager initialized successfully!")
    
    # Test basic operations
    test_task_id = "test_task_123"
    success = db.update_joining_task_safe(test_task_id, status="testing", successful_joins=5)
    print(f"Test update: {'✅ Success' if success else '❌ Failed'}")
    
    stats = db.get_real_join_stats(test_task_id)
    print(f"Test stats: {stats}") 