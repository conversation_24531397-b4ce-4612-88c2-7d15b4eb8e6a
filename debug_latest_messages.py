#!/usr/bin/env python3
"""
Minimal script to debug Telegram group message fetching.
Prints the latest 30 messages (ID, type, date, text) for a given group.
Fill in your own API ID, API hash, and phone/session.
"""

from telethon.sync import TelegramClient
from telethon.tl.types import MessageService
from datetime import datetime

# === FILL IN YOUR OWN CREDENTIALS ===
API_ID = 123456  # <-- your API ID here
API_HASH = 'your_api_hash_here'  # <-- your API hash here
PHONE = '+1234567890'  # <-- your phone number here (or use session file)
SESSION = None  # or 'your_session_file.session' if you use a session file

# === GROUP TO TEST ===
GROUP = 'https://t.me/BuysellZone1'  # Change to your test group link

# === SCRIPT ===

client = TelegramClient(SESSION or PHONE, API_ID, API_HASH)

async def main():
    await client.start(phone=PHONE)
    print(f"Connected as: {await client.get_me()}")
    print(f"Fetching latest 30 messages from: {GROUP}")
    messages = await client.get_messages(GROUP, limit=30)
    for idx, m in enumerate(messages):
        if m is None:
            msg_type = 'None'
        elif isinstance(m, MessageService):
            msg_type = 'service'
        elif getattr(m, 'message', None) is None and not getattr(m, 'media', None):
            msg_type = 'empty/deleted'
        else:
            msg_type = 'user'
        date_str = str(getattr(m, 'date', ''))
        text = getattr(m, 'message', '<media/other>')
        print(f"[{idx:02d}] id={getattr(m, 'id', None)}, type={msg_type}, date={date_str}, text={text[:40]}")

with client:
    client.loop.run_until_complete(main()) 