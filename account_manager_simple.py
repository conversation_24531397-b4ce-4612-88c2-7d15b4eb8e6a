#!/usr/bin/env python3
"""
Simplified Account Manager - Emergency Fix for Hanging Issue
This replaces the complex account_manager_fixed_enhanced.py that was causing hangs.
No complex retry logic, no long timeouts, no WAL mode complications.
"""

import os
import json
import sqlite3
import logging
from datetime import datetime
import threading

class AccountManager:
    """Simplified account manager without hanging issues."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self.accounts = []
        self.clients = {}
        self._db_lock = threading.RLock()
        
        # Initialize database with simple approach
        self._init_database()
        
        # Load accounts from database
        self._load_accounts()
    
    def _get_db_connection(self):
        """Simple database connection without complex retry logic."""
        try:
            # Simple connection with short timeout to prevent hanging
            conn = sqlite3.connect(self.db_path, timeout=5.0)
            conn.row_factory = sqlite3.Row
            
            # Use simple DELETE mode instead of problematic WAL mode
            conn.execute("PRAGMA journal_mode=DELETE")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=3000")  # Only 3 second timeout
            
            return conn
        except Exception as e:
            self.logger.error(f"Database connection error: {str(e)}")
            raise
    
    def _init_database(self):
        """Simple database initialization without hanging operations."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                # Create accounts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                ''')
                
                # Add columns if they don't exist (simple approach)
                columns_to_add = [
                    ("account_age_days", "INTEGER DEFAULT 0"),
                    ("is_aged", "INTEGER DEFAULT 0"), 
                    ("daily_group_limit", "INTEGER DEFAULT 0"),
                    ("last_age_check", "TEXT"),
                    ("name", "TEXT"),
                    ("username", "TEXT"),
                    ("error_message", "TEXT"),
                    ("disabled_until", "TEXT"),
                    ("account_type", "TEXT DEFAULT 'normal'")
                ]
                
                for column_name, column_def in columns_to_add:
                    try:
                        cursor.execute(f"ALTER TABLE accounts ADD COLUMN {column_name} {column_def}")
                    except sqlite3.OperationalError:
                        pass  # Column already exists
                
                # Create errors table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create deleted accounts table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deleted_accounts (
                        phone TEXT PRIMARY KEY,
                        deleted_at TEXT,
                        reason TEXT DEFAULT 'user_deleted'
                    )
                ''')
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def _load_accounts(self):
        """Simple account loading without complex error handling."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM accounts")
                rows = cursor.fetchall()
                
                self.accounts = [dict(row) for row in rows]
                
                conn.close()
                
            self.logger.info(f"Loaded {len(self.accounts)} accounts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
            self.accounts = []
    
    def add_account(self, phone, api_id, api_hash, session_file, name=None, username=None):
        """Add a new account to the database."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO accounts 
                    (phone, api_id, api_hash, session_file, active, status, last_check, name, username)
                    VALUES (?, ?, ?, ?, 1, 'active', ?, ?, ?)
                ''', (phone, api_id, api_hash, session_file, datetime.now().isoformat(), name, username))
                
                conn.commit()
                conn.close()
                
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Added account: {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding account {phone}: {str(e)}")
            return False
    
    def get_accounts(self):
        """Get all accounts."""
        return self.accounts
    
    def get_active_accounts(self):
        """Get only active accounts."""
        return [acc for acc in self.accounts if acc.get('active', 0) == 1]
    
    def update_account_status(self, phone, status, error_message=None):
        """Update account status."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE accounts 
                    SET status = ?, last_check = ?, error_message = ?
                    WHERE phone = ?
                ''', (status, datetime.now().isoformat(), error_message, phone))
                
                conn.commit()
                conn.close()
                
            # Reload accounts
            self._load_accounts()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account {phone}: {str(e)}")
            return False
    
    def delete_account(self, phone):
        """Delete an account from the database."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                # Move to deleted accounts table
                cursor.execute('''
                    INSERT OR REPLACE INTO deleted_accounts 
                    (phone, deleted_at, reason)
                    VALUES (?, ?, ?)
                ''', (phone, datetime.now().isoformat(), 'user_deleted'))
                
                # Remove from accounts table
                cursor.execute('DELETE FROM accounts WHERE phone = ?', (phone,))
                
                conn.commit()
                conn.close()
                
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Deleted account: {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting account {phone}: {str(e)}")
            return False
    
    def clean_database(self):
        """Simple database cleanup without hanging operations."""
        try:
            self.logger.info("Starting simple database cleanup...")
            
            with self._db_lock:
                conn = self._get_db_connection()
                
                # Only run PRAGMA optimize (safe and fast)
                # DO NOT run VACUUM as it can hang
                conn.execute("PRAGMA optimize")
                conn.commit()
                conn.close()
                
            self.logger.info("Database cleanup completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during database cleanup: {str(e)}")
            return False
    
    def get_client(self, phone):
        """Get Telegram client for account."""
        return self.clients.get(phone)
    
    def set_client(self, phone, client):
        """Set Telegram client for account."""
        self.clients[phone] = client
    
    def remove_client(self, phone):
        """Remove Telegram client for account."""
        if phone in self.clients:
            del self.clients[phone] 