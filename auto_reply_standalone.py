#!/usr/bin/env python3
"""
Standalone Auto-Reply Bot for Telegram Accounts
Runs independently to provide auto-reply functionality for all configured accounts.
"""

import asyncio
import sys
import os
import sqlite3
import time
import logging
from telethon import TelegramClient, events

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from account_manager import AccountManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('auto_reply.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AutoReplyBot:
    def __init__(self):
        self.clients = {}
        self.last_replies = {}
        self.account_manager = AccountManager()
        
    def get_account_reply_message(self, phone):
        """Get reply message for account from database."""
        try:
            forwarder_db_path = 'forwarder_tasks.db'
            conn = sqlite3.connect(forwarder_db_path, timeout=30)
            cursor = conn.cursor()
            cursor.execute('SELECT reply_message FROM account_forwarder_settings WHERE phone = ?', (phone,))
            row = cursor.fetchone()
            conn.close()
            
            if row and row[0]:
                return row[0].strip()
            else:
                return "Hi! This is an advertising bot. DMs won't be seen here. Please contact @vipstore"
        except Exception as e:
            logger.warning(f"Error getting reply message for {phone}: {str(e)}")
            return "Hi! This is an advertising bot. DMs won't be seen here. Please contact @vipstore"

    async def create_client(self, phone, account):
        """Create a Telegram client for the account."""
        try:
            # Validate account data
            if not account.get('api_id') or not account.get('api_hash'):
                logger.error(f"Account {phone} missing API credentials")
                return None
            
            # Ensure sessions directory exists
            sessions_dir = 'sessions'
            if not os.path.exists(sessions_dir):
                os.makedirs(sessions_dir)
            
            # Create client with session file
            session_file = os.path.join(sessions_dir, phone)
            client = TelegramClient(session_file, account['api_id'], account['api_hash'])
            
            # Connect with timeout
            await asyncio.wait_for(client.connect(), timeout=20)
            
            # Check authorization
            if not await client.is_user_authorized():
                logger.error(f"Account {phone} is not authorized")
                await client.disconnect()
                return None
            
            # Test connection
            me = await client.get_me()
            logger.info(f"Connected {phone} as: {me.first_name} (@{me.username or 'no_username'})")
            return client
            
        except Exception as e:
            logger.error(f"Failed to create client for {phone}: {str(e)}")
            return None

    async def setup_auto_reply(self, client, phone):
        """Set up auto-reply handler for a client."""
        try:
            reply_message = self.get_account_reply_message(phone)
            
            @client.on(events.NewMessage(incoming=True, func=lambda e: e.is_private))
            async def auto_reply_handler(event):
                try:
                    # Get sender info
                    sender = await event.get_sender()
                    sender_id = sender.id if sender else event.sender_id
                    
                    # Get current user to avoid replying to self
                    me = await client.get_me()
                    
                    # Don't reply to yourself or bots
                    if sender and (getattr(sender, 'bot', False) or sender_id == me.id):
                        return
                    
                    # Check if we already replied recently (anti-spam: 5 minutes cooldown)
                    current_time = time.time()
                    last_reply_key = f"last_reply_{phone}_{sender_id}"
                    
                    if last_reply_key in self.last_replies:
                        if current_time - self.last_replies[last_reply_key] < 300:  # 5 minutes
                            return
                    
                    # Send auto-reply
                    await event.reply(reply_message)
                    self.last_replies[last_reply_key] = current_time
                    
                    # Log the auto-reply
                    sender_name = getattr(sender, 'first_name', 'Unknown') or getattr(sender, 'username', f'ID:{sender_id}')
                    logger.info(f"AUTO-REPLY: {phone} replied to {sender_name} ({sender_id})")
                    
                except Exception as e:
                    logger.error(f"Error in auto-reply handler for {phone}: {str(e)}")
            
            logger.info(f"Auto-reply handler setup for {phone}: {reply_message[:50]}...")
            
        except Exception as e:
            logger.error(f"Failed to setup auto-reply for {phone}: {str(e)}")

    async def start(self):
        """Start the auto-reply bot for all accounts."""
        try:
            logger.info("🤖 Starting Auto-Reply Bot...")
            
            # Get all accounts
            accounts = self.account_manager.get_accounts()
            active_accounts = [acc for acc in accounts if acc and acc.get('is_active', True)]
            
            logger.info(f"Found {len(active_accounts)} active accounts")
            
            # Set up clients for each account
            for account in active_accounts:
                phone = account.get('phone')
                if not phone:
                    continue
                
                try:
                    client = await self.create_client(phone, account)
                    if client:
                        await self.setup_auto_reply(client, phone)
                        self.clients[phone] = client
                        logger.info(f"✅ Auto-reply active for {phone}")
                    else:
                        logger.warning(f"⚠️ Failed to setup auto-reply for {phone}")
                        
                except Exception as e:
                    logger.error(f"❌ Error setting up {phone}: {str(e)}")
                    continue
            
            if not self.clients:
                logger.error("❌ No clients were set up successfully")
                return
            
            logger.info(f"✅ Auto-reply bot started for {len(self.clients)} accounts")
            logger.info("📨 Bot is now listening for messages...")
            logger.info("Press Ctrl+C to stop")
            
            # Keep running
            try:
                while True:
                    await asyncio.sleep(60)  # Check every minute
                    # Log status every hour
                    current_time = int(time.time())
                    if current_time % 3600 == 0:
                        logger.info(f"🔄 Auto-reply bot running - {len(self.clients)} accounts active")
            except KeyboardInterrupt:
                logger.info("🛑 Stopping auto-reply bot...")
                
        except Exception as e:
            logger.error(f"Error starting auto-reply bot: {str(e)}")
        finally:
            # Disconnect all clients
            for phone, client in self.clients.items():
                try:
                    await client.disconnect()
                    logger.info(f"🔌 Disconnected {phone}")
                except Exception as e:
                    logger.error(f"Error disconnecting {phone}: {str(e)}")

async def main():
    """Main function."""
    bot = AutoReplyBot()
    await bot.start()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 Auto-reply bot stopped by user")
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        import traceback
        traceback.print_exc() 