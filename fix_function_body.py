#!/usr/bin/env python3
"""
ENHANCED INDENTATION FIX: Resolves indentation errors in stop_joining_task function and its body
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_function_indentation():
    """Fix the indentation error in the stop_joining_task function and its body."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # Find the problematic function
        function_start_idx = None
        for i, line in enumerate(lines):
            if "def stop_joining_task(self, task_id):" in line:
                function_start_idx = i
                break
        
        if function_start_idx is None:
            print("⚠️ Could not find the stop_joining_task function")
            return False
        
        print(f"Found stop_joining_task function at line {function_start_idx + 1}")
        
        # Find the previous function to determine correct indentation level
        prev_function_idx = None
        for i in range(function_start_idx - 1, 0, -1):
            if "def " in lines[i] and ":" in lines[i]:
                prev_function_idx = i
                break
        
        if prev_function_idx is None:
            print("⚠️ Could not find a previous function for reference")
            return False
        
        # Get the indentation of the previous function
        prev_indent = len(lines[prev_function_idx]) - len(lines[prev_function_idx].lstrip())
        print(f"Previous function at line {prev_function_idx + 1} has {prev_indent} spaces of indentation")
        
        # Fix the function definition indentation
        current_line = lines[function_start_idx]
        current_indent = len(current_line) - len(current_line.lstrip())
        
        if current_indent != prev_indent:
            lines[function_start_idx] = " " * prev_indent + current_line.lstrip()
            print(f"Fixed function definition indentation from {current_indent} to {prev_indent} spaces")
        
        # Find the function body and fix its indentation
        body_indent = prev_indent + 4  # Standard 4-space indentation for function body
        
        # Find where the function body starts
        body_start_idx = function_start_idx + 1
        
        # Find where the function body ends (next function at same level or less)
        body_end_idx = len(lines)
        for i in range(body_start_idx, len(lines)):
            line = lines[i].rstrip()
            if line and not line.isspace():  # Skip empty lines
                line_indent = len(lines[i]) - len(lines[i].lstrip())
                if line_indent <= prev_indent and "def " in lines[i] and ":" in lines[i]:
                    body_end_idx = i
                    break
        
        # Fix the indentation of each line in the function body
        for i in range(body_start_idx, body_end_idx):
            line = lines[i].rstrip()
            if line and not line.isspace():  # Skip empty lines
                # Preserve relative indentation within the function body
                current_indent = len(lines[i]) - len(lines[i].lstrip())
                if current_indent == 0:  # If no indentation, add body indentation
                    lines[i] = " " * body_indent + lines[i].lstrip() + "\n"
                elif current_indent < body_indent:  # If indentation is less than expected
                    lines[i] = " " * body_indent + lines[i].lstrip() + "\n"
        
        # Write the fixed content back
        with open("main.py", "w", encoding="utf-8") as f:
            f.writelines(lines)
        
        print(f"✅ Fixed indentation of stop_joining_task function and its body")
        return True
    except Exception as e:
        print(f"❌ Error fixing indentation: {str(e)}")
        return False

def main():
    print("🚨 ENHANCED INDENTATION FIX: Resolving indentation errors in stop_joining_task function...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fixed = fix_function_indentation()
    
    if fixed:
        print("\n✅ Enhanced indentation fix applied successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fix was applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 