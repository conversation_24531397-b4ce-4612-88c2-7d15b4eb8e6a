#!/usr/bin/env python3
"""
Fix specific errors at exact line numbers in main.py
"""

import os
import shutil

def fix_specific_errors():
    """Fix specific errors at exact line numbers in main.py"""
    print("=== Fixing specific errors at exact line numbers ===")
    
    input_file = "main.py"
    output_file = "main_clean.py"
    
    # Create a backup of the original file
    if not os.path.exists(input_file + ".original"):
        shutil.copy2(input_file, input_file + ".original")
        print(f"Created backup at {input_file}.original")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Get the exact error lines
    line_659 = lines[658] if len(lines) > 658 else None
    print(f"Line 659: {repr(line_659)}")
    
    # Fix the indentation error at line 659
    if line_659 and "def auto_refresh_missing_account_info" in line_659:
        print("Fixing indentation at line 659")
        lines[658] = "    def auto_refresh_missing_account_info(self):\n"
        
        # Fix indentation of the method body (next 10 lines to be safe)
        for i in range(659, min(669, len(lines))):
            if lines[i].strip() and not lines[i].lstrip().startswith("def "):
                lines[i] = "        " + lines[i].lstrip()
    
    # Fix the syntax error at line 4013
    line_4013 = lines[4012] if len(lines) > 4012 else None
    print(f"Line 4013: {repr(line_4013)}")
    
    # Look for the problematic try block
    try_line = None
    for i in range(4000, 4013):
        if i < len(lines) and "try:" in lines[i]:
            try_line = i
            break
    
    if try_line is not None:
        print(f"Found try block at line {try_line+1}")
        
        # Check if this try block has an except
        try_indent = len(lines[try_line]) - len(lines[try_line].lstrip())
        has_except = False
        
        for i in range(try_line + 1, 4013):
            if i >= len(lines):
                break
                
            line_indent = len(lines[i]) - len(lines[i].lstrip())
            if line_indent == try_indent:
                if lines[i].lstrip().startswith(("except", "finally")):
                    has_except = True
                    break
        
        if not has_except:
            print("Adding missing except block")
            indent = " " * try_indent
            # Add an except block just before line 4013
            lines.insert(4012, f"{indent}except Exception as e:\n{indent}    print(f\"Error: {{e}}\")\n")
    
    # Write the fixed content to a new file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"\nFixed specific errors and saved to {output_file}")
    
    # Create batch files
    with open("run_clean.bat", "w") as f:
        f.write(f"""@echo off
echo Running TG Checker with cleaned code...
python {output_file}
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_clean.bat")
    
    # Create Kurdish version
    with open("run_clean_kurdish.bat", "w") as f:
        f.write(f"""@echo off
echo TG Checker - Barnama pakrakrawa...
python {output_file}
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_clean_kurdish.bat")
    
    return True

if __name__ == "__main__":
    fix_specific_errors() 