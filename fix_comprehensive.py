import re
import os
import sys
import shutil

def fix_comprehensive(filename="main_final_fixed.py"):
    """Fix all syntax issues comprehensively."""
    # Find the right file to fix
    if not os.path.exists(filename):
        candidate_files = [
            "main_final.py",
            "main_fixed.py",
            "main.py"
        ]
        
        for candidate in candidate_files:
            if os.path.exists(candidate):
                filename = candidate
                print(f"Using {filename} as the input file")
                break
    
    # Create a backup
    backup_file = f"{filename}.backup"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix all issues
    content = fix_trailing_colons(content)
    content = fix_indentation_issues(content)
    
    # Save to a new file
    output_file = "main_comprehensive_fixed.py"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed all issues and saved to {output_file}")
    return output_file

def fix_trailing_colons(content):
    """Fix all trailing colons that cause syntax errors."""
    # Split into lines
    lines = content.split('\n')
    fixed_count = 0
    
    # Fix each line
    for i in range(len(lines)):
        line = lines[i]
        
        # Check for statement-ending colons that shouldn't be there
        if any(pattern in line for pattern in [
            "self.logger.",
            ".emit(",
            "return",
            "print(",
            ".setText(",
        ]) and line.strip().endswith(':'):
            # Remove the trailing colon
            lines[i] = line.rstrip(':')
            fixed_count += 1
            print(f"Fixed invalid colon on line {i+1}")
        
        # Check for method calls ending with colons
        method_call_pattern = r'^(\s*)(.+\.(emit|info|error|warning|debug|setText|append|write)\([^)]*\))(:)(\s*)$'
        match = re.match(method_call_pattern, line)
        if match:
            # Remove the trailing colon
            lines[i] = match.group(1) + match.group(2) + match.group(5)
            fixed_count += 1
            print(f"Fixed invalid method call colon on line {i+1}")
    
    print(f"Fixed {fixed_count} trailing colons")
    return '\n'.join(lines)

def fix_indentation_issues(content):
    """Fix indentation issues in the code."""
    fixed_count = 0
    
    # Fix indentation after if statements
    pattern_if = r'(\s+)(if\s+[^:]+:)\s*\n\s*([^\s])'
    content = re.sub(
        pattern_if,
        lambda m: f"{m.group(1)}{m.group(2)}\n{m.group(1)}    {m.group(3)}",
        content
    )
    
    # Fix indentation after for statements
    pattern_for = r'(\s+)(for\s+[^:]+:)\s*\n\s*([^\s])'
    content = re.sub(
        pattern_for,
        lambda m: f"{m.group(1)}{m.group(2)}\n{m.group(1)}    {m.group(3)}",
        content
    )
    
    # Fix indentation after else statements
    pattern_else = r'(\s+)(else:)\s*\n\s*([^\s])'
    content = re.sub(
        pattern_else,
        lambda m: f"{m.group(1)}{m.group(2)}\n{m.group(1)}    {m.group(3)}",
        content
    )
    
    # Fix indentation after except statements
    pattern_except = r'(\s+)(except[^:]*:)\s*\n\s*([^\s])'
    content = re.sub(
        pattern_except,
        lambda m: f"{m.group(1)}{m.group(2)}\n{m.group(1)}    {m.group(3)}",
        content
    )
    
    # Fix try statements without except/finally
    pattern_try = r'(\s+)(try:)\s*\n((?:.*\n)*?)(?!\1\s*except|\1\s*finally)'
    
    def fix_try_block(match):
        indent = match.group(1)
        try_statement = match.group(2)
        try_body = match.group(3)
        
        # Only add except if there isn't one already
        if not re.search(r'\s*except', try_body):
            return f"{indent}{try_statement}\n{try_body}{indent}except Exception as e:\n{indent}    self.logger.error(f\"Error: {{str(e)}}\")\n"
        return match.group(0)
    
    content = re.sub(pattern_try, fix_try_block, content)
    
    print(f"Fixed indentation issues")
    return content

if __name__ == "__main__":
    output_file = fix_comprehensive()
    
    # Create batch files
    batch_content = """@echo off
echo Running TG Checker with all syntax issues fixed...
python main_comprehensive_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_comprehensive_fixed.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Jarandni programi chakkrawi tawaw...
echo Hamu kishakani syntax charesar kra.
python main_comprehensive_fixed.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Bo zanini ziatr sairi faily log bka.
    pause
)
pause
"""
    
    with open("run_comprehensive_fixed_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print(f"Created batch files to run the fixed application")
    print(f"You can now run: python {output_file}")
    print(f"Or use the batch files: run_comprehensive_fixed.bat or run_comprehensive_fixed_kurdish.bat") 