import os
import json
import sqlite3
import logging
from datetime import datetime
import asyncio
import threading
import time
import signal
import atexit
from contextlib import contextmanager
from tg_client import TelegramClient

# Track all open connections globally for proper cleanup
_OPEN_CONNECTIONS = set()
_CONN_LOCK = threading.RLock()

def _exit_handler():
    """Clean up any open database connections on program exit."""
    with _CONN_LOCK:
        for conn in list(_OPEN_CONNECTIONS):
            try:
                conn.close()
            except:
                pass
        _OPEN_CONNECTIONS.clear()

# Register the exit handler
atexit.register(_exit_handler)

def fix_db_lock(db_path):
    """Utility function to fix database lock issues by creating a backup and recovering if necessary."""
    try:
        # First try to create a backup of the current database
        backup_path = f"{db_path}.backup_{int(datetime.now().timestamp())}"
        import shutil
        
        # Only backup if the database file exists
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            logging.info(f"Created database backup at {backup_path}")
            
            try:
                # Try to connect with DELETE journal mode
                conn = sqlite3.connect(db_path, timeout=120.0)
                
                # Try to switch to DELETE journal mode
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.execute("PRAGMA busy_timeout=120000")  # 2 minute timeout
                conn.commit()
                
                # Run VACUUM to compact the database
                conn.execute("VACUUM")
                conn.commit()
                
                # Switch back to WAL mode with optimized settings
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA busy_timeout=120000")
                conn.execute("PRAGMA wal_autocheckpoint=1000")
                conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping
                conn.commit()
                
                conn.close()
                logging.info("Database cleanup completed successfully")
                return True
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    logging.warning("Database still locked during fix attempt, trying alternative method")
                    # Database is still locked, try alternative approach
                    try:
                        # Wait a bit for locks to clear
                        time.sleep(3)
                        
                        # Delete the current database and restore from backup
                        if os.path.exists(db_path):
                            os.remove(db_path)
                            time.sleep(1)
                            
                        # Restore from backup
                        shutil.copy2(backup_path, db_path)
                        logging.info(f"Restored database from backup after lock issues")
                        return True
                    except Exception as restore_err:
                        logging.error(f"Failed to restore database from backup: {str(restore_err)}")
                else:
                    logging.error(f"Database error during fix attempt: {str(e)}")
        else:
            logging.warning(f"Database file {db_path} not found, can't fix locks")
            return False
            
    except Exception as e:
        logging.error(f"Error fixing database lock: {str(e)}")
        return False
        
    return False

class AccountManager:
    """Manages Telegram accounts for the application."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self.accounts = []
        self.clients = {}
        
        # Add database connection lock for thread safety
        self._db_lock = threading.RLock()
        
        # Track SQLite busy handler calls
        self._busy_handler_calls = 0
        self._max_busy_retries = 25  # Maximum times to retry when database is busy
        
        # Initialize database
        self._init_database()
        
        # Load accounts from database
        self._load_accounts()
    
    def _sqlite_busy_handler(self, tries):
        """Custom busy handler for SQLite to handle database locks."""
        self._busy_handler_calls += 1
        
        if tries >= self._max_busy_retries:
            self.logger.error(f"SQLite busy handler giving up after {tries} attempts")
            return 0  # Give up and return SQLITE_BUSY
            
        # Exponential backoff with jitter
        import random
        delay = min(0.1 * (1.5 ** tries) + random.uniform(0, 0.1), 10.0)
        
        self.logger.warning(f"Database is busy, waiting {delay:.2f}s (attempt {tries+1}/{self._max_busy_retries})")
        time.sleep(delay)
        
        # If we've had several retries, try to fix the database
        if tries == 10:
            try:
                self.logger.warning("Attempting emergency database recovery during busy wait")
                fix_db_lock(self.db_path)
            except Exception as e:
                self.logger.error(f"Failed to perform emergency recovery: {str(e)}")
        
        return 1  # Tell SQLite to retry
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 8  # Increased from 5 to 8
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                with self._db_lock:
                    # Reset busy handler call counter
                    self._busy_handler_calls = 0
                    
                    # Connect with higher timeout (increased from 60s to 120s)
                    conn = sqlite3.connect(self.db_path, timeout=120.0)
                    
                    # Track this connection for cleanup
                    with _CONN_LOCK:
                        _OPEN_CONNECTIONS.add(conn)
                    
                    conn.row_factory = sqlite3.Row
                    
                    # Enable WAL mode for better concurrent access with optimal settings
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA busy_timeout=120000")  # Increased from 60000 to 120000ms (2 minutes)
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA wal_autocheckpoint=1000")  # Checkpoint after 1000 pages
                    conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping for better performance
                    conn.execute("PRAGMA temp_store=MEMORY")  # Use memory for temp storage
                    
                    yield conn
                    
                    # Remove from tracked connections if yielded successfully
                    with _CONN_LOCK:
                        if conn in _OPEN_CONNECTIONS:
                            _OPEN_CONNECTIONS.remove(conn)
                    
                    # Close connection if it's still open
                    if conn:
                        conn.close()
                        conn = None
                    
                    return  # Successfully got connection, exit retry loop
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    # Database is locked, retry after delay
                    self.logger.warning(f"Database is locked, retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})")
                    if conn:
                        try:
                            # Remove from tracked connections
                            with _CONN_LOCK:
                                if conn in _OPEN_CONNECTIONS:
                                    _OPEN_CONNECTIONS.remove(conn)
                            conn.close()
                        except:
                            pass
                    conn = None
                    
                    # Increase retry delay for next attempt (exponential backoff)
                    retry_delay = min(retry_delay * 1.5, 10.0)
                    
                    # On the second attempt, try to fix database locks
                    if attempt in [1, 3, 5]:
                        try:
                            # Different approaches on different retry attempts
                            if attempt == 1:
                                self.logger.warning("First retry: Attempting standard database fix")
                                fix_db_lock(self.db_path)
                            elif attempt == 3:
                                self.logger.warning("Third retry: Attempting aggressive database recovery")
                                # Try to terminate any lingering connections first
                                self._kill_all_connections()
                                time.sleep(2)
                                fix_db_lock(self.db_path)
                            elif attempt == 5:
                                self.logger.warning("Fifth retry: Attempting database rebuild from backup")
                                self._rebuild_from_backup()
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database locks: {str(fix_err)}")
                    
                    time.sleep(retry_delay)
                    continue
                else:
                    # Other error or max retries reached
                    if conn:
                        try:
                            # Remove from tracked connections
                            with _CONN_LOCK:
                                if conn in _OPEN_CONNECTIONS:
                                    _OPEN_CONNECTIONS.remove(conn)
                            conn.rollback()
                            conn.close()
                        except:
                            pass
                    self.logger.error(f"Database error after {attempt+1} attempts: {str(e)}")
                    raise
            except Exception as e:
                if conn:
                    try:
                        # Remove from tracked connections
                        with _CONN_LOCK:
                            if conn in _OPEN_CONNECTIONS:
                                _OPEN_CONNECTIONS.remove(conn)
                        conn.rollback()
                        conn.close()
                    except:
                        pass
                self.logger.error(f"Database error: {str(e)}")
                raise
            finally:
                if conn and attempt == max_retries - 1:
                    try:
                        # Remove from tracked connections
                        with _CONN_LOCK:
                            if conn in _OPEN_CONNECTIONS:
                                _OPEN_CONNECTIONS.remove(conn)
                        conn.close()
                    except:
                        pass
    
    def _kill_all_connections(self):
        """Attempt to forcefully close all database connections."""
        try:
            self.logger.info("Forcefully closing all database connections")
            
            # Close all tracked connections
            with _CONN_LOCK:
                for conn in list(_OPEN_CONNECTIONS):
                    try:
                        conn.close()
                    except:
                        pass
                _OPEN_CONNECTIONS.clear()
            
            # Reset all client connections
            self.clients = {}
            
            # Wait a moment for connections to fully close
            time.sleep(1)
            
            self.logger.info("All database connections have been forcefully closed")
        except Exception as e:
            self.logger.error(f"Error killing connections: {str(e)}")
    
    def _rebuild_from_backup(self):
        """Attempt to rebuild the database from the most recent backup."""
        try:
            import glob
            import shutil
            
            # Find the most recent backup
            backups = glob.glob(f"{self.db_path}.backup_*")
            if not backups:
                self.logger.warning("No backup files found to rebuild from")
                return False
            
            # Sort by modification time (newest first)
            backups.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            most_recent = backups[0]
            
            self.logger.info(f"Rebuilding database from backup: {most_recent}")
            
            # Kill all connections first
            self._kill_all_connections()
            
            # Rename current database if it exists
            if os.path.exists(self.db_path):
                corrupt_name = f"{self.db_path}.corrupt_{int(datetime.now().timestamp())}"
                os.rename(self.db_path, corrupt_name)
                self.logger.info(f"Renamed current database to {corrupt_name}")
            
            # Copy the backup to the main database file
            shutil.copy2(most_recent, self.db_path)
            
            # Make sure WAL files are removed if they exist
            for ext in ['-wal', '-shm']:
                wal_file = f"{self.db_path}{ext}"
                if os.path.exists(wal_file):
                    try:
                        os.remove(wal_file)
                        self.logger.info(f"Removed WAL file: {wal_file}")
                    except Exception as e:
                        self.logger.warning(f"Could not remove WAL file {wal_file}: {str(e)}")
            
            self.logger.info("Database successfully rebuilt from backup")
            return True
        except Exception as e:
            self.logger.error(f"Failed to rebuild database from backup: {str(e)}")
            return False
    
    def _init_database(self):
        """Initialize the SQLite database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create accounts table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                ''')
                
                # Add new columns to existing accounts table if they don't exist
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_age_days INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN is_aged INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN daily_group_limit INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN last_age_check TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add missing columns that were causing errors
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN name TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                    
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN username TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add account_type column for session vs normal accounts
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_type TEXT DEFAULT 'normal'")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Create errors table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create blacklist table for permanently deleted accounts
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deleted_accounts (
                        phone TEXT PRIMARY KEY,
                        deleted_at TEXT,
                        reason TEXT DEFAULT 'user_deleted'
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def _load_accounts(self):
        """Load accounts from the database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM accounts")
                rows = cursor.fetchall()
                
                self.accounts = [dict(row) for row in rows]
                
            self.logger.info(f"Loaded {len(self.accounts)} accounts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
    
    # The rest of the AccountManager class methods remain unchanged...
    
    def clean_database(self):
        """Clean and optimize the database to resolve locking issues."""
        try:
            self.logger.info("Starting enhanced database cleanup...")
            
            # Close all active connections first
            self._kill_all_connections()
            
            # Create backup first
            backup_path = f"{self.db_path}.backup_{int(datetime.now().timestamp())}"
            try:
                import shutil
                if os.path.exists(self.db_path):
                    shutil.copy2(self.db_path, backup_path)
                    self.logger.info(f"Created database backup at {backup_path}")
            except Exception as backup_err:
                self.logger.error(f"Failed to create database backup: {str(backup_err)}")
            
            # Try to fix database locks
            try:
                with sqlite3.connect(self.db_path, timeout=120.0) as conn:
                    # Try to switch to DELETE journal mode
                    conn.execute("PRAGMA journal_mode=DELETE")
                    conn.execute("PRAGMA synchronous=OFF")
                    conn.commit()
                    
                    # Check integrity
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]
                    
                    if integrity_result == "ok":
                        self.logger.info("Database integrity check passed")
                    else:
                        self.logger.warning(f"Database integrity issues found: {integrity_result}")
                        # If database is corrupted, restore from backup
                        conn.close()
                        self.logger.info("Restoring from backup due to integrity issues...")
                        time.sleep(1)
                        os.remove(self.db_path)
                        shutil.copy2(backup_path, self.db_path)
                        self.logger.info("Database restored from backup due to integrity issues")
                        return True
                    
                    # Run VACUUM to compact the database
                    self.logger.info("Running VACUUM on database...")
                    conn.execute("VACUUM")
                    conn.commit()
                    
                    # Run ANALYZE to optimize query planning
                    self.logger.info("Running ANALYZE on database...")
                    conn.execute("ANALYZE")
                    conn.commit()
                    
                    # Set optimal settings for regular operation
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=120000")
                    conn.execute("PRAGMA wal_autocheckpoint=1000")
                    conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping
                    conn.commit()
                    
                    self.logger.info("Database optimized successfully")
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    self.logger.warning("Database still locked during optimization, trying recovery...")
                    # Try to recover from backup
                    try:
                        # Wait to ensure no active operations
                        time.sleep(3)
                        
                        # Make sure no connections are active
                        self._kill_all_connections()
                        
                        # Try to remove the database file
                        if os.path.exists(self.db_path):
                            os.remove(self.db_path)
                            self.logger.info("Removed locked database file")
                            time.sleep(1)
                        
                        # Restore from backup
                        import shutil
                        shutil.copy2(backup_path, self.db_path)
                        self.logger.info("Database restored from backup")
                        
                        # Reload accounts
                        self._load_accounts()
                        
                        return True
                    except Exception as recover_err:
                        self.logger.error(f"Failed to recover database: {str(recover_err)}")
                else:
                    self.logger.error(f"Database error during optimization: {str(e)}")
            except Exception as e:
                self.logger.error(f"Error during database optimization: {str(e)}")
            
            # Reload accounts
            self._load_accounts()
            
            return True
        except Exception as e:
            self.logger.error(f"Error during database cleanup: {str(e)}")
            return False 