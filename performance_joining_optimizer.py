"""
High-Performance Joining System
Fixes critical performance and stability issues in the joining functionality.
"""

import asyncio
import json
import sqlite3
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Callable
import logging
from concurrent.futures import Future

try:
    from performance_manager import <PERSON><PERSON><PERSON>askManager, ResourceMonitor, DatabasePool
    PERFORMANCE_AVAILABLE = True
except ImportError:
    PERFORMANCE_AVAILABLE = False
    print("Warning: Performance manager not available - using fallback mode")

class FloodWaitManager:
    """Manages flood waits per account without blocking other accounts."""
    
    def __init__(self):
        self.active_waits: Dict[str, float] = {}  # phone -> end_time
        self.callbacks: Dict[str, List[Callable]] = {}  # phone -> [callbacks]
        self._lock = threading.Lock()
        
    def add_flood_wait(self, phone: str, seconds: int, callback: Optional[Callable] = None):
        """Add flood wait for specific account."""
        with self._lock:
            end_time = time.time() + seconds
            self.active_waits[phone] = end_time
            
            if callback:
                if phone not in self.callbacks:
                    self.callbacks[phone] = []
                self.callbacks[phone].append(callback)
        
        # Start background timer for this account
        threading.Thread(
            target=self._wait_and_callback,
            args=(phone, seconds),
            daemon=True,
            name=f"FloodWait-{phone}"
        ).start()
    
    def _wait_and_callback(self, phone: str, seconds: int):
        """Wait for flood wait to end and execute callbacks."""
        time.sleep(seconds)
        
        with self._lock:
            # Remove from active waits
            if phone in self.active_waits:
                del self.active_waits[phone]
            
            # Execute callbacks
            callbacks = self.callbacks.get(phone, [])
            if phone in self.callbacks:
                del self.callbacks[phone]
        
        # Execute callbacks outside lock
        for callback in callbacks:
            try:
                callback()
            except Exception as e:
                print(f"Error in flood wait callback: {e}")
    
    def is_in_flood_wait(self, phone: str) -> bool:
        """Check if account is currently in flood wait."""
        with self._lock:
            if phone not in self.active_waits:
                return False
            return time.time() < self.active_waits[phone]
    
    def get_remaining_time(self, phone: str) -> int:
        """Get remaining flood wait time in seconds."""
        with self._lock:
            if phone not in self.active_waits:
                return 0
            remaining = self.active_waits[phone] - time.time()
            return max(0, int(remaining))

class OptimizedUIUpdater:
    """Batches and optimizes UI updates to prevent freezing."""
    
    def __init__(self, main_app):
        self.main_app = main_app
        self.pending_updates: Dict[str, Dict] = {}
        self.update_timer = None
        self.update_interval = 2.0  # Update every 2 seconds max
        self._lock = threading.Lock()
        
    def queue_task_update(self, task_id: str, **kwargs):
        """Queue a task update instead of immediate update."""
        with self._lock:
            if task_id not in self.pending_updates:
                self.pending_updates[task_id] = {}
            
            self.pending_updates[task_id].update(kwargs)
            
            # Start update timer if not already running
            if self.update_timer is None:
                self.update_timer = threading.Timer(self.update_interval, self._process_updates)
                self.update_timer.start()
    
    def _process_updates(self):
        """Process all pending updates in batch."""
        with self._lock:
            updates_to_process = self.pending_updates.copy()
            self.pending_updates.clear()
            self.update_timer = None
        
        if not updates_to_process:
            return
        
        try:
            # Batch database updates
            if hasattr(self.main_app, 'db_pool') and self.main_app.db_pool:
                self._batch_database_updates(updates_to_process)
            else:
                # Fallback to individual updates
                for task_id, updates in updates_to_process.items():
                    self.main_app.update_joining_task_status(task_id, **updates)
            
            # Single UI refresh for all updates
            if hasattr(self.main_app, 'refresh_joining_tasks'):
                self.main_app.refresh_joining_tasks()
                
        except Exception as e:
            print(f"Error processing UI updates: {e}")
    
    def _batch_database_updates(self, updates: Dict[str, Dict]):
        """Perform batch database updates."""
        try:
            conn = self.main_app.db_pool.get_connection()
            cursor = conn.cursor()
            
            for task_id, task_updates in updates.items():
                if not task_updates:
                    continue
                
                # Build update query
                set_clauses = ["updated_at = ?"]
                params = [datetime.now().isoformat()]
                
                for key, value in task_updates.items():
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
                
                params.append(task_id)
                
                cursor.execute(
                    f"UPDATE joining_tasks SET {', '.join(set_clauses)} WHERE id = ?",
                    params
                )
            
            conn.commit()
            self.main_app.db_pool.return_connection(conn)
            
        except Exception as e:
            print(f"Error in batch database update: {e}")

class HighPerformanceJoiningSystem:
    """High-performance joining system that fixes all critical performance issues."""
    
    def __init__(self, main_app):
        self.main_app = main_app
        self.flood_wait_manager = FloodWaitManager()
        self.ui_updater = OptimizedUIUpdater(main_app)
        
        # Performance manager integration
        if PERFORMANCE_AVAILABLE and hasattr(main_app, 'performance_integration'):
            self.task_manager = main_app.performance_integration.task_manager
            self.use_performance_manager = True
        else:
            self.task_manager = None
            self.use_performance_manager = False
        
        # Track active tasks efficiently
        self.active_tasks: Dict[str, Future] = {}
        self.task_stats: Dict[str, Dict] = {}
        
        self.logger = logging.getLogger(__name__)
    
    def start_joining_task(self, task_id: str) -> bool:
        """Start a joining task using high-performance system."""
        try:
            if task_id not in self.main_app.joining_tasks:
                self.logger.error(f"Task {task_id} not found")
                return False
            
            task = self.main_app.joining_tasks[task_id]
            phone = task['account_phone']
            
            # Check flood wait status
            if self.flood_wait_manager.is_in_flood_wait(phone):
                remaining = self.flood_wait_manager.get_remaining_time(phone)
                self.main_app.log_joining_message(
                    "warning", task_id, 
                    f"⏳ Account in flood wait for {remaining} seconds - will auto-start when ready"
                )
                
                # Schedule task to start after flood wait
                self.flood_wait_manager.add_flood_wait(
                    phone, remaining, 
                    lambda: self.start_joining_task(task_id)
                )
                return True
            
            # Use performance manager if available
            if self.use_performance_manager:
                future = self.task_manager.submit_task(
                    'joining',
                    self._run_optimized_joining_task,
                    task,
                    callback=lambda f: self._task_completed(task_id, f)
                )
                self.active_tasks[task_id] = future
            else:
                # Fallback to optimized threading
                future = self._submit_fallback_task(task)
                self.active_tasks[task_id] = future
            
            # Initialize task stats
            self.task_stats[task_id] = {
                'started_at': time.time(),
                'successful_joins': 0,
                'failed_joins': 0,
                'current_index': 0,
                'status': 'running'
            }
            
            # Queue UI update
            self.ui_updater.queue_task_update(task_id, status='running')
            
            self.main_app.log_joining_message("info", task_id, "🚀 Task started with high-performance system")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start task {task_id}: {e}")
            self.main_app.log_joining_message("error", task_id, f"Failed to start: {e}")
            return False
    
    def _submit_fallback_task(self, task):
        """Submit task using fallback threading."""
        from concurrent.futures import ThreadPoolExecutor
        
        if not hasattr(self, '_fallback_executor'):
            self._fallback_executor = ThreadPoolExecutor(
                max_workers=20, thread_name_prefix="FallbackJoin"
            )
        
        return self._fallback_executor.submit(self._run_optimized_joining_task, task)
    
    def _run_optimized_joining_task(self, task):
        """Run joining task with optimizations."""
        task_id = task['id']
        
        try:
            # Set up async environment
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run the optimized async task
            return loop.run_until_complete(self._join_groups_optimized(task))
            
        except Exception as e:
            self.logger.error(f"Task {task_id} error: {e}")
            self.main_app.log_joining_message("error", task_id, f"Task error: {e}")
            raise
        finally:
            # Clean up
            try:
                loop.close()
            except:
                pass
    
    async def _join_groups_optimized(self, task):
        """Optimized group joining with minimal UI blocking."""
        task_id = task['id']
        account_phone = task['account_phone']
        
        # Parse group links
        group_links = [link.strip() for link in task['group_links'].split('\n') if link.strip()]
        total_groups = len(group_links)
        
        # Get current progress
        current_index = task.get('current_index', 0)
        successful_joins = task.get('successful_joins', 0)
        failed_joins = task.get('failed_joins', 0)
        
        # Initialize Telegram client
        client = None
        try:
            client = await self._get_telegram_client(account_phone)
            if not client:
                raise Exception("Failed to initialize Telegram client")
            
            # Get current memberships once to avoid duplicate joins
            current_memberships = await self._get_current_memberships(client)
            
            # Process groups with optimized batching
            batch_size = 5  # Process in small batches for responsiveness
            for batch_start in range(current_index, total_groups, batch_size):
                batch_end = min(batch_start + batch_size, total_groups)
                
                # Check if task should stop
                if task_id not in self.active_tasks:
                    self.main_app.log_joining_message("info", task_id, "Task stopped by user")
                    break
                
                # Process batch
                for i in range(batch_start, batch_end):
                    group_link = group_links[i]
                    
                    try:
                        result = await self._join_single_group_optimized(
                            client, group_link, task_id, current_memberships
                        )
                        
                        if result == 'success':
                            successful_joins += 1
                            self.main_app.log_joining_message("success", task_id, f"✅ Joined: {group_link}")
                        elif result == 'already_joined':
                            self.main_app.log_joining_message("info", task_id, f"⏭️ Already joined: {group_link}")
                        elif result == 'flood_wait':
                            # Handle flood wait without stopping other accounts
                            self.main_app.log_joining_message("warning", task_id, f"⏳ Flood wait detected")
                            return 'flood_wait'  # Exit this task, others continue
                        else:
                            failed_joins += 1
                            
                    except Exception as e:
                        failed_joins += 1
                        self.main_app.log_joining_message("error", task_id, f"❌ Error: {group_link} - {e}")
                    
                    current_index = i + 1
                    
                    # Update stats
                    self.task_stats[task_id].update({
                        'current_index': current_index,
                        'successful_joins': successful_joins,
                        'failed_joins': failed_joins
                    })
                    
                    # Apply delay between joins
                    if i < total_groups - 1:
                        await self._apply_smart_delay(task)
                
                # Batch UI update (every 5 groups)
                self.ui_updater.queue_task_update(
                    task_id,
                    status='running',
                    current_index=current_index,
                    successful_joins=successful_joins,
                    failed_joins=failed_joins
                )
                
                # Small yield for UI responsiveness
                await asyncio.sleep(0.1)
            
            # Task completed
            self.ui_updater.queue_task_update(
                task_id,
                status='completed',
                current_index=current_index,
                successful_joins=successful_joins,
                failed_joins=failed_joins
            )
            
            # Final summary
            total_attempts = successful_joins + failed_joins
            self.main_app.log_joining_message("success", task_id, 
                f"🎯 Task completed! Joined: {successful_joins}, Failed: {failed_joins}, Total: {total_attempts}")
            
            return 'completed'
            
        except Exception as e:
            self.main_app.log_joining_message("error", task_id, f"Task failed: {e}")
            raise
        finally:
            if client:
                try:
                    await client.disconnect()
                except:
                    pass
    
    async def _join_single_group_optimized(self, client, group_link: str, task_id: str, current_memberships: set):
        """Join single group with optimization and flood wait handling."""
        try:
            # Check if already a member
            if await self._is_already_member_fast(client, group_link, current_memberships):
                return 'already_joined'
            
            # Try to join
            result = await client(JoinChannelRequest(group_link))
            
            if result:
                return 'success'
            else:
                return 'failed'
                
        except Exception as e:
            error_str = str(e).lower()
            
            # Handle flood wait
            if 'flood' in error_str and 'wait' in error_str:
                # Extract wait time
                import re
                wait_match = re.search(r'(\d+)', error_str)
                wait_seconds = int(wait_match.group(1)) if wait_match else 300
                
                # Add flood wait for this account only
                phone = task_id  # Use task_id as phone identifier
                self.flood_wait_manager.add_flood_wait(
                    phone, wait_seconds,
                    lambda: self.start_joining_task(task_id)
                )
                
                self.main_app.log_joining_message("warning", task_id, 
                    f"⏳ Flood wait {wait_seconds}s - other accounts continue normally")
                
                return 'flood_wait'
            
            # Other errors
            self.main_app.log_joining_message("error", task_id, f"Join error: {e}")
            return 'failed'
    
    async def _apply_smart_delay(self, task):
        """Apply smart delay that doesn't block UI."""
        try:
            settings = json.loads(task.get('settings', '{}'))
            delay_min = settings.get('delay_min', 30)
            delay_max = settings.get('delay_max', 90)
            use_random = settings.get('use_random_delay', True)
            
            if use_random:
                import random
                delay = random.randint(delay_min, delay_max)
            else:
                delay = delay_min
            
            # Use chunked delay for responsiveness
            chunk_size = 5
            elapsed = 0
            
            while elapsed < delay:
                await asyncio.sleep(min(chunk_size, delay - elapsed))
                elapsed += chunk_size
                
                # Check if task should stop
                if task['id'] not in self.active_tasks:
                    return
                    
        except Exception as e:
            self.logger.error(f"Error in smart delay: {e}")
            await asyncio.sleep(30)  # Default delay
    
    async def _get_telegram_client(self, phone: str):
        """Get Telegram client for account."""
        # Implementation depends on main app's client management
        # This should integrate with existing client creation logic
        return None  # Placeholder
    
    async def _get_current_memberships(self, client) -> set:
        """Get current memberships efficiently."""
        # Implementation for getting current memberships
        return set()  # Placeholder
    
    async def _is_already_member_fast(self, client, group_link: str, current_memberships: set) -> bool:
        """Fast membership check."""
        # Optimized membership checking
        return False  # Placeholder
    
    def _task_completed(self, task_id: str, future: Future):
        """Handle task completion."""
        try:
            result = future.result()
            self.logger.info(f"Task {task_id} completed with result: {result}")
        except Exception as e:
            self.logger.error(f"Task {task_id} failed: {e}")
        finally:
            # Clean up
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            if task_id in self.task_stats:
                del self.task_stats[task_id]
    
    def stop_joining_task(self, task_id: str):
        """Stop a joining task."""
        if task_id in self.active_tasks:
            future = self.active_tasks[task_id]
            future.cancel()
            del self.active_tasks[task_id]
            
        self.ui_updater.queue_task_update(task_id, status='stopped')
        self.main_app.log_joining_message("info", task_id, "🛑 Task stopped")
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        return {
            'active_tasks': len(self.active_tasks),
            'flood_waits': len(self.flood_wait_manager.active_waits),
            'use_performance_manager': self.use_performance_manager,
            'task_stats': self.task_stats.copy()
        }

# Global instance
joining_system: Optional[HighPerformanceJoiningSystem] = None

def initialize_joining_system(main_app):
    """Initialize the high-performance joining system."""
    global joining_system
    joining_system = HighPerformanceJoiningSystem(main_app)
    return joining_system

def get_joining_system() -> Optional[HighPerformanceJoiningSystem]:
    """Get the global joining system instance."""
    return joining_system 