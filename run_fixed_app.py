#!/usr/bin/env python
"""
Run TG Checker with <PERSON><PERSON><PERSON>ling
----------------------------------
This script serves as a safe entry point for running the fixed TG Checker application.
It includes error handling and will log any issues that occur.
"""

import sys
import os
import traceback
import logging
from datetime import datetime

# Set up logging
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"tg_checker_run_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def main():
    """Main entry point for running the TG Checker application."""
    print("TG Checker - Running Fixed Application")
    print("--------------------------------------")
    
    # Find the best version of the fixed application to run
    candidates = [
        "main_manual_fixed.py",    # Manual fixes (most reliable)
        "main_final_fix.py",       # Comprehensive fixes
        "main_fixed.py",           # Initial fixes
        "main.py"                  # Original as fallback
    ]
    
    target_file = None
    for candidate in candidates:
        if os.path.exists(candidate):
            target_file = candidate
            print(f"Found {candidate}, will use this version")
            break
    
    if not target_file:
        print("Error: Could not find any version of the application to run")
        return 1
    
    try:
        logging.info(f"Starting TG Checker using {target_file}")
        print(f"Starting application from {target_file}...")
        
        # Import the module dynamically
        import importlib.util
        spec = importlib.util.spec_from_file_location("tg_checker", target_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # Run the main function if it exists
        if hasattr(module, 'main'):
            logging.info("Calling main() function")
            module.main()
        else:
            # Alternative approach using exec
            logging.info("No main() function found, running module directly")
            with open(target_file, 'r', encoding='utf-8') as f:
                code = compile(f.read(), target_file, 'exec')
                exec(code, globals())
        
        return 0
        
    except Exception as e:
        logging.error(f"Error running TG Checker: {str(e)}")
        logging.error(traceback.format_exc())
        
        print("\nError running TG Checker application:")
        print(f"  {str(e)}")
        print(f"\nSee log file for details: {log_file}")
        
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        if exit_code != 0:
            print("\nApplication exited with errors")
            input("Press Enter to exit...")
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
        logging.info("Application stopped by user (KeyboardInterrupt)")
        sys.exit(0) 