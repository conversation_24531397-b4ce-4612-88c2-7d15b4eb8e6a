import re
import os
import sys
import shutil

def fix_line_2676(filename, output_filename):
    """Fix the indentation error after 'for' statement on line 2676."""
    print(f"Fixing indentation issue in {filename}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find line 2676
    lines = content.split('\n')
    if len(lines) < 2677:
        print(f"File only has {len(lines)} lines, cannot fix line 2676")
        return False
    
    # Get line 2676 and 2677
    line_2676 = lines[2675]  # 0-indexed, so 2675 is the 2676th line
    line_2677 = lines[2676]  # 0-indexed, so 2676 is the 2677th line
    
    print(f"Line 2676: {line_2676}")
    print(f"Line 2677: {line_2677}")
    
    # Check if this is a for loop with indentation issue
    if "for" in line_2676 and line_2676.strip().endswith(":"):
        indentation = re.match(r"(\s*)", line_2676).group(1)
        expected_indent = indentation + "    "
        
        # Check if next line has proper indentation
        if not line_2677.startswith(expected_indent) and line_2677.strip():
            # Fix the indentation
            lines[2676] = expected_indent + line_2677.lstrip()
            print(f"Fixed line 2677 to: {lines[2676]}")
            
            # Write the fixed content
            fixed_content = '\n'.join(lines)
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write(fixed_content)
            
            print(f"Fixed file saved to {output_filename}")
            return True
    
    print("No indentation issue found in line 2676-2677")
    return False

if __name__ == "__main__":
    input_filename = "main_comprehensive_fixed.py"
    output_filename = "main_indentation_fixed.py"
    
    # If file exists, use it
    if not os.path.exists(input_filename):
        print(f"File {input_filename} not found, trying main.py")
        input_filename = "main.py"
    
    # Create a backup
    backup_file = f"{input_filename}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(input_filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Fix the file
    success = fix_line_2676(input_filename, output_filename)
    
    if success:
        # Create a batch file to run the fixed application
        batch_content = """@echo off
echo Running fixed TG Checker application...
python main_indentation_fixed.py
pause
"""
        
        with open("run_indentation_fixed_app.bat", "w") as f:
            f.write(batch_content)
        
        print("Created run_indentation_fixed_app.bat to run the fixed application.")
        print(f"You can now run the fixed file: python {output_filename} or use the batch file.")
    else:
        print("No changes were made to the file.") 