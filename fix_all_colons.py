import re
import os
import sys

def fix_all_colons(filename="main_final.py"):
    """Fix all invalid trailing colons in the file."""
    # Check if the file exists, otherwise use main_fixed.py
    if not os.path.exists(filename):
        if os.path.exists("main_fixed.py"):
            filename = "main_fixed.py"
            print(f"File {filename} not found, using main_fixed.py instead")
        else:
            filename = "main.py"
            print(f"File main_fixed.py not found, using main.py instead")
    
    # Create a backup
    backup_file = f"{filename}.bak2"
    if not os.path.exists(backup_file):
        import shutil
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix all invalid trailing colons
    content = fix_logger_statements(content)
    
    # Save to a new file
    final_file = "main_final_fixed.py"
    with open(final_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed all invalid colons and saved to {final_file}")
    return final_file

def fix_logger_statements(content):
    """Fix all logger statements with invalid trailing colons."""
    # Split into lines
    lines = content.split('\n')
    fixed_count = 0
    
    # Pattern to match logger statements with trailing colons
    pattern = r'^(\s*)(self\.logger\.[a-z]+\(f".*?"(?:\s*[,)].*?)?)(:)(\s*)$'
    
    # Fix each line
    for i in range(len(lines)):
        match = re.match(pattern, lines[i])
        if match:
            # Remove the trailing colon
            lines[i] = match.group(1) + match.group(2) + match.group(4)
            fixed_count += 1
            print(f"Fixed invalid colon on line {i+1}")
        
        # Additional check for logger statement with trailing colon
        if "self.logger." in lines[i] and lines[i].strip().endswith(':'):
            # Remove the trailing colon
            lines[i] = lines[i].rstrip(':')
            fixed_count += 1
            print(f"Fixed invalid colon on line {i+1} using secondary check")
    
    print(f"Total fixes: {fixed_count} lines")
    return '\n'.join(lines)

if __name__ == "__main__":
    final_file = fix_all_colons()
    
    # Create a batch file to run the final fixed application
    batch_content = """@echo off
echo Running TG Checker application with all issues fixed...
python main_final_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_final_fixed_app.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Jarandni programi chakkrawi tawaw...
echo Hamu kishakanmon charesar kird.
python main_final_fixed.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Bo zanini ziatr sairi faily log bka.
    pause
)
pause
"""
    
    with open("run_final_fixed_app_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print("Created batch files to run the final fixed application.")
    print(f"You can now run the fixed file: python {final_file}")
    print("Or use one of the batch files: run_final_fixed_app.bat or run_final_fixed_app_kurdish.bat") 