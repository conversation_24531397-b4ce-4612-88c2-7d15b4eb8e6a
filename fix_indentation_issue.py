#!/usr/bin/env python3
"""
Directly fix the indentation issue on line 659 in main.py
"""

import os
import shutil

def fix_indentation():
    """Fix the indentation issue on line 659 in main.py"""
    print("Fixing indentation issue on line 659...")
    
    # Create a backup of the original file
    input_file = "main.py"
    backup_file = f"{input_file}.bak_indentation"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the line with the definition of auto_refresh_missing_account_info
    target_line = None
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            target_line = i
            print(f"Found target line at line {i+1}")
            break
    
    if target_line is None:
        print("Error: Could not find the target method")
        return False
    
    # Find the TGCheckerApp class definition to determine correct indentation
    class_line = None
    for i in range(target_line, 0, -1):
        if "class TGCheckerApp" in lines[i]:
            class_line = i
            print(f"Found class definition at line {i+1}")
            break
    
    if class_line is None:
        print("Error: Could not find the class definition")
        return False
    
    # Get the indentation of the class definition
    class_indent = len(lines[class_line]) - len(lines[class_line].lstrip())
    correct_indent = class_indent + 4  # Class methods should be indented 4 spaces from class definition
    
    # Fix the indentation of the method and its body
    method_end = target_line + 1
    while method_end < len(lines):
        # Skip empty lines
        if not lines[method_end].strip():
            method_end += 1
            continue
        
        # Get the indentation of the current line
        current_indent = len(lines[method_end]) - len(lines[method_end].lstrip())
        
        # If we've reached a line with less or equal indentation to what a class method should have,
        # then we've reached the end of the method
        if current_indent <= correct_indent and lines[method_end].strip():
            if not lines[method_end].lstrip().startswith("def "):
                # This is a line in the method body with incorrect indentation
                pass
            else:
                # This is the next method
                break
        
        method_end += 1
    
    print(f"Method body spans from line {target_line+1} to {method_end}")
    
    # Fix the indentation of the method definition
    current_indent = len(lines[target_line]) - len(lines[target_line].lstrip())
    lines[target_line] = ' ' * correct_indent + lines[target_line].lstrip()
    
    # Fix the indentation of the method body
    for i in range(target_line + 1, method_end):
        if not lines[i].strip():  # Skip empty lines
            continue
        
        line_indent = len(lines[i]) - len(lines[i].lstrip())
        if line_indent > current_indent:
            # This is a line inside the method that's indented further
            # Maintain its relative indentation
            relative_indent = line_indent - current_indent
            lines[i] = ' ' * (correct_indent + relative_indent) + lines[i].lstrip()
        else:
            # This is a line at the same level as the method definition
            lines[i] = ' ' * (correct_indent + 4) + lines[i].lstrip()
    
    # Write the fixed content back to the file
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("Fixed indentation issue on line 659")
    print("You can now run: python main.py")
    
    return True

if __name__ == "__main__":
    fix_indentation() 