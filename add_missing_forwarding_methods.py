import re

def add_missing_forwarding_methods():
    """Add missing forwarding methods to main.py"""
    print("Adding missing forwarding methods to main.py...")
    
    # Read the file content
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
    except UnicodeDecodeError:
        # Try alternate encoding
        with open("main.py", "r", encoding="latin-1") as f:
            content = f.read()
    
    # Make a backup
    with open("main.py.forwarding.bak", "w", encoding="utf-8") as f:
        f.write(content)
    
    # Define missing methods with proper triple quotes escaping
    run_task_method = '''
    def run_task(self, task_id):
        """Run a forwarding task by ID."""
        task = self.db.get_task(task_id)
        if task:
            self.set_task(task)
            return self.process_task()
        return {"success": False, "error": "Task not found"}
    '''
    
    create_forwarder_task_method = '''
    def create_forwarder_task(self, name, source_group, target_groups, message_filter=None, delay=0):
        """Create a new forwarding task."""
        try:
            task_id = self.db.create_task(name, source_group, target_groups, message_filter, delay)
            return {"success": True, "task_id": task_id}
        except Exception as e:
            return {"success": False, "error": str(e)}
    '''
    
    # Check if methods already exist
    if "def run_task(self, task_id)" not in content:
        # Find the TelegramForwarder class
        forwarder_class_match = re.search(r'class TelegramForwarder[^:]*:', content)
        if forwarder_class_match:
            # Find process_task method
            process_task_match = re.search(r'async def process_task\(self\)[^:]*:', content)
            if process_task_match:
                # Insert run_task method before process_task
                insert_pos = process_task_match.start()
                content = content[:insert_pos] + run_task_method + content[insert_pos:]
                print("Added run_task method")
    
    if "def create_forwarder_task(" not in content:
        # Find the end of the TelegramForwarder class
        class_end_match = re.search(r'class TelegramForwarder[^:]*:.*?(?=\nclass|\Z)', content, re.DOTALL)
        if class_end_match:
            # Insert create_forwarder_task method at the end of the class
            insert_pos = class_end_match.end()
            content = content[:insert_pos] + create_forwarder_task_method + content[insert_pos:]
            print("Added create_forwarder_task method")
    
    # Write the modified content back
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("Forwarding methods have been added to main.py")

if __name__ == "__main__":
    add_missing_forwarding_methods() 