# Usage Checker Feature

## Overview
The Usage Checker feature automatically tracks how many groups each account can safely check based on its age, using @TGDNAbot for age detection.

## Features Implemented

### ✅ Daily Usage Tracking per Account
- Each account displays its daily group checking limit based on age
- Information is permanently saved in the database
- Logs are saved under "Usage Checker" log type

### ✅ Auto-Age Detection
- Automatically checks account age using @TGDNAbot when adding new accounts
- Auto-checks during sync for accounts without age data
- No manual input required - fully automated

### ✅ Background Processing
- Handles everything automatically:
  - Sends `/start` command to @TGDNAbot
  - Reads and parses the bot's response
  - Calculates safe group limits based on age
  - Updates account records

### ✅ UI Log Integration
- New "Usage Checker" option in log dropdown
- Dedicated log file that persists indefinitely
- Thread-safe logging from background operations

## Database Schema Changes

New columns added to `accounts` table:
- `account_age_days` (INTEGER) - Account age in days
- `is_aged` (INTEGER) - Whether account is considered aged (0/1)
- `daily_group_limit` (INTEGER) - Calculated daily group limit
- `last_age_check` (TEXT) - Timestamp of last age check

## Group Limit Calculation Logic

### Base Limits by Age:
- **Aged accounts (1+ year)**: 800 base limit
- **6+ months**: 600 base limit  
- **3+ months**: 400 base limit
- **1+ month**: 200 base limit
- **New accounts**: 100 base limit

### Age Bonus:
- Additional 10 groups per month of age
- Maximum bonus: 200 groups
- Total cap: 1000 groups/day

### Examples:
```
Account: +33333 | Aged: Yes | Group Limit: 830
Account: +332322 | Aged: Yes | Group Limit: 500
Account: +123456 | Aged: No | Group Limit: 120
```

## UI Changes

### Accounts Tab:
- New "Group Limit" column showing daily limits
- Color coding:
  - 🟢 Green: 500+ groups (High capacity)
  - 🟠 Orange: 200-499 groups (Medium capacity)  
  - 🔴 Red: <200 groups (Low capacity)
- New "Check Ages" button for manual age verification

### Logs Tab:
- New "Usage Checker" option in log type dropdown
- Dedicated filtering for usage logs only
- Persistent logs that don't get auto-deleted

## Automatic Triggers

### When Adding New Accounts:
1. Account is added to database
2. Background thread automatically starts age check
3. Contacts @TGDNAbot for age information
4. Updates account with age data and group limit
5. Logs the result to Usage Checker logs

### During Sync Operations:
1. System identifies accounts without age data
2. Auto-initiates age checking for those accounts  
3. Updates all accounts with fresh age information
4. Logs all activities

### Age Check Frequency:
- New accounts: Immediately after addition
- Existing accounts: Re-checked every 30 days
- Manual checks: Available via "Check Ages" button

## Log Format

Usage Checker logs follow this format:
```
2025-05-23 18:46:30 - USAGE - INFO - Account: +********** | Aged: Yes | Group Limit: 850
2025-05-23 18:46:35 - USAGE - INFO - Starting automatic age check for new account: +**********
2025-05-23 18:46:40 - USAGE - INFO - Age check completed: 45 days, aged: false
```

## Error Handling

### Bot Communication Errors:
- Graceful fallback to default values
- Detailed error logging for troubleshooting
- Retry logic for temporary failures

### Rate Limiting:
- 5-second delays between age check requests
- Proper handling of Telegram rate limits
- Background processing to avoid UI blocking

## File Structure

### New Files:
- `logs/usage_checker.log` - Dedicated usage checker logs

### Modified Files:
- `account_manager.py` - Age checking logic
- `main.py` - UI integration and threading
- `logger.py` - Usage checker logging functions

## Integration Points

### With Existing Features:
- ✅ Thread-safe UI updates using signals
- ✅ Proper database schema migration
- ✅ Backward compatibility with existing accounts
- ✅ Integration with sync and monitoring systems

### Future Extensibility:
- Daily usage tracking can be extended
- Additional bot integrations possible
- Group limit algorithms can be refined
- Usage analytics and reporting ready

## Usage Instructions

### Automatic Operation:
1. Add accounts normally - age checking happens automatically
2. Click "Sync" to check ages for existing accounts
3. View results in Accounts tab and Usage Checker logs

### Manual Operation:
1. Go to Accounts tab
2. Click "Check Ages" to manually verify all accounts
3. Monitor progress in Usage Checker logs
4. View updated group limits in accounts table

### Monitoring:
1. Open Logs tab
2. Select "Usage Checker" from dropdown
3. View all age checking activities and results
4. Export logs if needed for analysis

## Benefits

### For Users:
- ✅ **Automatic age tracking** - No manual work required
- ✅ **Safe group limits** - Avoid account restrictions
- ✅ **Visual indicators** - Easy to see account capacity
- ✅ **Persistent logging** - Full audit trail of age checks

### For Operations:
- ✅ **Risk mitigation** - Prevents over-usage of accounts
- ✅ **Capacity planning** - Know total checking capacity
- ✅ **Account health** - Monitor account aging over time
- ✅ **Compliance** - Stay within Telegram's usage guidelines

The Usage Checker feature provides a comprehensive solution for managing account usage safely and efficiently, with full automation and detailed tracking capabilities. 