#!/usr/bin/env python3
"""
🧪 CLASSIFICATION VERIFICATION
Tests the classification logic with user's exact examples
"""

def simulate_classification():
    """Simulate how the examples should be classified based on recent logs."""
    
    print("🧪 CLASSIFICATION VERIFICATION")
    print("=" * 50)
    
    # Current filter settings (after adjustment)
    min_members = 400  # Lowered from 500
    min_message_time_hours = 1
    min_total_messages = 100
    
    # Test data based on recent logs and user requirements
    test_examples = {
        # Use<PERSON> wants these in Groups_Valid_Filter
        "imperiamarket": {
            "expected_folder": "Groups_Valid_Filter",
            "member_count": "TBD", 
            "activity_hours": "TBD",
            "total_messages": "TBD",
            "type": "group"
        },
        "infocoindogroup": {
            "expected_folder": "Groups_Valid_Filter",
            "member_count": 497,  # From logs
            "activity_hours": 0.0,  # From logs
            "total_messages": 3587,  # From logs
            "type": "group"
        },
        "instaaccountbuying": {
            "expected_folder": "Groups_Valid_Filter", 
            "member_count": 3831,  # From logs
            "activity_hours": 0.0,  # From logs
            "total_messages": 341052,  # From logs
            "type": "group"
        },
        
        # User wants these in Groups_Valid_Only
        "hyipinformer_com": {
            "expected_folder": "Groups_Valid_Only",
            "member_count": 1233,  # From logs
            "activity_hours": 23.33,  # From logs - FAILS activity filter
            "total_messages": 100,  # From logs
            "type": "group"
        },
        "islamic_hacker_army": {
            "expected_folder": "Groups_Valid_Only",
            "member_count": 875,  # From logs
            "activity_hours": 15.59,  # From logs - FAILS activity filter
            "total_messages": 3846,  # From logs
            "type": "group"
        },
        
        # User wants these in Topics_Groups_Only_Valid
        "RareHandle": {
            "expected_folder": "Topics_Groups_Only_Valid",
            "member_count": 2318,  # From logs
            "activity_hours": 0.0,  # From logs
            "total_messages": 64424,  # From logs
            "type": "topic"
        },
        
        # User wants these in Channels_Only_Valid
        "wallethuntersio": {
            "expected_folder": "Channels_Only_Valid",
            "member_count": 33535,  # From logs
            "activity_hours": 17.59,  # From logs
            "total_messages": 153,  # From logs
            "type": "channel"
        },
        
        # User wants these in Invalid_Groups_Channels
        "beklopptundgeil": {
            "expected_folder": "Invalid_Groups_Channels",
            "member_count": 0,  # Private/invalid
            "activity_hours": 999,
            "total_messages": 0,
            "type": "unknown"
        },
        "belgieiswakker": {
            "expected_folder": "Invalid_Groups_Channels", 
            "member_count": 0,  # Username not found
            "activity_hours": 999,
            "total_messages": 0,
            "type": "unknown"
        }
    }
    
    print(f"\n🔧 CURRENT FILTER SETTINGS:")
    print(f"📊 Min Members: {min_members}")
    print(f"📊 Min Activity: ≤ {min_message_time_hours} hour(s)")
    print(f"📊 Min Messages: {min_total_messages}")
    
    print(f"\n🧪 TESTING CLASSIFICATION LOGIC:")
    print("-" * 50)
    
    results = {
        "Groups_Valid_Filter": [],
        "Groups_Valid_Only": [],
        "Topics_Groups_Only_Valid": [],
        "Channels_Only_Valid": [],
        "Invalid_Groups_Channels": []
    }
    
    for name, data in test_examples.items():
        print(f"\n🔍 Testing: {name}")
        
        # Determine actual classification based on logic
        if data["type"] == "topic":
            actual_folder = "Topics_Groups_Only_Valid"
            reason = "Topic group"
            
        elif data["type"] == "channel":
            actual_folder = "Channels_Only_Valid"
            reason = "Channel"
            
        elif data["type"] == "unknown":
            actual_folder = "Invalid_Groups_Channels"
            reason = "Invalid/Private"
            
        elif data["type"] == "group":
            # Check filters for groups
            if (isinstance(data["member_count"], int) and 
                isinstance(data["activity_hours"], (int, float)) and 
                isinstance(data["total_messages"], int)):
                
                member_check = data["member_count"] >= min_members
                activity_check = data["activity_hours"] <= min_message_time_hours
                message_check = data["total_messages"] >= min_total_messages
                
                passes_filters = member_check and activity_check and message_check
                
                print(f"   Members: {data['member_count']} >= {min_members} = {member_check}")
                print(f"   Activity: {data['activity_hours']}h <= {min_message_time_hours}h = {activity_check}")
                print(f"   Messages: {data['total_messages']} >= {min_total_messages} = {message_check}")
                print(f"   PASSES ALL: {passes_filters}")
                
                if passes_filters:
                    actual_folder = "Groups_Valid_Filter"
                    reason = "Passes all filters"
                else:
                    actual_folder = "Groups_Valid_Only"
                    failures = []
                    if not member_check:
                        failures.append(f"members({data['member_count']}<{min_members})")
                    if not activity_check:
                        failures.append(f"activity({data['activity_hours']}h>{min_message_time_hours}h)")
                    if not message_check:
                        failures.append(f"messages({data['total_messages']}<{min_total_messages})")
                    reason = f"Fails: {', '.join(failures)}"
            else:
                actual_folder = "Groups_Valid_Only"
                reason = "Missing data - defaulting to valid only"
        
        expected_folder = data["expected_folder"]
        
        # Check if classification matches user expectation
        if actual_folder == expected_folder:
            print(f"   ✅ CORRECT: {actual_folder} ({reason})")
            results[actual_folder].append(name)
        else:
            print(f"   ❌ MISMATCH: Expected {expected_folder}, Got {actual_folder} ({reason})")
            print(f"      🔧 FIX NEEDED: Adjust logic or filters")
            results[actual_folder].append(f"{name} (SHOULD BE IN {expected_folder})")
    
    print(f"\n📊 FINAL CLASSIFICATION RESULTS:")
    print("=" * 50)
    
    for folder, items in results.items():
        print(f"\n📁 {folder}:")
        if items:
            for item in items:
                print(f"   • {item}")
        else:
            print("   (empty)")
    
    # Check if all examples match expectations
    mismatches = []
    for name, data in test_examples.items():
        found_in = None
        for folder, items in results.items():
            if any(name in item for item in items):
                found_in = folder
                break
        
        if found_in != data["expected_folder"]:
            mismatches.append(f"{name}: expected {data['expected_folder']}, got {found_in}")
    
    if mismatches:
        print(f"\n❌ MISMATCHES FOUND:")
        for mismatch in mismatches:
            print(f"   • {mismatch}")
        print(f"\n🔧 RECOMMENDATION: The filter settings may need further adjustment")
    else:
        print(f"\n✅ ALL EXAMPLES MATCH EXPECTATIONS!")
        print(f"🎉 Classification logic is working correctly!")

if __name__ == "__main__":
    simulate_classification() 