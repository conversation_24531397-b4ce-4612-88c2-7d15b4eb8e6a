"""
CRITICAL UI PERFORMANCE FIX for TG Checker
Fixes: UI freezing, "Not Responding" issues, slow updates

This replaces the broken UI update system that causes the app to freeze constantly.
"""

import threading
import time
import weakref
from collections import defaultdict, deque
from typing import Dict, Any, Callable, Optional, List
from datetime import datetime
import queue
import asyncio
from PyQt5.QtCore import QObject, QTimer, pyqtSignal, QThread, QMutex, QMutexLocker
from PyQt5.QtWidgets import QApplication


class UIUpdateBatcher(QObject):
    """
    CRITICAL FIX: Batches UI updates to prevent freezing.
    
    Instead of updating UI immediately for every change, this batches updates
    and applies them periodically to prevent the "Not Responding" issue.
    """
    
    # Signals for thread-safe UI updates
    batch_update_signal = pyqtSignal(dict)  # Batched updates
    log_message_signal = pyqtSignal(str, str, str)  # level, task_id, message
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # Batching system
        self.update_queue = defaultdict(dict)  # task_id -> updates
        self.batch_timer = QTimer()
        self.batch_timer.timeout.connect(self._process_batch)
        self.batch_interval = 2000  # 2 seconds
        self.mutex = QMutex()
        
        # Debouncing system (prevent duplicate rapid updates)
        self.last_update_time = defaultdict(float)
        self.min_update_interval = 1.0  # Minimum 1 second between updates
        
        # Log batching
        self.log_queue = deque(maxlen=100)  # Keep last 100 log messages
        self.log_timer = QTimer()
        self.log_timer.timeout.connect(self._process_log_batch)
        self.log_timer.setSingleShot(True)
        
        # Performance tracking
        self.update_count = 0
        self.batch_count = 0
        self.skipped_count = 0
        
        # Connect signals
        self.batch_update_signal.connect(self._apply_batched_updates)
        self.log_message_signal.connect(self._handle_log_message)
    
    def queue_update(self, task_id: str, **updates):
        """
        Queue an update for batching instead of applying immediately.
        This prevents UI freezing from too many rapid updates.
        """
        with QMutexLocker(self.mutex):
            current_time = time.time()
            
            # Check if we should debounce this update
            if task_id in self.last_update_time:
                time_since_last = current_time - self.last_update_time[task_id]
                if time_since_last < self.min_update_interval:
                    # Too soon since last update, just merge the data
                    self.update_queue[task_id].update(updates)
                    self.skipped_count += 1
                    return
            
            # Add to queue
            self.update_queue[task_id].update(updates)
            self.last_update_time[task_id] = current_time
            self.update_count += 1
            
            # Start batch timer if not already running
            if not self.batch_timer.isActive():
                self.batch_timer.start(self.batch_interval)
    
    def _process_batch(self):
        """Process batched updates safely."""
        with QMutexLocker(self.mutex):
            if not self.update_queue:
                return
            
            # Copy and clear the queue
            batch_data = dict(self.update_queue)
            self.update_queue.clear()
            self.batch_count += 1
        
        # Emit the batched updates (thread-safe)
        self.batch_update_signal.emit(batch_data)
        
        # Stop timer until next batch
        self.batch_timer.stop()
    
    def _apply_batched_updates(self, batch_data: Dict[str, Dict[str, Any]]):
        """Apply batched updates to the UI (runs on main thread)."""
        try:
            # Get the main app instance
            app = QApplication.instance()
            if not app:
                return
            
            # Find the main window
            main_window = None
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'update_joining_task_status'):
                    main_window = widget
                    break
            
            if not main_window:
                return
            
            # Apply all updates efficiently
            for task_id, updates in batch_data.items():
                try:
                    # Use the fixed database update method
                    from database_fix import fix_joining_task_update
                    success = fix_joining_task_update(main_window, task_id, **updates)
                    
                    if not success:
                        print(f"Warning: Failed to update task {task_id}")
                        
                except Exception as e:
                    print(f"Error updating task {task_id}: {e}")
            
            # Refresh the UI once after all updates (not for each update)
            try:
                if hasattr(main_window, 'refresh_joining_tasks_optimized'):
                    main_window.refresh_joining_tasks_optimized()
                elif hasattr(main_window, 'refresh_joining_tasks'):
                    # Use background refresh to prevent freezing
                    threading.Thread(
                        target=self._safe_background_refresh, 
                        args=(main_window,),
                        daemon=True,
                        name="SafeUIRefresh"
                    ).start()
            except Exception as e:
                print(f"Error refreshing UI: {e}")
                
        except Exception as e:
            print(f"Error in batch update processing: {e}")
    
    def _safe_background_refresh(self, main_window):
        """Safely refresh UI in background thread."""
        try:
            # Small delay to allow database operations to complete
            time.sleep(0.5)
            
            # Check if main window is still valid
            if hasattr(main_window, 'refresh_joining_tasks'):
                main_window.refresh_joining_tasks()
        except Exception as e:
            print(f"Background refresh error: {e}")
    
    def queue_log_message(self, level: str, task_id: str, message: str):
        """Queue log message for batching."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_queue.append((timestamp, level, task_id, message))
        
        # Start log timer with short delay
        if not self.log_timer.isActive():
            self.log_timer.start(500)  # 500ms delay for log batching
    
    def _process_log_batch(self):
        """Process batched log messages."""
        if not self.log_queue:
            return
        
        # Process all queued logs
        batch = list(self.log_queue)
        self.log_queue.clear()
        
        for timestamp, level, task_id, message in batch:
            self.log_message_signal.emit(level, task_id, message)
    
    def _handle_log_message(self, level: str, task_id: str, message: str):
        """Handle log message on main thread."""
        try:
            app = QApplication.instance()
            if not app:
                return
            
            # Find the main window
            main_window = None
            for widget in app.topLevelWidgets():
                if hasattr(widget, 'log_joining_message'):
                    main_window = widget
                    break
            
            if main_window and hasattr(main_window, 'log_joining_message'):
                main_window.log_joining_message(level, task_id, message)
                
        except Exception as e:
            print(f"Error handling log message: {e}")
    
    def get_performance_stats(self) -> Dict[str, int]:
        """Get performance statistics."""
        return {
            'total_updates': self.update_count,
            'batches_processed': self.batch_count,
            'updates_skipped': self.skipped_count,
            'queue_size': len(self.update_queue),
            'log_queue_size': len(self.log_queue)
        }


class ThreadSafeTaskManager(QObject):
    """
    CRITICAL FIX: Manages joining tasks in background threads to prevent UI blocking.
    """
    
    task_update_signal = pyqtSignal(str, dict)  # task_id, updates
    task_complete_signal = pyqtSignal(str, str)  # task_id, final_status
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.active_tasks = {}
        self.task_threads = {}
        self.ui_batcher = UIUpdateBatcher()
        self.mutex = QMutex()
        
        # Connect signals
        self.task_update_signal.connect(self._handle_task_update)
        self.task_complete_signal.connect(self._handle_task_complete)
    
    def start_task_optimized(self, task_data: Dict[str, Any], main_app):
        """Start a task with optimized threading."""
        task_id = task_data['id']
        
        with QMutexLocker(self.mutex):
            if task_id in self.active_tasks:
                print(f"Task {task_id} already running")
                return False
            
            # Create task thread
            task_thread = OptimizedTaskThread(task_data, main_app, self.ui_batcher)
            task_thread.task_update.connect(self._handle_task_update)
            task_thread.task_complete.connect(self._handle_task_complete)
            task_thread.finished.connect(lambda: self._cleanup_task(task_id))
            
            self.active_tasks[task_id] = task_data
            self.task_threads[task_id] = task_thread
            
            # Start the thread
            task_thread.start()
            return True
    
    def stop_task(self, task_id: str):
        """Stop a running task."""
        with QMutexLocker(self.mutex):
            if task_id in self.task_threads:
                thread = self.task_threads[task_id]
                thread.stop_requested = True
                thread.quit()
                thread.wait(5000)  # Wait up to 5 seconds
                self._cleanup_task(task_id)
    
    def _handle_task_update(self, task_id: str, updates: Dict[str, Any]):
        """Handle task update from thread."""
        self.ui_batcher.queue_update(task_id, **updates)
    
    def _handle_task_complete(self, task_id: str, final_status: str):
        """Handle task completion."""
        self.ui_batcher.queue_update(task_id, status=final_status)
        self._cleanup_task(task_id)
    
    def _cleanup_task(self, task_id: str):
        """Clean up completed task."""
        with QMutexLocker(self.mutex):
            self.active_tasks.pop(task_id, None)
            self.task_threads.pop(task_id, None)


class OptimizedTaskThread(QThread):
    """
    Optimized thread for running joining tasks without blocking UI.
    """
    
    task_update = pyqtSignal(str, dict)
    task_complete = pyqtSignal(str, str)
    
    def __init__(self, task_data: Dict[str, Any], main_app, ui_batcher: UIUpdateBatcher):
        super().__init__()
        self.task_data = task_data
        self.main_app_ref = weakref.ref(main_app)  # Weak reference to prevent memory leaks
        self.ui_batcher = ui_batcher
        self.stop_requested = False
    
    def run(self):
        """Run the task in background thread."""
        task_id = self.task_data['id']
        
        try:
            # Get main app
            main_app = self.main_app_ref()
            if not main_app:
                return
            
            # Set up async environment
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Run the optimized task
                result = loop.run_until_complete(self._run_task_async())
                self.task_complete.emit(task_id, result or 'completed')
            finally:
                loop.close()
                
        except Exception as e:
            print(f"Task thread error: {e}")
            self.task_complete.emit(task_id, 'failed')
    
    async def _run_task_async(self):
        """Run the actual task asynchronously."""
        main_app = self.main_app_ref()
        if not main_app:
            return 'failed'
        
        # Use the fixed joining method
        try:
            if hasattr(main_app, '_join_groups_high_performance_safe'):
                return await main_app._join_groups_high_performance_safe(self.task_data)
            else:
                # Fallback to standard method
                return await main_app._run_joining_task_async(self.task_data)
        except Exception as e:
            print(f"Async task error: {e}")
            return 'failed'


class DatabaseRefreshOptimizer:
    """
    CRITICAL FIX: Optimizes database refresh operations to prevent UI freezing.
    """
    
    def __init__(self, main_app):
        self.main_app_ref = weakref.ref(main_app)
        self.last_refresh_time = 0
        self.min_refresh_interval = 3.0  # Minimum 3 seconds between refreshes
        self.pending_refresh = False
        self.refresh_timer = None
    
    def request_refresh(self, immediate: bool = False):
        """Request a refresh with intelligent debouncing."""
        current_time = time.time()
        
        if immediate:
            self._do_refresh()
            return
        
        # Check if we need to debounce
        time_since_last = current_time - self.last_refresh_time
        if time_since_last < self.min_refresh_interval:
            # Schedule delayed refresh
            if not self.pending_refresh:
                self.pending_refresh = True
                delay = self.min_refresh_interval - time_since_last
                self.refresh_timer = threading.Timer(delay, self._delayed_refresh)
                self.refresh_timer.daemon = True
                self.refresh_timer.start()
        else:
            # Can refresh immediately
            self._do_refresh()
    
    def _delayed_refresh(self):
        """Execute delayed refresh."""
        self.pending_refresh = False
        self._do_refresh()
    
    def _do_refresh(self):
        """Actually perform the refresh."""
        main_app = self.main_app_ref()
        if not main_app:
            return
        
        self.last_refresh_time = time.time()
        
        # Run refresh in background thread to prevent UI blocking
        def background_refresh():
            try:
                if hasattr(main_app, 'refresh_joining_tasks'):
                    main_app.refresh_joining_tasks()
            except Exception as e:
                print(f"Background refresh error: {e}")
        
        threading.Thread(target=background_refresh, daemon=True, name="BgRefresh").start()


# CRITICAL INTEGRATION FUNCTIONS FOR MAIN.PY

_ui_batcher = None
_task_manager = None
_refresh_optimizer = None

def initialize_ui_performance_fixes(main_app):
    """CRITICAL: Initialize all UI performance fixes."""
    global _ui_batcher, _task_manager, _refresh_optimizer
    
    try:
        _ui_batcher = UIUpdateBatcher()
        _task_manager = ThreadSafeTaskManager()
        _refresh_optimizer = DatabaseRefreshOptimizer(main_app)
        
        # Replace broken methods with optimized versions
        if hasattr(main_app, '_queue_ui_update_optimized'):
            # Replace with our batcher
            main_app._queue_ui_update_optimized = lambda task_id, **kwargs: _ui_batcher.queue_update(task_id, **kwargs)
        
        if hasattr(main_app, 'log_joining_message'):
            # Replace with batched logging
            original_log = main_app.log_joining_message
            def batched_log(level, task_id, message):
                _ui_batcher.queue_log_message(level, task_id, message)
            main_app.log_joining_message = batched_log
        
        if hasattr(main_app, 'refresh_joining_tasks'):
            # Replace with optimized refresh
            main_app.refresh_joining_tasks_optimized = lambda: _refresh_optimizer.request_refresh()
        
        print("✅ UI Performance fixes initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize UI performance fixes: {e}")
        return False

def get_ui_batcher():
    """Get the global UI batcher instance."""
    return _ui_batcher

def get_task_manager():
    """Get the global task manager instance."""
    return _task_manager

def get_refresh_optimizer():
    """Get the global refresh optimizer instance."""
    return _refresh_optimizer

def fix_ui_freezing_for_task(main_app, task_data):
    """CRITICAL: Start a task with UI freezing prevention."""
    if _task_manager:
        return _task_manager.start_task_optimized(task_data, main_app)
    else:
        print("❌ Task manager not initialized")
        return False

def emergency_ui_cleanup(main_app):
    """Emergency cleanup for UI performance issues."""
    try:
        # Stop all batching timers
        if _ui_batcher:
            if hasattr(_ui_batcher, 'batch_timer'):
                _ui_batcher.batch_timer.stop()
            if hasattr(_ui_batcher, 'log_timer'):
                _ui_batcher.log_timer.stop()
        
        # Stop all task threads
        if _task_manager:
            with QMutexLocker(_task_manager.mutex):
                for task_id in list(_task_manager.task_threads.keys()):
                    _task_manager.stop_task(task_id)
        
        # Force garbage collection
        import gc
        collected = gc.collect()
        
        print(f"✅ Emergency UI cleanup completed - collected {collected} objects")
        return True
        
    except Exception as e:
        print(f"❌ Emergency UI cleanup failed: {e}")
        return False


if __name__ == "__main__":
    print("✅ UI Performance Fix module loaded successfully!")
    
    # Test the batcher
    from PyQt5.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    batcher = UIUpdateBatcher()
    
    # Test batching
    batcher.queue_update("test_task", status="running", progress=50)
    batcher.queue_update("test_task", progress=75)  # Should merge
    
    stats = batcher.get_performance_stats()
    print(f"Test stats: {stats}")
    
    app.exec_() 