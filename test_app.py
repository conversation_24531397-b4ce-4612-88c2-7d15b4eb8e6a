#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script for the TG Checker app
"""

import os
import sys
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger("test_app")

def main():
    """Main test function."""
    # Test importing the new app
    logger.info("Testing import of main_new.py")
    try:
        # Try to import the new app as a module
        import importlib.util
        spec = importlib.util.spec_from_file_location("main_new", "main_new.py")
        main_new = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(main_new)
        
        logger.info("Successfully imported main_new.py")
        logger.info("Import test passed!")
        
        return True
    except Exception as e:
        logger.error(f"Error importing main_new.py: {str(e)}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("All tests passed!")
        sys.exit(0)
    else:
        print("Tests failed.")
        sys.exit(1)
