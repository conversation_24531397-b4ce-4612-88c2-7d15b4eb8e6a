#!/usr/bin/env python3
"""
Create a clean version of main.py with fixed indentation and syntax
"""

import os
import re
import shutil

def create_clean_version():
    """Create a clean version of main.py with fixed syntax errors"""
    print("=== Creating clean version of main.py ===")
    
    # Create backup and output files
    input_file = "main.py"
    output_file = "main_clean_version.py"
    
    if not os.path.exists(input_file + ".original"):
        shutil.copy2(input_file, input_file + ".original")
        print(f"Created backup at {input_file}.original")
    
    # Read the file
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. Fix indentation for the problematic method (line 659)
    print("Fixing auto_refresh_missing_account_info method...")
    
    # Completely replace the problematic method with a fixed version
    # First, extract the method body
    method_pattern = r'def auto_refresh_missing_account_info\(self\):(.*?)(?=\n\s*def|\Z)'
    method_match = re.search(method_pattern, content, re.DOTALL)
    
    if method_match:
        method_body = method_match.group(1)
        # Clean up the body and ensure proper indentation
        lines = method_body.split('\n')
        cleaned_lines = []
        
        for line in lines:
            if line.strip():
                # Keep the content but fix indentation
                cleaned_lines.append("        " + line.strip())
            else:
                # Keep empty lines
                cleaned_lines.append("")
        
        # Create properly indented method
        fixed_method = "    def auto_refresh_missing_account_info(self):\n" + "\n".join(cleaned_lines)
        
        # Replace in the content
        content = re.sub(method_pattern, fixed_method, content, flags=re.DOTALL)
        print("Method fixed")
    else:
        print("Could not find the method to fix")
    
    # 2. Fix missing except blocks before else statements
    print("Fixing try-else blocks without except...")
    
    # Find all try blocks without except but with else
    matches = re.finditer(r'(\s+)try:\s*\n((?:.*\n)*?)(\s+)else:', content)
    fixed_content = content
    
    for match in matches:
        try_indent = match.group(1)
        try_body = match.group(2)
        else_part = match.group(3) + "else:"
        
        # Check if there's already an except
        if "except" not in try_body:
            # Insert an except block
            replacement = f"{try_indent}try:\n{try_body}{try_indent}except Exception as e:\n{try_indent}    print(f\"Error: {{e}}\")\n{else_part}"
            fixed_content = fixed_content.replace(match.group(0), replacement)
            print("Fixed a try-else block without except")
    
    # 3. Ensure Speed Check Time settings are present
    if "Speed Check Time Per 1 Group" not in fixed_content:
        print("Adding Speed Check Time settings...")
        
        # Find create_settings_tab method
        settings_match = re.search(r'def create_settings_tab\(self\):(.*?)(?=\n\s*def|\Z)', fixed_content, re.DOTALL)
        
        if settings_match:
            settings_method = settings_match.group(1)
            
            # Find a good insertion point
            if "# Filter settings" in settings_method:
                insert_point = settings_method.find("# Filter settings")
            elif "# Auto-check settings" in settings_method:
                insert_point = settings_method.find("# Auto-check settings")
            else:
                # Insert at the end of the method
                insert_point = len(settings_method)
            
            # Create settings group code
            speed_settings = """
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(1)
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(1)
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
"""
            
            # Insert settings
            new_settings_method = settings_method[:insert_point] + speed_settings + settings_method[insert_point:]
            fixed_content = fixed_content.replace(settings_method, new_settings_method)
            
            # Add update_speed_check_range method if it doesn't exist
            if "def update_speed_check_range" not in fixed_content:
                update_method = """
    def update_speed_check_range(self):
        # Ensure min seconds <= max seconds
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
    
"""
                # Find a good place to add this method
                insert_point = fixed_content.find("def create_settings_tab")
                before_insert = fixed_content[:insert_point]
                after_insert = fixed_content[insert_point:]
                fixed_content = before_insert + update_method + after_insert
            
            # Add code to save the settings
            save_settings_match = re.search(r'def save_settings\(self\):(.*?)(?=\n\s*def|\Z)', fixed_content, re.DOTALL)
            if save_settings_match:
                save_method = save_settings_match.group(1)
                save_code = """
            # Save Speed Check Time settings
            self.settings.setValue("min_check_seconds", self.min_seconds_input.value())
            self.settings.setValue("max_check_seconds", self.max_seconds_input.value())
"""
                # Find where to insert (before the QMessageBox.information)
                if "QMessageBox.information" in save_method:
                    insert_point = save_method.find("QMessageBox.information")
                    new_save_method = save_method[:insert_point] + save_code + save_method[insert_point:]
                    fixed_content = fixed_content.replace(save_method, new_save_method)
            
            # Make sure random is imported
            if "import random" not in fixed_content:
                import_match = re.search(r'import [^\n]+', fixed_content)
                if import_match:
                    import_line = import_match.group(0)
                    fixed_content = fixed_content.replace(import_line, import_line + "\nimport random")
            
            print("Speed Check Time settings added")
    
    # Write the fixed content to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print(f"\nCreated clean version at {output_file}")
    
    # Create batch files
    with open("run_clean_version.bat", "w") as f:
        f.write(f"""@echo off
echo Running TG Checker (Clean Version)...
python {output_file}
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_clean_version.bat")
    
    # Create Kurdish version
    with open("run_clean_version_kurdish.bat", "w") as f:
        f.write(f"""@echo off
echo TG Checker - Barnama pakrawa...
python {output_file}
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_clean_version_kurdish.bat")
    
    return True

if __name__ == "__main__":
    create_clean_version()