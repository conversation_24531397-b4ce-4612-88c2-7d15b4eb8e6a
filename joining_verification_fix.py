"""
CRITICAL JOINING VERIFICATION FIX for TG Checker
Fixes fake join results by actually verifying membership

This replaces the broken joining logic that shows "Joined 19" when only 11 are real.
"""

import asyncio
import logging
import time
import re
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple, Union
from telethon import TelegramClient
from telethon.tl.functions.channels import JoinChannelRequest, GetParticipantsRequest
from telethon.tl.functions.messages import GetDialogsRequest
from telethon.tl.types import InputPeerEmpty, ChannelParticipantsRecent
from telethon.errors import FloodWaitError, ChannelPrivateError, UserAlreadyParticipantError, ChannelInvalidError


class JoinResult:
    """Result of a join attempt with verification."""
    
    def __init__(self, success: bool, verified: bool = False, error: str = None, 
                 was_already_member: bool = False, needs_retry: bool = False, 
                 flood_wait_seconds: int = 0):
        self.success = success
        self.verified = verified
        self.error = error
        self.was_already_member = was_already_member
        self.needs_retry = needs_retry
        self.flood_wait_seconds = flood_wait_seconds
        
    def is_real_join(self) -> bool:
        """Check if this was a genuine new join."""
        return self.success and self.verified and not self.was_already_member
    
    def should_count_as_success(self) -> bool:
        """Check if this should count toward successful joins."""
        return self.success and self.verified
    
    def __str__(self):
        if self.is_real_join():
            return "✅ NEW_JOIN_VERIFIED"
        elif self.was_already_member:
            return "⏭️ ALREADY_MEMBER"
        elif self.flood_wait_seconds > 0:
            return f"⏳ FLOOD_WAIT_{self.flood_wait_seconds}s"
        elif self.needs_retry:
            return f"🔄 RETRY_NEEDED"
        elif self.error:
            return f"❌ FAILED: {self.error}"
        else:
            return "❓ UNKNOWN"


class CriticalJoiningVerifier:
    """
    CRITICAL FIX: Verified joining system that prevents fake results
    
    This class ensures every "successful join" is actually verified by:
    1. Checking if already a member before joining
    2. Attempting the join with proper error handling
    3. Verifying membership after join attempt
    4. Distinguishing between new joins vs already-member cases
    """
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.verification_cache = {}  # Cache recent verifications
        self.membership_cache = {}    # Cache membership checks
        self.cache_timeout = 300      # 5 minutes cache
        
    async def join_and_verify(self, client: TelegramClient, group_link: str, 
                            task_id: str = None, account_phone: str = None) -> JoinResult:
        """
        CRITICAL FIX: Join group with full verification to prevent fake results.
        
        Returns JoinResult with actual verification status.
        """
        try:
            self.logger.info(f"🔍 VERIFICATION: Starting join process for {group_link}")
            
            # Step 1: Clean and validate the group link
            cleaned_link = self._clean_group_link(group_link)
            if not cleaned_link:
                return JoinResult(False, error="Invalid group link format")
            
            # Step 2: Check if already a member BEFORE attempting join
            was_already_member = await self._check_membership_before_join(client, cleaned_link)
            
            if was_already_member:
                self.logger.info(f"⏭️ ALREADY_MEMBER: {cleaned_link} - skipping join attempt")
                return JoinResult(True, verified=True, was_already_member=True)
            
            # Step 3: Attempt to join the group
            join_success, error, flood_wait = await self._attempt_join(client, cleaned_link)
            
            if flood_wait > 0:
                self.logger.warning(f"⏳ FLOOD_WAIT: {flood_wait}s for {account_phone or 'unknown'}")
                return JoinResult(False, error=f"FloodWait {flood_wait}s", 
                                flood_wait_seconds=flood_wait)
            
            if not join_success:
                if "already" in (error or "").lower() or "participant" in (error or "").lower():
                    # Join failed because already a member (edge case)
                    self.logger.info(f"⏭️ ALREADY_MEMBER_ON_JOIN: {cleaned_link}")
                    return JoinResult(True, verified=True, was_already_member=True)
                else:
                    self.logger.error(f"❌ JOIN_FAILED: {cleaned_link} - {error}")
                    return JoinResult(False, error=error, needs_retry=self._should_retry(error))
            
            # Step 4: CRITICAL - Verify membership after join attempt
            verification_success = await self._verify_membership_after_join(client, cleaned_link)
            
            if verification_success:
                self.logger.info(f"✅ JOIN_VERIFIED: {cleaned_link} - membership confirmed")
                return JoinResult(True, verified=True, was_already_member=False)
            else:
                self.logger.error(f"❌ JOIN_NOT_VERIFIED: {cleaned_link} - join claimed success but verification failed")
                return JoinResult(False, error="Join not verified - not actually a member", 
                                needs_retry=True)
                
        except Exception as e:
            self.logger.error(f"❌ VERIFICATION_ERROR: {group_link} - {str(e)}")
            return JoinResult(False, error=f"Verification error: {str(e)}")
    
    def _clean_group_link(self, group_link: str) -> Optional[str]:
        """Clean and validate group link format."""
        if not group_link:
            return None
            
        link = group_link.strip()
        
        # Handle different link formats
        if link.startswith('@'):
            return link
        elif 't.me/' in link:
            # Extract username from t.me link
            match = re.search(r't\.me/([^/?]+)', link)
            if match:
                username = match.group(1)
                return f"@{username}" if not username.startswith('@') else username
        elif link.startswith('https://telegram.me/'):
            # Handle telegram.me links
            username = link.replace('https://telegram.me/', '').split('?')[0]
            return f"@{username}" if not username.startswith('@') else username
        
        # If it looks like a username without @, add it
        if re.match(r'^[a-zA-Z0-9_]+$', link):
            return f"@{link}"
        
        return link  # Return as-is for other formats
    
    async def _check_membership_before_join(self, client: TelegramClient, group_link: str) -> bool:
        """Check if already a member before attempting to join."""
        try:
            # Get cached result if available
            cache_key = f"{client.session.filename}:{group_link}"
            if cache_key in self.membership_cache:
                cached_time, cached_result = self.membership_cache[cache_key]
                if time.time() - cached_time < self.cache_timeout:
                    return cached_result
            
            # Try to get the entity (group/channel)
            try:
                entity = await client.get_entity(group_link)
            except Exception as e:
                self.logger.debug(f"Could not get entity for {group_link}: {e}")
                return False
            
            # Check if we can get participants (indicates membership)
            try:
                participants = await client.get_participants(entity, limit=1)
                # If we can get participants, we're a member
                result = True
                self.logger.debug(f"✅ PRE_JOIN_CHECK: Already member of {group_link}")
            except Exception as e:
                # If we can't get participants, we're likely not a member
                error_str = str(e).lower()
                if "forbidden" in error_str or "participants" in error_str:
                    result = False
                    self.logger.debug(f"❌ PRE_JOIN_CHECK: Not member of {group_link}")
                else:
                    # Unclear error, assume not a member
                    result = False
                    self.logger.debug(f"❓ PRE_JOIN_CHECK: Unclear membership for {group_link}: {e}")
            
            # Cache the result
            self.membership_cache[cache_key] = (time.time(), result)
            return result
            
        except Exception as e:
            self.logger.debug(f"Pre-join membership check failed for {group_link}: {e}")
            return False
    
    async def _attempt_join(self, client: TelegramClient, group_link: str) -> Tuple[bool, Optional[str], int]:
        """
        Attempt to join the group.
        Returns: (success, error_message, flood_wait_seconds)
        """
        try:
            self.logger.debug(f"🔗 ATTEMPTING_JOIN: {group_link}")
            
            # Get the entity first
            entity = await client.get_entity(group_link)
            
            # Attempt to join
            result = await client(JoinChannelRequest(entity))
            
            # If we got here, join was successful
            self.logger.debug(f"✅ JOIN_ATTEMPT_SUCCESS: {group_link}")
            return True, None, 0
            
        except UserAlreadyParticipantError:
            # Already a member
            return True, "Already a participant", 0
            
        except FloodWaitError as e:
            return False, f"FloodWait {e.seconds}s", e.seconds
            
        except ChannelPrivateError:
            return False, "Channel is private", 0
            
        except ChannelInvalidError:
            return False, "Channel is invalid or deleted", 0
            
        except Exception as e:
            error_str = str(e).lower()
            
            # Check for flood wait in error message
            flood_match = re.search(r'(\d+)', error_str)
            if 'flood' in error_str and 'wait' in error_str and flood_match:
                wait_seconds = int(flood_match.group(1))
                return False, f"FloodWait {wait_seconds}s", wait_seconds
            
            # Check for already participant errors
            if 'already' in error_str or 'participant' in error_str:
                return True, "Already a participant", 0
            
            return False, str(e), 0
    
    async def _verify_membership_after_join(self, client: TelegramClient, group_link: str) -> bool:
        """
        CRITICAL: Verify membership after join attempt to prevent fake results.
        """
        try:
            # Wait a moment for join to propagate
            await asyncio.sleep(2)
            
            # Method 1: Try to get participants (most reliable)
            try:
                entity = await client.get_entity(group_link)
                participants = await client.get_participants(entity, limit=1)
                self.logger.debug(f"✅ VERIFICATION_METHOD_1: Can get participants for {group_link}")
                return True
            except Exception as e:
                self.logger.debug(f"❌ VERIFICATION_METHOD_1 failed for {group_link}: {e}")
            
            # Method 2: Check dialogs (fallback)
            try:
                dialogs = await client.get_dialogs(limit=100)
                for dialog in dialogs:
                    if hasattr(dialog.entity, 'username') and dialog.entity.username:
                        if group_link.replace('@', '').lower() == dialog.entity.username.lower():
                            self.logger.debug(f"✅ VERIFICATION_METHOD_2: Found in dialogs {group_link}")
                            return True
                    elif hasattr(dialog.entity, 'title') and dialog.entity.title:
                        # For private groups, check by title
                        if group_link.replace('@', '').lower() in dialog.entity.title.lower():
                            self.logger.debug(f"✅ VERIFICATION_METHOD_2: Found by title {group_link}")
                            return True
            except Exception as e:
                self.logger.debug(f"❌ VERIFICATION_METHOD_2 failed for {group_link}: {e}")
            
            # Method 3: Try to send a message (last resort)
            try:
                entity = await client.get_entity(group_link)
                # Just check if we can access the entity for messaging
                if entity:
                    self.logger.debug(f"✅ VERIFICATION_METHOD_3: Can access entity {group_link}")
                    return True
            except Exception as e:
                self.logger.debug(f"❌ VERIFICATION_METHOD_3 failed for {group_link}: {e}")
            
            # All verification methods failed
            self.logger.warning(f"❌ ALL_VERIFICATION_FAILED: {group_link}")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ VERIFICATION_ERROR: {group_link} - {e}")
            return False
    
    def _should_retry(self, error: str) -> bool:
        """Determine if an error indicates the join should be retried."""
        if not error:
            return False
            
        error_lower = error.lower()
        
        # Temporary errors that should be retried
        retry_indicators = [
            'timeout', 'network', 'connection', 'temporary', 
            'try again', 'server error', 'internal error'
        ]
        
        # Permanent errors that should not be retried
        no_retry_indicators = [
            'private', 'invalid', 'deleted', 'banned', 'forbidden',
            'not found', 'does not exist'
        ]
        
        for indicator in no_retry_indicators:
            if indicator in error_lower:
                return False
        
        for indicator in retry_indicators:
            if indicator in error_lower:
                return True
        
        # Default: retry unknown errors
        return True
    
    def get_verification_summary(self, results: List[JoinResult]) -> Dict[str, int]:
        """Get summary statistics from join results."""
        summary = {
            'total_attempts': len(results),
            'real_new_joins': 0,
            'already_members': 0,
            'verified_successes': 0,
            'failed_joins': 0,
            'flood_waits': 0,
            'needs_retry': 0
        }
        
        for result in results:
            if result.is_real_join():
                summary['real_new_joins'] += 1
            
            if result.was_already_member:
                summary['already_members'] += 1
            
            if result.should_count_as_success():
                summary['verified_successes'] += 1
            
            if not result.success:
                summary['failed_joins'] += 1
            
            if result.flood_wait_seconds > 0:
                summary['flood_waits'] += 1
            
            if result.needs_retry:
                summary['needs_retry'] += 1
        
        return summary


# CRITICAL INTEGRATION FUNCTIONS FOR MAIN.PY

async def fix_join_single_group(client: TelegramClient, group_link: str, 
                               task_id: str = None, account_phone: str = None, 
                               verifier: CriticalJoiningVerifier = None) -> JoinResult:
    """
    CRITICAL FIX: Replace the broken _join_single_group_safe function.
    This actually verifies joins instead of giving fake results.
    """
    if verifier is None:
        verifier = CriticalJoiningVerifier()
    
    return await verifier.join_and_verify(client, group_link, task_id, account_phone)


def create_verification_summary_for_ui(results: List[JoinResult]) -> str:
    """Create a user-friendly summary for UI display."""
    if not results:
        return "No join attempts recorded"
    
    verifier = CriticalJoiningVerifier()
    summary = verifier.get_verification_summary(results)
    
    lines = [
        f"📊 JOIN VERIFICATION SUMMARY:",
        f"  ✅ Real New Joins: {summary['real_new_joins']}",
        f"  ⏭️ Already Members: {summary['already_members']}",
        f"  📈 Total Verified: {summary['verified_successes']}",
        f"  ❌ Failed: {summary['failed_joins']}",
        f"  ⏳ Flood Waits: {summary['flood_waits']}",
        f"  🔄 Need Retry: {summary['needs_retry']}"
    ]
    
    return "\n".join(lines)


if __name__ == "__main__":
    # Test the verification system
    import logging
    logging.basicConfig(level=logging.INFO)
    
    verifier = CriticalJoiningVerifier()
    print("✅ Critical Joining Verifier initialized!")
    
    # Test with some mock results
    test_results = [
        JoinResult(True, verified=True, was_already_member=False),  # Real join
        JoinResult(True, verified=True, was_already_member=True),   # Already member
        JoinResult(False, error="Channel private"),                 # Failed
        JoinResult(False, flood_wait_seconds=30),                  # Flood wait
    ]
    
    summary = verifier.get_verification_summary(test_results)
    print(f"Test summary: {summary}")
    print(create_verification_summary_for_ui(test_results)) 