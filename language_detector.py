"""
Language detection module for TG Checker.
"""

import logging
import re
from collections import Counter

logger = logging.getLogger(__name__)

class LanguageDetector:
    """Detect language of messages using character-based approach."""
    
    def __init__(self):
        # Language detection patterns
        self.language_patterns = {
            'en': lambda text: sum(1 for c in text if c.isascii() and c.isalpha()),
            'ar': lambda text: sum(1 for c in text if '\u0600' <= c <= '\u06FF'),  # Arabic
            'ru': lambda text: sum(1 for c in text if '\u0400' <= c <= '\u04FF'),  # Cyrillic
            'es': lambda text: sum(1 for c in text if c in 'áéíóúñ'),  # Spanish
            'fr': lambda text: sum(1 for c in text if c in 'éèêëàâçîïôûùüÿ'),  # French
            'de': lambda text: sum(1 for c in text if c in 'äöüß'),  # German
            'zh': lambda text: sum(1 for c in text if '\u4e00' <= c <= '\u9fff'),  # Chinese
            'hi': lambda text: sum(1 for c in text if '\u0900' <= c <= '\u097F'),  # Hindi
            'pt': lambda text: sum(1 for c in text if c in 'áàãâéêíóôõúüç'),  # Portuguese
            'tr': lambda text: sum(1 for c in text if c in 'çğıöşü'),  # Turkish
            'ku': lambda text: sum(1 for c in text if '\u0650' <= c <= '\u066F'),  # Kurdish
        }
        
        # Language names for display
        self.language_names = {
            'en': 'English',
            'ar': 'Arabic',
            'ru': 'Russian',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'hi': 'Hindi',
            'pt': 'Portuguese',
            'tr': 'Turkish',
            'ku': 'Kurdish',
            'unknown': 'Unknown'
        }
    
    def detect_language(self, text):
        """Detect language of a single text string."""
        if not text or not isinstance(text, str):
            return 'unknown'
            
        # Check for URL-only or very short text
        if self._is_url_only(text) or len(text.strip()) < 5:
            return 'unknown'
        
        # Count character matches for each language
        scores = {lang: pattern(text) for lang, pattern in self.language_patterns.items()}
        
        # Return language with highest score if score > 0
        if max(scores.values(), default=0) > 0:
            return max(scores.items(), key=lambda x: x[1])[0]
        return 'unknown'
    
    def detect_language_batch(self, messages, limit=50):
        """Detect dominant language in a batch of messages."""
        if not messages:
            return 'unknown'
            
        # Process only up to limit messages
        messages_to_check = messages[:limit]
        
        # Extract text from messages (assuming messages have a 'text' attribute)
        texts = []
        for msg in messages_to_check:
            if hasattr(msg, 'text') and msg.text:
                texts.append(msg.text)
        
        if not texts:
            return 'unknown'
        
        # Detect language for each message
        languages = [self.detect_language(text) for text in texts]
        
        # Count occurrences and return most common
        counter = Counter(languages)
        counter.pop('unknown', None)  # Remove unknown from consideration
        
        if counter:
            return counter.most_common(1)[0][0]
        return 'unknown'
    
    def _is_url_only(self, text):
        """Check if text is only a URL."""
        # Basic URL detection regex
        url_pattern = re.compile(r'^https?://\S+$')
        return bool(url_pattern.match(text.strip()))
    
    def get_language_name(self, language_code):
        """Get the full name of a language from its code."""
        return self.language_names.get(language_code, 'Unknown')
    
    def get_supported_languages(self):
        """Get a list of supported languages."""
        return list(self.language_names.keys())

# Create a global instance
detector = LanguageDetector()

def detect_language(text):
    """Detect language of a single text string."""
    return detector.detect_language(text)
    
def detect_language_batch(messages, limit=50):
    """Detect dominant language in a batch of messages."""
    return detector.detect_language_batch(messages, limit)
    
def get_language_name(language_code):
    """Get the full name of a language from its code."""
    return detector.get_language_name(language_code)
    
def get_supported_languages():
    """Get a list of supported languages."""
    return detector.get_supported_languages() 