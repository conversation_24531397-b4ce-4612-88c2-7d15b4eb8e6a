#!/usr/bin/env python3
"""
Test if the fixed version works
"""

import os
import sys

def test_fix():
    """Test if the fixed version can be imported successfully"""
    print("Testing fixed version...")
    
    fixed_file = "tg_checker_working.py"
    
    if not os.path.exists(fixed_file):
        print(f"Error: {fixed_file} not found. Run simple_fix.py first.")
        return False
    
    # Try to see if the Python interpreter can parse the file without syntax errors
    try:
        import subprocess
        result = subprocess.run(
            ["python", "-m", "py_compile", fixed_file], 
            capture_output=True, 
            text=True
        )
        
        if result.returncode == 0:
            print(f"Success! {fixed_file} compiled without syntax errors.")
            return True
        else:
            print(f"Error compiling {fixed_file}:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"Error testing fixed version: {e}")
        return False

if __name__ == "__main__":
    test_fix() 