"""
OPTIMIZED TG CHECKER LAUNCHER
Starts TG Checker with performance optimizations and reduced CPU usage
"""

import sys
import os
import gc
import threading
from pathlib import Path

def optimize_python():
    """Apply Python-level optimizations to reduce CPU usage."""
    
    # Optimize garbage collection for better performance
    gc.set_threshold(700, 10, 10)  # Less frequent GC = lower CPU usage
    
    # Set smaller thread stack size to reduce memory usage
    threading.stack_size(512*1024)  # 512KB instead of default 8MB
    
    # Disable bytecode generation to reduce I/O
    sys.dont_write_bytecode = True
    
    # Set recursion limit to prevent stack overflow
    sys.setrecursionlimit(1000)

def optimize_environment():
    """Set environment variables for better performance."""
    
    # Optimize Python performance
    os.environ['PYTHONOPTIMIZE'] = '1'  # Enable optimizations
    os.environ['PYTHONUNBUFFERED'] = '1'  # Reduce I/O buffering
    
    # Optimize Qt performance
    os.environ['QT_AUTO_SCREEN_SCALE_FACTOR'] = '0'  # Disable auto scaling
    os.environ['QT_ENABLE_HIGHDPI_SCALING'] = '0'   # Disable high DPI
    
    # Reduce Qt debug output
    os.environ['QT_LOGGING_RULES'] = '*.debug=false'

def main():
    """Launch TG Checker with all optimizations applied."""
    
    print("🚀 LAUNCHING OPTIMIZED TG CHECKER...")
    print("⚡ Applying performance optimizations...")
    
    # Apply all optimizations
    optimize_python()
    optimize_environment()
    
    # Set working directory to script location
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    try:
        # Import main application
        print("📦 Loading application modules...")
        from main import TGCheckerApp
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        # Create optimized QApplication
        print("🖥️ Initializing optimized GUI...")
        app = QApplication(sys.argv)
        
        # Apply Qt optimizations
        app.setAttribute(Qt.AA_DisableWindowContextHelpButton, True)
        app.setAttribute(Qt.AA_DontShowIconsInMenus, True)
        app.setQuitOnLastWindowClosed(True)
        
        # Create main window
        print("🏠 Creating main window...")
        window = TGCheckerApp()
        
        # Show window and start event loop
        window.show()
        print("✅ TG CHECKER STARTED SUCCESSFULLY!")
        print("💡 Performance optimizations active:")
        print("   - Reduced garbage collection frequency")
        print("   - Optimized thread stack size")
        print("   - Disabled unnecessary Qt features")
        print("   - Minimized I/O operations")
        
        # Start the application
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ Missing required modules: {e}")
        print("💡 Please install requirements: pip install -r requirements.txt")
        input("Press Enter to exit...")
        
    except Exception as e:
        print(f"❌ Failed to start TG Checker: {e}")
        print("🔧 Try running main.py directly if this launcher fails")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
