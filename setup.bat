@echo off
echo TG Checker Setup
echo -------------------
echo.

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

:: Create virtual environment
echo Creating virtual environment...
python -m venv venv
if %errorlevel% neq 0 (
    echo ERROR: Failed to create virtual environment
    pause
    exit /b 1
)

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

:: Install dependencies
echo Installing required packages...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)

:: Create sessions directory
echo Creating directories...
mkdir sessions
mkdir logs
mkdir backups

:: Initialize the database
echo Initializing database...
python init_db.py

:: Create application icon
echo Creating application icon...
python create_icon.py

:: Setup complete
echo.
echo Setup completed successfully!
echo.
echo You can now run the application using run.bat
echo.
echo For automatic startup, copy startup.bat to your Windows startup folder:
echo Right-click startup.bat ^> Send to ^> Startup Folder
echo.

:: Deactivate virtual environment
call venv\Scripts\deactivate.bat

pause 