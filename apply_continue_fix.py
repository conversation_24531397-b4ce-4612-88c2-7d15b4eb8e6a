#!/usr/bin/env python3
"""
🎯 Quick Apply: Continue Button Thread Fix
==========================================

Run this script to instantly fix the continue button blocking issue.
"""

import sys
import os

def apply_fix():
    """Apply the continue button thread fix."""
    try:
        # Import the fix module
        from continue_button_thread_fix import apply_continue_button_thread_fix, verify_continue_button_fix
        
        # Try to find and import the main application
        try:
            import main
            if hasattr(main, 'TGCheckerApp'):
                app_class = main.TGCheckerApp
                print("✅ Found TGCheckerApp in main module")
            else:
                print("❌ TGCheckerApp not found in main module")
                return False
        except ImportError:
            print("❌ Could not import main module")
            return False
        
        # For now, we'll patch the class itself since we don't have an instance
        print("\n🔧 Patching TGCheckerApp class...")
        
        # Add the fix as a method to the class
        def apply_to_instance(self):
            """Apply the continue button fix to this instance."""
            return apply_continue_button_thread_fix(self)
        
        def verify_fix_on_instance(self):
            """Verify the fix on this instance."""
            return verify_continue_button_fix(self)
        
        # Add methods to the class
        app_class.apply_continue_button_fix = apply_to_instance
        app_class.verify_continue_button_fix = verify_fix_on_instance
        
        print("✅ Continue button fix methods added to TGCheckerApp class")
        print("\n🎯 INTEGRATION INSTRUCTIONS:")
        print("=" * 50)
        print("Add this line to your TGCheckerApp.__init__ method:")
        print()
        print("    # Apply continue button fix")
        print("    self.apply_continue_button_fix()")
        print()
        print("Or call it manually on any TGCheckerApp instance:")
        print("    app.apply_continue_button_fix()")
        print()
        print("To verify the fix is working:")
        print("    app.verify_continue_button_fix()")
        
        return True
        
    except Exception as e:
        print(f"❌ Error applying fix: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Continue Button Thread Fix - Quick Apply")
    print("=" * 50)
    apply_fix() 