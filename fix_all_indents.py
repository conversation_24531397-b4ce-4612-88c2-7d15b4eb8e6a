def fix_indentation():
    print("Fixing all indentation issues in auto_refresh_missing_account_info method...")
    
    # Read the file content
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    # Find the start of the method
    method_start = None
    method_end = None
    indentation_base = None
    
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            method_start = i
            # Get the base indentation for the method
            indentation_base = " " * (len(line) - len(line.lstrip()))
            break
    
    if method_start is None:
        print("Method not found")
        return
    
    # Find the end of the method (next def at same indentation or less)
    for i in range(method_start + 1, len(lines)):
        if lines[i].strip() and not lines[i].startswith(indentation_base + " "):
            if "def " in lines[i] and lines[i].startswith(indentation_base):
                method_end = i
                break
    
    if method_end is None:
        method_end = len(lines)  # End of file
    
    # Fix indentation inside the method
    base_indent = indentation_base + "    "  # 4 spaces for first level
    inside_try = False
    fixes_made = 0
    
    for i in range(method_start + 1, method_end):
        line = lines[i]
        stripped_line = line.strip()
        
        # Skip empty lines
        if not stripped_line:
            continue
        
        # Determine correct indentation level
        if "try:" in stripped_line:
            inside_try = True
            correct_indent = indentation_base + "    "
        elif "except" in stripped_line and ":" in stripped_line:
            inside_try = False
            correct_indent = indentation_base + "    "
        elif inside_try:
            correct_indent = indentation_base + "        "  # 8 spaces (double indented)
        else:
            correct_indent = indentation_base + "    "  # 4 spaces for normal indentation
        
        # Check if line needs fixing
        if not line.startswith(correct_indent) and stripped_line:
            # Fix the indentation
            lines[i] = correct_indent + stripped_line + "\n"
            fixes_made += 1
            print(f"Fixed indentation on line {i+1}")
    
    # Write back the fixed content if any fixes were made
    if fixes_made > 0:
        with open("main.py", "w", encoding="utf-8") as file:
            file.writelines(lines)
        print(f"Applied {fixes_made} indentation fixes")
    else:
        print("No indentation issues found")

if __name__ == "__main__":
    fix_indentation() 