#!/usr/bin/env python3
"""
COMPREHENSIVE TEST - TG Checker Verification
Test with exact links to verify 100% accuracy of classification and saving
"""

import os
import shutil
import sqlite3
import logging
from datetime import datetime, timedelta
import asyncio
from telethon import TelegramClient
from telethon.errors import ChannelPrivateError, UsernameNotOccupiedError, FloodWaitError

# Test Configuration
TEST_LINKS = [
    "https://t.me/imperiamarket",      # Expected: Groups_Valid_Filter
    "https://t.me/NusantaraXploitNew", # Expected: Groups_Valid_Only
    "https://t.me/RareHandle",         # Expected: Topics_Groups_Only_Valid
    "https://t.me/wallethuntersio",    # Expected: Channels_Only_Valid
    "https://t.me/beklopptundgeil"     # Expected: Invalid_Groups_Channels
]

EXPECTED_RESULTS = {
    "imperiamarket": "Groups_Valid_Filter",
    "NusantaraXploitNew": "Groups_Valid_Only", 
    "RareHandle": "Topics_Groups_Only_Valid",
    "wallethuntersio": "Channels_Only_Valid",
    "beklopptundgeil": "Invalid_Groups_Channels"
}

RESULT_FOLDERS = [
    "Results/Groups_Valid_Filter",
    "Results/Groups_Valid_Only",
    "Results/Topics_Groups_Only_Valid", 
    "Results/Channels_Only_Valid",
    "Results/Invalid_Groups_Channels",
    "Results/Account_Issues"
]

def setup_logging():
    """Setup comprehensive logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('comprehensive_test.log'),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def clear_result_folders():
    """Clear all result folders before test"""
    logger = logging.getLogger(__name__)
    logger.info("🧹 Clearing result folders...")
    
    for folder in RESULT_FOLDERS:
        if os.path.exists(folder):
            # Clear contents
            for file in os.listdir(folder):
                file_path = os.path.join(folder, file)
                try:
                    if os.path.isfile(file_path):
                        os.unlink(file_path)
                        logger.info(f"   Deleted: {file_path}")
                except Exception as e:
                    logger.error(f"   Error deleting {file_path}: {e}")
        else:
            # Create folder if it doesn't exist
            os.makedirs(folder, exist_ok=True)
            logger.info(f"   Created: {folder}")
    
    logger.info("✅ All result folders cleared and ready")

def get_session_file():
    """Get available session file"""
    sessions_dir = "sessions"
    if os.path.exists(sessions_dir):
        session_files = [f for f in os.listdir(sessions_dir) if f.endswith('.session')]
        if session_files:
            return os.path.join(sessions_dir, session_files[0])
    return None

async def classify_link(client, link):
    """Classify a single link using the same logic as main.py"""
    logger = logging.getLogger(__name__)
    
    try:
        # Extract username from link
        username = link.replace("https://t.me/", "").replace("@", "")
        logger.info(f"🔍 Processing: {username}")
        
        # Get entity
        try:
            entity = await client.get_entity(username)
        except (ChannelPrivateError, UsernameNotOccupiedError) as e:
            logger.info(f"❌ {username}: Invalid - {str(e)}")
            return {
                'username': username,
                'classification': 'Invalid_Groups_Channels',
                'reason': f'Invalid - {str(e)}',
                'type': 'invalid'
            }
        
        # Determine type
        entity_type = type(entity).__name__
        logger.info(f"   Type: {entity_type}")
        
        if hasattr(entity, 'broadcast') and entity.broadcast:
            # Channel
            logger.info(f"   📺 Channel detected")
            return {
                'username': username,
                'classification': 'Channels_Only_Valid',
                'reason': 'Valid channel',
                'type': 'channel'
            }
        elif hasattr(entity, 'forum') and entity.forum:
            # Topic group
            logger.info(f"   💬 Topic group detected")
            return {
                'username': username,
                'classification': 'Topics_Groups_Only_Valid', 
                'reason': 'Valid topic group',
                'type': 'topic'
            }
        else:
            # Regular group - check activity
            try:
                # Get participants count
                participants = await client.get_participants(entity, limit=1)
                member_count = participants.total
                logger.info(f"   👥 Members: {member_count}")
                
                # Get recent messages to check activity
                messages = await client.get_messages(entity, limit=10)
                if messages:
                    latest_message = messages[0]
                    time_diff = datetime.now() - latest_message.date.replace(tzinfo=None)
                    hours_since_activity = time_diff.total_seconds() / 3600
                    logger.info(f"   ⏰ Last activity: {hours_since_activity:.2f} hours ago")
                    
                    # Apply 24-hour activity filter
                    if hours_since_activity <= 24:
                        logger.info(f"   ✅ Active group (≤24h) → Groups_Valid_Filter")
                        return {
                            'username': username,
                            'classification': 'Groups_Valid_Filter',
                            'reason': f'Active group ({hours_since_activity:.2f}h ≤ 24h)',
                            'type': 'group',
                            'member_count': member_count,
                            'last_activity_hours': hours_since_activity
                        }
                    else:
                        logger.info(f"   📝 Valid group (>24h) → Groups_Valid_Only")
                        return {
                            'username': username,
                            'classification': 'Groups_Valid_Only',
                            'reason': f'Valid group ({hours_since_activity:.2f}h > 24h)',
                            'type': 'group',
                            'member_count': member_count,
                            'last_activity_hours': hours_since_activity
                        }
                else:
                    logger.info(f"   📝 No messages found → Groups_Valid_Only")
                    return {
                        'username': username,
                        'classification': 'Groups_Valid_Only',
                        'reason': 'No recent messages',
                        'type': 'group',
                        'member_count': member_count
                    }
                    
            except Exception as e:
                logger.error(f"   ❌ Error checking group activity: {e}")
                return {
                    'username': username,
                    'classification': 'Invalid_Groups_Channels',
                    'reason': f'Error: {str(e)}',
                    'type': 'invalid'
                }
                
    except Exception as e:
        logger.error(f"❌ {username}: Unexpected error - {str(e)}")
        return {
            'username': username,
            'classification': 'Invalid_Groups_Channels',
            'reason': f'Unexpected error: {str(e)}',
            'type': 'invalid'
        }

def save_result_to_folder(result):
    """Save result to the appropriate folder"""
    logger = logging.getLogger(__name__)
    
    folder_path = f"Results/{result['classification']}"
    os.makedirs(folder_path, exist_ok=True)
    
    # Create filename
    if result['classification'] == 'Invalid_Groups_Channels':
        filename = "InvalidGroups.txt"
    elif result['classification'] == 'Channels_Only_Valid':
        filename = "Channels.txt"
    elif result['classification'] == 'Topics_Groups_Only_Valid':
        filename = "TopicsGroups.txt"
    elif result['classification'] == 'Groups_Valid_Filter':
        filename = "GroupsValidFilter.txt"
    elif result['classification'] == 'Groups_Valid_Only':
        filename = "GroupsValidOnly.txt"
    else:
        filename = "results.txt"
    
    file_path = os.path.join(folder_path, filename)
    
    # Prepare content
    link = f"https://t.me/{result['username']}"
    
    # Write to file
    try:
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(link + '\n')
        
        logger.info(f"💾 Saved to: {file_path}")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to save to {file_path}: {e}")
        return False

async def run_comprehensive_test():
    """Run the comprehensive test"""
    logger = setup_logging()
    logger.info("🚀 Starting COMPREHENSIVE TG CHECKER TEST")
    logger.info("=" * 60)
    
    # Clear folders
    clear_result_folders()
    
    # Get session
    session_file = get_session_file()
    if not session_file:
        logger.error("❌ No session file found!")
        return False
    
    logger.info(f"📱 Using session: {session_file}")
    
    # Load credentials from database
    try:
        conn = sqlite3.connect('tg_checker.db')
        cursor = conn.cursor()
        cursor.execute("SELECT api_id, api_hash FROM accounts LIMIT 1")
        result = cursor.fetchone()
        conn.close()
        
        if not result:
            logger.error("❌ No API credentials found in database!")
            return False
            
        api_id, api_hash = result
        logger.info("✅ API credentials loaded")
        
    except Exception as e:
        logger.error(f"❌ Error loading credentials: {e}")
        return False
    
    # Initialize client
    session_name = session_file.replace('.session', '').replace('sessions/', '')
    client = TelegramClient(session_file.replace('.session', ''), api_id, api_hash)
    
    try:
        await client.start()
        logger.info("✅ Telegram client connected")
        
        # Test each link
        results = []
        logger.info("\n🔍 TESTING LINKS:")
        logger.info("-" * 40)
        
        for link in TEST_LINKS:
            logger.info(f"\n🌐 Testing: {link}")
            result = await classify_link(client, link)
            results.append(result)
            
            # Save result
            save_success = save_result_to_folder(result)
            
            logger.info(f"   Result: {result['classification']}")
            logger.info(f"   Reason: {result['reason']}")
            logger.info(f"   Saved: {'✅' if save_success else '❌'}")
        
        # Verify results
        logger.info("\n" + "=" * 60)
        logger.info("📊 VERIFICATION RESULTS:")
        logger.info("=" * 60)
        
        all_correct = True
        
        for result in results:
            username = result['username']
            actual = result['classification']
            expected = EXPECTED_RESULTS.get(username, "UNKNOWN")
            
            correct = actual == expected
            status = "✅" if correct else "❌"
            
            logger.info(f"{status} {username}:")
            logger.info(f"   Expected: {expected}")
            logger.info(f"   Actual:   {actual}")
            
            if not correct:
                all_correct = False
                logger.error(f"   🚨 MISMATCH for {username}!")
        
        # Check saved files
        logger.info("\n📁 SAVED FILES VERIFICATION:")
        logger.info("-" * 40)
        
        for folder in RESULT_FOLDERS:
            if os.path.exists(folder):
                files = os.listdir(folder)
                if files:
                    for file in files:
                        file_path = os.path.join(folder, file)
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            lines = content.split('\n') if content else []
                            logger.info(f"   📄 {file_path}: {len(lines)} links")
                else:
                    logger.info(f"   📁 {folder}: Empty")
        
        # Final result
        logger.info("\n" + "=" * 60)
        if all_correct:
            logger.info("🎉 ALL TESTS PASSED! TG Checker is working correctly!")
        else:
            logger.error("❌ SOME TESTS FAILED! Check the mismatches above.")
        logger.info("=" * 60)
        
        return all_correct
        
    finally:
        await client.disconnect()

if __name__ == "__main__":
    # Run the test
    success = asyncio.run(run_comprehensive_test())
    
    if success:
        print("\n🎯 COMPREHENSIVE TEST: SUCCESS")
        print("✅ TG Checker is working exactly as required!")
    else:
        print("\n❌ COMPREHENSIVE TEST: FAILED")
        print("🔧 Some issues need to be fixed.") 