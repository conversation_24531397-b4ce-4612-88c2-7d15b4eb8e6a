#!/usr/bin/env python3
"""
Test script to identify joining system crashes.
"""

import sys
import traceback
import threading
from concurrent.futures import ThreadPoolExecutor

def test_imports():
    """Test if all required imports work."""
    try:
        print("Testing imports...")
        from PyQt5.QtWidgets import QApplication
        from main import TGCheckerApp
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_app_creation():
    """Test if we can create the app without crashing."""
    try:
        print("Testing app creation...")
        from PyQt5.QtWidgets import QApplication
        from main import TGCheckerApp
        
        # Create app and keep it alive
        app = QApplication([])
        window = TGCheckerApp()
        
        # Keep references alive 
        window._test_app_ref = app  # Prevent garbage collection
        app._test_window_ref = window  # Prevent garbage collection
        
        print("✅ App created successfully")
        
        # Wait a moment for background tasks to settle
        import time
        time.sleep(2)
        
        return window
    except Exception as e:
        print(f"❌ App creation error: {e}")
        traceback.print_exc()
        return None

def test_performance_methods(window):
    """Test if the performance methods exist and can be called."""
    try:
        print("Testing performance methods...")
        
        # Initialize performance components
        if not hasattr(window, 'high_perf_executor'):
            window.high_perf_executor = ThreadPoolExecutor(
                max_workers=50, thread_name_prefix="HighPerfJoin"
            )
            print("✅ ThreadPoolExecutor created")
        
        if not hasattr(window, 'ui_update_queue'):
            window.ui_update_queue = {}
            window.ui_update_timer = None
            window.ui_update_lock = threading.Lock()
            print("✅ UI update system initialized")
        
        if not hasattr(window, 'active_hp_tasks'):
            window.active_hp_tasks = {}
            print("✅ Active tasks tracker initialized")
        
        # Test critical methods exist
        methods = [
            '_queue_ui_update_optimized',
            '_process_ui_updates_batch', 
            '_run_joining_task_optimized',
            '_join_groups_high_performance',
            '_join_single_group_safe',
            '_apply_optimized_delay'
        ]
        
        missing_methods = []
        for method in methods:
            if hasattr(window, method):
                print(f"✅ Method {method} exists")
            else:
                print(f"❌ Method {method} MISSING!")
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ Missing methods: {missing_methods}")
            return False
        
        print("✅ All performance methods exist")
        return True
        
    except Exception as e:
        print(f"❌ Performance methods error: {e}")
        traceback.print_exc()
        return False

def test_joining_task_creation(window):
    """Test if we can create a mock joining task."""
    try:
        print("Testing joining task creation...")
        
        # Create a mock task
        mock_task = {
            'id': 'test_task_123',
            'name': 'Test Task',
            'account_phone': '**********',
            'group_links': 'https://t.me/test_group1\nhttps://t.me/test_group2',
            'settings': '{"delay_min": 30, "delay_max": 90, "use_random_delay": true}',
            'current_index': 0,
            'successful_joins': 0,
            'failed_joins': 0
        }
        
        # Test the start_joining_task method
        print("Testing start_joining_task method...")
        window.start_joining_task('test_task_123')
        print("✅ start_joining_task called without immediate error")
        
        return True
        
    except Exception as e:
        print(f"❌ Joining task creation error: {e}")
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🔍 DEBUGGING JOINING SYSTEM CRASHES")
    print("=" * 50)
    
    # Test 1: Imports
    if not test_imports():
        print("❌ Failed at imports")
        return
    
    # Test 2: App creation
    window = test_app_creation()
    if not window:
        print("❌ Failed at app creation")
        return
    
    # Test 3: Performance methods
    if not test_performance_methods(window):
        print("❌ Failed at performance methods")
        return
    
    # Test 4: Joining task creation
    if not test_joining_task_creation(window):
        print("❌ Failed at joining task creation")
        return
    
    print("=" * 50)
    print("✅ ALL TESTS PASSED - No obvious crash causes found")
    print("Crash might be in specific runtime conditions or user interactions")

if __name__ == "__main__":
    main() 