#!/usr/bin/env python3
"""
Database Lock Fix Utility for TG Checker

This script attempts to fix database locking issues by:
1. Creating a backup of the current database
2. Cleaning and optimizing the database
3. Restoring from backup if necessary

Usage:
    python fix_db_locks.py [--backup-only]
"""

import os
import sys
import time
import sqlite3
import logging
import shutil
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("db_fix.log")
    ]
)

# Database file path
DB_PATH = "tg_checker.db"

def fix_database_locks(backup_only=False):
    """Fix database lock issues."""
    logging.info("Starting database lock fix utility...")
    
    # Step 1: Create a backup
    backup_path = f"{DB_PATH}.backup_{int(datetime.now().timestamp())}"
    
    try:
        if os.path.exists(DB_PATH):
            shutil.copy2(DB_PATH, backup_path)
            logging.info(f"Created database backup at {backup_path}")
        else:
            logging.warning(f"Database file {DB_PATH} not found")
            return False
    except Exception as e:
        logging.error(f"Failed to create backup: {str(e)}")
        return False
    
    if backup_only:
        logging.info("Backup-only mode: backup created successfully")
        return True
    
    # Step 2: Try to fix the database
    try:
        # Wait a moment for any other processes to release locks
        time.sleep(2)
        
        # Try to connect with increased timeout
        conn = sqlite3.connect(DB_PATH, timeout=60.0)
        
        # Try to switch to DELETE journal mode
        conn.execute("PRAGMA journal_mode=DELETE")
        conn.commit()
        logging.info("Switched to DELETE journal mode")
        
        # Run integrity check
        cursor = conn.cursor()
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()[0]
        
        if integrity_result == "ok":
            logging.info("Database integrity check passed")
        else:
            logging.warning(f"Database integrity issues found: {integrity_result}")
            # If database is corrupted, restore from backup
            conn.close()
            logging.info("Restoring from backup due to integrity issues...")
            time.sleep(1)
            os.remove(DB_PATH)
            shutil.copy2(backup_path, DB_PATH)
            logging.info("Database restored from backup")
            return True
        
        # Run VACUUM to compact the database
        logging.info("Running VACUUM on database...")
        conn.execute("VACUUM")
        conn.commit()
        logging.info("Database VACUUM completed")
        
        # Switch back to WAL mode
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=60000")
        conn.commit()
        logging.info("Restored WAL journal mode")
        
        conn.close()
        logging.info("Database lock issues fixed successfully")
        return True
        
    except sqlite3.OperationalError as e:
        if "database is locked" in str(e):
            logging.error("Database is still locked, attempting more aggressive fix...")
            
            try:
                # Wait longer
                time.sleep(5)
                
                # Try to replace the database with the backup
                if os.path.exists(DB_PATH):
                    os.remove(DB_PATH)
                    time.sleep(1)
                
                # Restore from backup
                shutil.copy2(backup_path, DB_PATH)
                logging.info("Database replaced with backup after lock issues")
                return True
            except Exception as restore_err:
                logging.error(f"Failed to restore database: {str(restore_err)}")
                return False
        else:
            logging.error(f"Database error: {str(e)}")
            return False
    except Exception as e:
        logging.error(f"Error fixing database: {str(e)}")
        return False

if __name__ == "__main__":
    backup_only = "--backup-only" in sys.argv
    
    if fix_database_locks(backup_only):
        print("✅ Database fix completed successfully!")
        print(f"A backup was created at: {DB_PATH}.backup_*")
        if backup_only:
            print("No changes were made to the original database (backup-only mode)")
        else:
            print("The database has been optimized and lock issues should be resolved")
    else:
        print("❌ Failed to fix database lock issues completely")
        print("Please check the db_fix.log file for details")
    
    print("\nIf you continue to experience issues, you can:")
    print("1. Close all instances of the application")
    print("2. Run this script with --backup-only")
    print("3. Manually delete the tg_checker.db file")
    print("4. Rename the most recent backup to tg_checker.db") 