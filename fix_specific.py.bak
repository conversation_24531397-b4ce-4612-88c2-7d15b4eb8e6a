import re
import os
import sys

def fix_line_575():
    """Fix indentation issue at line 575."""
    with open("main.py", 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # This part seems to already be correct
    if '        if self.settings.value("auto_start_monitor", False, type=bool):' in lines[574]:
        if '            self.start_monitor()' in lines[575]:
            print("Line 575 already has correct indentation")
            return lines
    
    # Fix it if needed
    lines[575] = '            self.start_monitor()\n'
    return lines

def fix_refresh_account_method():
    """Fix indentation in the refresh_account_info method."""
    with open("main.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the refresh_account_info method
    pattern = r'def refresh_account_info\(self, phone\):(.*?)def refresh_all_accounts_info\('
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        print("Could not find refresh_account_info method")
        return content
    
    old_method = match.group(1)
    
    # Create a properly indented version
    new_method = '''
        """Refresh account information from Telegram."""
        try:
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity(f"Account {phone} not found")
                return False
            
            # Create client and get user info
            client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
            user_info = client.get_account_info()
            
            if user_info:
                name = user_info.get("full_name", "").strip()
                username = user_info.get("username", "").strip()
                
                # Update account info in database
                success = self.account_manager.update_account_info(phone, name=name, username=username)
                if success:
                    self.log_activity(f"Updated account info for {phone}: {name} / @{username}")
                    return True
                else:
                    self.log_activity(f"Failed to update account info for {phone}")
                    return False
            else:
                self.log_activity(f"Could not retrieve user info for {phone}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing account info for {phone}: {str(e)}")
            self.log_activity(f"Error refreshing account info for {phone}: {str(e)}")
            return False
    
    '''
    
    # Replace the old method with the fixed one
    fixed_content = content.replace(old_method, new_method)
    return fixed_content

def fix_refresh_specific_method():
    """Fix indentation in the refresh_specific_account_info and _refresh_specific_account_thread methods."""
    with open("main.py", 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the refresh_specific_account_info method
    pattern = r'def refresh_specific_account_info\(self, account\):(.*?)def _auto_check_new_account_age\('
    match = re.search(pattern, content, re.DOTALL)
    if not match:
        print("Could not find refresh_specific_account_info method")
        return content
    
    old_method = match.group(1)
    
    # Create a properly indented version for both methods
    new_method = '''
        """Refresh information for a specific account."""
        try:
            phone = account.get("phone", "")
            if not phone:
                return
            
            self.update_status_signal.emit(f"Refreshing info for {phone}...")
            self.log_activity_signal.emit(f"Refreshing info for {phone}...")
            
            # Run in background thread
            threading.Thread(target=self._refresh_specific_account_thread, args=(phone,), daemon=True).start()
        except Exception as e:
            self.logger.error(f"Failed to refresh account info for {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh account info: {str(e)}")
    
    def _refresh_specific_account_thread(self, phone):
        """Background thread for refreshing specific account information."""
        try:
            success = self.refresh_account_info(phone)
            
            if success:
                # Update UI using signal
                self.update_ui_signal.emit()
                self.update_status_signal.emit(f"Account info refreshed for {phone}")
            else:
                self.update_status_signal.emit(f"Failed to refresh info for {phone}")
                
        except Exception as e:
            self.logger.error(f"Error in refresh thread for {phone}: {str(e)}")
            self.update_status_signal.emit(f"Refresh error for {phone}: {str(e)}")
    
    '''
    
    # Replace the old method with the fixed one
    fixed_content = content.replace(old_method, new_method)
    return fixed_content

def save_fixed_file(content, filename="main_fixed_specific.py"):
    """Save the fixed content to a new file."""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Fixed file saved as {filename}")
    return filename

def run_fixes():
    """Run all the specific fixes."""
    # First fix the refresh_account_info method
    content = fix_refresh_account_method()
    
    # Then fix the refresh_specific_account_info method
    content = fix_refresh_specific_method()
    
    # Save the fixed content
    fixed_file = save_fixed_file(content)
    
    print(f"All specific fixes applied. You can run the fixed file with: python {fixed_file}")

if __name__ == "__main__":
    run_fixes() 