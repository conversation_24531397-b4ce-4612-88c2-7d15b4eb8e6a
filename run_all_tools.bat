@echo off
echo ===================================
echo  TG CHECKER - ALL REPAIR TOOLS
echo ===================================
echo.
echo 1. View fix guide
echo 2. Run Speed Checker app
echo 3. Create simple fix (easy method)
echo 4. Create emergency fix (alternative method)
echo 5. Exit
echo.
set /p choice=Enter your choice (1-5): 

if "%choice%"=="1" goto view_guide
if "%choice%"=="2" goto speed_checker
if "%choice%"=="3" goto simple_fix
if "%choice%"=="4" goto emergency_fix
if "%choice%"=="5" goto end

:view_guide
echo.
echo Opening fix guide...
notepad TG_CHECKER_FIX_GUIDE.txt
goto end

:speed_checker
echo.
echo Running Speed Checker app...
python speed_checker_app.py
goto end

:simple_fix
echo.
echo Creating simple fix...
python simple_stub.py
echo.
echo Done! You can now run Run_Simple_Fix.bat
pause
goto end

:emergency_fix
echo.
echo Creating emergency fix...
python emergency_fix.py
echo.
echo Done! You can now run Run_Emergency_Fix.bat
pause
goto end

:end
echo.
echo Thank you for using TG Checker repair tools. 