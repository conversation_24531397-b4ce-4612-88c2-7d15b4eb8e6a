#!/usr/bin/env python3
"""
Test script to verify performance system integration
"""

import sys
import time
import threading

def test_performance_manager():
    """Test the performance manager import and basic functionality"""
    try:
        print("🧪 Testing Performance Manager...")
        from performance_manager import get_performance_manager
        
        # Initialize performance manager
        perf_manager = get_performance_manager()
        print("✅ Performance manager imported successfully")
        
        # Test basic functionality
        stats = perf_manager.get_performance_report()
        print(f"✅ Performance stats: Memory: {stats['current_memory_mb']:.1f}MB, CPU: {stats['cpu_percent']:.1f}%")
        
        # Test task submission
        def test_task():
            time.sleep(0.1)
            return "Task completed"
            
        perf_manager.submit_account_task("test_phone", "check", test_task)
        print("✅ Task submission works")
        
        # Wait a moment for task to complete
        time.sleep(0.5)
        
        return True
        
    except Exception as e:
        print(f"❌ Performance manager test failed: {e}")
        return False

def test_performance_integration():
    """Test the performance integration import"""
    try:
        print("🧪 Testing Performance Integration...")
        from performance_integration import integrate_performance_manager
        print("✅ Performance integration imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Performance integration test failed: {e}")
        return False

def test_main_app_import():
    """Test if main.py can be imported without errors"""
    try:
        print("🧪 Testing Main App Import...")
        import main
        print("✅ Main app imports successfully")
        
        # Check if performance manager constant exists
        if hasattr(main, 'PERFORMANCE_MANAGER_AVAILABLE'):
            print(f"✅ Performance manager availability: {main.PERFORMANCE_MANAGER_AVAILABLE}")
        else:
            print("⚠️  Performance manager constant not found")
            
        return True
        
    except Exception as e:
        print(f"❌ Main app import failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing TG Checker Performance System")
    print("=" * 50)
    
    tests = [
        ("Performance Manager", test_performance_manager),
        ("Performance Integration", test_performance_integration),
        ("Main App Import", test_main_app_import)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                failed += 1
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name}: ERROR - {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("""
🎉 All tests passed! The performance system is ready.

🚀 Performance Features Available:
• ThreadPoolExecutor with 50+ workers for joins
• 30 workers for checking operations
• 20 workers for forwarding operations
• Real-time resource monitoring
• Automatic memory management
• Database connection pooling
• Task queuing with priorities

📋 Ready for 100+ account operations!
""")
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 