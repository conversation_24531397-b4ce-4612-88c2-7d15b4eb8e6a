import difflib
import sys

def compare_files(file1, file2):
    with open(file1, 'r', encoding='utf-8') as f1, open(file2, 'r', encoding='utf-8') as f2:
        file1_lines = f1.readlines()
        file2_lines = f2.readlines()
        
    diff = difflib.unified_diff(file1_lines, file2_lines, fromfile=file1, tofile=file2)
    return ''.join(diff)

if __name__ == '__main__':
    if len(sys.argv) < 3:
        print("Usage: python compare_files.py file1 file2")
        sys.exit(1)
    
    file1 = sys.argv[1]
    file2 = sys.argv[2]
    
    print(compare_files(file1, file2)) 