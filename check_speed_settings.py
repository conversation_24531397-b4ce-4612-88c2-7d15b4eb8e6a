#!/usr/bin/env python3
"""
Check if Speed Check Time settings were successfully added to main.py
"""

import os
import re

def check_speed_settings():
    """Check if the Speed Check Time settings were added to main.py"""
    # Check if main.py exists
    if not os.path.exists('main.py'):
        print("Error: main.py not found")
        return False
    
    # Read the file content
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check for the Speed Check Time settings
    if "Speed Check Time Per 1 Group" in content:
        print("✅ Speed Check Time settings group found")
    else:
        print("❌ Speed Check Time settings group not found")
    
    # Check for min/max seconds inputs
    if "min_seconds_input" in content and "max_seconds_input" in content:
        print("✅ Min/Max seconds inputs found")
    else:
        print("❌ Min/Max seconds inputs not found")
    
    # Check for update_speed_check_range method
    if "def update_speed_check_range" in content:
        print("✅ update_speed_check_range method found")
    else:
        print("❌ update_speed_check_range method not found")
    
    # Check if settings are saved
    if "min_check_seconds" in content and "max_check_seconds" in content:
        print("✅ Speed check settings save code found")
    else:
        print("❌ Speed check settings save code not found")
    
    # Check for random delay code in checker thread
    if "random.uniform(min_seconds, max_seconds)" in content:
        print("✅ Random delay code found in checker thread")
    else:
        print("❌ Random delay code not found in checker thread")
    
    # Check if import random was added
    if "import random" in content:
        print("✅ import random statement found")
    else:
        print("❌ import random statement not found")
    
    # Check batch files
    if os.path.exists("run_with_speed_check.bat"):
        print("✅ run_with_speed_check.bat created")
    else:
        print("❌ run_with_speed_check.bat not found")
    
    if os.path.exists("run_with_speed_check_kurdish.bat"):
        print("✅ run_with_speed_check_kurdish.bat created")
    else:
        print("❌ run_with_speed_check_kurdish.bat not found")
    
    # Look for syntax errors
    invalid_syntax = [
        "'''Ensure min seconds <= max seconds.'''",
        "\"\"\"Ensure min seconds <= max seconds.\"\"\""
    ]
    
    for syntax in invalid_syntax:
        if syntax in content:
            print(f"⚠️ Warning: Potential invalid syntax found: {syntax}")
    
    print("\nSummary of additions:")
    print("1. Added Speed Check Time Per 1 Group settings box")
    print("2. Added Min and Max seconds input fields")
    print("3. Added validation to ensure Min <= Max")
    print("4. Added code to apply random delay between each group check")
    print("5. Created batch files to run the application with speed check settings")
    
    print("\nTo test the implementation:")
    print("1. Run the application using: run_with_speed_check.bat")
    print("2. Open Settings tab and configure Min/Max seconds")
    print("3. Start group checking and observe random delays between checks")

if __name__ == "__main__":
    check_speed_settings() 