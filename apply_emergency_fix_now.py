#!/usr/bin/env python3
"""
Apply Emergency UI Freeze Fix to Running TG Checker
This script can be run while TG Checker is running to apply the fix.
"""

import sys
import time
import psutil
import gc

def find_tg_checker_process():
    """Find running TG Checker process"""
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline']:
                cmdline = ' '.join(proc.info['cmdline'])
                if 'main.py' in cmdline or 'TG Checker' in cmdline:
                    return proc
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def apply_fix_to_existing_app():
    """Try to apply fix to existing TG Checker application objects"""
    
    print("🔍 Searching for TG Checker application objects in memory...")
    
    # Look for TGCheckerApp instances in memory
    tg_checker_apps = []
    
    for obj in gc.get_objects():
        if hasattr(obj, '__class__') and 'TGCheckerApp' in str(obj.__class__):
            tg_checker_apps.append(obj)
    
    if not tg_checker_apps:
        print("❌ No TG Checker application objects found in memory")
        return False
    
    print(f"✅ Found {len(tg_checker_apps)} TG Checker application object(s)")
    
    # Apply fix to each instance
    fixed_count = 0
    for app in tg_checker_apps:
        try:
            from emergency_ui_freeze_fix import apply_emergency_ui_freeze_fix
            fix = apply_emergency_ui_freeze_fix(app)
            print(f"✅ Applied emergency fix to TG Checker instance")
            fixed_count += 1
        except Exception as e:
            print(f"❌ Failed to apply fix to instance: {e}")
    
    return fixed_count > 0

def main():
    """Main function"""
    
    print("🚑 Emergency UI Freeze Fix Application")
    print("=" * 50)
    
    # Check if TG Checker is running
    proc = find_tg_checker_process()
    if proc:
        print(f"✅ Found TG Checker process (PID: {proc.pid})")
    else:
        print("⚠️ TG Checker process not found - it may not be running")
    
    # Try to apply fix
    if apply_fix_to_existing_app():
        print("=" * 50)
        print("✅ EMERGENCY FIX APPLIED SUCCESSFULLY!")
        print("🎯 Your TG Checker should now have:")
        print("   - Non-blocking joining operations")
        print("   - Responsive UI during join tasks")
        print("   - No more 'Not Responding' freeze")
        print("=" * 50)
        print("🔥 Try clicking 'Start All Tasks' now - it should work!")
    else:
        print("=" * 50)
        print("❌ Could not apply fix to running instance")
        print("💡 Try restarting TG Checker with the fixed version")
        print("💡 Use 'Run_TG_Checker_Fixed.bat' for automatic fix application")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main() 