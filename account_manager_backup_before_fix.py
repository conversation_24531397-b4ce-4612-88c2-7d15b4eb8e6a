import os
import json
import sqlite3
import logging
from datetime import datetime
import asyncio
import threading
import time
import signal
import atexit
from contextlib import contextmanager
from tg_client import TelegramClient

# Track all open connections globally for proper cleanup
_OPEN_CONNECTIONS = set()
_CONN_LOCK = threading.RLock()

def _exit_handler():
    """Clean up any open database connections on program exit."""
    with _CONN_LOCK:
        for conn in list(_OPEN_CONNECTIONS):
            try:
                conn.close()
            except:
                pass
        _OPEN_CONNECTIONS.clear()

# Register the exit handler
atexit.register(_exit_handler)

def fix_db_lock(db_path):
    """Utility function to fix database lock issues by creating a backup and recovering if necessary."""
    try:
        # First try to create a backup of the current database
        backup_path = f"{db_path}.backup_{int(datetime.now().timestamp())}"
        import shutil
        
        # Only backup if the database file exists
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            logging.info(f"Created database backup at {backup_path}")
            
            try:
                # Try to connect with DELETE journal mode
                conn = sqlite3.connect(db_path, timeout=120.0)
                
                # Try to switch to DELETE journal mode
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.execute("PRAGMA busy_timeout=120000")  # 2 minute timeout
                conn.commit()
                
                # Run VACUUM to compact the database
                conn.execute("VACUUM")
                conn.commit()
                
                # Switch back to WAL mode with optimized settings
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA busy_timeout=120000")
                conn.execute("PRAGMA wal_autocheckpoint=1000")
                conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping
                conn.commit()
                
                conn.close()
                logging.info("Database cleanup completed successfully")
                return True
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    logging.warning("Database still locked during fix attempt, trying alternative method")
                    # Database is still locked, try alternative approach
                    try:
                        # Wait a bit for locks to clear
                        time.sleep(3)
                        
                        # Delete the current database and restore from backup
                        if os.path.exists(db_path):
                            os.remove(db_path)
                            time.sleep(1)
                            
                        # Restore from backup
                        shutil.copy2(backup_path, db_path)
                        logging.info(f"Restored database from backup after lock issues")
                        return True
                    except Exception as restore_err:
                        logging.error(f"Failed to restore database from backup: {str(restore_err)}")
                else:
                    logging.error(f"Database error during fix attempt: {str(e)}")
        else:
            logging.warning(f"Database file {db_path} not found, can't fix locks")
            return False
            
    except Exception as e:
        logging.error(f"Error fixing database lock: {str(e)}")
        return False
        
    return False

class AccountManager:
    """Manages Telegram accounts for the application."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self.accounts = []
        self.clients = {}
        
        # Add database connection lock for thread safety
        self._db_lock = threading.RLock()
        
        # Track SQLite busy handler calls
        self._busy_handler_calls = 0
        self._max_busy_retries = 25  # Maximum times to retry when database is busy
        
        # Initialize database
        self._init_database()
        
        # Load accounts from database
        self._load_accounts()
    
    def _sqlite_busy_handler(self, tries):
        """Custom busy handler for SQLite to handle database locks."""
        self._busy_handler_calls += 1
        
        if tries >= self._max_busy_retries:
            self.logger.error(f"SQLite busy handler giving up after {tries} attempts")
            return 0  # Give up and return SQLITE_BUSY
            
        # Exponential backoff with jitter
        import random
        delay = min(0.1 * (1.5 ** tries) + random.uniform(0, 0.1), 10.0)
        
        self.logger.warning(f"Database is busy, waiting {delay:.2f}s (attempt {tries+1}/{self._max_busy_retries})")
        time.sleep(delay)
        
        # If we've had several retries, try to fix the database
        if tries == 10:
            try:
                self.logger.warning("Attempting emergency database recovery during busy wait")
                fix_db_lock(self.db_path)
            except Exception as e:
                self.logger.error(f"Failed to perform emergency recovery: {str(e)}")
        
        return 1  # Tell SQLite to retry
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 8  # Increased from 5 to 8
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                with self._db_lock:
                    # Reset busy handler call counter
                    self._busy_handler_calls = 0
                    
                    # Connect with higher timeout (increased from 60s to 120s)
                    conn = sqlite3.connect(self.db_path, timeout=120.0)
                    
                    # Register the busy handler
                    # conn.set_busy_handler(self._sqlite_busy_handler)
                    # Instead of set_busy_handler, we use timeout and PRAGMA busy_timeout
                    
                    # Track this connection for cleanup
                    with _CONN_LOCK:
                        _OPEN_CONNECTIONS.add(conn)
                    
                    conn.row_factory = sqlite3.Row
                    
                    # Enable WAL mode for better concurrent access with optimal settings
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA busy_timeout=120000")  # Increased from 60000 to 120000ms (2 minutes)
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA wal_autocheckpoint=1000")  # Checkpoint after 1000 pages
                    conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping for better performance
                    conn.execute("PRAGMA temp_store=MEMORY")  # Use memory for temp storage
                    
                    yield conn
                    
                    # Remove from tracked connections if yielded successfully
                    with _CONN_LOCK:
                        if conn in _OPEN_CONNECTIONS:
                            _OPEN_CONNECTIONS.remove(conn)
                    
                    # Close connection if it's still open
                    if conn:
                        conn.close()
                        conn = None
                    
                    return  # Successfully got connection, exit retry loop
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    # Database is locked, retry after delay
                    self.logger.warning(f"Database is locked, retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})")
                    if conn:
                        try:
                            # Remove from tracked connections
                            with _CONN_LOCK:
                                if conn in _OPEN_CONNECTIONS:
                                    _OPEN_CONNECTIONS.remove(conn)
                            conn.close()
                        except:
                            pass
                    conn = None
                    
                    # Increase retry delay for next attempt (exponential backoff)
                    retry_delay = min(retry_delay * 1.5, 10.0)
                    
                    # On the second attempt, try to fix database locks
                    if attempt in [1, 3, 5]:
                        try:
                            # Different approaches on different retry attempts
                            if attempt == 1:
                                self.logger.warning("First retry: Attempting standard database fix")
                                fix_db_lock(self.db_path)
                            elif attempt == 3:
                                self.logger.warning("Third retry: Attempting aggressive database recovery")
                                # Try to terminate any lingering connections first
                                self._kill_all_connections()
                                time.sleep(2)
                                fix_db_lock(self.db_path)
                            elif attempt == 5:
                                self.logger.warning("Fifth retry: Attempting database rebuild from backup")
                                self._rebuild_from_backup()
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database locks: {str(fix_err)}")
                    
                    time.sleep(retry_delay)
                    continue
                else:
                    # Other error or max retries reached
                    if conn:
                        try:
                            # Remove from tracked connections
                            with _CONN_LOCK:
                                if conn in _OPEN_CONNECTIONS:
                                    _OPEN_CONNECTIONS.remove(conn)
                            conn.rollback()
                            conn.close()
                        except:
                            pass
                    self.logger.error(f"Database error after {attempt+1} attempts: {str(e)}")
                    raise
            except Exception as e:
                if conn:
                    try:
                        # Remove from tracked connections
                        with _CONN_LOCK:
                            if conn in _OPEN_CONNECTIONS:
                                _OPEN_CONNECTIONS.remove(conn)
                        conn.rollback()
                        conn.close()
                    except:
                        pass
                self.logger.error(f"Database error: {str(e)}")
                raise
            finally:
                if conn and attempt == max_retries - 1:
                    try:
                        # Remove from tracked connections
                        with _CONN_LOCK:
                            if conn in _OPEN_CONNECTIONS:
                                _OPEN_CONNECTIONS.remove(conn)
                        conn.close()
                    except:
                        pass
    
    def _kill_all_connections(self):
        """Attempt to forcefully close all database connections."""
        try:
            self.logger.info("Forcefully closing all database connections")
            
            # Close all tracked connections
            with _CONN_LOCK:
                for conn in list(_OPEN_CONNECTIONS):
                    try:
                        conn.close()
                    except:
                        pass
                _OPEN_CONNECTIONS.clear()
            
            # Reset all client connections
            self.clients = {}
            
            # Wait a moment for connections to fully close
            time.sleep(1)
            
            self.logger.info("All database connections have been forcefully closed")
        except Exception as e:
            self.logger.error(f"Error killing connections: {str(e)}")
    
    def _rebuild_from_backup(self):
        """Attempt to rebuild the database from the most recent backup."""
        try:
            import glob
            import shutil
            
            # Find the most recent backup
            backups = glob.glob(f"{self.db_path}.backup_*")
            if not backups:
                self.logger.warning("No backup files found to rebuild from")
                return False
            
            # Sort by modification time (newest first)
            backups.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            most_recent = backups[0]
            
            self.logger.info(f"Rebuilding database from backup: {most_recent}")
            
            # Kill all connections first
            self._kill_all_connections()
            
            # Rename current database if it exists
            if os.path.exists(self.db_path):
                corrupt_name = f"{self.db_path}.corrupt_{int(datetime.now().timestamp())}"
                os.rename(self.db_path, corrupt_name)
                self.logger.info(f"Renamed current database to {corrupt_name}")
            
            # Copy the backup to the main database file
            shutil.copy2(most_recent, self.db_path)
            
            # Make sure WAL files are removed if they exist
            for ext in ['-wal', '-shm']:
                wal_file = f"{self.db_path}{ext}"
                if os.path.exists(wal_file):
                    try:
                        os.remove(wal_file)
                        self.logger.info(f"Removed WAL file: {wal_file}")
                    except Exception as e:
                        self.logger.warning(f"Could not remove WAL file {wal_file}: {str(e)}")
            
            self.logger.info("Database successfully rebuilt from backup")
            return True
        except Exception as e:
            self.logger.error(f"Failed to rebuild database from backup: {str(e)}")
            return False
    
    def _init_database(self):
        """Initialize the SQLite database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create accounts table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                ''')
                
                # Add new columns to existing accounts table if they don't exist
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_age_days INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN is_aged INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN daily_group_limit INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN last_age_check TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add missing columns that were causing errors
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN name TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                    
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN username TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add account_type column for session vs normal accounts
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_type TEXT DEFAULT 'normal'")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Create errors table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create blacklist table for permanently deleted accounts
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deleted_accounts (
                        phone TEXT PRIMARY KEY,
                        deleted_at TEXT,
                        reason TEXT DEFAULT 'user_deleted'
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def _load_accounts(self):
        """Load accounts from the database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM accounts")
                rows = cursor.fetchall()
                
                self.accounts = [dict(row) for row in rows]
                
            self.logger.info(f"Loaded {len(self.accounts)} accounts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
    
    def get_accounts(self):
        """Get all accounts."""
        return self.accounts
    
    def get_active_accounts(self):
        """Get only active accounts."""
        return [acc for acc in self.accounts if acc.get("active", 0) == 1]
    
    def get_account(self, phone):
        """Get a specific account by phone number."""
        for account in self.accounts:
            if account.get("phone") == phone:
                return account
        return None
    
    def add_account(self, phone, api_id, api_hash, active=False, account_type="normal", session_file=None):
        """Add a new account to the database."""
        try:
            # Check if account already exists
            existing_account = self.get_account(phone)
            if existing_account:
                # Update existing account instead of raising error
                self.logger.info(f"Updating existing account {phone}")
                return self.update_account_credentials(phone, api_id, api_hash, account_type)
            
            # Use multiple retries for database operations
            max_retries = 3
            retry_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    with self._get_db_connection() as conn:
                        cursor = conn.cursor()
                        
                        # Set session file path based on phone number (remove + sign) if not provided
                        if not session_file:
                            clean_phone = phone.replace('+', '')
                            session_file = f"sessions/{clean_phone}"
                        
                        # Ensure sessions directory exists
                        os.makedirs(os.path.dirname(session_file) if os.path.dirname(session_file) else "sessions", exist_ok=True)
                        
                        self.logger.info(f"Adding account {phone} with session file: {session_file}")
                        
                        cursor.execute(
                            "INSERT INTO accounts (phone, api_id, api_hash, session_file, active, status, last_check, account_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                            (phone, api_id, api_hash, session_file, 1 if active else 0, "new", datetime.now().isoformat(), account_type)
                        )
                        
                        conn.commit()
                    
                    # Reload accounts
                    self._load_accounts()
                    
                    self.logger.info(f"Added {account_type} account {phone}")
                    return True
                    
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        self.logger.warning(f"Database locked when adding account {phone}, retrying ({attempt+1}/{max_retries})...")
                        time.sleep(retry_delay)
                        retry_delay *= 1.5
                        
                        # On the second attempt, try to fix database locks
                        if attempt == 1:
                            try:
                                fix_db_lock(self.db_path)
                            except Exception as fix_err:
                                self.logger.error(f"Failed to fix database locks: {str(fix_err)}")
                        
                        continue
                    else:
                        self.logger.error(f"Database error adding account {phone}: {str(e)}")
                        raise
                except Exception as e:
                    self.logger.error(f"Error adding account {phone}: {str(e)}")
                    raise
                
        except Exception as e:
            self.logger.error(f"Error adding account {phone}: {str(e)}")
            return False
    
    def update_account_credentials(self, phone, api_id, api_hash, account_type="normal"):
        """Update account credentials and type."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET api_id = ?, api_hash = ?, account_type = ?, status = ? WHERE phone = ?",
                    (api_id, api_hash, account_type, "updated", phone)
                )
                
                conn.commit()
            
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Updated credentials for {account_type} account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account credentials {phone}: {str(e)}")
            return False
    
    def remove_account(self, phone):
        """Remove an account from the database and add to blacklist."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Add to blacklist first
                cursor.execute(
                    "INSERT OR REPLACE INTO deleted_accounts (phone, deleted_at, reason) VALUES (?, ?, ?)",
                    (phone, datetime.now().isoformat(), "user_deleted")
                )
                
                # Delete errors for this account
                cursor.execute("DELETE FROM errors WHERE phone = ?", (phone,))
                
                # Delete the account
                cursor.execute("DELETE FROM accounts WHERE phone = ?", (phone,))
                
                conn.commit()
            
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Removed account {phone}")
            return True, "Account removed successfully"
            
        except Exception as e:
            self.logger.error(f"Error removing account {phone}: {str(e)}")
            return False, str(e)
    
    def activate_account(self, phone):
        """Activate an account (allows multiple active accounts)."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Simply activate the specified account without deactivating others
            cursor.execute(
                "UPDATE accounts SET active = 1 WHERE phone = ?",
                (phone,)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["active"] = 1
            
            self.logger.info(f"Activated account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error activating account {phone}: {str(e)}")
            return False
    
    def set_active(self, phone, active):
        """Set active status for an account."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET active = ? WHERE phone = ?",
                    (1 if active else 0, phone)
                )
                
                conn.commit()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["active"] = 1 if active else 0
            
            self.logger.info(f"Set account {phone} active status to {active}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting active status for {phone}: {str(e)}")
            return False
    
    def update_account_info(self, phone, name=None, username=None):
        """Update account information like name and username."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Build the update query dynamically
                updates = []
                params = []
                
                if name is not None:
                    updates.append("name = ?")
                    params.append(name)
                
                if username is not None:
                    updates.append("username = ?")
                    params.append(username)
                
                if updates:
                    query = f"UPDATE accounts SET {', '.join(updates)} WHERE phone = ?"
                    params.append(phone)
                    
                    cursor.execute(query, params)
                    conn.commit()
                    
                    # Update local accounts list
                    for account in self.accounts:
                        if account.get("phone") == phone:
                            if name is not None:
                                account["name"] = name
                            if username is not None:
                                account["username"] = username
                    
                    self.logger.info(f"Updated account info for {phone}")
                    return True, "Account info updated"
                else:
                    return True, "No changes to make"
                    
        except Exception as e:
            self.logger.error(f"Error updating account info for {phone}: {str(e)}")
            return False, str(e)
    
    def update_check_time(self, phone, status="OK"):
        """Update the last check time for an account."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET last_check = ?, status = ? WHERE phone = ?",
                    (datetime.now().isoformat(), status, phone)
                )
                
                conn.commit()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["last_check"] = datetime.now().isoformat()
                    account["status"] = status
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating check time for {phone}: {str(e)}")
            return False
    
    def update_account_status(self, phone, status, error_count=None):
        """Update the status of an account."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                if error_count is not None:
                    cursor.execute(
                        "UPDATE accounts SET status = ?, last_check = ?, errors = ? WHERE phone = ?",
                        (status, datetime.now().isoformat(), error_count, phone)
                    )
                else:
                    cursor.execute(
                        "UPDATE accounts SET status = ?, last_check = ? WHERE phone = ?",
                        (status, datetime.now().isoformat(), phone)
                    )
                
                conn.commit()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["status"] = status
                    account["last_check"] = datetime.now().isoformat()
                    if error_count is not None:
                        account["errors"] = error_count
            
            self.logger.info(f"Updated status for account {phone} to {status}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account {phone} status: {str(e)}")
            return False
    
    def sync_accounts(self):
        """Synchronize accounts with the database."""
        try:
            # Simply reload accounts from the database
            self._load_accounts()
            return True
        except Exception as e:
            self.logger.error(f"Error syncing accounts: {str(e)}")
            return False

    def clean_database(self):
        """Clean and optimize the database to resolve locking issues."""
        try:
            self.logger.info("Starting enhanced database cleanup...")
            
            # Close all active connections first
            self._kill_all_connections()
            
            # Create backup first
            backup_path = f"{self.db_path}.backup_{int(datetime.now().timestamp())}"
            try:
                import shutil
                if os.path.exists(self.db_path):
                    shutil.copy2(self.db_path, backup_path)
                    self.logger.info(f"Created database backup at {backup_path}")
            except Exception as backup_err:
                self.logger.error(f"Failed to create database backup: {str(backup_err)}")
            
            # Try to fix database locks
            try:
                with sqlite3.connect(self.db_path, timeout=120.0) as conn:
                    # Try to switch to DELETE journal mode
                    conn.execute("PRAGMA journal_mode=DELETE")
                    conn.execute("PRAGMA synchronous=OFF")
                    conn.commit()
                    
                    # Check integrity
                    cursor = conn.cursor()
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]
                    
                    if integrity_result == "ok":
                        self.logger.info("Database integrity check passed")
                    else:
                        self.logger.warning(f"Database integrity issues found: {integrity_result}")
                        # If database is corrupted, restore from backup
                        conn.close()
                        self.logger.info("Restoring from backup due to integrity issues...")
                        time.sleep(1)
                        os.remove(self.db_path)
                        shutil.copy2(backup_path, self.db_path)
                        self.logger.info("Database restored from backup due to integrity issues")
                        return True
                    
                    # Run VACUUM to compact the database
                    self.logger.info("Running VACUUM on database...")
                    conn.execute("VACUUM")
                    conn.commit()
                    
                    # Run ANALYZE to optimize query planning
                    self.logger.info("Running ANALYZE on database...")
                    conn.execute("ANALYZE")
                    conn.commit()
                    
                    # Set optimal settings for regular operation
                    conn.execute("PRAGMA journal_mode=WAL")
                    conn.execute("PRAGMA synchronous=NORMAL")
                    conn.execute("PRAGMA busy_timeout=120000")
                    conn.execute("PRAGMA wal_autocheckpoint=1000")
                    conn.execute("PRAGMA mmap_size=67108864")  # 64MB memory mapping
                    conn.commit()
                    
                    self.logger.info("Database optimized successfully")
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    self.logger.warning("Database still locked during optimization, trying recovery...")
                    # Try to recover from backup
                    try:
                        # Wait to ensure no active operations
                        time.sleep(3)
                        
                        # Make sure no connections are active
                        self._kill_all_connections()
                        
                        # Try to remove the database file
                        if os.path.exists(self.db_path):
                            os.remove(self.db_path)
                            self.logger.info("Removed locked database file")
                            time.sleep(1)
                        
                        # Restore from backup
                        import shutil
                        shutil.copy2(backup_path, self.db_path)
                        self.logger.info("Database restored from backup")
                        
                        # Reload accounts
                        self._load_accounts()
                        
                        return True
                    except Exception as recover_err:
                        self.logger.error(f"Failed to recover database: {str(recover_err)}")
                else:
                    self.logger.error(f"Database error during optimization: {str(e)}")
            except Exception as e:
                self.logger.error(f"Error during database optimization: {str(e)}")
            
            # Reload accounts
            self._load_accounts()
            
            return True
        except Exception as e:
            self.logger.error(f"Error during database cleanup: {str(e)}")
            return False
    
    async def check_account_age_with_bot(self, phone):
        """Check account age using @TGDNAbot."""
        account = self.get_account(phone)
        if not account:
            return False, "Account not found"
        
        client = None
        try:
            import asyncio
            from logger import log_usage_checker
            from tg_client import TelegramClient
            
            # Create client for the account
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            session_file = account.get("session_file")
            
            client = TelegramClient(api_id, api_hash, phone, session_file, self.logger)
            
            # Connect and check authorization with timeout
            try:
                connected = await asyncio.wait_for(client.connect(), timeout=15)
                if not connected:
                    log_usage_checker(self.logger, f"Failed to connect client for age check: {phone}", logging.ERROR)
                    return False, "Failed to connect client"
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Connection timeout for age check: {phone}", logging.ERROR)
                return False, "Connection timeout"
            
            try:
                authorized = await asyncio.wait_for(client.check_authorization(), timeout=10)
                if not authorized:
                    log_usage_checker(self.logger, f"Client not authorized for age check: {phone}", logging.ERROR)
                    return False, "Client not authorized"
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Authorization check timeout for age check: {phone}", logging.ERROR)
                return False, "Authorization timeout"
            
            # Get the @TGDNAbot entity with timeout
            try:
                bot_entity = await asyncio.wait_for(
                    client.client.get_entity("@TGDNAbot"), 
                    timeout=10
                )
                log_usage_checker(self.logger, f"Found @TGDNAbot entity for age check: {phone}")
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Timeout finding @TGDNAbot for {phone}", logging.ERROR)
                return False, "Timeout finding @TGDNAbot"
            except Exception as e:
                log_usage_checker(self.logger, f"Failed to find @TGDNAbot: {str(e)}", logging.ERROR)
                return False, f"Failed to find @TGDNAbot: {str(e)}"
            
            # Send /start command to the bot with timeout
            try:
                await asyncio.wait_for(
                    client.client.send_message(bot_entity, "/start"), 
                    timeout=10
                )
                log_usage_checker(self.logger, f"Sent /start command to @TGDNAbot for {phone}")
                
                # Wait a bit for the bot to respond
                await asyncio.sleep(3)
                
                # Get messages from the bot with timeout
                messages = await asyncio.wait_for(
                    client.client.get_messages(bot_entity, limit=5), 
                    timeout=10
                )
                
                # Parse the response for age information
                age_days = None
                is_aged = False
                
                for message in messages:
                    if message.message:
                        text = message.message.lower()
                        
                        # Look for age patterns in the response
                        import re
                        
                        # Pattern for "X days old" or "X months old" or "X years old"
                        days_match = re.search(r'(\d+)\s*days?\s*old', text)
                        months_match = re.search(r'(\d+)\s*months?\s*old', text)
                        years_match = re.search(r'(\d+)\s*years?\s*old', text)
                        
                        if days_match:
                            age_days = int(days_match.group(1))
                        elif months_match:
                            age_days = int(months_match.group(1)) * 30  # Approximate
                        elif years_match:
                            age_days = int(years_match.group(1)) * 365  # Approximate
                        
                        # Check if account is considered aged
                        if any(keyword in text for keyword in ['aged', 'old account', 'mature']):
                            is_aged = True
                        elif any(keyword in text for keyword in ['new', 'young', 'fresh']):
                            is_aged = False
                        
                        if age_days is not None:
                            break
                
                # If we couldn't parse the age, set defaults
                if age_days is None:
                    # Check for aged status from text patterns
                    if any('aged' in msg.message.lower() or 'old' in msg.message.lower() 
                           for msg in messages if msg.message):
                        age_days = 365  # Default to 1 year for aged accounts
                        is_aged = True
                    else:
                        age_days = 30   # Default to 30 days for new accounts
                        is_aged = False
                
                # Update account age information
                success = self.update_account_age(phone, age_days, is_aged)
                
                if success:
                    # Calculate and log the daily group limit
                    daily_limit = self.calculate_daily_group_limit(age_days, is_aged)
                    aged_status = "Yes" if is_aged else "No"
                    
                    log_usage_checker(
                        self.logger, 
                        f"Account: {phone} | Aged: {aged_status} | Group Limit: {daily_limit}"
                    )
                    
                    return True, f"Age check completed: {age_days} days, aged: {is_aged}"
                else:
                    return False, "Failed to update account age information"
                
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Timeout communicating with @TGDNAbot for {phone}", logging.ERROR)
                return False, "Timeout communicating with bot"
            except Exception as e:
                log_usage_checker(self.logger, f"Error communicating with @TGDNAbot for {phone}: {str(e)}", logging.ERROR)
                return False, f"Error communicating with bot: {str(e)}"
            
        except Exception as e:
            from logger import log_usage_checker
            log_usage_checker(self.logger, f"Age check error for {phone}: {str(e)}", logging.ERROR)
            return False, str(e)
        finally:
            # Always disconnect the client
            if client:
                try:
                    await client.disconnect()
                except Exception as e:
                    from logger import log_usage_checker
                    log_usage_checker(self.logger, f"Error disconnecting client for {phone}: {str(e)}", logging.WARNING)
    
    def update_account_age(self, phone, age_days, is_aged):
        """Update account age information in the database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Calculate daily group limit
                daily_limit = self.calculate_daily_group_limit(age_days, is_aged)
                
                cursor.execute(
                    """UPDATE accounts SET 
                       account_age_days = ?, 
                       is_aged = ?, 
                       daily_group_limit = ?, 
                       last_age_check = ? 
                       WHERE phone = ?""",
                    (age_days, 1 if is_aged else 0, daily_limit, datetime.now().isoformat(), phone)
                )
                
                conn.commit()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["account_age_days"] = age_days
                    account["is_aged"] = 1 if is_aged else 0
                    account["daily_group_limit"] = daily_limit
                    account["last_age_check"] = datetime.now().isoformat()
            
            self.logger.info(f"Updated age info for account {phone}: {age_days} days, aged: {is_aged}, limit: {daily_limit}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account age for {phone}: {str(e)}")
            return False
    
    def calculate_daily_group_limit(self, age_days, is_aged):
        """Calculate daily group checking limit based on account age."""
        # Base limits
        if is_aged or age_days >= 365:  # 1 year or explicitly aged
            base_limit = 800
        elif age_days >= 180:  # 6 months
            base_limit = 600
        elif age_days >= 90:   # 3 months
            base_limit = 400
        elif age_days >= 30:   # 1 month
            base_limit = 200
        else:  # New accounts
            base_limit = 100
        
        # Add bonus based on specific age
        age_bonus = min(age_days // 30 * 10, 200)  # 10 extra per month, max 200
        
        total_limit = base_limit + age_bonus
        return min(total_limit, 1000)  # Cap at 1000 per day
    
    async def auto_check_account_ages(self):
        """Check ages for all accounts that need it."""
        accounts_needing_check = self.get_accounts_needing_age_check()
        results = []
        
        for account in accounts_needing_check:
            phone = account.get("phone")
            if phone:
                try:
                    success, message = await self.check_account_age_with_bot(phone)
                    results.append((phone, success, message))
                    
                    # Small delay between checks to avoid rate limiting
                    await asyncio.sleep(2)
                except Exception as e:
                    self.logger.error(f"Error checking age for {phone}: {str(e)}")
                    results.append((phone, False, str(e)))
        
        return results
    
    def get_accounts_needing_age_check(self):
        """Get accounts that need age checking (no age data or old check)."""
        needing_check = []
        
        for account in self.accounts:
            age_days = account.get("account_age_days")
            last_check = account.get("last_age_check")
            
            # Need check if no age data or check is older than 7 days
            needs_check = (
                age_days is None or 
                age_days == 0 or 
                not last_check or
                (last_check and (datetime.now() - datetime.fromisoformat(last_check)).days > 7)
            )
            
            if needs_check:
                needing_check.append(account)
        
        return needing_check 