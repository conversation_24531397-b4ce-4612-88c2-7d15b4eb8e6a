#!/usr/bin/env python3
"""
Emergency fix for main.py - create a new file with only the problematic line fixed
"""

import os
import re
import shutil

def emergency_fix():
    """Create a new version of main.py with only the problematic line fixed"""
    print("=== Creating emergency fixed version of main.py ===")
    
    input_file = "main.py"
    output_file = "main_fixed_emergency.py"
    
    # Create a clean copy first
    shutil.copy2(input_file, output_file)
    print(f"Created copy at {output_file}")
    
    # Read the content, find and replace the problematic line directly
    with open(output_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. Fix indentation issue - replace the problematic method definition with fixed indentation
    content = re.sub(
        r'[^\n]*def auto_refresh_missing_account_info\(self\):[^\n]*\n',
        '    def auto_refresh_missing_account_info(self):\n',
        content
    )
    
    # 2. Fix syntax error - find try blocks followed by else without except
    content = re.sub(
        r'(\s+)try:\n((?:[^\n]*\n)*?)(\s+)else:',
        r'\1try:\n\2\1except Exception as e:\n\1    print(f"Error: {e}")\n\3else:',
        content
    )
    
    # Write the fixed content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed content written to {output_file}")
    
    # Create batch file
    with open("run_emergency.bat", "w") as f:
        f.write(f"""@echo off
echo Running TG Checker (Emergency Fixed Version)...
python {output_file}
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_emergency.bat")
    
    # Create Kurdish version
    with open("run_emergency_kurdish.bat", "w") as f:
        f.write(f"""@echo off
echo TG Checker - Barnama charasarkrawa...
python {output_file}
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_emergency_kurdish.bat")
    
    print("\nYou can now run the emergency fixed version using:")
    print("python main_fixed_emergency.py")
    print("or by double-clicking run_emergency.bat")
    
    return True

def fix_syntax_error():
    print("Fixing syntax error in main.py...")
    
    # Read the file content
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    # Fix the line with the syntax error (line 808)
    for i, line in enumerate(lines):
        if "self.last_processed_index = task.get('current_index', 0) if t" in line:
            lines[i] = line.replace("if t", "if task else 0")
            print(f"Fixed line {i+1}: {line.strip()} -> {lines[i].strip()}")
    
    # Write the fixed content back to the file
    with open("main.py", "w", encoding="utf-8") as file:
        file.writelines(lines)
    
    print("Fix applied. Try running the program now.")

if __name__ == "__main__":
    emergency_fix()
    fix_syntax_error() 