@echo off
echo Creating application icon...

:: Activate the virtual environment if it exists
if exist "venv" 2025-05-28 09:53:40,387 - tg_checker - INFO - Disconnected client for +*************
C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py:971: DeprecationWarning: sipPyTypeDict() is deprecated, the extension module should use sipPyTypeDictRef() instead
  class AccountDialog(QDialog):
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 963, in add_account
    self._add_account_via_code()
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 1454, in _add_account_via_code
    dialog = AccountDialog(self)
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\main.py", line 1041, in __init__
    self.api_credentials_toggle.stateChanged.connect(self.toggle_api_credentials_visibility)
                                                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'AccountDialog' object has no attribute 'toggle_api_credentials_visibility'
(
    call venv\Scripts\activate.bat
) else (
    echo Virtual environment not found, using system Python...
)

:: Run the icon creation script
python create_icon.py

:: Deactivate the virtual environment if it was activated
if exist "venv" (
    call venv\Scripts\deactivate.bat
)

echo Icon creation complete!
pause 