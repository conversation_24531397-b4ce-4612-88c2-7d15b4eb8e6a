"""
URGENT FIX APPLICATION SCRIPT
🔥 IMMEDIATE FIX FOR TG CHECKER CRASHES AND FREEZING

This script applies critical network and performance fixes to resolve:
- Network connectivity issues causing 99% CPU usage
- Infinite retry loops from WinError 1232
- UI freezing during join operations
- Crashes from poor resource management

RUN THIS SCRIPT IMMEDIATELY!
"""

import sys
import os
import importlib.util
import traceback
from pathlib import Path

def apply_urgent_fixes():
    """Apply all critical fixes to resolve TG Checker issues."""
    
    print("🔥 URGENT TG CHECKER FIX - STARTING APPLICATION")
    print("=" * 60)
    
    try:
        # Import the critical network fix
        from critical_network_fix import apply_critical_network_fix
        
        # Import main application
        print("📥 Importing main TG Checker application...")
        main_spec = importlib.util.spec_from_file_location("main", "main.py")
        main_module = importlib.util.module_from_spec(main_spec)
        main_spec.loader.exec_module(main_module)
        
        # Import PyQt5 to create application instance
        from PyQt5.QtWidgets import QApplication
        import sys
        
        # Create Qt application if it doesn't exist
        if not QApplication.instance():
            app = QApplication(sys.argv)
        else:
            app = QApplication.instance()
        
        print("🏗️ Creating TG Checker instance...")
        # Create main window instance
        window = main_module.TGCheckerApp()
        
        print("🔧 Applying critical network fixes...")
        # Apply the critical network fix
        fix_success = apply_critical_network_fix(window)
        
        if fix_success:
            print("✅ CRITICAL NETWORK FIX APPLIED SUCCESSFULLY!")
            print("✅ Network error handling is now active")
            print("✅ Connection pooling is enabled")
            print("✅ Resource monitoring is running")
            print("✅ CPU usage should be normalized")
            print("✅ No more infinite retry loops")
            
            # Add CPU monitoring display
            def monitor_and_display():
                import threading
                import time
                import psutil
                
                def monitor_loop():
                    consecutive_high_cpu = 0
                    while True:
                        try:
                            cpu = psutil.cpu_percent(interval=2)
                            memory = psutil.virtual_memory().percent
                            
                            if cpu > 70:
                                consecutive_high_cpu += 1
                                if consecutive_high_cpu >= 3:
                                    print(f"⚠️ WARNING: CPU usage still high: {cpu}% - Check for issues")
                                    consecutive_high_cpu = 0
                            else:
                                consecutive_high_cpu = 0
                                
                            if cpu > 90:
                                print(f"🚨 CRITICAL: CPU at {cpu}% - Performance fixes may need adjustment")
                            
                            # Report good performance
                            if cpu < 30:
                                print(f"✅ CPU usage healthy: {cpu}% (Memory: {memory}%)")
                                
                            time.sleep(30)
                        except Exception as e:
                            print(f"Monitor error: {e}")
                            time.sleep(60)
                
                threading.Thread(target=monitor_loop, daemon=True, name="UrgentFixMonitor").start()
            
            monitor_and_display()
            
            print("\n" + "=" * 60)
            print("🎯 TG CHECKER IS NOW READY FOR HIGH-PERFORMANCE OPERATIONS!")
            print("🎯 You can now run joining tasks without crashes or freezing")
            print("🎯 Network errors will be handled gracefully")
            print("🎯 Multiple accounts can run simultaneously")
            print("=" * 60)
            
            # Show the main window
            window.show()
            
            # Start the Qt event loop
            if hasattr(app, 'exec_'):
                sys.exit(app.exec_())
            else:
                sys.exit(app.exec())
            
        else:
            print("❌ CRITICAL ERROR: Failed to apply network fixes!")
            print("Please check the error messages above and contact support.")
            return False
            
    except ImportError as e:
        print(f"❌ IMPORT ERROR: {e}")
        print("Make sure all required files are present:")
        print("- critical_network_fix.py")
        print("- main.py")
        return False
        
    except Exception as e:
        print(f"❌ CRITICAL ERROR: {e}")
        print("Full error details:")
        traceback.print_exc()
        return False

def check_requirements():
    """Check if all required components are available."""
    print("🔍 Checking system requirements...")
    
    required_files = [
        "main.py",
        "critical_network_fix.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {missing_files}")
        return False
    
    # Check Python packages
    required_packages = [
        "telethon",
        "PyQt5",
        "psutil",
        "asyncio"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ Missing required packages: {missing_packages}")
        print("Install them with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All requirements satisfied")
    return True

def main():
    """Main entry point for urgent fix application."""
    print("\n🔥 TG CHECKER URGENT FIX SCRIPT 🔥")
    print("Resolving crashes, freezing, and network issues...")
    print()
    
    # Check requirements first
    if not check_requirements():
        print("\n❌ Requirements check failed. Please fix the issues above.")
        input("Press Enter to exit...")
        return
    
    # Apply urgent fixes
    print("🚀 Starting urgent fix application...")
    success = apply_urgent_fixes()
    
    if not success:
        print("\n❌ Urgent fix application failed.")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main() 