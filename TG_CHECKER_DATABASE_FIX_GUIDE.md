# TG Checker Database Fix Guide

## Understanding Common Errors

### The "Database is Locked" Error

When you see the error message "database is locked" in TG Checker, it means multiple parts of the application are trying to access the SQLite database simultaneously, causing conflicts. This is a common issue with SQLite databases in multi-threaded applications.

### The "Indentation Error" 

When you see an error message like "unindent does not match any outer indentation level", it means there's a Python indentation issue in the code. Python requires consistent indentation for code blocks to work properly.

## Quick Fix Options

We've provided several tools to fix common issues in TG Checker:

### Option 1: Run the Complete Fix Utility (RECOMMENDED)

This new utility fixes multiple issues at once:
1. Database locking issues
2. Missing 'start_checker' method error
3. Indentation errors

To use it:
1. Close all instances of TG Checker
2. Run `Fix_TG_Checker_Complete.bat` (or `Fix_TG_Checker_Complete_Kurdish.bat` for Kurdish language)
3. Wait for the utility to complete
4. Once completed, run the application using `Run_Fixed_TG_Checker.bat` (or `Run_Fixed_TG_Checker_Kurdish.bat`)

### Option 2: Fix Indentation Issues Only

If you're only experiencing indentation errors:

1. Close all instances of TG Checker
2. Run `Fix_Indentation.bat` (or `Fix_Indentation_Kurdish.bat` for Kurdish language)
3. Wait for the utility to complete
4. Start the application using `Run_Fixed_TG_Checker.bat`

### Option 3: Fix Database Issues Only

If you only want to fix the database locking issues:

1. Close all instances of TG Checker
2. Run `Fix_Database.bat` (or `Fix_Database_Kurdish.bat` for Kurdish language)
3. Wait for the utility to complete
4. The database should now be fixed

### Option 4: Use the Fixed Version of TG Checker

1. Close all instances of TG Checker
2. Run `Run_TG_Checker_Fixed.bat` (or `Run_TG_Checker_Fixed_Kurdish.bat` for Kurdish language)
3. This will automatically apply the database fix and start TG Checker

## What These Fixes Do

Our solution implements several improvements to prevent database locking and code issues:

1. **Enhanced Connection Management**: Better handling of database connections with proper cleanup
2. **Optimized SQLite Settings**: Using optimal SQLite settings to prevent locks
3. **Smart Retry Logic**: Sophisticated retry mechanism with exponential backoff
4. **Automatic Database Repair**: Self-healing capabilities when lock issues are detected
5. **Proper Backup System**: Creates backups before any potentially risky operations
6. **Code Fixes**: Adds missing methods that were causing application crashes
7. **Indentation Fixes**: Corrects Python indentation issues that prevented the application from starting

## Advanced Manual Fix (If Automated Tools Don't Work)

If the automated tools don't resolve your issue, try this manual approach:

1. Close all instances of TG Checker
2. Run `enhanced_db_fix.py --force` from the command line
3. If it still doesn't work, try the manual recovery process:
   - Rename or delete the file `tg_checker.db`
   - Find the newest backup file (named like `tg_checker.db.backup_*`)
   - Copy this file and rename it to `tg_checker.db`

## Prevention Tips

To prevent issues in the future:

1. **Only Run One Instance**: Don't run multiple instances of TG Checker at the same time
2. **Proper Shutdown**: Always close TG Checker properly, don't force-quit
3. **Regular Maintenance**: Run the fix utility occasionally for maintenance
4. **Backup Regularly**: Make regular backups of your database
5. **Use the Fixed Version**: Always use the provided batch files to start the application

## Technical Details

Our solution makes these specific improvements:

- Uses SQLite's WAL (Write-Ahead Logging) journal mode with optimized settings
- Implements a busy handler with exponential backoff
- Uses connection pooling and proper connection tracking
- Includes integrity checking and automatic repair
- Employs exclusive locking during critical operations
- Implements proper cleanup of lingering connections
- Adds missing methods and fixes code issues
- Ensures proper indentation throughout the codebase

## Support

If you continue to experience issues after trying these solutions, please report them with:
1. The exact error message you're seeing
2. What you were doing when the error occurred
3. Which fix options you've already tried

## Credits

This solution was developed specifically for TG Checker to address SQLite locking issues and code problems in multi-threaded Python applications. 