#!/usr/bin/env python3
import sqlite3
import os

db_path = "tg_checker.db"

if os.path.exists(db_path):
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    # Remove accounts with error status
    cursor.execute("DELETE FROM accounts WHERE status = 'error'")
    rows_affected = cursor.rowcount
    conn.commit()
    
    print(f"✅ Removed {rows_affected} accounts with error status")
    
    # Show remaining accounts
    cursor.execute("SELECT phone, api_id, account_type, status, active FROM accounts")
    accounts = cursor.fetchall()
    
    print(f"\n📊 Remaining accounts ({len(accounts)}):")
    for phone, api_id, acc_type, status, active in accounts:
        active_str = "✅" if active else "❌"
        print(f"{active_str} {phone} | Type: {acc_type} | Status: {status}")
    
    conn.close()
else:
    print("Database not found!") 