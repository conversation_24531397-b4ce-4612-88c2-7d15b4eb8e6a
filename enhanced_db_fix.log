2025-06-01 13:18:50,260 - INFO - Starting Enhanced Database Lock Fix Utility...
2025-06-01 13:18:50,266 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_131850
2025-06-01 13:18:50,268 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_131850
2025-06-01 13:18:50,268 - INFO - Attempting standard database optimization...
2025-06-01 13:18:52,282 - INFO - Running VACUUM on database...
2025-06-01 13:18:52,285 - INFO - Database VACUUM completed
2025-06-01 13:18:52,285 - INFO - Running ANALYZE on database...
2025-06-01 13:18:52,286 - INFO - Database ANALYZE completed
2025-06-01 13:18:52,287 - INFO - Setting optimal database parameters...
2025-06-01 13:18:52,289 - INFO - Database optimization completed
2025-06-01 13:18:52,298 - INFO - Database integrity check passed
2025-06-01 13:18:52,299 - INFO - Standard optimization successful
2025-06-01 13:18:52,300 - INFO - Database repair completed successfully
2025-06-01 13:19:57,679 - INFO - Starting Enhanced Database Lock Fix Utility...
2025-06-01 13:19:57,681 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_131957
2025-06-01 13:19:57,684 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_131957
2025-06-01 13:19:57,684 - INFO - Attempting standard database optimization...
2025-06-01 13:19:59,687 - INFO - Running VACUUM on database...
2025-06-01 13:19:59,693 - INFO - Database VACUUM completed
2025-06-01 13:19:59,693 - INFO - Running ANALYZE on database...
2025-06-01 13:19:59,694 - INFO - Database ANALYZE completed
2025-06-01 13:19:59,694 - INFO - Setting optimal database parameters...
2025-06-01 13:19:59,695 - INFO - Database optimization completed
2025-06-01 13:19:59,706 - INFO - Database integrity check passed
2025-06-01 13:19:59,707 - INFO - Standard optimization successful
2025-06-01 13:19:59,708 - INFO - Database repair completed successfully
2025-06-01 13:23:57,181 - INFO - Starting Enhanced Database Lock Fix Utility...
2025-06-01 13:23:57,184 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_132357
2025-06-01 13:23:57,187 - INFO - Created database backup at db_backups\tg_checker.db_backup_20250601_132357
2025-06-01 13:23:57,188 - INFO - Attempting standard database optimization...
2025-06-01 13:23:59,191 - INFO - Running VACUUM on database...
2025-06-01 13:23:59,197 - INFO - Database VACUUM completed
2025-06-01 13:23:59,198 - INFO - Running ANALYZE on database...
2025-06-01 13:23:59,198 - INFO - Database ANALYZE completed
2025-06-01 13:23:59,198 - INFO - Setting optimal database parameters...
2025-06-01 13:23:59,200 - INFO - Database optimization completed
2025-06-01 13:23:59,211 - INFO - Database integrity check passed
2025-06-01 13:23:59,212 - INFO - Standard optimization successful
2025-06-01 13:23:59,212 - INFO - Database repair completed successfully
