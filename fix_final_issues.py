import re
import os
import shutil

def fix_final_issues(filename="main.py"):
    """Fix the remaining linter issues in main.py"""
    # Create a backup
    backup_file = f"{filename}.final_bak"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.readlines()
    
    # Fix line 576 - Expected indented block
    for i in range(570, 580):
        if i < len(content) and "if self.settings.value(\"auto_start_monitor\"" in content[i]:
            # Fix indentation of the next line
            indent = ' ' * (content[i].find('if'))
            content[i+1] = indent + "    self.start_monitor()\n"
            print(f"Fixed indentation on line {i+1}")
    
    # Fix line 3013-3014 - Expected expression & Unexpected indentation
    for i in range(3010, 3020):
        if i < len(content) and "else:" in content[i] and not content[i].strip().endswith(':'):
            # Fix by adding colon
            content[i] = content[i].rstrip() + ":\n"
            print(f"Fixed missing colon on line {i+1}")
            
            # Also fix indentation of the next line if needed
            if i+1 < len(content) and not content[i+1].startswith(' '):
                indent = ' ' * (content[i].find('else'))
                content[i+1] = indent + "    " + content[i+1].lstrip()
                print(f"Fixed indentation on line {i+2}")
    
    # Fix line 3062 - Try statement must have at least one except or finally clause
    for i in range(3060, 3070):
        if i < len(content) and "try:" in content[i]:
            # Find where the try block ends
            try_level = content[i].find('try:')
            try_end = i + 1
            
            while try_end < len(content):
                if content[try_end].strip() and len(content[try_end]) - len(content[try_end].lstrip()) <= try_level:
                    break
                try_end += 1
            
            # Check if there's an except or finally
            has_except = False
            for j in range(i+1, try_end+1):
                if j < len(content) and ("except" in content[j] or "finally" in content[j]):
                    has_except = True
                    break
            
            if not has_except:
                # Add except block at the end of try block
                indent = ' ' * try_level
                content.insert(try_end, indent + "except Exception as e:\n")
                content.insert(try_end+1, indent + "    self.logger.error(f\"Failed to refresh account info for {account.get('phone', '')}: {str(e)}\")\n")
                content.insert(try_end+2, indent + "    QMessageBox.critical(self, \"Error\", f\"Failed to refresh account info: {str(e)}\")\n")
                print(f"Added except block after try on line {i+1}")
    
    # Fix line 3073-3074 - Unexpected indentation & Expected expression
    for i in range(3070, 3080):
        if i < len(content) and "self.logger.error" in content[i] and not content[i].startswith(' '):
            # Find the appropriate indentation level from nearby lines
            for j in range(max(i-5, 0), i):
                if content[j].strip().startswith("def "):
                    # Function definition found, get indent level
                    indent = ' ' * 8  # Standard indent inside function
                    content[i] = indent + content[i].lstrip()
                    
                    # Also fix indentation of the next line if needed
                    if i+1 < len(content) and not content[i+1].startswith(' '):
                        content[i+1] = indent + content[i+1].lstrip()
                    
                    print(f"Fixed indentation on lines {i+1}-{i+2}")
                    break
    
    # Fix line 3086 - Expected expression (missing colon after else)
    for i in range(3080, 3090):
        if i < len(content) and "else:" in content[i] and not content[i].strip().endswith(':'):
            content[i] = content[i].rstrip() + ":\n"
            print(f"Fixed missing colon on line {i+1}")
    
    # Save the fixed content
    output_file = "main_fixed.py"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(content)
    
    print(f"Fixed all issues and saved to {output_file}")
    return output_file

if __name__ == "__main__":
    output_file = fix_final_issues()
    
    # Test if the fixed file compiles correctly
    import py_compile
    try:
        py_compile.compile(output_file, doraise=True)
        print(f"✅ Compilation successful! No syntax errors in {output_file}")
    except Exception as e:
        print(f"❌ Compilation failed: {str(e)}")
    
    # Create batch file to run the fixed version
    batch_content = """@echo off
echo TG Checker - All remaining issues fixed!
echo Running final fixed application...
python main_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_fixed.bat", "w") as f:
        f.write(batch_content)
    
    print("Created batch file: run_fixed.bat") 