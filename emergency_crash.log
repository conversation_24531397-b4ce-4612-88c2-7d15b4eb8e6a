
==================================================
Emergency crash at: 2025-08-03 18:31:53.995522
Error: EMERGENCY CRASH CAUGHT: ModuleNotFoundError: No module named 'PyQt5'
Traceback:
Traceback (most recent call last):
  File "d:\TG PY\main.py", line 61, in <module>
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
    ...<4 lines>...
                               QScrollArea, QFrame)
ModuleNotFoundError: No module named 'PyQt5'
==================================================

==================================================
Emergency crash at: 2025-08-03 18:33:22.011374
Error: EMERGENCY CRASH CAUGHT: ModuleNotFoundError: No module named 'PyQt5.sip'
Traceback:
Traceback (most recent call last):
  File "D:\TG PY\main.py", line 61, in <module>
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
    ...<4 lines>...
                               QScrollArea, QFrame)
ModuleNotFoundError: No module named 'PyQt5.sip'
==================================================

==================================================
Emergency crash at: 2025-08-03 18:34:58.845547
Error: EMERGENCY CRASH CAUGHT: ModuleNotFoundError: No module named 'imghdr'
Traceback:
Traceback (most recent call last):
  File "D:\TG PY\main.py", line 69, in <module>
    from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError
  File "D:\TG PY\venv\Lib\site-packages\telethon\__init__.py", line 1, in <module>
    from .client.telegramclient import TelegramClient
  File "D:\TG PY\venv\Lib\site-packages\telethon\client\__init__.py", line 12, in <module>
    from .telegrambaseclient import TelegramBaseClient
  File "D:\TG PY\venv\Lib\site-packages\telethon\client\telegrambaseclient.py", line 14, in <module>
    from ..network import MTProtoSender, Connection, ConnectionTcpFull, TcpMTProxy
  File "D:\TG PY\venv\Lib\site-packages\telethon\network\__init__.py", line 7, in <module>
    from .mtprotosender import MTProtoSender
  File "D:\TG PY\venv\Lib\site-packages\telethon\network\mtprotosender.py", line 11, in <module>
    from .. import helpers, utils
  File "D:\TG PY\venv\Lib\site-packages\telethon\utils.py", line 7, in <module>
    import imghdr
ModuleNotFoundError: No module named 'imghdr'
==================================================

==================================================
Emergency crash at: 2025-08-03 19:04:42.972336
Error: EMERGENCY CRASH CAUGHT: ModuleNotFoundError: No module named 'PyQt5'
Traceback:
Traceback (most recent call last):
  File "D:\TG PY\main.py", line 61, in <module>
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
    ...<4 lines>...
                               QScrollArea, QFrame)
ModuleNotFoundError: No module named 'PyQt5'
==================================================

==================================================
Emergency crash at: 2025-08-03 19:04:52.039695
Error: EMERGENCY CRASH CAUGHT: ModuleNotFoundError: No module named 'PyQt5'
Traceback:
Traceback (most recent call last):
  File "D:\TG PY\main.py", line 61, in <module>
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
    ...<4 lines>...
                               QScrollArea, QFrame)
ModuleNotFoundError: No module named 'PyQt5'
==================================================
