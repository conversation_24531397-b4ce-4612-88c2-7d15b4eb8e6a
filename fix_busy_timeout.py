"""
Fix SQLite busy_timeout syntax error in main.py
"""
import re

def fix_busy_timeout():
    """Fix the syntax error in PRAGMA busy_timeout statements"""
    print("Making a backup of main.py...")
    with open("main.py", "r", encoding="utf-8") as file:
        content = file.read()
    
    with open("main.py.bak_syntax", "w", encoding="utf-8") as backup:
        backup.write(content)
    
    # Replace the problematic PRAGMA statement with the correct one
    fixed_content = re.sub(
        r'conn\.execute\("PRAGMA busy_timeout = \\1"\)',
        r'conn.execute("PRAGMA busy_timeout = 10000")',
        content
    )
    
    # Write the fixed content back
    with open("main.py", "w", encoding="utf-8") as file:
        file.write(fixed_content)
    
    print("Fixed PRAGMA busy_timeout syntax error in main.py")

if __name__ == "__main__":
    fix_busy_timeout() 