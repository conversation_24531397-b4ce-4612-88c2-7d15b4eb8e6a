#!/usr/bin/env python3
"""
TG Checker Startup Script with Emergency UI Freeze Fix
This script applies the emergency fix and then runs TG Checker normally.

Use this script instead of running main.py directly to ensure the UI freeze fix is applied.
"""

import sys
import os
import traceback
from PyQt5.QtWidgets import QApplication, QMessageBox

def main():
    """Main startup function with emergency fix application"""
    
    print("🚀 TG Checker Startup with Emergency UI Freeze Fix")
    print("=" * 60)
    
    try:
        # Create Qt application
        app = QApplication(sys.argv)
        
        # Import TG Checker
        print("📥 Importing TG Checker...")
        from main import TGCheckerApp
        
        # Create main window
        print("🏗️ Creating TG Checker window...")
        window = TGCheckerApp()
        
        # Apply emergency UI freeze fix
        print("🚑 Applying emergency UI freeze fix...")
        try:
            from emergency_ui_freeze_fix import apply_emergency_ui_freeze_fix
            ui_fix = apply_emergency_ui_freeze_fix(window)
            print("✅ Emergency UI freeze fix applied successfully!")
            print("🎯 Joining operations will now run in background threads")
            print("🛡️ UI will remain responsive during join operations")
        except Exception as fix_error:
            print(f"⚠️ Emergency fix failed: {fix_error}")
            print("⚠️ TG Checker will run but may experience UI freezing")
        
        # Show the window
        print("🖥️ Showing TG Checker window...")
        window.show()
        
        print("✅ TG Checker started successfully with UI freeze protection!")
        print("=" * 60)
        print("🔥 CRITICAL: The joining operations are now NON-BLOCKING!")
        print("🔥 You can now click 'Start All Tasks' without UI freezing!")
        print("=" * 60)
        
        # Run the application
        sys.exit(app.exec_())
        
    except ImportError as import_error:
        print(f"❌ Import error: {import_error}")
        print("❌ Make sure main.py and emergency_ui_freeze_fix.py are in the same directory")
        input("Press Enter to exit...")
        sys.exit(1)
        
    except Exception as error:
        print(f"❌ Startup error: {error}")
        print("❌ Full traceback:")
        traceback.print_exc()
        
        # Try to show error dialog if PyQt5 is available
        try:
            error_msg = f"TG Checker startup failed:\n\n{error}\n\nCheck console for details."
            QMessageBox.critical(None, "TG Checker Startup Error", error_msg)
        except:
            pass
        
        input("Press Enter to exit...")
        sys.exit(1)

if __name__ == "__main__":
    main() 