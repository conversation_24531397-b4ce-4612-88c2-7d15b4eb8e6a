#!/usr/bin/env python3
"""
🚨 URGENT: Account Fallback Fix for TG Checker
Fix the critical issue where valid groups are being skipped due to account-level problems.

PROBLEMS IDENTIFIED:
1. Groups marked as "account_issue" when only ONE account fails
2. No fallback to try same group with different accounts  
3. Database locks/connection issues cause immediate group failure
4. Silent skipping without proper logging
5. Groups not saved to correct result folders

SOLUTION:
- Enhanced account fallback system
- Multiple account attempts per group
- Better error classification 
- Comprehensive logging
- Guaranteed group processing and saving
"""

import re

def apply_account_fallback_fix():
    """Apply comprehensive account fallback fixes to main.py"""
    
    # Read current main.py
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("🔧 APPLYING ACCOUNT FALLBACK FIXES...")
    
    # ========================================
    # FIX 1: Enhanced check_group_or_channel method with account fallback
    # ========================================
    
    old_check_method = r'''    def check_group_or_channel\(self, link\):.*?return result.*?""".*?(\n\s+except.*?)?(\n\s+def|\nclass|\Z)'''
    
    new_check_method = '''    def check_group_or_channel(self, link):
        """Enhanced group checking with multi-account fallback and comprehensive logging."""
        print(f"🔍 DEBUG: Starting check for: {link}")
        self.log_activity_signal.emit(f"🔍 DEBUG: Starting check for: {link}")
        
        # Get all available accounts for fallback
        available_accounts = []
        try:
            accounts = self.account_manager.get_active_accounts()
            for account in accounts:
                phone = account.get("phone")
                status = account.get("status", "").lower()
                # Skip accounts with known issues
                if "banned" in status or "limit" in status or "disable" in status:
                    continue
                available_accounts.append(phone)
        except Exception as e:
            self.log_activity_signal.emit(f"⚠️ WARNING: Could not get accounts list: {str(e)}")
            # Fallback to default account selection
            available_accounts = ["default"]
        
        if not available_accounts:
            self.log_activity_signal.emit(f"🚨 CRITICAL: No available accounts for: {link}")
            return {
                "valid": False,
                "error_type": "account_issue", 
                "reason": "No available accounts",
                "type": "unknown",
                "members": 0,
                "last_message_age_hours": 999,
                "total_messages": 0
            }
        
        self.log_activity_signal.emit(f"✅ DEBUG: Found {len(available_accounts)} available accounts")
        
        # Try each account until success or all accounts exhausted
        last_error = None
        account_errors = []
        
        for attempt, phone in enumerate(available_accounts, 1):
            try:
                self.log_activity_signal.emit(f"✅ DEBUG: Using account {phone} for check (attempt {attempt}/{len(available_accounts)})")
                
                # Create client for this specific account
                try:
                    from tg_client import TelegramClient
                    client = TelegramClient(phone)
                    print(f"🔍 DEBUG: Created TelegramClient for account: {phone}")
                    self.log_activity_signal.emit(f"🔍 DEBUG: Checking entity info for: {link}")
                    
                    # Attempt to get entity info with this account
                    result = None
                    error = None

                    def check_with_timeout():
                        nonlocal result, error
                        try:
                            result = client.get_entity_info(link)
                            print(f"✅ DEBUG: get_entity_info completed for: {link}")
                        except Exception as _e:
                            error = _e
                            print(f"❌ DEBUG: get_entity_info error: {_e}")

                    # Run with 30 second timeout
                    import threading
                    thread = threading.Thread(target=check_with_timeout, daemon=True)
                    thread.start()
                    thread.join(timeout=30.0)

                    if thread.is_alive():
                        print(f"⏰ DEBUG: Timeout checking {link} with account {phone}")
                        self.log_activity_signal.emit(f"⏰ TIMEOUT: Group check timed out for: {link} on account {phone}")
                        account_errors.append(f"Account {phone}: Timeout")
                        continue  # Try next account

                    if error:
                        error_str = str(error).lower()
                        print(f"❌ DEBUG: Error with account {phone}: {error_str}")
                        
                        # Check if this is an account-specific issue that should trigger fallback
                        if any(keyword in error_str for keyword in [
                            "database is locked", "could not connect", "connection error",
                            "session", "auth", "login", "flood", "rate limit"
                        ]):
                            account_errors.append(f"Account {phone}: {str(error)}")
                            self.log_activity_signal.emit(f"🔄 FALLBACK: Account {phone} failed ({str(error)[:50]}...), trying next account")
                            continue  # Try next account
                        else:
                            # This is likely a group-level error, not account-specific
                            raise error

                    print(f"✅ DEBUG: Successfully checked {link}: {result}")
                    self.log_activity_signal.emit(f"✅ DEBUG: Check completed for: {link}")
                    
                    # Check if result indicates account issue but group might be valid
                    if (not result["valid"] and 
                        result.get("error_type") == "account_issue" and 
                        attempt < len(available_accounts)):
                        
                        account_errors.append(f"Account {phone}: {result.get('reason', 'Account issue')}")
                        self.log_activity_signal.emit(f"🔄 ACCOUNT ISSUE: {phone} failed, trying next account")
                        continue  # Try next account
                    
                    # Success or definitive group-level result
                    if account_errors:
                        self.log_activity_signal.emit(f"✅ SUCCESS: After {len(account_errors)} failed attempts, account {phone} succeeded for: {link}")
                    
                    return result

                except Exception as e:
                    error_str = str(e).lower()
                    account_errors.append(f"Account {phone}: {str(e)}")
                    self.log_activity_signal.emit(f"❌ ERROR: Account {phone} failed: {str(e)[:100]}...")
                    last_error = e
                    
                    # If this is the last account, don't continue the loop
                    if attempt >= len(available_accounts):
                        break
                    
                    # Check if we should try next account
                    if any(keyword in error_str for keyword in [
                        "database is locked", "could not connect", "connection error", 
                        "session", "auth", "login", "timeout", "flood", "rate limit"
                    ]):
                        self.log_activity_signal.emit(f"🔄 FALLBACK: Trying next account due to: {str(e)[:50]}...")
                        continue
                    else:
                        # Group-level error, no point trying other accounts
                        break
                        
            except Exception as e:
                account_errors.append(f"Account {phone}: Critical error - {str(e)}")
                last_error = e
                continue
        
        # All accounts failed
        self.log_activity_signal.emit(f"🚨 ALL ACCOUNTS FAILED for {link}: {len(account_errors)} attempts")
        for i, error in enumerate(account_errors, 1):
            self.log_activity_signal.emit(f"  {i}. {error}")
        
        # Classify the final error
        if last_error:
            error_str = str(last_error).lower()
            if any(keyword in error_str for keyword in ["timeout", "connection", "network", "unreachable"]):
                return {
                    "valid": False,
                    "error_type": "invalid_group",
                    "reason": f"Group unreachable after {len(account_errors)} attempts: {str(last_error)}",
                    "type": "unknown",
                    "members": 0,
                    "last_message_age_hours": 999,
                    "total_messages": 0
                }
            else:
                return {
                    "valid": False,
                    "error_type": "account_issue",
                    "reason": f"All {len(available_accounts)} accounts failed: {str(last_error)}",
                    "type": "unknown", 
                    "members": 0,
                    "last_message_age_hours": 999,
                    "total_messages": 0
                }
        
        # No specific error, just failed
        return {
            "valid": False,
            "error_type": "account_issue",
            "reason": f"All {len(available_accounts)} accounts failed to check group",
            "type": "unknown",
            "members": 0, 
            "last_message_age_hours": 999,
            "total_messages": 0
        }

'''
    
    # Apply the replacement
    if re.search(old_check_method, content, re.DOTALL):
        content = re.sub(old_check_method, new_check_method + r'\3', content, flags=re.DOTALL)
        print("✅ FIXED: Enhanced check_group_or_channel method with account fallback")
    else:
        print("⚠️ WARNING: Could not find check_group_or_channel method to replace")
    
    # ========================================
    # FIX 2: Enhanced group processing with guaranteed saving
    # ========================================
    
    # Find and enhance the group processing loop
    old_processing = r'''                    # Handle based on error type if invalid
                    if not result\["valid"\]:
                        error_type = result\.get\("error_type", "invalid_group"\)

                        if error_type == "invalid_group":
                            invalid_groups\.append\(link\)
                            self\.log_activity_signal\.emit\(f"\[GROUP #\{actual_group_number\}\] INVALID: \{link\} → InvalidGroups_Channels\.txt"\)
                        elif error_type == "account_issue":
                            account_issues\.append\(link\)
                            wait_seconds = result\.get\("wait_seconds", 0\)
                            if wait_seconds > 0:
                                self\.log_activity_signal\.emit\(f"\[GROUP #\{actual_group_number\}\] ACCOUNT ISSUE: FloodWait → Paused \{wait_seconds//60\} mins → Saved to AccountIssue\.txt"\)
                            else:
                                self\.log_activity_signal\.emit\(f"\[GROUP #\{actual_group_number\}\] ACCOUNT ISSUE: \{result\['reason'\]\} → Saved to AccountIssue\.txt"\)
                        elif error_type == "join_request":
                            join_requests\.append\(link\)
                            self\.log_activity_signal\.emit\(f"\[GROUP #\{actual_group_number\}\] JOIN REQUEST: \{link\} → Saved to JoinRequest\.txt"\)'''
    
    new_processing = '''                    # Enhanced processing with guaranteed logging and saving
                    if not result["valid"]:
                        error_type = result.get("error_type", "invalid_group")
                        reason = result.get("reason", "Unknown error")
                        
                        # COMPREHENSIVE LOGGING - Log every decision
                        self.log_activity_signal.emit(f"📋 GROUP #{actual_group_number} ANALYSIS: {link}")
                        self.log_activity_signal.emit(f"   ├── Status: INVALID")
                        self.log_activity_signal.emit(f"   ├── Error Type: {error_type}")
                        self.log_activity_signal.emit(f"   ├── Reason: {reason}")
                        
                        if error_type == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: InvalidGroups_Channels.txt")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ❌ INVALID: {link} → InvalidGroups_Channels.txt")
                            
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            wait_seconds = result.get("wait_seconds", 0)
                            if wait_seconds > 0:
                                self.log_activity_signal.emit(f"   └── Saved to: AccountIssue.txt (FloodWait: {wait_seconds//60}m)")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ⏰ FLOOD WAIT: {link} → AccountIssue.txt")
                            else:
                                self.log_activity_signal.emit(f"   └── Saved to: AccountIssue.txt")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 🚫 ACCOUNT ISSUE: {link} → AccountIssue.txt")
                                
                        elif error_type == "join_request":
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: JoinRequest.txt")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 📝 JOIN REQUEST: {link} → JoinRequest.txt")
                        
                        else:
                            # Unknown error type - classify as invalid group
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: InvalidGroups_Channels.txt (Unknown error)")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ❓ UNKNOWN ERROR: {link} → InvalidGroups_Channels.txt")'''

    # Apply the replacement
    if re.search(old_processing, content, re.DOTALL):
        content = re.sub(old_processing, new_processing, content, flags=re.DOTALL)
        print("✅ FIXED: Enhanced group processing with comprehensive logging")
    else:
        print("⚠️ WARNING: Could not find group processing logic to enhance")

    # ========================================
    # FIX 3: Enhanced valid group processing
    # ========================================
    
    # Find the valid group processing section and enhance it
    old_valid_processing = r'''                    # Link is valid, categorize based on type and filters
                    if result\["type"\] == "channel":'''
    
    new_valid_processing = '''                    # COMPREHENSIVE LOGGING - Valid group analysis
                    self.log_activity_signal.emit(f"📋 GROUP #{actual_group_number} ANALYSIS: {link}")
                    self.log_activity_signal.emit(f"   ├── Status: VALID ✅")
                    self.log_activity_signal.emit(f"   ├── Type: {result['type']}")
                    self.log_activity_signal.emit(f"   ├── Members: {result.get('member_count', 0)}")
                    self.log_activity_signal.emit(f"   ├── Messages: {result.get('total_messages', 0)}")
                    self.log_activity_signal.emit(f"   ├── Last Activity: {result.get('last_message_age_hours', 999):.1f}h ago")
                    
                    # Link is valid, categorize based on type and filters
                    if result["type"] == "channel":'''
    
    # Apply the replacement
    if re.search(old_valid_processing, content):
        content = re.sub(old_valid_processing, new_valid_processing, content)
        print("✅ FIXED: Enhanced valid group processing with detailed logging")
    else:
        print("⚠️ WARNING: Could not find valid group processing section")

    # ========================================
    # FIX 4: Add group saving verification
    # ========================================
    
    # Find the end of the checker thread and add verification
    old_ending = r'''            # Final results and saving
            self\.log_activity_signal\.emit\(f"✅ COMPLETED: Checked \{len\(group_links\)\} groups"\)'''
    
    new_ending = '''            # VERIFICATION: Ensure all groups were processed and saved
            total_processed = (len(valid_filtered) + len(valid_only) + len(topics_groups) + 
                             len(channels_only) + len(invalid_groups) + len(account_issues) + len(join_requests))
            
            self.log_activity_signal.emit(f"🔍 VERIFICATION: Processing complete")
            self.log_activity_signal.emit(f"   ├── Groups to check: {len(group_links)}")
            self.log_activity_signal.emit(f"   ├── Groups processed: {total_processed}")
            self.log_activity_signal.emit(f"   ├── Valid (filtered): {len(valid_filtered)}")
            self.log_activity_signal.emit(f"   ├── Valid (only): {len(valid_only)}")
            self.log_activity_signal.emit(f"   ├── Topics: {len(topics_groups)}")
            self.log_activity_signal.emit(f"   ├── Channels: {len(channels_only)}")
            self.log_activity_signal.emit(f"   ├── Invalid: {len(invalid_groups)}")
            self.log_activity_signal.emit(f"   ├── Account Issues: {len(account_issues)}")
            self.log_activity_signal.emit(f"   └── Join Requests: {len(join_requests)}")
            
            if total_processed != len(group_links):
                missing = len(group_links) - total_processed
                self.log_activity_signal.emit(f"🚨 WARNING: {missing} groups not accounted for!")
            else:
                self.log_activity_signal.emit(f"✅ VERIFICATION PASSED: All {len(group_links)} groups processed")

            # Final results and saving
            self.log_activity_signal.emit(f"✅ COMPLETED: Checked {len(group_links)} groups")'''
    
    # Apply the replacement
    if re.search(old_ending, content):
        content = re.sub(old_ending, new_ending, content)
        print("✅ FIXED: Added group processing verification")
    else:
        print("⚠️ WARNING: Could not find ending section to enhance")

    # Write the enhanced file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ ACCOUNT FALLBACK FIXES APPLIED SUCCESSFULLY!")
    print("\n🎯 FIXES APPLIED:")
    print("  ✅ Enhanced account fallback system")
    print("  ✅ Multi-account attempts per group")
    print("  ✅ Comprehensive error logging")
    print("  ✅ Guaranteed group processing")
    print("  ✅ Result verification system")
    print("\n🚀 The checker will now:")
    print("  • Try each group with multiple accounts before giving up")
    print("  • Log every decision and attempt clearly")
    print("  • Never silently skip groups")
    print("  • Save all groups to correct result folders")
    print("  • Verify all groups are accounted for")

if __name__ == "__main__":
    apply_account_fallback_fix() 