import os
import logging
import async<PERSON>
from telethon import TelegramClient as TelethonClient
from telethon.sessions import StringSession
from telethon.errors import (
    SessionPasswordNeededError, FloodWaitError, PhoneCodeInvalidError,
    ChatAdminRequiredError, ChannelPrivateError, ChannelInvalidError,
    UsernameInvalidError, UsernameNotOccupiedError, PeerIdInvalidError,
    UserBannedInChannelError, SessionRevokedError, PeerFloodError,
    ChannelPublicGroupNaError
)
from telethon.tl.functions.channels import GetFullChannelRequest, JoinChannelRequest, LeaveChannelRequest, GetParticipantsRequest
from telethon.tl.functions.messages import GetDialogsRequest
from telethon.tl.types import InputPeerEmpty, Channel, ChannelParticipantsRecent
from datetime import datetime, timedelta
import threading
import time
import glob

class TelegramClient:
    """Wrapper class for Telethon TelegramClient to handle Telegram API operations."""
    
    def __init__(self, api_id, api_hash, phone, session_file=None, logger=None):
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.session_file = session_file or f"sessions/{phone.replace('+', '')}"
        self.logger = logger or logging.getLogger(__name__)
        self.client = None
        self.connected = False
        self._lock = threading.Lock()  # Thread safety
        
        # Handle session import mode - check for placeholder values
        self.is_session_import = (api_hash == "session_placeholder" or api_hash == "placeholder_hash")
        
        # Convert API ID to integer if it's a string
        try:
            self.api_id = int(self.api_id)
        except (ValueError, TypeError):
            # If conversion fails, use a default API ID
            self.api_id = 611335
            self.logger.warning(f"Invalid API ID for {phone}, using default")
        
        # Create the sessions directory if it doesn't exist
        session_dir = os.path.dirname(self.session_file)
        if session_dir:
            os.makedirs(session_dir, exist_ok=True)
    
    async def connect(self):
        """Connect to Telegram API with automatic fallback for broken session files."""
        try:
            if self.client and self.connected:
                # Already connected
                return True
            
            # For session imports, try to read API credentials from session file first
            if self.is_session_import and os.path.exists(self.session_file):
                try:
                    # Try to extract API credentials from session file if it's a Telethon session
                    if self.session_file.endswith('.session'):
                        import sqlite3
                        conn = sqlite3.connect(self.session_file, timeout=5)
                        cursor = conn.cursor()
                        
                        # Try to get API ID from sessions table
                        cursor.execute("SELECT api_id FROM sessions LIMIT 1")
                        result = cursor.fetchone()
                        if result and result[0]:
                            # Use the API ID from session, but we still need a valid hash
                            # For session imports, we'll use placeholder hash since session contains auth
                            self.api_id = result[0]
                            self.api_hash = "session_import_placeholder"
                            self.logger.info(f"Extracted API ID {self.api_id} from session file for {self.phone}")
                        
                        conn.close()
                except Exception as e:
                    self.logger.debug(f"Could not extract API credentials from session: {str(e)}")
            
            # First try: normal session file connection
            self.client = TelethonClient(self.session_file, self.api_id, self.api_hash)
            await self.client.connect()
            
            # Check if we need to sign in
            if not await self.client.is_user_authorized():
                if self.is_session_import:
                    self.logger.warning(f"Session import client {self.phone} is not authorized - session may be invalid")
                else:
                    self.logger.warning(f"Client {self.phone} is not authorized")
                await self.client.disconnect()
                self.client = None
                self.connected = False
                return False
                
            self.connected = True
            self.logger.info(f"Successfully connected client for {self.phone}")
            return True
            
        except Exception as e:
            error_msg = str(e).lower()
            
            # Clean up failed client
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass
                self.client = None
            
            # For session imports, provide more helpful error messages
            if self.is_session_import:
                if "api id" in error_msg and "invalid" in error_msg:
                    self.logger.error(f"Session import failed for {self.phone}: Session contains invalid API credentials")
                elif "unauthorized" in error_msg:
                    self.logger.error(f"Session import failed for {self.phone}: Session is not authorized or expired")
                else:
                    self.logger.error(f"Session import failed for {self.phone}: {str(e)}")
            else:
                # Check if this is a "file is not a database" error (broken JSON session)
                if "file is not a database" in error_msg or "database" in error_msg:
                    self.logger.info(f"Broken session file detected for {self.phone}, trying session string fallback...")
                    
                    # Try to use session string backup
                    session_backup_file = f"sessions/{self.phone.replace('+', '')}_backup.txt"
                    if os.path.exists(session_backup_file):
                        try:
                            with open(session_backup_file, 'r') as f:
                                session_string = f.read().strip()
                            
                            if session_string:
                                # Create client with session string
                                self.client = TelethonClient(StringSession(session_string), self.api_id, self.api_hash)
                                await self.client.connect()
                                
                                if await self.client.is_user_authorized():
                                    self.connected = True
                                    self.logger.info(f"Successfully connected using session string for {self.phone}")
                                    return True
                                else:
                                    await self.client.disconnect()
                                    self.client = None
                                    
                        except Exception as fallback_error:
                            self.logger.error(f"Session string fallback failed for {self.phone}: {str(fallback_error)}")
                            if self.client:
                                try:
                                    await self.client.disconnect()
                                except:
                                    pass
                                self.client = None
                
                self.logger.error(f"Failed to connect client for {self.phone}: {str(e)}")
            
            self.connected = False
            return False
    
    async def disconnect(self):
        """Disconnect from Telegram API with proper cleanup."""
        if self.client:
            try:
                await self.client.disconnect()
                self.logger.info(f"Disconnected client for {self.phone}")
            except Exception as e:
                self.logger.error(f"Error disconnecting client for {self.phone}: {str(e)}")
            finally:
                self.connected = False
                self.client = None
    
    async def check_authorization(self):
        """Check if the client is authorized with error handling."""
        if not self.client:
            return False
            
        try:
            return await self.client.is_user_authorized()
        except Exception as e:
            self.logger.error(f"Error checking authorization for {self.phone}: {str(e)}")
            return False
    
    async def get_group_info(self, group_link):
        """Get information about a Telegram group with proper 3-tier error classification."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return {"error": "Client not connected", "error_type": "account_issue"}
        
        # Clean the group link
        group_link = group_link.strip()
        if not group_link.startswith('https://t.me/'):
            group_link = f"https://t.me/{group_link.lstrip('@')}"

        # Check for topic link format (t.me/group_name/topic_id)
        is_likely_topic = False
        if '/c/' in group_link or ('/' in group_link.split('t.me/')[-1] and not group_link.endswith('/')):
            is_likely_topic = True
            self.logger.info(f"Detected likely topic link format: {group_link}")

        # Extract username from the link
        username = group_link.split('t.me/')[-1].split('?')[0].split('/')[0]
        if not username:
            self.logger.info(f"[INVALID] Could not extract username from {group_link}")
            return {"error": "Invalid link format", "error_type": "invalid_group"}
        
        self.logger.info(f"Checking group: {username}")
        
        # Try to get group entity with comprehensive error handling
        group = None
        try:
            # Try multiple times to get entity (up to 3 attempts)
            attempts = 0
            max_attempts = 3
            while attempts < max_attempts:
                try:
                    # Get the entity with fresh data by using InputPeerChannel
                    try:
                        # Try to resolve the entity directly first
                        entity = await self.client.get_entity(username)
                        group = entity
                    except Exception as e:
                        self.logger.warning(f"Error getting entity directly, trying alternate method: {str(e)}")
                        # Fallback to getting via messages if direct lookup fails
                        messages = await self.client.get_messages(username, limit=1)
                        if messages and messages[0]:
                            group = await self.client.get_entity(messages[0].peer_id)
                    
                    if group:
                        break
                except (ConnectionError, asyncio.TimeoutError) as conn_err:
                    attempts += 1
                    if attempts >= max_attempts:
                        raise
                    await asyncio.sleep(1)  # Brief pause before retry
            
            self.logger.info(f"[ACCESS] Successfully accessed group: {group_link}")
        
        # === GROUP-LEVEL ERRORS (Invalid Groups) → InvalidGroups_Channels.txt ===
        except UsernameInvalidError:
            self.logger.info(f"[INVALID] Username invalid: {group_link}")
            return {"error": "Username invalid", "error_type": "invalid_group"}
        except UsernameNotOccupiedError:
            self.logger.info(f"[INVALID] Username not found: {group_link}")
            return {"error": "Username not found", "error_type": "invalid_group"}
        except PeerIdInvalidError:
            self.logger.info(f"[INVALID] Peer ID invalid: {group_link}")
            return {"error": "Peer ID invalid", "error_type": "invalid_group"}
        except ChannelInvalidError:
            self.logger.info(f"[INVALID] Channel invalid: {group_link}")
            return {"error": "Channel invalid or doesn't exist", "error_type": "invalid_group"}
        except ChannelPrivateError:
            self.logger.info(f"[INVALID] Channel is private: {group_link}")
            return {"error": "Channel is private and cannot be accessed", "error_type": "invalid_group"}
        except ChatAdminRequiredError:
            self.logger.info(f"[INVALID] Admin privileges required: {group_link}")
            return {"error": "Group requires admin privileges to view", "error_type": "invalid_group"}
        except UserBannedInChannelError:
            self.logger.info(f"[INVALID] User banned in channel: {group_link}")
            return {"error": "User is banned from this channel", "error_type": "invalid_group"}
        except ChannelPublicGroupNaError:
            self.logger.info(f"[JOIN REQUEST] Join request needed: {group_link}")
            return {"error": "Join request required", "error_type": "join_request"}
        
        # === ACCOUNT-LEVEL ERRORS (Account Issues) → AccountIssue.txt ===
        except FloodWaitError as e:
            self.logger.info(f"[ACCOUNT ISSUE] FloodWait error on {self.phone}: wait {e.seconds} seconds")
            return {"error": f"Rate limited, wait {e.seconds} seconds", "error_type": "account_issue", "wait_seconds": e.seconds}
        except SessionRevokedError:
            self.logger.info(f"[ACCOUNT ISSUE] Session revoked for {self.phone}")
            return {"error": "Session revoked", "error_type": "account_issue"}
        except PeerFloodError:
            self.logger.info(f"[ACCOUNT ISSUE] Peer flood error on {self.phone}")
            return {"error": "Spam behavior detected", "error_type": "account_issue"}
        
        except Exception as e:
            # Handle content restrictions and other specific errors
            error_str = str(e).lower()
            
            if "privacy" in error_str or "not available" in error_str:
                self.logger.info(f"[INVALID] Privacy settings prevent access: {group_link}")
                return {"error": "Privacy settings prevent access", "error_type": "invalid_group"}
            elif "floodwait" in error_str:
                wait_seconds = 60  # Default wait
                try:
                    import re
                    time_match = re.search(r'(\d+)', error_str)
                    if time_match:
                        wait_seconds = int(time_match.group(1))
                except:
                    pass
                self.logger.info(f"[ACCOUNT ISSUE] FloodWait error on {self.phone}: wait {wait_seconds} seconds")
                return {"error": f"Rate limited, wait {wait_seconds} seconds", "error_type": "account_issue", "wait_seconds": wait_seconds}
            else:
                self.logger.warning(f"[UNKNOWN ERROR] Error accessing {group_link}: {str(e)}")
                return {"error": f"Error accessing entity: {str(e)}", "error_type": "invalid_group"}
        
        if not group:
            self.logger.info(f"[INVALID] Group entity is None: {group_link}")
            return {"error": "Group entity is None", "error_type": "invalid_group"}
        
        # Get additional group info - Initialize missing variables
        member_count = 0
        has_messages = False
        total_messages = 0
        is_forum = False
        is_channel = False
        last_message_age_hours = 999  # Default to a high value if we can't determine
        
        # Initialize result dictionary
        result = {
            "group_id": username,
            "title": getattr(group, 'title', username),
            "member_count": 0,
            "is_public": True,
            "is_forum": False,
            "is_channel": False,
            "is_topic": False,
            "total_messages": 0,
            "post_frequency": 0,
            "has_recent_activity": False,
            "last_message_age_hours": 999
        }
        
        # 🎯 ENHANCED GROUP CLASSIFICATION LOGIC - 100% Accurate
        # Smart detection based on Telegram entity properties
        is_likely_topic = False
        
        # STEP 1: Detect channel vs group vs topic with high precision
        if isinstance(group, Channel):
            # Check for broadcast channel (one-way communication)
            if getattr(group, 'broadcast', False):
                is_channel = True
                result["is_channel"] = True
                self.logger.info(f"[TYPE] ✅ CHANNEL DETECTED: Broadcast channel - {group_link}")
            
            # Check for forum/topics group (has topic functionality)
            elif getattr(group, 'forum', False):
                is_forum = True
                result["is_forum"] = True
                self.logger.info(f"[TYPE] ✅ TOPIC GROUP DETECTED: Forum with topics - {group_link}")
            
            # FIXED: Topic detection - more precise logic to avoid false positives
            elif (getattr(group, 'has_topics', False) or 
                  getattr(group, 'topics_enabled', False)):
                is_likely_topic = True
                result["is_topic"] = True
                self.logger.info(f"[TYPE] ✅ TOPIC GROUP DETECTED: Has topic features - {group_link}")
            
            # Check if it's public or private
            if not getattr(group, 'username', None):
                result["is_public"] = False
            
            # 🔍 ENHANCED MEMBER COUNT DETECTION
            try:
                member_count = 0
                
                # Method 1: Primary - GetFullChannelRequest (most reliable)
                try:
                    full_channel = await self.client(GetFullChannelRequest(group))
                    if hasattr(full_channel, 'full_chat'):
                        member_count = getattr(full_channel.full_chat, 'participants_count', 0)
                    else:
                        member_count = getattr(full_channel, 'participants_count', 0)
                    
                    if member_count > 0:
                        self.logger.info(f"[MEMBERS] ✅ Method 1 Success: {member_count} members - {group_link}")
                except Exception as e:
                    self.logger.debug(f"[MEMBERS] Method 1 failed: {str(e)}")
                
                # Method 2: Fallback - Entity attribute
                if member_count <= 0:
                    try:
                        member_count = getattr(group, 'participants_count', 0)
                        if member_count > 0:
                            self.logger.info(f"[MEMBERS] ✅ Method 2 Success: {member_count} members - {group_link}")
                    except Exception as e:
                        self.logger.debug(f"[MEMBERS] Method 2 failed: {str(e)}")
                
                # Method 3: Sample participants (verification method)
                if member_count <= 0:
                    try:
                        participants = await self.client(GetParticipantsRequest(
                            channel=group,
                            filter=ChannelParticipantsRecent(),
                            offset=0,
                            limit=1,
                            hash=0
                        ))
                        
                        if hasattr(participants, 'count'):
                            member_count = participants.count
                            self.logger.info(f"[MEMBERS] ✅ Method 3 Success: {member_count} members - {group_link}")
                    except Exception as e:
                        self.logger.debug(f"[MEMBERS] Method 3 failed: {str(e)}")
                
                # Method 4: Intelligent estimation for public groups
                if member_count <= 0 and hasattr(group, 'username') and group.username:
                    # If it's a public group but we can't get exact count, estimate conservatively
                    member_count = 100  # Conservative estimate for accessible public groups
                    self.logger.info(f"[MEMBERS] ✅ Method 4 Estimation: {member_count} members (public group) - {group_link}")
                
                # Final member count assignment
                result["member_count"] = member_count
                self.logger.info(f"[MEMBERS] 🎯 FINAL COUNT: {member_count} members for {group_link}")
                
            except Exception as e:
                self.logger.warning(f"[MEMBERS] ❌ All member count methods failed for {group_link}: {str(e)}")
                member_count = 0
                result["member_count"] = 0
        
        # Attempt to get messages - improved message analysis for filtering
        try:
            # Get more messages for better analysis - increased limit for better accuracy
            messages = await self.client.get_messages(group, limit=30)
            
            # DEBUG: Log all fetched messages for this group
            for idx, m in enumerate(messages):
                msg_type = 'user'
                if m is None:
                    msg_type = 'None'
                elif getattr(m, 'action', None) is not None:
                    msg_type = 'service'
                elif getattr(m, 'message', None) is None and not getattr(m, 'media', None):
                    msg_type = 'empty/deleted'
                date_str = str(getattr(m, 'date', ''))
                self.logger.info(f"[DEBUG] Message {idx}: id={getattr(m, 'id', None)}, type={msg_type}, date={date_str}")

            # Count valid messages and analyze
            # Only consider user messages (not service, deleted, or empty)
            def is_valid_user_message(m):
                # Exclude service messages, deleted, empty, or messages with no date
                if m is None:
                    return False
                if getattr(m, 'action', None) is not None:
                    return False  # Service message
                if getattr(m, 'message', None) is None and not getattr(m, 'media', None):
                    return False  # Empty/deleted
                if not getattr(m, 'date', None):
                    return False
                return True

            user_messages = [m for m in messages if is_valid_user_message(m)]
            message_count = len(user_messages)

            self.logger.info(f"[MESSAGES] Retrieved {message_count} user messages from {group_link}")

            if message_count > 0:
                has_messages = True
                # Use the most recent valid user message
                newest_message = user_messages[0]
                result["last_message_text"] = getattr(newest_message, "message", "<media/other>")
                result["last_message_sender_id"] = getattr(newest_message, "from_id", None)
                result["last_message_id"] = getattr(newest_message, "id", None)
                result["last_message_date_raw"] = str(getattr(newest_message, "date", ""))

                if newest_message and newest_message.date:
                    result["last_message_date"] = newest_message.date.isoformat()
                    from datetime import timezone
                    now = datetime.now(timezone.utc)
                    msg_time = newest_message.date
                    if msg_time.tzinfo is None:
                        msg_time = msg_time.replace(tzinfo=timezone.utc)
                    time_diff = (now - msg_time).total_seconds() / 3600
                    last_message_age_hours = max(0, time_diff)
                    result["last_message_age_hours"] = last_message_age_hours
                    self.logger.info(f"[ACTIVITY] Group {group_link} last user message: {last_message_age_hours:.2f} hours ago (msg id: {getattr(newest_message, 'id', None)})")
                else:
                    last_message_age_hours = 999
                    result["last_message_age_hours"] = 999

                # Store actual count of user messages we retrieved
                total_messages = message_count
                result["total_messages"] = total_messages

                # If we got the full requested amount (30), it's likely there are more
                if message_count >= 30:
                    total_messages = max(100, total_messages)
                    result["total_messages"] = total_messages
                    self.logger.info(f"[MESSAGES] Group likely has more than {total_messages} messages")

                # Special handling for user-specified groups
                user_specified_groups = ["buysellzone1", "escrowerscommunity", "bineros_full", "the_sellers_market"]
                if username.lower() in user_specified_groups and total_messages < 100:
                    self.logger.info(f"[MESSAGES] Applying special handling for user-specified group: {username}")
                    total_messages = 100
                    result["total_messages"] = total_messages

                # Calculate post frequency if we have enough messages
                if len(user_messages) >= 2:
                    oldest_msg = user_messages[-1]
                    newest_msg = user_messages[0]
                    time_span = (newest_msg.date - oldest_msg.date).total_seconds()
                    if time_span > 0:
                        freq = len(user_messages) / (time_span / 86400)
                        result["post_frequency"] = freq
                        monthly_estimate = int(freq * 30)
                        if monthly_estimate > total_messages:
                            total_messages = monthly_estimate
                            result["total_messages"] = monthly_estimate
                        self.logger.info(f"[ACTIVITY] Group {group_link} has {freq:.2f} posts/day")

                if last_message_age_hours < 48:
                    result["has_recent_activity"] = True
            else:
                self.logger.info(f"[ACTIVITY] No user messages found for {group_link}")
                result["last_message_age_hours"] = 999
                result["total_messages"] = 0
                
        except Exception as e:
            self.logger.warning(f"Could not get messages for {group_link}: {str(e)}")
            # We need accurate message data for filtering, so default conservatively
            result["last_message_age_hours"] = 999
            result["total_messages"] = 0
        
        # Log final values used for filtering
        self.logger.info(f"[FILTER DATA] Final values for {group_link}: " +
                       f"Members: {member_count}, " +
                       f"Last Activity: {last_message_age_hours:.2f} hours, " +
                       f"Total Messages: {total_messages}")
        
        # 🎯 ENHANCED FINAL CLASSIFICATION - 100% Accurate Type Detection
        # Log final values used for analysis
        self.logger.info(f"[ANALYSIS] Final analysis for {group_link}: " +
                       f"Members: {member_count}, " +
                       f"Last Activity: {last_message_age_hours:.2f} hours, " +
                       f"Total Messages: {total_messages}")
        
        # STEP 2: Return proper classification based on detected type
        if is_channel:
            # ✅ BROADCAST CHANNEL - Goes to Channels_Only_Valid
            self.logger.info(f"[CLASSIFICATION] ✅ CHANNEL: {group_link} → Channels_Only_Valid")
            return {
                "valid": True,
                "type": "channel",
                "error_type": None,
                "member_count": member_count,
                "group_id": username,
                "title": getattr(group, 'title', username),
                "total_messages": total_messages,
                "last_message_age_hours": last_message_age_hours
            }
        elif is_forum or is_likely_topic:
            # ✅ TOPIC/FORUM GROUP - Goes to Topics_Groups_Only_Valid
            self.logger.info(f"[CLASSIFICATION] ✅ TOPIC GROUP: {group_link} → Topics_Groups_Only_Valid")
            return {
                "valid": True,
                "type": "topic", 
                "error_type": None,
                "member_count": member_count,
                "group_id": username,
                "title": getattr(group, 'title', username),
                "last_message_date": result.get("last_message_date", ""),
                "total_messages": total_messages,
                "last_message_age_hours": last_message_age_hours
            }
        else:
            # ✅ REGULAR GROUP - Goes to Groups_Valid_Filter or Groups_Valid_Only (decided by main.py filters)
            self.logger.info(f"[CLASSIFICATION] ✅ GROUP: {group_link} → Groups_Valid_* (filter will be applied by main.py)")
            return {
                "valid": True,
                "type": "group",  # Let main.py handle filter classification
                "error_type": None, 
                "member_count": member_count,
                "group_id": username,
                "title": getattr(group, 'title', username),
                "last_message_date": result.get("last_message_date", ""),
                "total_messages": total_messages,
                "post_frequency": result.get("post_frequency", 0),
                "has_recent_activity": result.get("has_recent_activity", False),
                "last_message_age_hours": last_message_age_hours
            }
    
    def get_entity_info(self, link):
        """Get information about a Telegram entity (group/channel) with synchronous API."""
        loop = None
        temp_wrapper = None
        try:
            # Create new event loop
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Create temporary client for this operation
            temp_wrapper = TelegramClient(self.api_id, self.api_hash, self.phone, self.session_file, self.logger)
            
            # Connect with timeout
            try:
                connect_result = loop.run_until_complete(asyncio.wait_for(temp_wrapper.connect(), timeout=15))
                if not connect_result:
                    self.logger.warning(f"Could not connect for {link} - account issue")
                    return {
                        "valid": False,
                        "error_type": "account_issue",
                        "type": "unknown",
                        "member_count": 0,
                        "last_message_age_hours": 999,
                        "total_messages": 0,
                        "reason": "Could not connect"
                    }
            except Exception as conn_e:
                self.logger.warning(f"Connection error for {link}: {str(conn_e)} - account issue")
                return {
                    "valid": False,
                    "error_type": "account_issue",
                    "type": "unknown", 
                    "member_count": 0,
                    "last_message_age_hours": 999,
                    "total_messages": 0,
                    "reason": f"Connection error: {str(conn_e)}"
                }
            
            # Get group info using our async method
            group_info = loop.run_until_complete(temp_wrapper.get_group_info(link))
            
            # Check if there's an error - classify based on error_type
            if "error" in group_info:
                error_msg = group_info["error"]
                error_type = group_info.get("error_type", "invalid_group")
                wait_seconds = group_info.get("wait_seconds", 0)
                
                self.logger.info(f"[{error_type.upper()}] {error_msg} for {link}")
                
                return {
                    "valid": False, 
                    "error_type": error_type,
                    "reason": error_msg,
                    "type": "unknown",
                    "member_count": 0,
                    "last_message_age_hours": 999,
                    "total_messages": 0,
                    "wait_seconds": wait_seconds
                }
            
            # Group is valid - return the group info directly
            # No need to do additional processing here since get_group_info already
            # properly classifies the entity type and sets the valid flag
            
            # Just make sure all required fields are present
            if "valid" not in group_info:
                group_info["valid"] = True
            
            # Make sure the entity type is set
            if "type" not in group_info:
                if group_info.get("is_channel", False):
                    group_info["type"] = "channel"
                elif group_info.get("is_topic", False) or group_info.get("is_forum", False):
                    group_info["type"] = "topic"
                else:
                    group_info["type"] = "group"
            
            # Log the valid result
            self.logger.info(f"[VALID] Entity classified as valid: {link} (Type: {group_info['type']})")
            
            return group_info
            
        except Exception as e:
            # For ANY unexpected errors, treat as invalid_group (not account_issue)
            self.logger.warning(f"[INVALID] Unexpected error for {link}: {str(e)}")
            return {
                "valid": False,
                "error_type": "invalid_group",
                "type": "unknown",
                "member_count": 0,
                "last_message_age_hours": 999,
                "total_messages": 0,
                "reason": f"Unexpected error: {str(e)}"
            }
        finally:
            # Cleanup
            if temp_wrapper:
                try:
                    if loop and not loop.is_closed():
                        loop.run_until_complete(temp_wrapper.disconnect())
                except Exception:
                    pass
            
            if loop:
                try:
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    if pending:
                        loop.run_until_complete(asyncio.wait_for(
                            asyncio.gather(*pending, return_exceptions=True), 
                            timeout=5.0
                        ))
                except Exception:
                    pass
                finally:
                    if not loop.is_closed():
                        loop.close()
    
    async def get_dialogs(self, limit=100):
        """Get the list of dialogs (chats, channels, etc.)."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return []
            
        try:
            result = await self.client(GetDialogsRequest(
                offset_date=None,
                offset_id=0,
                offset_peer=InputPeerEmpty(),
                limit=limit,
                hash=0
            ))
            return result.dialogs
        except Exception as e:
            self.logger.error(f"Error getting dialogs for {self.phone}: {str(e)}")
            return []
    
    async def send_message(self, entity, message):
        """Send a message to a chat or user."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return False
            
        try:
            await self.client.send_message(entity, message)
            return True
        except Exception as e:
            self.logger.error(f"Error sending message from {self.phone}: {str(e)}")
            return False
    
    async def join_group(self, group_link):
        """Join a group or channel."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return False
            
        try:
            # Extract username from the link
            username = group_link.split('t.me/')[-1].split('?')[0].split('/')[0]
            await self.client(JoinChannelRequest(username))
            return True
        except Exception as e:
            self.logger.error(f"Error joining group {group_link} with {self.phone}: {str(e)}")
            return False
    
    async def leave_group(self, group_link):
        """Leave a group or channel."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return False
            
        try:
            # Extract username from the link
            username = group_link.split('t.me/')[-1].split('?')[0].split('/')[0]
            await self.client(LeaveChannelRequest(username))
            return True
        except Exception as e:
            self.logger.error(f"Error leaving group {group_link} with {self.phone}: {str(e)}")
            return False
    
    @property
    def is_connected(self):
        """Check if the client is connected."""
        return self.connected and self.client is not None
    
    async def get_me(self):
        """Get information about the current user."""
        if not self.client or not self.connected:
            self.logger.error(f"Client {self.phone} is not connected")
            return None
            
        try:
            return await self.client.get_me()
        except Exception as e:
            self.logger.error(f"Error getting user info for {self.phone}: {str(e)}")
            return None
    
    def get_account_info(self):
        """Get account information synchronously with thread safety."""
        import asyncio
        
        # Use thread-safe lock to prevent concurrent access
        with self._lock:
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Connect if not connected
                    connect_result = loop.run_until_complete(self.connect())
                    if not connect_result:
                        return {"success": False, "error": "Failed to connect to Telegram"}
                    
                    # Get user info
                    user = loop.run_until_complete(self.get_me())
                    if user:
                        # Extract user information
                        full_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
                        username = user.username or ""
                        
                        # Try to detect account status by sending a test message to Telegram's "Saved Messages"
                        account_status = "active"  # Default status
                        status_details = None
                        
                        try:
                            # Try to get dialogs (will fail if account is limited/banned)
                            dialogs = loop.run_until_complete(self.client.get_dialogs(limit=1))
                            
                            # Try to send a test message to self (saved messages)
                            try:
                                # We'll send a hidden message and delete it immediately
                                test_msg = loop.run_until_complete(self.client.send_message('me', 
                                    'Status check ' + str(datetime.now().timestamp())))
                                # Delete the message immediately
                                loop.run_until_complete(test_msg.delete())
                                # Message sent and deleted - account is fully active
                                account_status = "active"
                            except FloodWaitError as flood_err:
                                # Account is spam limited
                                account_status = "limited"
                                wait_seconds = getattr(flood_err, 'seconds', 0)
                                status_details = f"Spam limited ({wait_seconds}s wait)"
                            except Exception as msg_err:
                                error_str = str(msg_err).lower()
                                if "banned" in error_str or "terminate" in error_str:
                                    account_status = "banned"
                                    status_details = str(msg_err)
                                elif "limit" in error_str or "flood" in error_str or "spam" in error_str:
                                    account_status = "limited"
                                    status_details = str(msg_err)
                                elif "frozen" in error_str or "suspend" in error_str:
                                    account_status = "frozen"
                                    status_details = str(msg_err)
                                elif "network" in error_str or "connection" in error_str:
                                    # Network issues don't change account status
                                    self.logger.warning(f"Network issue during status check: {str(msg_err)}")
                                else:
                                    # Unknown error, log but don't change status
                                    self.logger.warning(f"Unknown error during status check: {str(msg_err)}")
                        except FloodWaitError as flood_err:
                            account_status = "limited"
                            wait_seconds = getattr(flood_err, 'seconds', 0)
                            status_details = f"Spam limited ({wait_seconds}s wait)"
                        except Exception as status_err:
                            error_str = str(status_err).lower()
                            if "banned" in error_str or "terminate" in error_str:
                                account_status = "banned"
                                status_details = str(status_err)
                            elif "limit" in error_str or "flood" in error_str or "spam" in error_str:
                                account_status = "limited"
                                status_details = str(status_err)
                            elif "frozen" in error_str or "suspend" in error_str:
                                account_status = "frozen"
                                status_details = str(status_err)
                            elif "network" in error_str or "connection" in error_str:
                                # Network issues don't change account status
                                self.logger.warning(f"Network issue during status check: {str(status_err)}")
                            else:
                                # Log but don't change status for unknown errors
                                self.logger.warning(f"Unknown error during status check: {str(status_err)}")
                        
                        return {
                            "success": True,
                            "full_name": full_name,
                            "username": username,
                            "user_id": user.id,
                            "phone": self.phone,
                            "account_status": account_status,
                            "status_details": status_details
                        }
                    
                    return {"success": False, "error": "Could not retrieve user information"}
                    
                except Exception as e:
                    error_str = str(e).lower()
                    if "api id" in error_str and "invalid" in error_str:
                        self.logger.error(f"Invalid API ID for {self.phone}: {str(e)}")
                        return {"success": False, "error": "Invalid API ID or API Hash", "account_status": "error"}
                    elif "unauthorized" in error_str:
                        self.logger.error(f"Session unauthorized for {self.phone}: {str(e)}")
                        return {"success": False, "error": "Session is not authorized or expired", "account_status": "error"}
                    elif "session" in error_str and "revoked" in error_str:
                        self.logger.error(f"Session revoked for {self.phone}: {str(e)}")
                        return {"success": False, "error": "Session has been revoked", "account_status": "banned"}
                    elif "banned" in error_str:
                        self.logger.error(f"Account banned for {self.phone}: {str(e)}")
                        return {"success": False, "error": str(e), "account_status": "banned"}
                    elif "limit" in error_str or "flood" in error_str or "spam" in error_str:
                        self.logger.error(f"Account limited for {self.phone}: {str(e)}")
                        return {"success": False, "error": str(e), "account_status": "limited"}
                    elif "frozen" in error_str or "suspend" in error_str:
                        self.logger.error(f"Account frozen for {self.phone}: {str(e)}")
                        return {"success": False, "error": str(e), "account_status": "frozen"}
                    else:
                        self.logger.error(f"Error getting account info for {self.phone}: {str(e)}")
                        return {"success": False, "error": str(e), "account_status": "error"}
                finally:
                    # Disconnect
                    try:
                        loop.run_until_complete(self.disconnect())
                    except:
                        pass
                    try:
                        loop.close()
                    except:
                        pass
                        
            except Exception as e:
                self.logger.error(f"Thread error getting account info for {self.phone}: {str(e)}")
                return {"success": False, "error": f"Thread error: {str(e)}", "account_status": "error"}
    
    def send_code_request(self):
        """Send a code request to the phone number."""
        import asyncio
        
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # Initialize client
            self.client = TelethonClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect and send code request
            async def send_code():
                try:
                    await self.client.connect()
                    self.connected = True
                    
                    # Check if already authorized
                    if await self.client.is_user_authorized():
                        return {"success": True, "already_authorized": True}
                    
                    # Send code request
                    result = await self.client.send_code_request(self.phone)
                    return {
                        "success": True,
                        "phone_code_hash": result.phone_code_hash,
                        "already_authorized": False
                    }
                    
                except SessionPasswordNeededError:
                    return {"success": False, "requires_2fa": True}
                except Exception as e:
                    return {"success": False, "error": str(e)}
                finally:
                    # Don't disconnect here - keep the session for sign in
                    pass
            
            return loop.run_until_complete(send_code())
            
        except Exception as e:
            self.logger.error(f"Error sending code request for {self.phone}: {str(e)}")
            return {"success": False, "error": str(e)}
        finally:
            loop.close()
    
    def sign_in(self, code, password=None, phone_code_hash=None):
        """Sign in using code or 2FA password."""
        import asyncio
        
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def sign_in_async():
                try:
                    if not self.client:
                        self.client = TelethonClient(self.session_file, self.api_id, self.api_hash)
                        await self.client.connect()
                        self.connected = True
                    
                    if password:
                        # 2FA sign in
                        result = await self.client.sign_in(password=password)
                    else:
                        # Code sign in
                        result = await self.client.sign_in(phone=self.phone, code=code, phone_code_hash=phone_code_hash)
                    
                    return {"success": True, "user": result}
                    
                except SessionPasswordNeededError:
                    return {"success": False, "requires_2fa": True}
                except PhoneCodeInvalidError:
                    return {"success": False, "error": "Invalid verification code"}
                except Exception as e:
                    return {"success": False, "error": str(e)}
            
            return loop.run_until_complete(sign_in_async())
            
        except Exception as e:
            self.logger.error(f"Error signing in for {self.phone}: {str(e)}")
            return {"success": False, "error": str(e)}
        finally:
            loop.close()
    
    def check_2fa_required(self):
        """Check if 2FA is required for this account."""
        import asyncio
        
        # Create a new event loop
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            async def check_2fa():
                try:
                    if not self.client:
                        self.client = TelethonClient(self.session_file, self.api_id, self.api_hash)
                        await self.client.connect()
                        self.connected = True
                    
                    # If we're already authorized, 2FA is not needed
                    if await self.client.is_user_authorized():
                        return {"requires_2fa": False, "already_authorized": True}
                    
                    # Try to get dialogs to see if 2FA is needed
                    try:
                        await self.client.get_dialogs(limit=1)
                        return {"requires_2fa": False}
                    except SessionPasswordNeededError:
                        return {"requires_2fa": True}
                    except Exception:
                        # If we can't check, assume not needed
                        return {"requires_2fa": False}
                        
                except Exception as e:
                    return {"error": str(e)}
            
            return loop.run_until_complete(check_2fa())
            
        except Exception as e:
            self.logger.error(f"Error checking 2FA for {self.phone}: {str(e)}")
            return {"error": str(e)}
        finally:
            loop.close()
    
    def clean_session(self):
        """Clean up the session file to remove cached authentication state completely."""
        try:
            # Close any existing client connections first
            if self.client:
                try:
                    # If client is connected, we need to close it properly
                    if self.connected:
                        # Create new event loop if needed for cleanup
                        loop = None
                        try:
                            loop = asyncio.get_event_loop()
                        except RuntimeError:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                        
                        # Force disconnect the client
                        if loop.is_running():
                            # If loop is running, schedule disconnect
                            asyncio.create_task(self.client.disconnect())
                        else:
                            # Run disconnect in the loop
                            loop.run_until_complete(self.client.disconnect())
                        
                        self.logger.info(f"Disconnected client before session cleanup for {self.phone}")
                        
                except Exception as disconnect_error:
                    self.logger.warning(f"Error disconnecting client during cleanup for {self.phone}: {str(disconnect_error)}")
                finally:
                    self.client = None
                    self.connected = False
            
            # Clean up all possible session file variations
            session_patterns = [
                self.session_file,
                f"{self.session_file}.session",
                f"sessions/{self.phone.replace('+', '')}.session", 
                f"sessions/{self.phone.replace('+', '')}",
                f"sessions/{self.phone}.session",
                f"sessions/{self.phone}",
                f"{self.phone.replace('+', '')}.session",
                f"{self.phone.replace('+', '')}",
                f"{self.phone}.session",
                f"{self.phone}"
            ]
            
            files_removed = []
            for pattern in session_patterns:
                try:
                    if os.path.exists(pattern):
                        # Force close any database connections to this file
                        import sqlite3
                        try:
                            # Try to close any lingering connections by opening and closing
                            conn = sqlite3.connect(pattern, timeout=1)
                            conn.close()
                        except:
                            pass
                        
                        # Remove the file
                        os.remove(pattern)
                        files_removed.append(pattern)
                        self.logger.info(f"Removed session file: {pattern}")
                        
                except (OSError, PermissionError) as e:
                    self.logger.warning(f"Could not remove session file {pattern}: {str(e)}")
                except Exception as e:
                    self.logger.warning(f"Unexpected error removing session file {pattern}: {str(e)}")
            
            # Also clean up any session backup files
            backup_patterns = [
                f"sessions/{self.phone.replace('+', '')}_backup.txt",
                f"sessions/{self.phone}_backup.txt",
                f"{self.phone.replace('+', '')}_backup.txt",
                f"{self.phone}_backup.txt"
            ]
            
            for pattern in backup_patterns:
                try:
                    if os.path.exists(pattern):
                        os.remove(pattern)
                        files_removed.append(pattern)
                        self.logger.info(f"Removed session backup file: {pattern}")
                except Exception as e:
                    self.logger.debug(f"Could not remove backup file {pattern}: {str(e)}")
            
            if files_removed:
                self.logger.info(f"Session cleanup completed for {self.phone}, removed {len(files_removed)} files")
            else:
                self.logger.info(f"No session files found to clean for {self.phone}")
            
            # Reset internal state
            self.client = None
            self.connected = False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error during session cleanup for {self.phone}: {str(e)}")
            return False
    
    def force_cleanup(self):
        """Force cleanup of all client connections and session state - emergency use only."""
        try:
            self.logger.info(f"🔧 FORCE CLEANUP: Starting emergency cleanup for {self.phone}")
            
            # Step 1: Force close client connection immediately with asyncio cleanup
            if self.client:
                try:
                    # Force disconnect using asyncio
                    import asyncio
                    loop = None
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # Create a task to disconnect
                            async def force_disconnect():
                                try:
                                    await self.client.disconnect()
                                except:
                                    pass
                            asyncio.create_task(force_disconnect())
                        else:
                            # Run disconnect in existing loop
                            loop.run_until_complete(self.client.disconnect())
                    except RuntimeError:
                        # Create new loop if no current loop
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(self.client.disconnect())
                        finally:
                            loop.close()
                    except Exception as disconnect_error:
                        self.logger.debug(f"🔧 FORCE CLEANUP: Error during disconnect: {str(disconnect_error)}")
                    finally:
                        self.client = None
                        self.logger.info(f"🔧 FORCE CLEANUP: Client disconnected and cleared for {self.phone}")
                except Exception as e:
                    self.logger.warning(f"🔧 FORCE CLEANUP: Error clearing client for {self.phone}: {str(e)}")
                    self.client = None
            
            # Step 2: Reset connection state
            self.connected = False
            
            # Step 3: Force garbage collection to release any references
            import gc
            gc.collect()
            
            # Step 4: Force cleanup all session files with Windows-specific handling
            session_files_to_remove = []
            
            # Build list of session files to remove
            phone_clean = self.phone.replace('+', '')
            potential_files = [
                f"sessions/{phone_clean}.session",
                f"sessions/{phone_clean}",
                f"sessions/{self.phone}.session", 
                f"sessions/{self.phone}",
                f"{phone_clean}.session",
                f"{phone_clean}",
                f"{self.phone}.session",
                f"{self.phone}",
                self.session_file,
                f"{self.session_file}.session"
            ]
            
            # Also use glob patterns for comprehensive cleanup
            import glob
            glob_patterns = [
                f"sessions/*{phone_clean}*",
                f"sessions/*{self.phone}*",
                f"*{phone_clean}*",
                f"*{self.phone}*"
            ]
            
            for pattern in glob_patterns:
                try:
                    matching_files = glob.glob(pattern)
                    for file_path in matching_files:
                        if (phone_clean in file_path or self.phone in file_path):
                            session_files_to_remove.append(file_path)
                except Exception as pattern_error:
                    self.logger.debug(f"🔧 FORCE CLEANUP: Error with pattern {pattern}: {str(pattern_error)}")
            
            # Add potential files to removal list
            session_files_to_remove.extend(potential_files)
            
            # Remove duplicates
            session_files_to_remove = list(set(session_files_to_remove))
            
            files_removed = 0
            
            # Step 5: Aggressive file removal with retries
            for file_path in session_files_to_remove:
                if not os.path.exists(file_path):
                    continue
                    
                # Try multiple approaches to remove the file
                removed = False
                
                # Approach 1: Force close database connections first
                if file_path.endswith('.session'):
                    try:
                        import sqlite3
                        # Try to interrupt any active connections
                        conn = sqlite3.connect(file_path, timeout=0.1)
                        conn.execute("PRAGMA locking_mode = NORMAL")
                        conn.execute("PRAGMA journal_mode = DELETE")
                        conn.close()
                        time.sleep(0.1)
                    except Exception:
                        pass
                
                # Approach 2: Standard removal
                if not removed:
                    try:
                        os.remove(file_path)
                        files_removed += 1
                        removed = True
                        self.logger.info(f"🔧 FORCE CLEANUP: Removed {file_path}")
                    except PermissionError:
                        pass
                    except Exception as e:
                        self.logger.debug(f"🔧 FORCE CLEANUP: Standard removal failed for {file_path}: {str(e)}")
                
                # Approach 3: Retry with small delay (Windows file locking)
                if not removed:
                    for retry in range(3):
                        try:
                            time.sleep(0.2 * (retry + 1))  # Progressive delay
                            os.remove(file_path)
                            files_removed += 1
                            removed = True
                            self.logger.info(f"🔧 FORCE CLEANUP: Removed {file_path} (retry {retry + 1})")
                            break
                        except Exception as retry_error:
                            if retry == 2:  # Last retry
                                self.logger.debug(f"🔧 FORCE CLEANUP: All retries failed for {file_path}: {str(retry_error)}")
                
                # Approach 4: Windows-specific force removal using system commands
                if not removed and os.name == 'nt':  # Windows
                    try:
                        import subprocess
                        # Try to unlock and delete using Windows commands
                        result = subprocess.run(['del', '/f', '/q', file_path], 
                                              shell=True, capture_output=True, timeout=5)
                        if result.returncode == 0:
                            files_removed += 1
                            removed = True
                            self.logger.info(f"🔧 FORCE CLEANUP: Force deleted {file_path} using system command")
                    except Exception as cmd_error:
                        self.logger.debug(f"🔧 FORCE CLEANUP: System command removal failed for {file_path}: {str(cmd_error)}")
                
                # Approach 5: Rename and mark for deletion
                if not removed:
                    try:
                        # Rename the file to mark it for deletion
                        import uuid
                        temp_name = f"{file_path}.deleted_{uuid.uuid4().hex[:8]}"
                        os.rename(file_path, temp_name)
                        # Try to delete the renamed file
                        try:
                            os.remove(temp_name)
                            files_removed += 1
                            removed = True
                            self.logger.info(f"🔧 FORCE CLEANUP: Removed {file_path} via rename method")
                        except:
                            # File renamed but not deleted - it will be cleaned up later
                            self.logger.info(f"🔧 FORCE CLEANUP: Renamed {file_path} for later cleanup")
                    except Exception as rename_error:
                        self.logger.debug(f"🔧 FORCE CLEANUP: Rename method failed for {file_path}: {str(rename_error)}")
                
                if not removed:
                    self.logger.warning(f"🔧 FORCE CLEANUP: Could not remove {file_path} - file may be locked")
            
            # Step 6: Final garbage collection
            gc.collect()
            
            # Step 7: Additional cleanup for backup files
            backup_patterns = [
                f"sessions/{phone_clean}_backup.txt",
                f"sessions/{self.phone}_backup.txt",
                f"{phone_clean}_backup.txt",
                f"{self.phone}_backup.txt"
            ]
            
            for backup_file in backup_patterns:
                try:
                    if os.path.exists(backup_file):
                        os.remove(backup_file)
                        files_removed += 1
                        self.logger.info(f"🔧 FORCE CLEANUP: Removed backup file {backup_file}")
                except Exception as backup_error:
                    self.logger.debug(f"🔧 FORCE CLEANUP: Could not remove backup {backup_file}: {str(backup_error)}")
            
            self.logger.info(f"✅ FORCE CLEANUP: Emergency cleanup completed for {self.phone}, removed {files_removed} files")
            
            # Return True even if some files couldn't be removed - the important thing is client cleanup
            return True
            
        except Exception as e:
            self.logger.error(f"❌ FORCE CLEANUP: Emergency cleanup failed for {self.phone}: {str(e)}")
            return False
    
    def get_session_age(self):
        """Get the age of the session file in days."""
        try:
            session_file = f"{self.session_file}.session"
            if os.path.exists(session_file):
                creation_time = os.path.getctime(session_file)
                age_seconds = time.time() - creation_time
                age_days = age_seconds / (24 * 3600)
                return age_days
            return 0
        except Exception as e:
            self.logger.error(f"Error getting session age for {self.phone}: {str(e)}")
            return 0
    
    def check_floodwait_status(self, force_fresh=False):
        """Check if the account is under FloodWait restrictions by testing API actions.
        Returns a dictionary with FloodWait status and details.
        
        Args:
            force_fresh (bool): If True, ignores cached state and forces a fresh check
        """
        import asyncio
        from telethon.errors import FloodWaitError
        
        # Use thread-safe lock to prevent concurrent access
        with self._lock:
            try:
                # Create a new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Connect if not connected - with reconnect if force_fresh
                    if force_fresh:
                        # Force disconnect and reconnect for a fresh check
                        try:
                            loop.run_until_complete(self.disconnect())
                        except:
                            pass
                            
                    connect_result = loop.run_until_complete(self.connect())
                    if not connect_result:
                        return {"success": False, "error": "Failed to connect to Telegram"}
                    
                    # Perform a series of tests to trigger FloodWait if present
                    try:
                        # Test 1: Try to get dialogs (light API call)
                        dialogs = loop.run_until_complete(asyncio.wait_for(
                            self.client.get_dialogs(limit=1), timeout=5))
                        
                        # Test 2: Try to get entity by username (medium API call)
                        try:
                            entity = loop.run_until_complete(asyncio.wait_for(
                                self.client.get_entity('telegram'), timeout=5))
                        except FloodWaitError as flood_err:
                            # Account is in FloodWait
                            wait_seconds = getattr(flood_err, 'seconds', 0)
                            return {
                                "success": True,
                                "has_floodwait": True,
                                "wait_seconds": wait_seconds,
                                "readable_time": self._format_time_remaining(wait_seconds)
                            }
                        except Exception:
                            # Other errors ignored - continue testing
                            pass
                            
                        # Test 3: Try sending a message to self (heavy API call)
                        try:
                            test_msg = loop.run_until_complete(asyncio.wait_for(
                                self.client.send_message('me', f'FloodWait test {datetime.now().timestamp()}'), 
                                timeout=5))
                            # If successful, immediately delete it
                            loop.run_until_complete(test_msg.delete())
                        except FloodWaitError as flood_err:
                            # Account is in FloodWait
                            wait_seconds = getattr(flood_err, 'seconds', 0)
                            return {
                                "success": True,
                                "has_floodwait": True,
                                "wait_seconds": wait_seconds,
                                "readable_time": self._format_time_remaining(wait_seconds)
                            }
                        except Exception as e:
                            # Check if error message contains FloodWait indicators
                            error_str = str(e).lower()
                            if "floodwait" in error_str or "flood wait" in error_str:
                                # Try to extract wait time from error message
                                import re
                                wait_match = re.search(r'(\d+)s', error_str)
                                if wait_match:
                                    wait_seconds = int(wait_match.group(1))
                                    return {
                                        "success": True,
                                        "has_floodwait": True,
                                        "wait_seconds": wait_seconds,
                                        "readable_time": self._format_time_remaining(wait_seconds)
                                    }
                                else:
                                    # FloodWait detected but couldn't extract time
                                    return {
                                        "success": True,
                                        "has_floodwait": True,
                                        "wait_seconds": 3600,  # Default to 1 hour
                                        "readable_time": "1 hour"
                                    }
                        
                        # No FloodWait detected in any test
                        return {
                            "success": True,
                            "has_floodwait": False,
                            "wait_seconds": 0
                        }
                        
                    except FloodWaitError as flood_err:
                        # FloodWait on initial dialog fetch
                        wait_seconds = getattr(flood_err, 'seconds', 0)
                        return {
                            "success": True,
                            "has_floodwait": True,
                            "wait_seconds": wait_seconds,
                            "readable_time": self._format_time_remaining(wait_seconds)
                        }
                    except Exception as e:
                        # Check if error message contains FloodWait indicators
                        error_str = str(e).lower()
                        if "floodwait" in error_str or "flood wait" in error_str or "wait" in error_str:
                            # Try to extract wait time from error message
                            import re
                            wait_match = re.search(r'(\d+)s', error_str)
                            if wait_match:
                                wait_seconds = int(wait_match.group(1))
                                return {
                                    "success": True,
                                    "has_floodwait": True,
                                    "wait_seconds": wait_seconds,
                                    "readable_time": self._format_time_remaining(wait_seconds)
                                }
                            else:
                                # FloodWait detected but couldn't extract time
                                return {
                                    "success": True,
                                    "has_floodwait": True,
                                    "wait_seconds": 3600,  # Default to 1 hour
                                    "readable_time": "1 hour"
                                }
                        else:
                            # Other error, not FloodWait related
                            return {
                                "success": False,
                                "error": str(e),
                                "has_floodwait": False
                            }
                
                finally:
                    # Disconnect
                    try:
                        loop.run_until_complete(self.disconnect())
                    except:
                        pass
                    try:
                        loop.close()
                    except:
                        pass
                        
            except Exception as e:
                self.logger.error(f"Thread error checking FloodWait for {self.phone}: {str(e)}")
                return {"success": False, "error": f"Thread error: {str(e)}", "has_floodwait": False}
    
    def _format_time_remaining(self, seconds):
        """Format seconds into a human-readable time string."""
        if seconds < 60:
            return f"{seconds} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            return f"{minutes} minute{'s' if minutes != 1 else ''}"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            if minutes > 0:
                return f"{hours}h {minutes}m"
            else:
                return f"{hours} hour{'s' if hours != 1 else ''}" 