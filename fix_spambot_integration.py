#!/usr/bin/env python3
"""
SpamBot Integration and Fix Logic Implementation
This script adds functionality to detect account status (Active/Limited/Banned),
contact @SpamBot automatically, and provide manual fix options.
"""

import os
import re
import logging
from datetime import datetime, timedelta

def main():
    # Read the account_manager.py file
    with open('account_manager.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. Enhance account_manager.py with status detection and history tracking
    if "def update_account_status" not in content:
        # Add status tracking to account_manager.py
        status_tracking_code = """
    def update_account_status(self, phone, status, restrictions=None, until_date=None, send_notification=True):
        """Update account status with detailed information and history tracking."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_file)
                c = conn.cursor()
                
                # Get current status to check if it's changed
                c.execute("SELECT status FROM accounts WHERE phone = ?", (phone,))
                current_status = c.fetchone()
                status_changed = current_status is None or current_status[0] != status
                
                # Update status in accounts table
                c.execute(
                    "UPDATE accounts SET status = ?, restrictions = ?, restricted_until = ?, last_checked = ? WHERE phone = ?",
                    (status, str(restrictions) if restrictions else None, until_date, datetime.now().strftime("%Y-%m-%d %H:%M:%S"), phone)
                )
                
                # Add to status history if status changed
                if status_changed:
                    c.execute(
                        "INSERT INTO account_status_history (phone, status, restrictions, restricted_until, timestamp) VALUES (?, ?, ?, ?, ?)",
                        (phone, status, str(restrictions) if restrictions else None, until_date, datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                    )
                    
                    # If status changed to Limited or Banned and notifications are enabled, trigger notification
                    if send_notification and status in ['Limited', 'Banned'] and status_changed:
                        self.logger.warning(f"Account {phone} status changed to {status}")
                        # Will be implemented in TGCheckerApp
                
                conn.commit()
                conn.close()
                
                return True
        except Exception as e:
            self.logger.error(f"Error updating account status: {str(e)}")
            return False
    
    def get_account_status_history(self, phone):
        """Get the status history for an account."""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_file)
                c = conn.cursor()
                
                c.execute(
                    "SELECT status, restrictions, restricted_until, timestamp FROM account_status_history WHERE phone = ? ORDER BY timestamp DESC",
                    (phone,)
                )
                
                history = []
                for row in c.fetchall():
                    history.append({
                        'status': row[0],
                        'restrictions': row[1],
                        'restricted_until': row[2],
                        'timestamp': row[3]
                    })
                
                conn.close()
                return history
        except Exception as e:
            self.logger.error(f"Error getting account status history: {str(e)}")
            return []
            
    async def _contact_spambot_for_recovery(self, phone):
        """Contact @SpamBot to check account status and initiate recovery."""
        try:
            # Get the account session
            session_file = f"sessions/{phone}"
            if not os.path.exists(session_file):
                self.logger.error(f"Session file not found for {phone}")
                return False, "Session file not found"
                
            # Get API credentials
            account = self.get_account(phone)
            if not account:
                return False, "Account not found in database"
                
            api_id = account.get('api_id', '1234567')  # Use default if not found
            api_hash = account.get('api_hash', 'abcdef1234567890abcdef1234567890')  # Use default if not found
            
            # Connect to Telegram
            from telethon import TelegramClient
            from telethon.tl.functions.contacts import SearchRequest
            from telethon.tl.types import InputPeerUser, InputPeerEmpty
            
            client = TelegramClient(session_file, api_id, api_hash)
            await client.connect()
            
            if not await client.is_user_authorized():
                await client.disconnect()
                return False, "Account not authorized"
                
            # Find SpamBot
            spambot_username = "SpamBot"  # Default, can be customized
            try:
                # Search for SpamBot
                result = await client(SearchRequest(
                    q=spambot_username,
                    limit=1
                ))
                
                if not result.users:
                    await client.disconnect()
                    return False, f"Could not find @{spambot_username}"
                
                spambot = result.users[0]
                spambot_entity = await client.get_entity(spambot)
                
                # Send message to SpamBot
                await client.send_message(spambot_entity, "/start")
                
                # Wait for response and check for restrictions
                self.logger.info(f"Sent message to @{spambot_username} for account {phone}")
                
                # Get the conversation history to analyze the response
                messages = await client.get_messages(spambot_entity, limit=5)
                
                response_text = ""
                for msg in messages:
                    if msg.out:  # Skip our own messages
                        continue
                    response_text = msg.text
                    break
                
                # Analyze the response to determine account status
                status = "Active"
                restrictions = None
                until_date = None
                
                if "Good news" in response_text or "no restrictions" in response_text:
                    status = "Active"
                elif "limited" in response_text.lower():
                    status = "Limited"
                    
                    # Try to extract time remaining
                    time_pattern = r"(\d+) hours? and (\d+) minutes?"
                    match = re.search(time_pattern, response_text)
                    if match:
                        hours, minutes = int(match.group(1)), int(match.group(2))
                        until_date = (datetime.now() + timedelta(hours=hours, minutes=minutes)).strftime("%Y-%m-%d %H:%M:%S")
                        restrictions = f"Limited for {hours}h {minutes}m"
                    else:
                        restrictions = "Limited (unknown duration)"
                        
                elif "permanently limited" in response_text.lower():
                    status = "Banned"
                    restrictions = "Permanent ban"
                
                # Update account status
                self.update_account_status(phone, status, restrictions, until_date)
                
                await client.disconnect()
                return True, status
                
            except Exception as e:
                self.logger.error(f"Error contacting SpamBot: {str(e)}")
                await client.disconnect()
                return False, f"Error: {str(e)}"
                
        except Exception as e:
            self.logger.error(f"Error in SpamBot contact: {str(e)}")
            return False, f"Error: {str(e)}"
"""
        # Insert the status tracking code before the last function in account_manager.py
        if "def _init_db" in content:
            content = content.replace("def _init_db", status_tracking_code + "\n    def _init_db")
        else:
            # Append at the end if _init_db not found
            content += status_tracking_code
            
        # Ensure the database schema includes status history table
        db_init_code = """
        # Create account_status_history table if it doesn't exist
        c.execute('''
            CREATE TABLE IF NOT EXISTS account_status_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT,
                status TEXT,
                restrictions TEXT,
                restricted_until TEXT,
                timestamp TEXT,
                FOREIGN KEY (phone) REFERENCES accounts(phone)
            )
        ''')
"""
        if "_init_db" in content and "account_status_history" not in content:
            # Find the end of the CREATE TABLE statements in _init_db
            match = re.search(r"CREATE TABLE IF NOT EXISTS[^;]+;", content)
            if match:
                idx = match.end()
                content = content[:idx] + db_init_code + content[idx:]
        
        # Write back the enhanced account_manager.py
        with open('account_manager.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print("Enhanced account_manager.py with status tracking and SpamBot integration")
    
    # 2. Add SpamBot integration to main.py
    with open('main.py', 'r', encoding='utf-8') as f:
        main_content = f.read()
    
    # Check if we need to enhance the _fix_account_thread method
    if "_fix_account_thread" in main_content and "self._contact_spambot_for_recovery" not in main_content:
        # Find the _fix_account_thread method
        fix_account_method = re.search(r"def _fix_account_thread\([^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+", main_content)
        if fix_account_method:
            old_method = fix_account_method.group(0)
            
            # Create enhanced method with SpamBot integration
            enhanced_method = """    def _fix_account_thread(self, phone):
        """Background thread for fixing account issues."""
        try:
            self.log_activity_signal.emit(f"Starting account fix for {phone}...")
            
            # Update UI to show we're working on it
            table = self.accounts_table
            for row in range(table.rowCount()):
                if table.item(row, 0) and table.item(row, 0).text() == phone:
                    # Set status cell to "Checking..."
                    status_cell = table.item(row, 5)
                    if status_cell:
                        status_cell.setText("Checking...")
                        status_cell.setForeground(QColor("#FFA500"))  # Orange
                    break
            
            # Contact SpamBot to check status and attempt recovery
            success, result = asyncio.run(self.account_manager._contact_spambot_for_recovery(phone))
            
            if success:
                # Update the account in DB based on result from SpamBot
                self.log_activity_signal.emit(f"SpamBot check complete: {result}")
                
                # Refresh account info to update UI
                self.refresh_account_info(phone)
                
                # Show message about the results
                if result == "Active":
                    self.log_activity_signal.emit(f"✅ Account {phone} is active with no restrictions")
                    QMetaObject.invokeMethod(
                        self, 
                        "showMessageBox", 
                        Qt.QueuedConnection,
                        Q_ARG(str, "Account Status"),
                        Q_ARG(str, f"Account {phone} is active with no restrictions.")
                    )
                elif result == "Limited":
                    self.log_activity_signal.emit(f"⚠️ Account {phone} is limited. Check status for details.")
                    QMetaObject.invokeMethod(
                        self, 
                        "showMessageBox", 
                        Qt.QueuedConnection,
                        Q_ARG(str, "Account Limited"),
                        Q_ARG(str, f"Account {phone} is limited. The limitation will expire automatically.")
                    )
                elif result == "Banned":
                    self.log_activity_signal.emit(f"🚫 Account {phone} is banned permanently.")
                    QMetaObject.invokeMethod(
                        self, 
                        "showMessageBox", 
                        Qt.QueuedConnection,
                        Q_ARG(str, "Account Banned"),
                        Q_ARG(str, f"Account {phone} is banned permanently. You may need to contact Telegram support.")
                    )
            else:
                self.log_activity_signal.emit(f"❌ Failed to check status: {result}")
                QMetaObject.invokeMethod(
                    self, 
                    "showMessageBox", 
                    Qt.QueuedConnection,
                    Q_ARG(str, "Error"),
                    Q_ARG(str, f"Failed to check account status: {result}")
                )
                
        except Exception as e:
            self.log_activity_signal.emit(f"❌ Error in account fix: {str(e)}")
            self.logger.error(f"Error in account fix thread: {str(e)}")
            
    @pyqtSlot(str, str)
    def showMessageBox(self, title, message):
        """Show a message box (called from a thread via invokeMethod)."""
        QMessageBox.information(self, title, message)
            
    def notify_telegram_bot(self, phone, status, restrictions=None):
        """Send a notification to the configured Telegram bot when account status changes."""
        try:
            # Get bot token and chat ID from settings
            bot_token = self.settings.value("telegram_bot_token", "")
            chat_id = self.settings.value("telegram_alert_chat_id", "")
            
            if not bot_token or not chat_id:
                self.logger.warning("Telegram bot notification skipped: missing token or chat ID")
                return False
            
            # Get account details
            account = self.account_manager.get_account(phone)
            if not account:
                self.logger.warning(f"Cannot send notification: account {phone} not found")
                return False
                
            # Create message text
            message = f"⚠️ *ACCOUNT STATUS ALERT* ⚠️\\n\\n"
            message += f"📱 *Account:* `{phone}`\\n"
            
            if account.get("name") or account.get("username"):
                message += f"👤 *User:* "
                if account.get("name"):
                    message += account.get("name")
                if account.get("username"):
                    message += f" (@{account.get('username')})"
                message += "\\n"
                
            message += f"🔶 *Status:* *{status}*\\n"
            
            if restrictions:
                message += f"📝 *Details:* {restrictions}\\n"
                
            message += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n"
            
            # Add recovery instructions
            if status == "Limited":
                message += "🔄 *Recovery:* The limitation will expire automatically after the specified time.\\n"
                message += "You can also use the 'Fix' button to contact @SpamBot for more details."
            elif status == "Banned":
                message += "🔄 *Recovery:* Use the 'Fix' button to contact @SpamBot for details.\\n"
                message += "If permanently banned, you may need to contact Telegram support."
            
            # Send using Telegram Bot API
            import requests
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }
            
            response = requests.post(url, data=data)
            response_data = response.json()
            
            if response.status_code == 200 and response_data.get("ok"):
                self.log_activity_signal.emit(f"✅ Telegram alert sent successfully")
                self.logger.info(f"Telegram notification sent successfully")
                return True
            else:
                error_msg = response_data.get("description", "Unknown error")
                self.log_activity_signal.emit(f"❌ Failed to send Telegram alert: {error_msg}")
                self.logger.error(f"Failed to send Telegram notification: {error_msg}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error sending Telegram notification: {str(e)}")
            return False
"""
            
            # Replace the old method with enhanced one
            main_content = main_content.replace(old_method, enhanced_method)
        
        # Add settings for SpamBot and notifications to settings tab
        if "create_settings_tab" in main_content and "SpamBox Configuration" not in main_content:
            settings_tab_code = """
        # SpamBox settings
        spambox_group = QGroupBox("SpamBox Configuration")
        spambox_layout = QFormLayout()
        
        # Default SpamBot username
        self.spambox_username_input = QLineEdit()
        self.spambox_username_input.setPlaceholderText("Default: SpamBot")
        self.spambox_username_input.setText(self.settings.value("spambox_bot_username", "SpamBot"))
        
        # Auto alert on detection
        self.auto_alert_check = QCheckBox()
        self.auto_alert_check.setChecked(self.settings.value("auto_alert_on_detection", True, type=bool))
        
        # Auto fix on detection
        self.auto_fix_banned_check = QCheckBox()
        self.auto_fix_banned_check.setChecked(self.settings.value("auto_fix_banned_accounts", True, type=bool))
        
        spambox_layout.addRow("SpamBot Username:", self.spambox_username_input)
        spambox_layout.addRow("Auto-Alert on Detection:", self.auto_alert_check)
        spambox_layout.addRow("Auto-Fix Banned Accounts:", self.auto_fix_banned_check)
        
        # Add help text
        help_label = QLabel("Note: Auto-Fix will automatically contact SpamBot to check banned accounts")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        spambox_layout.addRow("", help_label)
        
        spambox_group.setLayout(spambox_layout)
        layout.addWidget(spambox_group)
        
        # Telegram Bot settings for notifications
        telegram_group = QGroupBox("Telegram Bot Notifications")
        telegram_layout = QFormLayout()
        
        self.telegram_bot_token_input = QLineEdit()
        self.telegram_bot_token_input.setPlaceholderText("Enter Telegram Bot Token")
        self.telegram_bot_token_input.setText(self.settings.value("telegram_bot_token", ""))
        self.telegram_bot_token_input.setEchoMode(QLineEdit.Password)
        
        self.telegram_chat_id_input = QLineEdit()
        self.telegram_chat_id_input.setPlaceholderText("Enter Chat ID to receive alerts")
        self.telegram_chat_id_input.setText(self.settings.value("telegram_alert_chat_id", ""))
        
        self.test_notification_button = QPushButton("Test Notification")
        self.test_notification_button.clicked.connect(self.send_test_notification)
        
        telegram_layout.addRow("Bot Token:", self.telegram_bot_token_input)
        telegram_layout.addRow("Chat ID:", self.telegram_chat_id_input)
        telegram_layout.addRow("", self.test_notification_button)
        
        # Add help text
        bot_help = QLabel("Create a bot with @BotFather and add it to your alerts channel/group")
        bot_help.setStyleSheet("color: gray; font-style: italic;")
        telegram_layout.addRow("", bot_help)
        
        telegram_group.setLayout(telegram_layout)
        layout.addWidget(telegram_group)"""
            
            # Insert the SpamBox settings code before the Filter settings section
            if "Filter settings" in main_content:
                main_content = main_content.replace("# Filter settings", settings_tab_code + "\n\n        # Filter settings")
            
            # Add the save settings code to the save_settings method
            save_settings_code = """
            # Save SpamBot settings
            self.settings.setValue("spambox_bot_username", self.spambox_username_input.text())
            self.settings.setValue("auto_alert_on_detection", self.auto_alert_check.isChecked())
            self.settings.setValue("auto_fix_banned_accounts", self.auto_fix_banned_check.isChecked())
            
            # Save Telegram Bot settings
            self.settings.setValue("telegram_bot_token", self.telegram_bot_token_input.text())
            self.settings.setValue("telegram_alert_chat_id", self.telegram_chat_id_input.text())"""
            
            # Insert the save settings code at the appropriate point
            if "def save_settings" in main_content:
                save_method = re.search(r"def save_settings\([^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+", main_content)
                if save_method:
                    old_save = save_method.group(0)
                    # Insert our code before the end of the method
                    if "QMessageBox.information" in old_save:
                        insert_point = old_save.find("QMessageBox.information")
                        new_save = old_save[:insert_point] + save_settings_code + "\n            " + old_save[insert_point:]
                        main_content = main_content.replace(old_save, new_save)
            
            # Add test notification method
            if "def send_test_notification" not in main_content:
                test_notification_method = """
    def send_test_notification(self):
        """Send a test notification to verify Telegram Bot configuration."""
        try:
            # First save the current settings
            bot_token = self.telegram_bot_token_input.text()
            chat_id = self.telegram_chat_id_input.text()
            
            if not bot_token or not chat_id:
                QMessageBox.warning(self, "Missing Information", "Please enter both Bot Token and Chat ID")
                return
                
            # Create a test message
            message = f"🧪 *TEST NOTIFICATION* 🧪\\n\\n"
            message += f"This is a test notification from TG Checker.\\n"
            message += f"If you can see this message, your notification settings are working correctly!\\n\\n"
            message += f"⏰ *Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # Send using Telegram Bot API
            import requests
            url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
            data = {
                "chat_id": chat_id,
                "text": message,
                "parse_mode": "Markdown"
            }
            
            response = requests.post(url, data=data)
            response_data = response.json()
            
            if response.status_code == 200 and response_data.get("ok"):
                QMessageBox.information(self, "Success", "Test notification sent successfully!")
                self.log_activity("Test notification sent successfully")
            else:
                error_msg = response_data.get("description", "Unknown error")
                QMessageBox.critical(self, "Error", f"Failed to send test notification: {error_msg}")
                self.log_activity(f"Failed to send test notification: {error_msg}")
                
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Error sending test notification: {str(e)}")
            self.logger.error(f"Error sending test notification: {str(e)}")"""
                
                # Add the test notification method before the end of the class
                if "# End of TGCheckerApp" in main_content:
                    main_content = main_content.replace("# End of TGCheckerApp", test_notification_method + "\n\n    # End of TGCheckerApp")
                else:
                    # Try to find a good insertion point near the end of the class
                    last_method = re.search(r"def [a-zA-Z_]+\([^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+", main_content[::-1])
                    if last_method:
                        reverse_idx = last_method.start()
                        idx = len(main_content) - reverse_idx
                        main_content = main_content[:idx] + test_notification_method + "\n" + main_content[idx:]
        
        # Enhance the refresh_account_info method to check status and update UI
        if "def refresh_account_info" in main_content and "detect spam/ban status" not in main_content:
            refresh_method = re.search(r"def refresh_account_info\([^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+", main_content)
            if refresh_method:
                old_refresh = refresh_method.group(0)
                
                # Add status check and detection
                enhanced_refresh = old_refresh
                
                # Check if there's already a return statement we need to replace
                if "return True" in enhanced_refresh:
                    # Add status check before the return
                    status_check_code = """
                # Check and update account status
                try:
                    # Detect spam/ban status from the user info response
                    status = "Active"
                    restrictions = None
                    until_date = None
                    
                    # Check if account is banned or limited
                    if result.get('user', {}).get('restricted'):
                        # Account has restrictions
                        if result.get('user', {}).get('restriction_reason'):
                            restriction_text = result.get('user', {}).get('restriction_reason', '')
                            
                            if "permanently" in restriction_text.lower():
                                status = "Banned"
                                restrictions = "Permanent ban"
                            else:
                                status = "Limited"
                                
                                # Try to extract duration if available
                                duration_match = re.search(r"until (\d{4}-\d{2}-\d{2})", restriction_text)
                                if duration_match:
                                    until_date = duration_match.group(1)
                                    restrictions = f"Limited until {until_date}"
                                else:
                                    restrictions = "Limited (unknown duration)"
                    
                    # Update account status in database
                    self.account_manager.update_account_status(phone, status, restrictions, until_date)
                    
                    # If auto-alert is enabled and status is not Active, send notification
                    if status != "Active" and self.settings.value("auto_alert_on_detection", True, type=bool):
                        self.notify_telegram_bot(phone, status, restrictions)
                    
                    # If auto-fix is enabled and status is Limited/Banned, try to fix
                    if status != "Active" and self.settings.value("auto_fix_banned_accounts", True, type=bool):
                        threading.Thread(target=self._fix_account_thread, args=(phone,), daemon=True).start()
                    
                except Exception as e:
                    self.logger.error(f"Error checking account status: {str(e)}")
                """
                    enhanced_refresh = enhanced_refresh.replace("                return True", status_check_code + "\n                return True")
                
                main_content = main_content.replace(old_refresh, enhanced_refresh)
        
        # Enhance the update_account_row method to display status with colors
        if "def update_account_row" in main_content and "set status color" not in main_content:
            update_row_method = re.search(r"def update_account_row\([^)]*\):[^\n]*\n(?:[ \t]+[^\n]*\n)+", main_content)
            if update_row_method:
                old_update = update_row_method.group(0)
                
                # Check if we need to enhance the status display
                if "status_item" in old_update and "status_color" not in old_update:
                    # Find where we set the status text
                    status_line_match = re.search(r"status_item\.setText\([^)]+\)", old_update)
                    if status_line_match:
                        status_line = status_line_match.group(0)
                        # Add color coding after the status text setting
                        status_color_code = """
                # Set status color based on account status
                status_color = QColor("#008000")  # Green for Active
                if status == "Limited":
                    status_color = QColor("#FFA500")  # Orange for Limited
                elif status == "Banned":
                    status_color = QColor("#FF0000")  # Red for Banned
                elif status == "Unknown":
                    status_color = QColor("#808080")  # Gray for Unknown
                
                status_item.setForeground(status_color)"""
                        
                        # Insert the color code after setting the status text
                        enhanced_update = old_update.replace(status_line, status_line + status_color_code)
                        main_content = main_content.replace(old_update, enhanced_update)
        
        # Write the enhanced main.py back
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(main_content)
        print("Enhanced main.py with SpamBot integration and status handling")
    
    print("SpamBot integration and status handling implemented successfully")
    print("You can now detect account limitations, send alerts, and fix accounts automatically")

if __name__ == "__main__":
    main() 