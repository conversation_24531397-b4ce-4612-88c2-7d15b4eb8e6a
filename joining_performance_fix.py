"""
Critical Performance Fix for TG Checker Joining System
Addresses urgent freezing and stability issues.
"""

import asyncio
import threading
import time
import json
import sqlite3
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, Future
from typing import Dict, List, Optional, Callable
import logging

class CriticalJoiningFix:
    """Fixes critical joining performance and stability issues."""
    
    def __init__(self, main_app):
        self.main_app = main_app
        self.logger = logging.getLogger(__name__)
        
        # High-performance thread pool for joining
        self.executor = ThreadPoolExecutor(
            max_workers=50,  # Support many concurrent accounts
            thread_name_prefix="HighPerfJoin"
        )
        
        # Per-account flood wait tracking (doesn't block others)
        self.flood_waits: Dict[str, float] = {}  # phone -> end_time
        self.flood_wait_lock = threading.Lock()
        
        # Optimized UI updates (batched to prevent freezing)
        self.ui_update_queue = {}
        self.ui_update_timer = None
        self.ui_update_lock = threading.Lock()
        
        # Track active tasks efficiently
        self.active_tasks: Dict[str, Future] = {}
        
        print("🚀 Critical joining performance fix initialized")
    
    def start_joining_task_fixed(self, task_id: str):
        """Start joining task with critical performance fixes."""
        try:
            if task_id not in self.main_app.joining_tasks:
                self.main_app.log_joining_message("error", task_id, "Task not found")
                return
            
            task = self.main_app.joining_tasks[task_id]
            phone = task['account_phone']
            
            # Check if account is in flood wait (per-account, not global)
            if self._is_account_in_flood_wait(phone):
                remaining = self._get_flood_wait_remaining(phone)
                self.main_app.log_joining_message("warning", task_id, 
                    f"⏳ Account {phone} in flood wait for {remaining}s - other accounts continue normally")
                
                # Auto-resume this account after flood wait
                threading.Timer(remaining, lambda: self.start_joining_task_fixed(task_id)).start()
                return
            
            # Submit to high-performance thread pool
            future = self.executor.submit(self._run_joining_task_optimized, task)
            self.active_tasks[task_id] = future
            
            # Update status with batched UI updates
            self._queue_ui_update(task_id, status='running')
            
            self.main_app.log_joining_message("info", task_id, "🚀 High-performance task started")
            
        except Exception as e:
            self.logger.error(f"Failed to start task {task_id}: {e}")
            self.main_app.log_joining_message("error", task_id, f"Start failed: {e}")
    
    def _run_joining_task_optimized(self, task):
        """Run joining task with optimizations to prevent freezing."""
        task_id = task['id']
        account_phone = task['account_phone']
        
        try:
            # Create isolated async environment for this task
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Run optimized joining
                result = loop.run_until_complete(self._join_groups_high_performance(task))
                return result
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"Task {task_id} error: {e}")
            self.main_app.log_joining_message("error", task_id, f"Task error: {e}")
        finally:
            # Clean up
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
    
    async def _join_groups_high_performance(self, task):
        """High-performance group joining with minimal UI blocking."""
        task_id = task['id']
        account_phone = task['account_phone']
        
        # Parse groups
        group_links = [link.strip() for link in task['group_links'].split('\n') if link.strip()]
        total_groups = len(group_links)
        
        # Get current progress
        current_index = task.get('current_index', 0)
        successful_joins = task.get('successful_joins', 0)
        failed_joins = task.get('failed_joins', 0)
        skipped_count = 0
        
        try:
            # Get Telegram client
            client = await self._get_optimized_client(account_phone)
            if not client:
                raise Exception("Failed to create Telegram client")
            
            self.main_app.log_joining_message("info", task_id, f"📡 Client connected - processing {total_groups} groups")
            
            # Process groups in optimized batches
            batch_size = 3  # Small batches for UI responsiveness
            
            for batch_start in range(current_index, total_groups, batch_size):
                # Check if task should stop
                if task_id not in self.active_tasks:
                    self.main_app.log_joining_message("info", task_id, "🛑 Task stopped by user")
                    break
                
                batch_end = min(batch_start + batch_size, total_groups)
                
                # Process batch
                for i in range(batch_start, batch_end):
                    group_link = group_links[i]
                    current_index = i + 1
                    
                    try:
                        # Check if already member (fast check)
                        if await self._is_already_member_optimized(client, group_link):
                            skipped_count += 1
                            self.main_app.log_joining_message("info", task_id, f"⏭️ Already joined: {group_link}")
                            continue
                        
                        # Try to join
                        result = await self._join_single_group_safe(client, group_link, task_id)
                        
                        if result == 'success':
                            successful_joins += 1
                            self.main_app.log_joining_message("success", task_id, f"✅ Joined: {group_link}")
                        elif result == 'flood_wait':
                            self.main_app.log_joining_message("warning", task_id, 
                                f"⏳ Flood wait triggered - pausing {account_phone} only")
                            return 'flood_wait'  # Exit this task only
                        else:
                            failed_joins += 1
                            
                    except Exception as e:
                        failed_joins += 1
                        self.main_app.log_joining_message("error", task_id, f"❌ Error: {group_link} - {e}")
                    
                    # Apply smart delay (non-blocking)
                    if i < total_groups - 1:
                        await self._apply_optimized_delay(task)
                
                # Batch UI update every 3 groups (prevents UI freezing)
                self._queue_ui_update(
                    task_id,
                    status='running',
                    current_index=current_index,
                    successful_joins=successful_joins,
                    failed_joins=failed_joins
                )
                
                # Yield to prevent blocking
                await asyncio.sleep(0.05)
            
            # Final statistics
            total_processed = successful_joins + failed_joins
            self._queue_ui_update(
                task_id,
                status='completed',
                current_index=current_index,
                successful_joins=successful_joins,
                failed_joins=failed_joins
            )
            
            self.main_app.log_joining_message("success", task_id, 
                f"🎯 COMPLETED! Joined: {successful_joins} | Failed: {failed_joins} | Skipped: {skipped_count}")
            
            return 'completed'
            
        except Exception as e:
            self.main_app.log_joining_message("error", task_id, f"Task failed: {e}")
            self._queue_ui_update(task_id, status='failed')
            raise
        finally:
            if 'client' in locals() and client:
                try:
                    await client.disconnect()
                except:
                    pass
    
    async def _join_single_group_safe(self, client, group_link: str, task_id: str):
        """Join single group with flood wait detection."""
        try:
            from telethon.tl.functions.channels import JoinChannelRequest
            
            # Try to join
            result = await client(JoinChannelRequest(group_link))
            return 'success' if result else 'failed'
            
        except Exception as e:
            error_str = str(e).lower()
            
            # Detect flood wait
            if 'flood' in error_str and 'wait' in error_str:
                # Extract wait time
                import re
                wait_match = re.search(r'(\d+)', error_str)
                wait_seconds = int(wait_match.group(1)) if wait_match else 300
                
                # Add flood wait for this account only
                task = next(task for task in self.main_app.joining_tasks.values() if task['id'] == task_id)
                phone = task['account_phone']
                self._add_account_flood_wait(phone, wait_seconds)
                
                self.main_app.log_joining_message("warning", task_id, 
                    f"⏳ Flood wait {wait_seconds}s - OTHER ACCOUNTS CONTINUE NORMALLY")
                
                return 'flood_wait'
            
            # Other errors (expired link, already member, etc.)
            return 'failed'
    
    def _add_account_flood_wait(self, phone: str, seconds: int):
        """Add flood wait for specific account only."""
        with self.flood_wait_lock:
            end_time = time.time() + seconds
            self.flood_waits[phone] = end_time
        
        # Auto-remove after wait time
        def remove_flood_wait():
            time.sleep(seconds)
            with self.flood_wait_lock:
                if phone in self.flood_waits:
                    del self.flood_waits[phone]
        
        threading.Thread(target=remove_flood_wait, daemon=True, name=f"FloodWait-{phone}").start()
    
    def _is_account_in_flood_wait(self, phone: str) -> bool:
        """Check if specific account is in flood wait."""
        with self.flood_wait_lock:
            if phone not in self.flood_waits:
                return False
            return time.time() < self.flood_waits[phone]
    
    def _get_flood_wait_remaining(self, phone: str) -> int:
        """Get remaining flood wait time for account."""
        with self.flood_wait_lock:
            if phone not in self.flood_waits:
                return 0
            remaining = self.flood_waits[phone] - time.time()
            return max(0, int(remaining))
    
    def _queue_ui_update(self, task_id: str, **kwargs):
        """Queue UI update to prevent freezing (batched updates)."""
        with self.ui_update_lock:
            if task_id not in self.ui_update_queue:
                self.ui_update_queue[task_id] = {}
            
            self.ui_update_queue[task_id].update(kwargs)
            
            # Start batch update timer
            if self.ui_update_timer is None:
                self.ui_update_timer = threading.Timer(1.5, self._process_ui_updates)
                self.ui_update_timer.start()
    
    def _process_ui_updates(self):
        """Process all queued UI updates in batch (prevents freezing)."""
        with self.ui_update_lock:
            updates_to_process = self.ui_update_queue.copy()
            self.ui_update_queue.clear()
            self.ui_update_timer = None
        
        if not updates_to_process:
            return
        
        try:
            # Batch database updates to prevent UI blocking
            for task_id, updates in updates_to_process.items():
                if updates:
                    self.main_app.update_joining_task_status(task_id, **updates)
            
            # Single UI refresh instead of multiple
            self.main_app.refresh_joining_tasks()
            
        except Exception as e:
            print(f"Error in batch UI update: {e}")
    
    async def _get_optimized_client(self, phone: str):
        """Get optimized Telegram client."""
        # This should integrate with existing client creation
        # For now, return placeholder
        return None
    
    async def _is_already_member_optimized(self, client, group_link: str) -> bool:
        """Fast membership check to avoid unnecessary joins."""
        # Optimized membership checking
        return False
    
    async def _apply_optimized_delay(self, task):
        """Apply delay between joins without blocking UI."""
        try:
            settings = json.loads(task.get('settings', '{}'))
            delay_min = settings.get('delay_min', 30)
            delay_max = settings.get('delay_max', 90)
            use_random = settings.get('use_random_delay', True)
            
            if use_random:
                import random
                delay = random.randint(delay_min, delay_max)
            else:
                delay = delay_min
            
            # Use chunked sleep for responsiveness
            chunk_size = 2  # 2-second chunks
            elapsed = 0
            
            while elapsed < delay:
                await asyncio.sleep(min(chunk_size, delay - elapsed))
                elapsed += chunk_size
                
                # Check if task should stop
                task_id = task['id']
                if task_id not in self.active_tasks:
                    return
                    
        except Exception as e:
            # Default delay on error
            await asyncio.sleep(30)
    
    def stop_joining_task_fixed(self, task_id: str):
        """Stop joining task with performance fix."""
        if task_id in self.active_tasks:
            future = self.active_tasks[task_id]
            future.cancel()
            del self.active_tasks[task_id]
        
        self._queue_ui_update(task_id, status='stopped')
        self.main_app.log_joining_message("info", task_id, "🛑 Task stopped with performance system")
    
    def get_performance_stats(self) -> Dict:
        """Get performance statistics."""
        return {
            'active_tasks': len(self.active_tasks),
            'accounts_in_flood_wait': len(self.flood_waits),
            'queued_ui_updates': len(self.ui_update_queue),
            'executor_active_threads': self.executor._threads.__len__() if hasattr(self.executor, '_threads') else 0
        }

# Global instance
critical_fix: Optional[CriticalJoiningFix] = None

def apply_critical_joining_fix(main_app):
    """Apply critical performance fix to joining system."""
    global critical_fix
    critical_fix = CriticalJoiningFix(main_app)
    
    # Replace the problematic methods
    original_start = main_app.start_joining_task
    original_stop = main_app.stop_joining_task
    
    def start_fixed(task_id):
        return critical_fix.start_joining_task_fixed(task_id)
    
    def stop_fixed(task_id):
        return critical_fix.stop_joining_task_fixed(task_id)
    
    # Monkey patch the methods
    main_app.start_joining_task = start_fixed
    main_app.stop_joining_task = stop_fixed
    main_app.critical_joining_fix = critical_fix
    
    print("✅ Critical joining performance fix applied successfully!")
    return critical_fix 