#!/usr/bin/env python3
"""
Speed Checker: A standalone app to configure speed check settings
for TG Checker
"""

import os
import sys
import json
import random
import time
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, 
    QGroupBox, QFormLayout, QSpinBox, QLabel, QPushButton,
    QHBoxLayout, QTextEdit, QCheckBox
)
from PyQt5.QtCore import QSettings, QTimer, Qt


# Register Qt meta types for thread-safe operations
qRegisterMetaType('QTextCursor')
qRegisterMetaType('QString')

class SpeedCheckerApp(QMainWindow, qRegisterMetaType, pyqtSlot):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TG Checker - Speed Check Settings")
        self.resize(500, 400)
        
        # Load settings
        self.settings = QSettings("TGChecker", "SpeedSettings")
        
        # Create main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # Create settings group
        self.create_settings_group(main_layout)
        
        # Create test area
        self.create_test_area(main_layout)
        
        # Create buttons
        self.create_buttons(main_layout)
        
        # Status area
        self.status_text = QTextEdit()
        self.status_text.setReadOnly(True)
        self.status_text.setMaximumHeight(150)
        main_layout.addWidget(self.status_text)
        
        # Initialize timer
        self.timer = QTimer()
        self.timer.timeout.connect(self.on_timer_tick)
        self.current_iteration = 0
        self.max_iterations = 5
        
        # Log startup
        self.log_message("Speed Checker app started")
        self.log_message("Configure and test random timing between groups")
    
    def create_settings_group(self, layout):
        """Create the settings group with min/max values"""
        settings_group = QGroupBox("Speed Check Time Per 1 Group")
        settings_layout = QFormLayout()
        
        # Min seconds
        self.min_seconds = QSpinBox()
        self.min_seconds.setMinimum(1)
        self.min_seconds.setMaximum(60)
        self.min_seconds.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds.setSuffix(" seconds")
        self.min_seconds.valueChanged.connect(self.update_range)
        
        # Max seconds
        self.max_seconds = QSpinBox()
        self.max_seconds.setMinimum(1)
        self.max_seconds.setMaximum(120)
        self.max_seconds.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds.setSuffix(" seconds")
        self.max_seconds.valueChanged.connect(self.update_range)
        
        # Add to layout
        settings_layout.addRow("Minimum Time:", self.min_seconds)
        settings_layout.addRow("Maximum Time:", self.max_seconds)
        
        # Help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        settings_layout.addRow("", help_label)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
    
    def create_test_area(self, layout):
        """Create the test area for running simulations"""
        test_group = QGroupBox("Test Speed Settings")
        test_layout = QVBoxLayout()
        
        # Test controls
        controls_layout = QHBoxLayout()
        
        self.start_button = QPushButton("Start Test")
        self.start_button.clicked.connect(self.start_test)
        controls_layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("Stop Test")
        self.stop_button.clicked.connect(self.stop_test)
        self.stop_button.setEnabled(False)
        controls_layout.addWidget(self.stop_button)
        
        # Number of groups to simulate
        self.group_count = QSpinBox()
        self.group_count.setMinimum(2)
        self.group_count.setMaximum(20)
        self.group_count.setValue(5)
        controls_layout.addWidget(QLabel("Groups:"))
        controls_layout.addWidget(self.group_count)
        
        test_layout.addLayout(controls_layout)
        
        # Add verbose output option
        self.verbose_output = QCheckBox("Show detailed timing information")
        self.verbose_output.setChecked(True)
        test_layout.addWidget(self.verbose_output)
        
        test_group.setLayout(test_layout)
        layout.addWidget(test_group)
    
    def create_buttons(self, layout):
        """Create the action buttons"""
        button_layout = QHBoxLayout()
        
        # Save button
        save_button = QPushButton("Save Settings")
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(save_button)
        
        # Reset button
        reset_button = QPushButton("Reset to Defaults")
        reset_button.clicked.connect(self.reset_settings)
        button_layout.addWidget(reset_button)
        
        layout.addLayout(button_layout)
    
    def update_range(self):
        """Ensure min <= max for the speed settings"""
        if self.min_seconds.value() > self.max_seconds.value():
            self.max_seconds.setValue(self.min_seconds.value())
    
    def save_settings(self):
        """Save settings to the config file"""
        self.settings.setValue("min_check_seconds", self.min_seconds.value())
        self.settings.setValue("max_check_seconds", self.max_seconds.value())
        self.log_message(f"Settings saved: Min={self.min_seconds.value()}s, Max={self.max_seconds.value()}s")
        
        # Also save as JSON for other programs
        config = {
            "min_check_seconds": self.min_seconds.value(),
            "max_check_seconds": self.max_seconds.value()
        }
        
        try:
            with open("speed_settings.json", "w") as f:
                json.dump(config, f, indent=2)
            self.log_message("Settings also saved to speed_settings.json")
        except Exception as e:
            self.log_message(f"Error saving JSON: {e}")
    
    def reset_settings(self):
        """Reset settings to defaults"""
        self.min_seconds.setValue(2)
        self.max_seconds.setValue(5)
        self.log_message("Settings reset to defaults")
    
    def start_test(self):
        """Start the test simulation"""
        self.current_iteration = 0
        self.max_iterations = self.group_count.value()
        
        # Update UI
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.min_seconds.setEnabled(False)
        self.max_seconds.setEnabled(False)
        self.group_count.setEnabled(False)
        
        # Clear status
        self.status_text.clear()
        
        # Log
        self.log_message(f"Starting test with {self.max_iterations} groups")
        self.log_message(f"Speed settings: Min={self.min_seconds.value()}s, Max={self.max_seconds.value()}s")
        
        # Start first iteration immediately
        self.on_timer_tick()
    
    def stop_test(self):
        """Stop the test simulation"""
        if self.timer.isActive():
            self.timer.stop()
        
        # Update UI
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.min_seconds.setEnabled(True)
        self.max_seconds.setEnabled(True)
        self.group_count.setEnabled(True)
        
        # Log
        self.log_message("Test stopped")
    
    def on_timer_tick(self):
        """Handle timer tick - simulate checking a group"""
        if self.current_iteration < self.max_iterations:
            # First group has no delay
            if self.current_iteration == 0:
                self.log_message(f"Checking group 1...")
                # Start timer for next group
                self.current_iteration += 1
                delay = random.uniform(self.min_seconds.value(), self.max_seconds.value())
                
                if self.verbose_output.isChecked():
                    self.log_message(f"⏱️ Waiting {delay:.1f}s before checking next group...")
                
                self.timer.start(int(delay * 1000))
            else:
                # Check the group
                self.log_message(f"Checking group {self.current_iteration + 1}...")
                
                # Set up next iteration
                self.current_iteration += 1
                
                # If we have more iterations, start timer again
                if self.current_iteration < self.max_iterations:
                    delay = random.uniform(self.min_seconds.value(), self.max_seconds.value())
                    
                    if self.verbose_output.isChecked():
                        self.log_message(f"⏱️ Waiting {delay:.1f}s before checking next group...")
                    
                    self.timer.start(int(delay * 1000))
                else:
                    self.log_message("Test complete!")
                    self.stop_test()
        else:
            self.stop_test()
    
    def log_message(self, message):
        """Add a message to the log"""
        self.status_text.append(message)
        # Scroll to bottom
        self.status_text.verticalScrollBar().setValue(
            self.status_text.verticalScrollBar().maximum()
        )

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SpeedCheckerApp()
    window.show()
    sys.exit(app.exec_()) 