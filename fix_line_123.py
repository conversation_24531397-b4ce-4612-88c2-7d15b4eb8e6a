import re
import os

def fix_line_123():
    """Fix the syntax error at line 123 in main.py"""
    print("Fixing line 123 indentation issue in main.py...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
    except UnicodeDecodeError:
        try:
            with open("main.py", "r", encoding="latin-1") as f:
                lines = f.readlines()
        except:
            print("Could not read main.py with any encoding")
            return False
    
    # Make a backup
    with open("main.py.line123.bak", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    # Check and fix line 123
    if len(lines) >= 123:
        print(f"Original line 123: {lines[122]}")
        # Get the context around line 123
        context_start = max(0, 122 - 5)
        context_end = min(len(lines), 122 + 5)
        
        print("\nContext around line 123:")
        for i in range(context_start, context_end):
            prefix = ">" if i == 122 else " "
            line_num = i + 1
            print(f"{prefix} {line_num}: {lines[i]}", end="")
        
        # Determine correct indentation from surrounding lines
        # Look for the previous line that starts with a sensible indentation
        prev_indent = 0
        for i in range(122-1, max(0, 122-10), -1):
            stripped = lines[i].lstrip()
            if stripped and not stripped.isspace():
                prev_indent = len(lines[i]) - len(stripped)
                break
        
        # Look for the next line that starts with a sensible indentation
        next_indent = 0
        for i in range(122+1, min(len(lines), 122+10)):
            stripped = lines[i].lstrip()
            if stripped and not stripped.isspace():
                next_indent = len(lines[i]) - len(stripped)
                break
        
        # Use the minimum of prev and next indents if both are non-zero
        correct_indent = 4  # Default to 4 spaces
        if prev_indent > 0 and next_indent > 0:
            correct_indent = min(prev_indent, next_indent)
        elif prev_indent > 0:
            correct_indent = prev_indent
        elif next_indent > 0:
            correct_indent = next_indent
        
        # Fix the line
        line_123 = lines[122]
        stripped_123 = line_123.lstrip()
        fixed_line = ' ' * correct_indent + stripped_123
        
        lines[122] = fixed_line
        
        # Also fix any other indentation issues in this section
        for i in range(context_start, context_end):
            if i != 122:  # Skip the line we already fixed
                stripped = lines[i].lstrip()
                if stripped and not stripped.isspace():
                    indent = len(lines[i]) - len(stripped)
                    if indent % 4 != 0:
                        # Fix indentation to nearest multiple of 4
                        new_indent = round(indent / 4) * 4
                        lines[i] = ' ' * new_indent + stripped
        
        # Write the fixed content back
        with open("main.py", "w", encoding="utf-8") as f:
            f.writelines(lines)
        
        print(f"\nFixed line 123: {lines[122]}")
        print(f"Applied indentation of {correct_indent} spaces")
        
        # Verify if syntax is now valid
        try:
            with open("main.py", "r", encoding="utf-8") as f:
                content = f.read()
            compile(content, "main.py", "exec")
            print("✓ Syntax is now valid")
            return True
        except SyntaxError as e:
            print(f"✗ Syntax error still exists: {str(e)}")
            # Try to fix the specific error
            if "unexpected indent" in str(e):
                line_num = e.lineno
                if line_num > 0 and line_num <= len(lines):
                    print(f"Attempting to fix indentation error at line {line_num}")
                    stripped = lines[line_num-1].lstrip()
                    lines[line_num-1] = "    " * 3 + stripped  # Use a safe indentation
                    with open("main.py", "w", encoding="utf-8") as f:
                        f.writelines(lines)
                    print(f"Applied safe indentation to line {line_num}")
            return False
    else:
        print("Line 123 does not exist in main.py")
        return False

if __name__ == "__main__":
    fix_line_123() 