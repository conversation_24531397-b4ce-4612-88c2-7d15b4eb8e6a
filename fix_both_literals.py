def fix_decimal_literals():
    print("Fixing both invalid decimal literals...")
    
    # Read the file content
    with open("main.py", "r", encoding="utf-8") as file:
        content = file.read()
    
    # Replace all occurrences of the problematic pattern
    if "if task else 0ask else 0" in content:
        fixed_content = content.replace("if task else 0ask else 0", "if task else 0")
        print("Found and fixed invalid decimal literals")
    else:
        print("Pattern not found, searching for variations...")
        patterns = [
            "if task else 0ask",
            "0ask else 0",
            "if task else 0 ask",
            "0 ask else 0"
        ]
        
        fixed_content = content
        for pattern in patterns:
            if pattern in fixed_content:
                fixed_content = fixed_content.replace(pattern, "if task else 0" if pattern.startswith("if") else "0")
                print(f"Fixed variation: '{pattern}'")
    
    # Save the fixed content
    with open("main.py", "w", encoding="utf-8") as file:
        file.write(fixed_content)
    
    print("Fix completed for all instances!")

if __name__ == "__main__":
    fix_decimal_literals() 