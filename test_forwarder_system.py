import sqlite3
import json
from datetime import datetime

def test_forwarder():
    print("Testing Telegram Forwarder System...")
    
    # Test database creation
    try:
        conn = sqlite3.connect("forwarder.db")
        cursor = conn.cursor()
        
        # Test tables
        tables = ['forwarder_tasks', 'account_forwarder_settings', 'global_forwarder_settings', 'forwarding_logs']
        for table in tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if cursor.fetchone():
                print(f"✅ {table} table exists")
            else:
                print(f"❌ {table} table missing")
        
        # Test task creation
        task_data = {
            'id': 'test_001',
            'name': 'Test Task',
            'account_phone': '+**********',
            'message_link': 'https://t.me/test/123',
            'target_groups': json.dumps(['@group1', '@group2']),
            'status': 'stopped',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'settings': json.dumps({'interval_min': 20, 'interval_max': 25})
        }
        
        cursor.execute('''
            INSERT OR REPLACE INTO forwarder_tasks 
            (id, name, account_phone, message_link, target_groups, status, created_at, updated_at, settings)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            task_data['id'], task_data['name'], task_data['account_phone'],
            task_data['message_link'], task_data['target_groups'], task_data['status'],
            task_data['created_at'], task_data['updated_at'], task_data['settings']
        ))
        
        conn.commit()
        print("✅ Test task created successfully")
        
        # Verify task
        cursor.execute("SELECT * FROM forwarder_tasks WHERE id = ?", (task_data['id'],))
        if cursor.fetchone():
            print("✅ Task verification successful")
        else:
            print("❌ Task verification failed")
        
        # Clean up
        cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_data['id'],))
        conn.commit()
        print("✅ Test cleanup completed")
        
        conn.close()
        print("🎉 Forwarder system test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")

if __name__ == "__main__":
    test_forwarder() 