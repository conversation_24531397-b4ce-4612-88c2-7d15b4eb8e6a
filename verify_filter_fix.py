#!/usr/bin/env python3
"""
🔍 VERIFY FILTER FIX
Automatically checks if the filter logic is working correctly
"""

import os
from datetime import datetime

def check_results_accuracy():
    """Check if results are accurately classified in correct folders."""
    print("🔍 VERIFYING FILTER FIX ACCURACY")
    print("=" * 60)
    
    # Check each folder
    folders = {
        "Groups_Valid_Filter": "Results/Groups_Valid_Filter",
        "Groups_Valid_Only": "Results/Groups_Valid_Only",
        "Topics_Groups_Only_Valid": "Results/Topics_Groups_Only_Valid",
        "Channels_Only_Valid": "Results/Channels_Only_Valid",
        "Invalid_Groups_Channels": "Results/Invalid_Groups_Channels",
        "Account_Issues": "Results/Account_Issues"
    }
    
    results = {}
    total_groups = 0
    
    for folder_name, folder_path in folders.items():
        groups = []
        if os.path.exists(folder_path):
            files = os.listdir(folder_path)
            
            for file in files:
                if file.endswith('.txt'):
                    file_path = os.path.join(folder_path, file)
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:
                                lines = [line.strip() for line in content.split('\n') 
                                        if line.strip() and 'https://t.me/' in line]
                                for line in lines:
                                    username = line.replace('https://t.me/', '').strip()
                                    if username:
                                        groups.append(username)
                                        total_groups += 1
                    except Exception as e:
                        print(f"   ❌ Error reading {file}: {e}")
        
        results[folder_name] = groups
        print(f"📂 {folder_name}: {len(groups)} groups")
        for group in groups:
            print(f"   • {group}")
    
    print(f"\n📊 TOTAL GROUPS PROCESSED: {total_groups}")
    
    # Analyze specific test cases
    print(f"\n🎯 FILTER ACCURACY ANALYSIS:")
    print("=" * 60)
    
    # Check critical test cases
    masco_location = None
    imperial_location = None
    hyip_location = None
    islamic_location = None
    
    for folder, groups in results.items():
        if "MASCO_HACKERS" in groups:
            masco_location = folder
        if "imperiamarket" in groups:
            imperial_location = folder
        if "hyipinformer_com" in groups:
            hyip_location = folder
        if "islamic_hacker_army" in groups:
            islamic_location = folder
    
    # Results analysis
    accuracy_score = 0
    total_tests = 4
    
    print("📋 Individual Group Analysis:")
    print(f"• MASCO_HACKERS → {masco_location or 'NOT_FOUND'}")
    print(f"• imperiamarket → {imperial_location or 'NOT_FOUND'}")
    print(f"• hyipinformer_com → {hyip_location or 'NOT_FOUND'}")
    print(f"• islamic_hacker_army → {islamic_location or 'NOT_FOUND'}")
    
    # Filter logic check (with current settings: 500 members, 1 hour, 100 messages)
    print(f"\n🔍 FILTER COMPLIANCE CHECK:")
    
    # Groups that should pass filters (if they have recent activity)
    recent_activity_groups = ["imperiamarket", "hyipinformer_com", "islamic_hacker_army"]
    
    # MASCO_HACKERS is the key test - it should go to Groups_Valid_Only if it has >1 hour activity
    if masco_location == "Groups_Valid_Only":
        print("✅ MASCO_HACKERS in Groups_Valid_Only - CORRECT (likely >1h activity)")
        accuracy_score += 1
    elif masco_location == "Groups_Valid_Filter":
        print("⚠️ MASCO_HACKERS in Groups_Valid_Filter - Check if it actually has ≤1h activity")
        accuracy_score += 1  # Could still be correct if it has recent activity
    else:
        print("❌ MASCO_HACKERS not found or in wrong folder")
    
    # Check other groups
    for group in recent_activity_groups:
        location = None
        for folder, groups in results.items():
            if group in groups:
                location = folder
                break
        
        if location in ["Groups_Valid_Filter", "Groups_Valid_Only"]:
            print(f"✅ {group} in {location} - Valid classification")
            accuracy_score += 1
        else:
            print(f"❌ {group} in {location or 'NOT_FOUND'} - Unexpected")
    
    # Final assessment
    accuracy_percentage = (accuracy_score / total_tests) * 100
    print(f"\n🎯 FILTER ACCURACY: {accuracy_score}/{total_tests} ({accuracy_percentage:.1f}%)")
    
    if accuracy_percentage >= 100:
        print("🎉 PERFECT! Filter logic is working correctly")
        status = "SUCCESS"
    elif accuracy_percentage >= 75:
        print("✅ GOOD! Filter logic is mostly working")
        status = "MOSTLY_SUCCESS"
    else:
        print("❌ ISSUES! Filter logic needs more fixes")
        status = "NEEDS_FIX"
    
    # Key insight about the fix
    print(f"\n🔧 FIX STATUS:")
    if len(results["Groups_Valid_Filter"]) > 0 and len(results["Groups_Valid_Only"]) > 0:
        print("✅ Both Groups_Valid_Filter and Groups_Valid_Only have content")
        print("✅ Filter separation is working")
    elif len(results["Groups_Valid_Filter"]) > 0 and len(results["Groups_Valid_Only"]) == 0:
        print("⚠️ Only Groups_Valid_Filter has content")
        print("   This means either all groups pass filters OR filter logic still has issues")
    elif len(results["Groups_Valid_Filter"]) == 0 and len(results["Groups_Valid_Only"]) > 0:
        print("⚠️ Only Groups_Valid_Only has content")
        print("   This means no groups pass filters OR filter logic is too strict")
    else:
        print("❌ No groups in either folder - check if test ran correctly")
    
    return status, results

def main():
    """Run verification and provide recommendations."""
    status, results = check_results_accuracy()
    
    print(f"\n🚀 RECOMMENDATIONS:")
    print("=" * 60)
    
    if status == "SUCCESS":
        print("✅ Filter fix is working correctly!")
        print("✅ System is ready for production use")
        print("✅ 100% filter compliance achieved")
    elif status == "MOSTLY_SUCCESS":
        print("⚠️ Filter fix is mostly working")
        print("📋 Monitor results for edge cases")
        print("✅ System is usable for production")
    else:
        print("❌ Filter fix needs more work")
        print("🔧 Check enhanced logging in TG Checker output")
        print("📋 Verify filter settings match expectations")
    
    print(f"\n📁 Results saved in Results/ folder structure")
    print(f"📊 Total groups processed: {sum(len(groups) for groups in results.values())}")
    
    return status == "SUCCESS"

if __name__ == "__main__":
    main() 