import sys
import os
import logging
import json
import asyncio
import sqlite3
import threading
import time
import random
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QPushButton, QTextEdit, QTableWidget,
                           QTableWidgetItem, QComboBox, QProgressBar, QMessageBox,
                           QSystemTrayIcon, QMenu, QAction, QHeaderView, QCheckBox,
                           QLineEdit, QGroupBox, QFormLayout, QSpinBox, QFileDialog, QDialog)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings, QMetaObject, Q_ARG
from PyQt5.QtGui import QIcon, QFont, QPixmap, QTextCursor, QColor
from telethon.errors import SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError

# Import custom modules
from tg_client import TelegramClient
from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

class TelegramLoginWorker(QThread):
    login_success = pyqtSignal(dict)
    login_2fa_required = pyqtSignal()
    login_error = pyqtSignal(str)

    def __init__(self, api_id, api_hash, phone, code=None, password=None, phone_code_hash=None, session_file=None, logger=None):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.code = code
        self.password = password
        self.phone_code_hash = phone_code_hash
        self.session_file = session_file or f'sessions/{phone}'
        self.logger = logger
        self.client = None

    def run(self):
        import asyncio
        asyncio.run(self._login_flow())

    async def _login_flow(self):
        from telethon import TelegramClient
        try:
            self.client = TelegramClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect with timeout
            try:
                await asyncio.wait_for(self.client.connect(), timeout=10)
                if self.logger:
                    self.logger.info(f"Connected to Telegram for {self.phone}")
            except asyncio.TimeoutError:
                if self.logger:
                    self.logger.error(f"Connection timeout for {self.phone}")
                self.login_error.emit("Connection to Telegram timed out. Please try again.")
                return
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Connection error for {self.phone}: {str(e)}")
                self.login_error.emit(f"Connection error: {str(e)}")
                return
                
            if not await self.client.is_user_authorized():
                if self.logger:
                    self.logger.info(f"User not authorized, proceeding to login flow for {self.phone}")
    
                try:
                    if self.password:
                        # 2FA flow
                        if self.logger:
                            self.logger.info(f"Attempting 2FA login for {self.phone}")
                        await asyncio.wait_for(self.client.sign_in(password=self.password), timeout=15)
                    else:
                        # Code flow
                        if self.logger:
                            self.logger.info(f"Attempting code login for {self.phone} with code {self.code}")
                        await asyncio.wait_for(
                            self.client.sign_in(phone=self.phone, code=self.code, phone_code_hash=self.phone_code_hash),
                            timeout=15
                        )
                    
                    # Get user info
                    me = await self.client.get_me()
                    if self.logger:
                        self.logger.info(f"Login successful for {self.phone}")
                    self.login_success.emit({'user': me})
                    
                except asyncio.TimeoutError:
                    if self.logger:
                        self.logger.error(f"Login timeout for {self.phone}")
                    self.login_error.emit("Login verification timed out. Please try again.")
                except SessionPasswordNeededError:
                    if self.logger:
                        self.logger.info(f"2FA required for {self.phone}")
                    self.login_2fa_required.emit()
                except PhoneCodeInvalidError:
                    if self.logger:
                        self.logger.error(f"Invalid code for {self.phone}")
                    self.login_error.emit("Invalid or expired code.")
                except Exception as e:
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        if self.logger:
                            self.logger.info(f"2FA detected from login error for {self.phone}")
                        self.login_2fa_required.emit()
                    elif any(keyword in error_msg for keyword in ["invalid", "code", "expired", "wrong"]):
                        if self.logger:
                            self.logger.error(f"Invalid verification code for {self.phone}")
                        self.login_error.emit("Invalid or expired verification code.")
                    else:
                        if self.logger:
                            self.logger.error(f"Login error for {self.phone}: {str(e)}")
                        self.login_error.emit(str(e))
            else:
                # Already authorized
                me = await self.client.get_me()
                if self.logger:
                    self.logger.info(f"Already authorized for {self.phone}")
                self.login_success.emit({'user': me})
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Connection error for {self.phone}: {str(e)}")
            self.login_error.emit(str(e))
        finally:
            # Proper cleanup
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass

class TGCheckerApp(QMainWindow):
    """Main application window for the TG Checker tool."""
    
    # Qt signals for thread-safe UI updates
    update_ui_signal = pyqtSignal()
    update_status_signal = pyqtSignal(str)
    update_analyzing_signal = pyqtSignal(str)
    update_result_counts_signal = pyqtSignal(int, int, int, int, int, int)  # valid_filtered, valid_only, topics, channels, invalid, account_issues
    update_progress_signal = pyqtSignal(int, int)  # current, total
    log_activity_signal = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        
        # Initialize settings
        self.settings = QSettings("TG PY", "TGChecker")
        
        # Set up logging
        self.logger = setup_logger()
        
        # Initialize components
        self.account_manager = AccountManager(self.logger)
        self.monitor = Monitor(self.account_manager, self.logger)
        
        # Initialize checker state tracking
        self.is_checker_running = False
        self.is_task_checker_running = False
        self.checker_should_stop = False
        self.task_checker_should_stop = False
        
        # Progress tracking for crash recovery
        self.current_group_index = 0
        self.total_groups = 0
        self.progress_file = "last_checked.txt"
        self.current_group_links = []
        
        # Enhanced FloodWait handling
        self.account_states = {}  # Track account availability states
        self.pending_groups_queue = []  # Queue for groups that need reassignment
        self.retry_scheduled_groups = []  # Groups scheduled for retry after wait
        
        # Thread-safe result collections for multi-account task checker
        self.valid_filtered = []
        self.valid_only = []
        self.topics_groups = []
        self.channels_only = []
        self.invalid_groups = []
        self.account_issues = []
        self.join_requests = []
        self.results_lock = threading.RLock()
        self.account_states_lock = threading.RLock()
        
        # Global progress tracking for multi-account checker
        self.global_groups_processed = 0
        self.global_progress_lock = threading.RLock()
        
        # Connect signals to their handlers
        self.update_ui_signal.connect(self.update_ui)
        self.update_status_signal.connect(self._update_status_label)
        self.update_analyzing_signal.connect(self._update_analyzing_label)
        self.update_result_counts_signal.connect(self._update_result_counts)
        self.update_progress_signal.connect(self._update_progress)
        self.log_activity_signal.connect(self._log_activity_to_ui)
        
        # Set up the UI
        self.setup_ui()
        
        # Load and apply settings (including theme)
        self.load_settings()
        
        # Start background processes
        self.start_background_processes()
    
    def _update_status_label(self, text):
        """Thread-safe status label update."""
        self.status_label.setText(text)
    
    def _update_analyzing_label(self, text):
        """Thread-safe analyzing label update."""
        self.currently_analyzing_label.setText(text)
    
    def _update_result_counts(self, valid_filtered, valid_only, topics, channels, invalid, account_issues):
        """Thread-safe result counts update."""
        self.valid_filtered_count.setText(str(valid_filtered))
        self.valid_only_count.setText(str(valid_only))
        self.topics_count.setText(str(topics))
        self.channels_count.setText(str(channels))
        self.invalid_count.setText(str(invalid))
        self.account_issues_count.setText(str(account_issues))
    
    def _update_progress(self, current, total):
        """Thread-safe progress update with console logging."""
        try:
            self.progress_count.setText(f"{current} / {total}")
            # Also log to console for monitoring
            if total > 0:
                percentage = (current / total) * 100
                self.logger.info(f"[PROGRESS] Groups Checked: {current} / {total} ({percentage:.1f}%)")
        except Exception as e:
            self.logger.error(f"Error updating progress: {str(e)}")
    
    def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.activities_text.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            self.logger.info(message)
            
        except Exception as e:
            print(f"Failed to log activity: {str(e)}")
    
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("TG Checker")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create tabs
        self.dashboard_tab = self.create_dashboard_tab()
        self.accounts_tab = self.create_accounts_tab()
        self.monitor_tab = self.create_monitor_tab()
        self.logs_tab = self.create_logs_tab()
        self.settings_tab = self.create_settings_tab()
        
        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Dashboard")
        self.tabs.addTab(self.accounts_tab, "Accounts")
        self.tabs.addTab(self.monitor_tab, "Monitor")
        self.tabs.addTab(self.logs_tab, "Logs")
        self.tabs.addTab(self.settings_tab, "Settings")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_label = QLabel("Ready")
        self.status_bar = self.statusBar()
        self.status_bar.addPermanentWidget(self.status_label)
        
        # Set the main widget
        self.setCentralWidget(main_widget)
        
    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add status overview
        status_group = QGroupBox("System Status")
        status_layout = QFormLayout()
        
        self.active_accounts_label = QLabel("0")
        self.total_accounts_label = QLabel("0")
        self.monitor_status_label = QLabel("Stopped")
        self.last_sync_label = QLabel("Never")
        
        status_layout.addRow("Active Accounts:", self.active_accounts_label)
        status_layout.addRow("Total Accounts:", self.total_accounts_label)
        status_layout.addRow("Monitor Status:", self.monitor_status_label)
        status_layout.addRow("Last Sync:", self.last_sync_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # Add quick actions
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QHBoxLayout()
        
        self.sync_button = QPushButton("Sync Now")
        self.toggle_monitor_button = QPushButton("Start Monitor")
        self.sync_and_monitor_button = QPushButton("Sync & Monitor")
        self.start_checker_button = QPushButton("Start Checker")
        self.stop_checker_button = QPushButton("Stop Checker")
        self.start_task_checker_button = QPushButton("Task Checker")
        self.stop_task_checker_button = QPushButton("Stop Task Checker")
        self.theme_toggle_button = QPushButton("🌙 Dark Theme")
        
        self.sync_button.clicked.connect(self.sync_accounts)
        self.toggle_monitor_button.clicked.connect(self.toggle_monitor)
        self.sync_and_monitor_button.clicked.connect(self.sync_and_monitor)
        self.start_checker_button.clicked.connect(self.start_checker)
        self.stop_checker_button.clicked.connect(self.stop_checker)
        self.start_task_checker_button.clicked.connect(self.start_task_checker)
        self.stop_task_checker_button.clicked.connect(self.stop_task_checker)
        self.theme_toggle_button.clicked.connect(self.toggle_theme)
        
        # Initially disable stop buttons
        self.stop_checker_button.setEnabled(False)
        self.stop_task_checker_button.setEnabled(False)
        
        actions_layout.addWidget(self.sync_button)
        actions_layout.addWidget(self.toggle_monitor_button)
        actions_layout.addWidget(self.sync_and_monitor_button)
        actions_layout.addWidget(self.start_checker_button)
        actions_layout.addWidget(self.stop_checker_button)
        actions_layout.addWidget(self.start_task_checker_button)
        actions_layout.addWidget(self.stop_task_checker_button)
        actions_layout.addWidget(self.theme_toggle_button)
        
        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)
        
        # Add groups input section
        groups_group = QGroupBox("Add Groups")
        groups_layout = QVBoxLayout()
        
        self.groups_input = QTextEdit()
        self.groups_input.setPlaceholderText("Enter Telegram group/channel links (one per line)")
        self.currently_analyzing_label = QLabel("Currently analyzing: None")
        
        groups_layout.addWidget(self.groups_input)
        groups_layout.addWidget(self.currently_analyzing_label)
        
        groups_group.setLayout(groups_layout)
        layout.addWidget(groups_group)
        
        # Add results section
        results_group = QGroupBox("Results")
        results_layout = QFormLayout()
        
        self.valid_filtered_count = QLabel("0")
        self.valid_only_count = QLabel("0")
        self.topics_count = QLabel("0")
        self.channels_count = QLabel("0")
        self.invalid_count = QLabel("0")
        self.account_issues_count = QLabel("0")
        self.progress_count = QLabel("0 / 0")
        
        results_layout.addRow("Groups Checked:", self.progress_count)
        results_layout.addRow("Groups Valid & Filter ON:", self.valid_filtered_count)
        results_layout.addRow("Groups Valid Only:", self.valid_only_count)
        results_layout.addRow("Topics Groups Only Valid:", self.topics_count)
        results_layout.addRow("Channels Only Valid:", self.channels_count)
        results_layout.addRow("Invalid Groups/Channels:", self.invalid_count)
        results_layout.addRow("Account Issues:", self.account_issues_count)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        # Add recent activities log
        activities_group = QGroupBox("Recent Activities")
        activities_layout = QVBoxLayout()
        
        self.activities_text = QTextEdit()
        self.activities_text.setReadOnly(True)
        
        activities_layout.addWidget(self.activities_text)
        activities_group.setLayout(activities_layout)
        layout.addWidget(activities_group)
        
        return tab
    
    def create_accounts_tab(self):
        """Create the accounts tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add account table
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(7)
        self.accounts_table.setHorizontalHeaderLabels(["Phone", "Name / Username", "Status", "Last Check", "Errors", "Group Limit", "Actions"])
        self.accounts_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(self.accounts_table)
        
        # Add account actions
        actions_layout = QHBoxLayout()
        
        self.add_account_button = QPushButton("Add Account")
        self.remove_account_button = QPushButton("Remove Account")
        self.fix_account_button = QPushButton("Fix Selected")
        self.refresh_info_button = QPushButton("Refresh Account Info")
        self.check_ages_button = QPushButton("Check Ages")
        self.activate_all_button = QPushButton("Activate All")
        self.deactivate_all_button = QPushButton("Deactivate All")
        
        self.add_account_button.clicked.connect(self.add_account)
        self.remove_account_button.clicked.connect(self.remove_account)
        self.fix_account_button.clicked.connect(self.fix_account)
        self.refresh_info_button.clicked.connect(self.refresh_all_accounts_info)
        self.activate_all_button.clicked.connect(self.activate_all_accounts)
        self.deactivate_all_button.clicked.connect(self.deactivate_all_accounts)
        
        actions_layout.addWidget(self.add_account_button)
        actions_layout.addWidget(self.remove_account_button)
        actions_layout.addWidget(self.fix_account_button)
        actions_layout.addWidget(self.refresh_info_button)
        actions_layout.addWidget(self.activate_all_button)
        actions_layout.addWidget(self.deactivate_all_button)
        layout.addLayout(actions_layout)
        
        return tab
    
    def create_monitor_tab(self):
        """Create the monitor tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add monitor settings
        settings_group = QGroupBox("Monitor Settings")
        settings_layout = QFormLayout()
        
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 60)
        self.check_interval_spin.setValue(5)
        self.check_interval_spin.setSuffix(" minutes")
        
        self.auto_fix_check = QCheckBox()
        self.auto_fix_check.setChecked(True)
        
        settings_layout.addRow("Check Interval:", self.check_interval_spin)
        settings_layout.addRow("Auto-Fix Issues:", self.auto_fix_check)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Add monitor status
        status_group = QGroupBox("Monitor Status")
        status_layout = QVBoxLayout()
        
        self.monitor_log = QTextEdit()
        self.monitor_log.setReadOnly(True)
        
        status_layout.addWidget(self.monitor_log)
        
        # Add apply settings button
        apply_button = QPushButton("Apply Monitor Settings")
        apply_button.clicked.connect(self.apply_monitor_settings)
        status_layout.addWidget(apply_button)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        return tab
    
    def create_logs_tab(self):
        """Create the logs tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add log filter controls
        filter_layout = QHBoxLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["All", "Info", "Warning", "Error", "Critical"])
        
        self.log_type_combo = QComboBox()
        self.log_type_combo.addItems(["General", "Authentication", "Usage Checker"])
        self.log_type_combo.currentIndexChanged.connect(self.update_log_display)
        
        self.export_logs_button = QPushButton("Export Logs")
        self.export_logs_button.clicked.connect(self.export_logs)
        
        filter_layout.addWidget(QLabel("Log Type:"))
        filter_layout.addWidget(self.log_type_combo)
        filter_layout.addWidget(QLabel("Filter Level:"))
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addStretch()
        filter_layout.addWidget(self.export_logs_button)
        
        layout.addLayout(filter_layout)
        
        # Add log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # Set up a timer to refresh logs
        self.log_refresh_timer = QTimer(self)
        self.log_refresh_timer.timeout.connect(self.update_log_display)
        self.log_refresh_timer.start(2000)  # Refresh every 2 seconds
        
        return tab
    
    def create_settings_tab(self):
        """Create the settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # General settings
        general_group = QGroupBox("General Settings")
        general_layout = QFormLayout()
        
        self.auto_start_check = QCheckBox()
        self.dark_mode_check = QCheckBox()
        self.dark_mode_check.stateChanged.connect(self.on_theme_changed)
        
        general_layout.addRow("Auto-Start Monitor:", self.auto_start_check)
        general_layout.addRow("Dark Mode:", self.dark_mode_check)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        # Filter settings
        filter_group = QGroupBox("Filter Settings")
        filter_layout = QFormLayout()
        
        self.min_members_spin = QSpinBox()
        self.min_members_spin.setRange(0, 1000000)
        self.min_members_spin.setValue(500)
        self.min_members_spin.setSingleStep(100)
        
        self.min_message_time_spin = QSpinBox()
        self.min_message_time_spin.setRange(0, 168)  # Up to 1 week (168 hours)
        self.min_message_time_spin.setValue(1)
        self.min_message_time_spin.setSuffix(" hours")
        
        self.min_total_messages_spin = QSpinBox()
        self.min_total_messages_spin.setRange(0, 100000)
        self.min_total_messages_spin.setValue(100)
        self.min_total_messages_spin.setSingleStep(10)
        
        filter_layout.addRow("Min Members:", self.min_members_spin)
        filter_layout.addRow("Min Chat Message Time:", self.min_message_time_spin)
        filter_layout.addRow("Min Total Messages:", self.min_total_messages_spin)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # Save settings button
        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_settings_button)
        
        return tab
    
    def start_background_processes(self):
        """Start background processes like monitoring and auto-sync."""
        # Start monitor if auto-start is enabled
        if self.settings.value("auto_start_monitor", False, type=bool):
            self.start_monitor()
        
        # Set up auto-sync timer
        self.sync_timer = QTimer(self)
        self.sync_timer.timeout.connect(self.sync_accounts)
        self.sync_timer.start(300000)  # Sync every 5 minutes
        
        # Initial sync
        self.sync_accounts()
        
        # Auto-refresh account info for accounts missing name/username
        QTimer.singleShot(3000, self.auto_refresh_missing_account_info)  # Wait 3 seconds after startup
        
        # Update UI
        self.update_ui_timer = QTimer(self)
        self.update_ui_timer.timeout.connect(self.update_ui)
        self.update_ui_timer.start(1000)  # Update every second
    
    def auto_refresh_missing_account_info(self):
        """Automatically refresh account info for accounts missing name/username."""
        try:
            accounts = self.account_manager.get_accounts()
            accounts_needing_refresh = []
            
            for account in accounts:
                if account is None:
                    continue
                    
                name = account.get("name", "")
                username = account.get("username", "")
                
                # Safe string operations
                name = name.strip() if name else ""
                username = username.strip() if username else ""
                
                # Check if account info is missing
                if not name and not username:
                    phone = account.get("phone")
                    if phone else 0:
                        accounts_needing_refresh.append(phone)
            
            if accounts_needing_refresh:
                self.log_activity(f"Found {len(accounts_needing_refresh)} accounts missing info. Auto-refreshing...")
                # Start background refresh for these accounts
                threading.Thread(target=self._auto_refresh_accounts_thread, args=(accounts_needing_refresh,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh: {str(e)}")
    
    def _auto_refresh_accounts_thread(self, phone_list):
        """Background thread for auto-refreshing missing account info."""
        try:
            updated_count = 0
            
            for phone in phone_list:
                try:
                    if self.refresh_account_info(phone):
                        updated_count += 1
                    time.sleep(1)  # Small delay between requests
                except Exception as e:
                    self.logger.error(f"Error refreshing {phone}: {str(e)}")
                    continue
            
            if updated_count > 0:
                # Update UI on main thread using signal
                self.update_ui_signal.emit()
                self.log_activity_signal.emit(f"Auto-refresh completed: {updated_count} accounts updated")
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh thread: {str(e)}")
    
    def sync_accounts(self):
        """Synchronize accounts with the backend."""
        self.update_status_signal.emit("Syncing accounts...")
        self.log_activity_signal.emit("Syncing accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_accounts_thread, daemon=True).start()
    
    def _sync_accounts_thread(self):
        """Background thread for account synchronization."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Check for accounts needing age verification
            accounts_needing_check = self.account_manager.get_accounts_needing_age_check()
            
            if accounts_needing_check:
                log_usage_checker(self.logger, f"Found {len(accounts_needing_check)} accounts needing age check during sync")
                self.update_status_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                self.log_activity_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                
                # Run age check in a separate thread to avoid blocking
                age_check_thread = threading.Thread(
                    target=self._age_check_background_thread, 
                    args=(accounts_needing_check,), 
                    daemon=True
                )
                age_check_thread.start()
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Account sync completed")
            self.log_activity_signal.emit("Account sync completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync error: {str(e)}")
            self.logger.error(f"Account sync error: {str(e)}")
            self.log_activity_signal.emit(f"Account sync failed: {str(e)}")
    
    def _age_check_background_thread(self, accounts_needing_check):
        """Background thread specifically for age checking to avoid blocking sync."""
        try:
            import asyncio
            
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(self.account_manager.auto_check_account_ages())
                
                success_count = sum(1 for _, success, _ in results if success)
                log_usage_checker(self.logger, f"Age check completed: {success_count}/{len(results)} successful")
                self.log_activity_signal.emit(f"Age check completed: {success_count}/{len(results)} successful")
                
                # Update UI after age check completes
                self.update_ui_signal.emit()
                
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"Error in age check background thread: {str(e)}"
            self.logger.error(error_msg)
            log_usage_checker(self.logger, error_msg, logging.ERROR)
            self.log_activity_signal.emit(f"Age check failed: {str(e)}")
    
    def toggle_monitor(self):
        """Toggle the monitor on/off."""
        if self.monitor.is_running:
            self.stop_monitor()
        else:
            self.start_monitor()
    
    def start_monitor(self):
        """Start the monitor."""
        try:
            interval = self.check_interval_spin.value() * 60  # Convert to seconds
            self.monitor.start(interval)
            
            self.toggle_monitor_button.setText("Stop Monitor")
            self.monitor_status_label.setText("Running")
            self.log_activity("Monitor started")
            self.status_label.setText("Monitor is running")
            
        except Exception as e:
            self.logger.error(f"Failed to start monitor: {str(e)}")
            self.log_activity(f"Failed to start monitor: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start monitor: {str(e)}")
    
    def stop_monitor(self):
        """Stop the monitor."""
        try:
            self.monitor.stop()
            
            self.toggle_monitor_button.setText("Start Monitor")
            self.monitor_status_label.setText("Stopped")
            self.log_activity("Monitor stopped")
            self.status_label.setText("Monitor is stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop monitor: {str(e)}")
            self.log_activity(f"Failed to stop monitor: {str(e)}")
    
    def sync_and_monitor(self):
        """Sync and monitor accounts."""
        self.update_status_signal.emit("Syncing and monitoring accounts...")
        self.log_activity_signal.emit("Syncing and monitoring accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_and_monitor_thread, daemon=True).start()
    
    def _sync_and_monitor_thread(self):
        """Background thread for syncing and monitoring accounts."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Start monitor (this needs to be called from main thread)
            QMetaObject.invokeMethod(self, "start_monitor", Qt.QueuedConnection)
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Sync and monitor completed")
            self.log_activity_signal.emit("Sync and monitor completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync and monitor error: {str(e)}")
            self.logger.error(f"Sync and monitor error: {str(e)}")
            self.log_activity_signal.emit(f"Sync and monitor failed: {str(e)}")
    
    def add_account(self):
        """Add a new account with a simplified, reliable approach."""
        # Create a custom dialog class with proper slots
        class AccountDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Add Account")
                self.setMinimumWidth(400)
                self.layout = QVBoxLayout(self)
                
                # Main form layout
                self.form_layout = QFormLayout()
                
                # Phone number input
                self.phone_input = QLineEdit()
                self.phone_input.setPlaceholderText("e.g. +*********")
                
                # API credentials
                self.api_id_input = QLineEdit()
                self.api_hash_input = QLineEdit()
                
                # Add basic inputs to form layout
                self.form_layout.addRow("Phone Number:", self.phone_input)
                self.form_layout.addRow("API ID:", self.api_id_input)
                self.form_layout.addRow("API Hash:", self.api_hash_input)
                
                # Create all authentication-related fields
                self.code_input = QLineEdit()
                self.code_input.setPlaceholderText("Verification code")
                self.code_input.setEnabled(False)
                # Connect textChanged signal to enable continue button when code is entered
                self.code_input.textChanged.connect(self.on_code_text_changed)
                
                self.twofa_input = QLineEdit()
                self.twofa_input.setEchoMode(QLineEdit.Password)
                self.twofa_input.setPlaceholderText("2FA Password")
                self.twofa_input.setEnabled(False)
                # Connect textChanged signal to enable continue button when 2FA password is entered
                self.twofa_input.textChanged.connect(self.on_2fa_text_changed)
                
                # Add the basic form to the layout
                self.layout.addLayout(self.form_layout)
                
                # Status and progress indication
                self.status_label = QLabel("Enter your Telegram account details")
                self.layout.addWidget(self.status_label)
                
                # Add a container for additional form fields (code/2FA) to be added later
                self.auth_container = QWidget()
                self.auth_layout = QFormLayout(self.auth_container)
                self.auth_container.setVisible(False)
                self.layout.addWidget(self.auth_container)
                
                # Add authentication log display
                self.auth_log_label = QLabel("Authentication Logs:")
                self.auth_log_display = QTextEdit()
                self.auth_log_display.setReadOnly(True)
                self.auth_log_display.setMaximumHeight(150)
                self.layout.addWidget(self.auth_log_label)
                self.layout.addWidget(self.auth_log_display)
                
                # Buttons
                self.button_layout = QHBoxLayout()
                
                self.connect_button = QPushButton("Connect")
                self.continue_button = QPushButton("Continue")
                self.continue_button.setVisible(False)
                self.cancel_button = QPushButton("Cancel")
                
                self.button_layout.addWidget(self.connect_button)
                self.button_layout.addWidget(self.continue_button)
                self.button_layout.addWidget(self.cancel_button)
                
                self.layout.addLayout(self.button_layout)
                
                # Connect button signals
                self.connect_button.clicked.connect(self.on_connect_clicked)
                self.continue_button.clicked.connect(self.on_continue_clicked)
                self.cancel_button.clicked.connect(self.reject)
                
                # Initialize variables
                self.client = None
                self.phone_code_hash = None
                self.authentication_stage = "initial"  # Can be "initial", "code", "2fa", "complete"
                
                # Load and display initial auth logs
                self.update_auth_log_display()
                
                # Start auth log update timer
                self.log_timer = QTimer(self)
                self.log_timer.timeout.connect(self.update_auth_log_display)
                self.log_timer.start(1000)  # Update logs every second
            
            # Add the update_verification_status method to the AccountDialog class
            def update_verification_status(self, message):
                """Update the status label with verification progress information."""
                try:
                    QMetaObject.invokeMethod(
                        self.status_label,
                        "setText",
                        Qt.QueuedConnection,
                        Q_ARG(str, message)
                    )
                except Exception as e:
                    # Fall back to direct update if invokeMethod fails
                    try:
                        self.status_label.setText(message)
                    except Exception as e2:
                        log_auth(self_outer.logger, f"Failed to update verification status: {str(e)} -> {str(e2)}", logging.ERROR)
            
            def update_auth_log_display(self):
                """Update the authentication log display with recent logs"""
                try:
                    auth_logs = read_auth_log(max_lines=20)
                    self.auth_log_display.clear()
                    for log in auth_logs:
                        self.auth_log_display.append(log.strip())
                    
                    # Scroll to bottom
                    scroll_bar = self.auth_log_display.verticalScrollBar()
                    scroll_bar.setValue(scroll_bar.maximum())
                except Exception as e:
                    print(f"Error updating auth log display: {str(e)}")
            
            def on_code_text_changed(self, text):
                """Enable continue button when code text is entered"""
                if self.authentication_stage == "code":
                    self.continue_button.setEnabled(len(text.strip()) > 0)
            
            def on_2fa_text_changed(self, text):
                """Enable continue button when 2FA password is entered"""
                if self.authentication_stage == "2fa":
                    self.continue_button.setEnabled(len(text.strip()) > 0)
            
            # Proper PyQt slots that can be invoked from other threads
            from PyQt5.QtCore import pyqtSlot
            
            @pyqtSlot()
            def show_code_input(self):
                # Clear the auth container
                while self.auth_layout.count():
                    item = self.auth_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                
                # Add code input to the auth container
                self.auth_layout.addRow("Verification Code:", self.code_input)
                
                # Update state
                self.authentication_stage = "code"
                
                # Enable and show the relevant UI elements
                self.auth_container.setVisible(True)
                self.code_input.setEnabled(True)
                self.code_input.setFocus()
                self.code_input.clear()  # Clear any previous code
                
                self.connect_button.setVisible(False)
                self.continue_button.setVisible(True)
                # Initially disabled until code is entered
                self.continue_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Enter the verification code sent to your Telegram app")
                
                # Log the event
                phone = self.phone_input.text().strip()
                log_auth(self_outer.logger, f"Verification code requested for account {phone}")
            
            @pyqtSlot()
            def show_2fa_input(self):
                # Clear the auth container
                while self.auth_layout.count():
                    item = self.auth_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                
                # Add 2FA input to the auth container
                self.auth_layout.addRow("2FA Password:", self.twofa_input)
                
                # Update state
                self.authentication_stage = "2fa"
                
                # Enable and show the relevant UI elements
                self.auth_container.setVisible(True)
                self.twofa_input.setEnabled(True)
                self.twofa_input.setFocus()
                self.twofa_input.clear()  # Clear any previous password
                
                self.connect_button.setVisible(False)
                self.continue_button.setVisible(True)
                # Initially disabled until 2FA password is entered
                self.continue_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Two-factor authentication required. Please enter your 2FA password.")
                
                # Log the event
                phone = self.phone_input.text().strip()
                log_auth(self_outer.logger, f"2FA password required for account {phone}")
            
            def on_connect_clicked(self):
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                # Validate inputs
                if not phone or not api_id or not api_hash:
                    self.status_label.setText("Please fill all required fields")
                    return
                
                # Log the connection attempt
                log_auth(self_outer.logger, f"Connect button clicked for account {phone}")
                
                # Disable inputs during connection
                self.phone_input.setEnabled(False)
                self.api_id_input.setEnabled(False)
                self.api_hash_input.setEnabled(False)
                self.connect_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Connecting to Telegram...")
                
                # Process in the background
                threading.Thread(target=self.connect_thread, daemon=True).start()
            
            def connect_thread(self):
                try:
                    # Get account details
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    
                    # Log the attempt
                    log_auth(self_outer.logger, f"Connecting to Telegram for account {phone}")
                    
                    # Create client
                    self.client = TelegramClient(api_id, api_hash, phone)
                    
                    # Request confirmation code (this step includes the connection)
                    log_auth(self_outer.logger, f"Sending code request for account {phone}")
                    result = self.client.send_code_request()
                    
                    # Check result
                    if result.get("success", False):
                        # Store the phone code hash
                        self.phone_code_hash = result.get("phone_code_hash")
                        
                        # Log success
                        log_auth(self_outer.logger, f"Code request successful for account {phone}")
                        
                        # Update UI to show code input
                        QMetaObject.invokeMethod(self, "show_code_input", Qt.QueuedConnection)
                    elif result.get("requires_2fa", False):
                        # 2FA detected during code request
                        log_auth(self_outer.logger, f"2FA detected immediately for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                    elif result.get("already_authorized", False):
                        # Account is already authorized
                        log_auth(self_outer.logger, f"Account {phone} is already authorized")
                        # Get user info and add account
                        user_info = {"user": {"first_name": "Unknown", "last_name": "", "username": ""}}
                        QMetaObject.invokeMethod(
                            self, "on_login_success", 
                            Qt.QueuedConnection,
                            Q_ARG(dict, user_info)
                        )
                    else:
                        # Handle error
                        error_msg = result.get("error", "Unknown error")
                        log_auth(self_outer.logger, f"Code request failed for account {phone}: {error_msg}", logging.ERROR)
                        
                        # Update UI with specific messages for common errors
                        if "timed out" in error_msg.lower() or "timeout" in error_msg.lower():
                            error_display = "Connection to Telegram timed out. Please check your internet connection and try again."
                        elif "flood" in error_msg.lower() or "wait" in error_msg.lower():
                            error_display = error_msg  # Keep the original message as it contains time information
                        elif "invalid" in error_msg.lower():
                            error_display = "Invalid API credentials. Please check your API ID and API Hash."
                        else:
                            error_display = f"Error: {error_msg}"
                            
                        QMetaObject.invokeMethod(
                            self.status_label, 
                            "setText", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_display)
                        )
                        
                        # Re-enable inputs
                        self._enable_inputs()
                        
                except Exception as e:
                    # Log the error
                    phone = self.phone_input.text().strip()
                    log_auth(self_outer.logger, f"Connection error for account {phone}: {str(e)}", logging.ERROR)
                    
                    # Check for 2FA in error
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        log_auth(self_outer.logger, f"2FA detected from exception for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                    else:
                        # Update UI with error - specific message for timeout
                        if "timeout" in error_msg or "timed out" in error_msg:
                            error_display = "Connection to Telegram timed out. Please check your internet connection and try again."
                        elif "invalid" in error_msg:
                            error_display = "Invalid API credentials. Please check your API ID and API Hash."
                        else:
                            error_display = f"Error: {str(e)}"
                            
                        QMetaObject.invokeMethod(
                            self.status_label, 
                            "setText", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_display)
                        )
                        
                        # Re-enable inputs
                        self._enable_inputs()
            
            def _enable_inputs(self):
                """Re-enable input fields after an error."""
                QMetaObject.invokeMethod(self.phone_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_id_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_hash_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.connect_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_continue_clicked(self):
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                if self.authentication_stage == "code":
                    # Get code input
                    code = self.code_input.text().strip()
                    
                    if not code:
                        self.status_label.setText("Please enter the verification code")
                        return
                    
                    # Log code submission
                    log_auth(self_outer.logger, f"Verification code submitted for account {phone}")
                    
                    # Disable inputs during verification
                    self.code_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying code...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_code_thread, args=(code,), daemon=True).start()
                    
                elif self.authentication_stage == "2fa":
                    # Get 2FA password
                    password = self.twofa_input.text().strip()
                    
                    if not password:
                        self.status_label.setText("Please enter your 2FA password")
                        return
                    
                    # Log 2FA password submission
                    log_auth(self_outer.logger, f"2FA password submitted for account {phone}")
                    
                    # Disable inputs
                    self.twofa_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying 2FA password...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_2fa_thread, args=(password,), daemon=True).start()
            
            def verify_code_thread(self, code):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    session_file = f"sessions/{phone}"
                    
                    # Create and start login worker
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, code, 
                        session_file=session_file, 
                        phone_code_hash=self.phone_code_hash, 
                        logger=self_outer.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_2fa_required.connect(self.on_2fa_required)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self_outer.logger, f"Error in verify_code_thread: {str(e)}", logging.ERROR)
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"Verification error: {str(e)}")
                    )
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.code_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def verify_2fa_thread(self, password):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    session_file = f"sessions/{phone}"
                    
                    # Create and start login worker for 2FA
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, None, 
                        password=password,
                        phone_code_hash=self.phone_code_hash, 
                        session_file=session_file, 
                        logger=self_outer.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self_outer.logger, f"Error in verify_2fa_thread: {str(e)}", logging.ERROR)
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"2FA verification error: {str(e)}")
                    )
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.twofa_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_login_success(self, user_info):
                try:
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    user = user_info.get('user')
                    
                    # Extract user information safely with better handling
                    name = ""
                    username = ""
                    if user:
                        # Handle both Telethon User object and dict
                        if hasattr(user, 'first_name'):  # Telethon User object
                            first_name = getattr(user, 'first_name', '') or ''
                            last_name = getattr(user, 'last_name', '') or ''
                            username = getattr(user, 'username', '') or ''
                        elif isinstance(user, dict):  # Dict format
                            first_name = user.get('first_name', '') or ''
                            last_name = user.get('last_name', '') or ''
                            username = user.get('username', '') or ''
                        else:
                            first_name = str(user.get('first_name', '')) if hasattr(user, 'get') else ''
                            last_name = str(user.get('last_name', '')) if hasattr(user, 'get') else ''
                            username = str(user.get('username', '')) if hasattr(user, 'get') else ''
                        
                        name = f"{first_name} {last_name}".strip()
                    
                    # Add account to database
                    success = self_outer.account_manager.add_account(phone, api_id, api_hash)
                    if success:
                        # Update account info with user details
                        if name or username:
                            self_outer.account_manager.update_account_info(phone, name=name, username=username)
                            log_auth(self_outer.logger, f"Successfully added account {phone} with info: {name} / @{username}")
                        else:
                            # Try to get user info separately if not available
                            try:
                                client = TelegramClient(api_id, api_hash, phone)
                                account_info = client.get_account_info()
                                if account_info:
                                    name = account_info.get("full_name", "").strip()
                                    username = account_info.get("username", "").strip()
                                    if name or username:
                                        self_outer.account_manager.update_account_info(phone, name=name, username=username)
                                        log_auth(self_outer.logger, f"Retrieved and updated account info for {phone}: {name} / @{username}")
                            except Exception as info_e:
                                log_auth(self_outer.logger, f"Could not retrieve additional account info for {phone}: {str(info_e)}", logging.WARNING)
                            
                            log_auth(self_outer.logger, f"Successfully added account {phone}")
                        
                        # Start age check in background for new account
                        log_usage_checker(self_outer.logger, f"Starting automatic age check for new account: {phone}")
                        threading.Thread(target=self._auto_check_new_account_age, args=(phone,), daemon=True).start()
                        
                        self_outer.log_activity(f"Added account: {phone}")
                        self_outer.update_ui()
                        
                        # Update status and close dialog
                        self.update_verification_status("Login successful! Account added.")
                        QMetaObject.invokeMethod(self, "accept", Qt.QueuedConnection)
                    else:
                        log_auth(self_outer.logger, f"Failed to add account {phone} to database", logging.ERROR)
                        self.update_verification_status("Failed to add account to database.")
                        
                except Exception as e:
                    log_auth(self_outer.logger, f"Error in on_login_success: {str(e)}", logging.ERROR)
                    self.update_verification_status(f"Error processing login: {str(e)}")

            def on_2fa_required(self):
                QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)

            def on_login_error(self, error_msg):
                log_auth(self_outer.logger, f"Login error: {error_msg}", logging.ERROR)
                self.update_verification_status(f"Error: {error_msg}")
                
                # Re-enable the appropriate input field based on current stage
                if self.authentication_stage == "code":
                    QMetaObject.invokeMethod(self.code_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                elif self.authentication_stage == "2fa":
                    QMetaObject.invokeMethod(self.twofa_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            
            def closeEvent(self, event):
                """Handle dialog close event"""
                # Stop the log update timer
                if hasattr(self, 'log_timer'):
                    self.log_timer.stop()
                event.accept()
        
        # Store a reference to the outer self
        self_outer = self
        
        # Create and show the dialog
        dialog = AccountDialog(self)
        
        # Show the dialog and wait for user interaction
        if dialog.exec_() == QDialog.Accepted:
            # Dialog was accepted, account added successfully
            return True
        else:
            # Dialog was rejected or closed
            return False
    
    def activate_all_accounts(self):
        """Activate all accounts."""
        self.update_status_signal.emit("Activating all accounts...")
        self.log_activity_signal.emit("Activating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._activate_all_accounts_thread, daemon=True).start()
    
    def _activate_all_accounts_thread(self):
        """Background thread for activating all accounts."""
        try:
            self.account_manager.activate_all()
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit("All accounts activated")
            self.log_activity_signal.emit("All accounts have been activated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Activation error: {str(e)}")
            self.logger.error(f"Account activation error: {str(e)}")
            self.log_activity_signal.emit(f"Account activation failed: {str(e)}")
    
    def deactivate_all_accounts(self):
        """Deactivate all accounts."""
        self.update_status_signal.emit("Deactivating all accounts...")
        self.log_activity_signal.emit("Deactivating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._deactivate_all_accounts_thread, daemon=True).start()
    
    def _deactivate_all_accounts_thread(self):
        """Background thread for deactivating all accounts."""
        try:
            self.account_manager.deactivate_all()
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit("All accounts deactivated")
            self.log_activity_signal.emit("All accounts have been deactivated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Deactivation error: {str(e)}")
            self.logger.error(f"Account deactivation error: {str(e)}")
            self.log_activity_signal.emit(f"Account deactivation failed: {str(e)}")
    
    def remove_account(self):
        """Remove the selected account."""
        selected_rows = self.accounts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select an account to remove")
            return
        
        # Get phone number from the first column of the selected row
        phone = self.accounts_table.item(selected_rows[0].row(), 0).text()
        
        # Confirm before removing
        reply = QMessageBox.question(
            self, 
            "Confirm Remove", 
            f"Are you sure you want to remove account {phone}?",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.account_manager.remove_account(phone)
            
            if success:
                self.log_activity(f"Removed account: {phone}")
                self.update_ui()
            else:
                QMessageBox.critical(self, "Error", f"Failed to remove account: {message}")
    
    def fix_account(self):
        """Fix the selected account."""
        selected_rows = self.accounts_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select an account to fix")
            return
        
        # Get phone number from the first column of the selected row
        phone = self.accounts_table.item(selected_rows[0].row(), 0).text()
        
        self.update_status_signal.emit(f"Fixing account {phone}...")
        self.log_activity_signal.emit(f"Fixing account {phone}...")
        
        # Run in background thread
        threading.Thread(target=self._fix_account_thread, args=(phone,), daemon=True).start()
    
    def fix_specific_account(self, account):
        """Fix a specific account."""
        try:
            phone = account.get("phone", "")
            if not else 0 phone:
                return
                
            self.update_status_signal.emit(f"Fixing account {phone}...")
            self.log_activity_signal.emit(f"Fixing account {phone}...")
            
            # Run in background thread
            threading.Thread(target=self._fix_account_thread, args=(phone,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Failed to fix account {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to fix account: {str(e)}")
    
    def _fix_account_thread(self, phone):
        """Background thread for fixing an account."""
        try:
            # Log the start of fixing
            log_auth(self.logger, f"Starting account fix process for {phone}")
            
            # First, get the account to see its current status
            accounts = self.account_manager.get_accounts()
            account = None
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.update_status_signal.emit(f"Account {phone} not found")
                self.log_activity_signal.emit(f"Account {phone} not found")
                log_auth(self.logger, f"Account fix failed: Account {phone} not found", logging.ERROR)
                return
            
            # Log account details
            log_auth(self.logger, f"Fixing account {phone} (API ID: {account.get('api_id')}, Status: {account.get('active', False)}, Errors: {account.get('errors', 0)})")
            
            # Try to create a client and connect
            log_auth(self.logger, f"Creating TelegramClient for account {phone}")
            client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
            
            log_auth(self.logger, f"Attempting to get account info for {phone}")
            account_info = client.get_account_info()
            
            if account_info:
                # Update account info if needed
                if not account.get("name") or not account.get("username"):
                    name = account_info.get("full_name", "")
                    username = account_info.get("username", "")
                    self.account_manager.update_account_info(phone, name=name, username=username)
                    log_auth(self.logger, f"Updated account info for {phone}: {name} / @{username}")
                
                # Reset error status
                self.account_manager.update_check_time(phone, "OK")
                log_auth(self.logger, f"Account {phone} fixed successfully", logging.INFO)
                
                # Update UI using signal
                self.update_ui_signal.emit()
                
                self.update_status_signal.emit(f"Account {phone} fixed")
                self.log_activity_signal.emit(f"Account {phone} has been fixed")
            else:
                log_auth(self.logger, f"Failed to connect to account {phone}", logging.ERROR)
                self.update_status_signal.emit(f"Failed to connect to account {phone}")
                self.log_activity_signal.emit(f"Failed to connect to account {phone}")
            
        except Exception as e:
            log_auth(self.logger, f"Error during account fix for {phone}: {str(e)}", logging.ERROR)
            self.update_status_signal.emit(f"Fix error: {str(e)}")
            self.logger.error(f"Account fix error for {phone}: {str(e)}")
            self.log_activity_signal.emit(f"Account fix failed for {phone}: {str(e)}")
    
    def export_logs(self):
        """Export logs to a file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Logs", "", "Log Files (*.log);;Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, "w") as f:
                    f.write(self.log_display.toPlainText())
                self.update_status_signal.emit(f"Logs exported to {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to export logs: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to export logs: {str(e)}")
    
    def save_settings(self):
        """Save settings to the configuration file."""
        try:
            # Save general settings
            self.settings.setValue("auto_start_monitor", self.auto_start_check.isChecked())
            self.settings.setValue("dark_mode", self.dark_mode_check.isChecked())
            
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # Save filter settings
            self.settings.setValue("min_members", self.min_members_spin.value())
            self.settings.setValue("min_message_time", self.min_message_time_spin.value())
            self.settings.setValue("min_total_messages", self.min_total_messages_spin.value())
            
            self.update_status_signal.emit("Settings saved")
            self.log_activity_signal.emit("Settings saved")
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
            else:
                self.set_light_mode()
                
        except Exception as e:
            self.logger.error(f"Failed to save settings: {str(e)}")
            self.log_activity_signal.emit(f"Failed to save settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save settings: {str(e)}")
    
    def load_settings(self):
        """Load settings from the configuration file."""
        try:
            # Load general settings
            self.auto_start_check.setChecked(self.settings.value("auto_start_monitor", False, type=bool))
            self.dark_mode_check.setChecked(self.settings.value("dark_mode", False, type=bool))
            
            # Load monitor settings
            self.check_interval_spin.setValue(self.settings.value("check_interval", 5, type=int))
            self.auto_fix_check.setChecked(self.settings.value("auto_fix", True, type=bool))
            
            # Load filter settings
            self.min_members_spin.setValue(self.settings.value("min_members", 500, type=int))
            self.min_message_time_spin.setValue(self.settings.value("min_message_time", 1, type=int))
            self.min_total_messages_spin.setValue(self.settings.value("min_total_messages", 100, type=int))
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("☀️ Light Theme")
            else:
                self.set_light_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("🌙 Dark Theme")
                
        except Exception as e:
            self.logger.error(f"Failed to load settings: {str(e)}")
    
    def set_dark_mode(self):
        """Set the application to dark mode."""
        dark_style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #404040;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            background-color: #353535;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        
        QPushButton {
            background-color: #0078d4;
            border: 1px solid #005a9e;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #555555;
            border: 1px solid #777777;
            color: #999999;
        }
        
        /* Special button styles */
        QPushButton[class="success"] {
            background-color: #107c10;
            border: 1px solid #0c5a0c;
        }
        
        QPushButton[class="success"]:hover {
            background-color: #118a11;
        }
        
        QPushButton[class="warning"] {
            background-color: #ff8c00;
            border: 1px solid #cc7000;
        }
        
        QPushButton[class="warning"]:hover {
            background-color: #ff9a1a;
        }
        
        QPushButton[class="danger"] {
            background-color: #d13438;
            border: 1px solid #a12327;
        }
        
        QPushButton[class="danger"]:hover {
            background-color: #dc4649;
        }
        
        QLineEdit, QTextEdit, QSpinBox {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus {
            border: 2px solid #0078d4;
        }
        
        QComboBox {
            background-color: #404040;
            border: 1px solid #555555;
            color: #ffffff;
            padding: 5px;
            border-radius: 3px;
        }
        
        QComboBox::drop-down {
            border: none;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #ffffff;
        }
        
        QComboBox QAbstractItemView {
            background-color: #404040;
            border: 1px solid #555555;
            selection-background-color: #0078d4;
            color: #ffffff;
        }
        
        QTableWidget {
            background-color: #353535;
            gridline-color: #555555;
            color: #ffffff;
            border: 1px solid #555555;
        }
        
        QTableWidget::item {
            border-bottom: 1px solid #555555;
            padding: 5px;
        }
        
        QTableWidget::item:selected {
            background-color: #0078d4;
        }
        
        QHeaderView::section {
            background-color: #404040;
            color: #ffffff;
            padding: 8px;
            border: 1px solid #555555;
            font-weight: bold;
        }
        
        QCheckBox {
            color: #ffffff;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #555555;
            border-radius: 3px;
            background-color: #404040;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #005a9e;
        }
        
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: #ffffff;
            font-weight: bold;
        }
        
        QProgressBar {
            border: 1px solid #555555;
            border-radius: 3px;
            background-color: #404040;
            text-align: center;
            color: #ffffff;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QStatusBar {
            background-color: #353535;
            color: #ffffff;
            border-top: 1px solid #555555;
        }
        
        QScrollBar:vertical {
            background-color: #404040;
            width: 15px;
            border: 1px solid #555555;
        }
        
        QScrollBar::handle:vertical {
            background-color: #0078d4;
            border-radius: 3px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #106ebe;
        }
        
        QScrollBar:horizontal {
            background-color: #404040;
            height: 15px;
            border: 1px solid #555555;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #0078d4;
            border-radius: 3px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #106ebe;
        }
        
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QFormLayout QLabel {
            color: #ffffff;
        }
        """
        
        self.setStyleSheet(dark_style)
        self.log_activity("Dark theme applied")
    
    def set_light_mode(self):
        """Set the application to light mode."""
        light_style = """
        QMainWindow {
            background-color: #ffffff;
            color: #000000;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #f0f0f0;
            color: #000000;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
            color: #ffffff;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #e6e6e6;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            background-color: #f9f9f9;
            color: #000000;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #000000;
        }
        
        QLabel {
            color: #000000;
            background-color: transparent;
        }
        
        QPushButton {
            background-color: #0078d4;
            border: 1px solid #005a9e;
            color: #ffffff;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        
        QPushButton:hover {
            background-color: #106ebe;
        }
        
        QPushButton:pressed {
            background-color: #005a9e;
        }
        
        QPushButton:disabled {
            background-color: #cccccc;
            border: 1px solid #999999;
            color: #666666;
        }
        
        /* Special button styles */
        QPushButton[class="success"] {
            background-color: #107c10;
            border: 1px solid #0c5a0c;
            color: #ffffff;
        }
        
        QPushButton[class="success"]:hover {
            background-color: #118a11;
        }
        
        QPushButton[class="warning"] {
            background-color: #ff8c00;
            border: 1px solid #cc7000;
            color: #ffffff;
        }
        
        QPushButton[class="warning"]:hover {
            background-color: #ff9a1a;
        }
        
        QPushButton[class="danger"] {
            background-color: #d13438;
            border: 1px solid #a12327;
            color: #ffffff;
        }
        
        QPushButton[class="danger"]:hover {
            background-color: #dc4649;
        }
        
        QLineEdit, QTextEdit, QSpinBox {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }
        
        QLineEdit:focus, QTextEdit:focus, QSpinBox:focus {
            border: 2px solid #0078d4;
        }
        
        QComboBox {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            color: #000000;
            padding: 5px;
            border-radius: 3px;
        }
        
        QComboBox::drop-down {
            border: none;
        }
        
        QComboBox::down-arrow {
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid #000000;
        }
        
        QComboBox QAbstractItemView {
            background-color: #ffffff;
            border: 1px solid #cccccc;
            selection-background-color: #0078d4;
            color: #000000;
        }
        
        QTableWidget {
            background-color: #ffffff;
            gridline-color: #cccccc;
            color: #000000;
            border: 1px solid #cccccc;
        }
        
        QTableWidget::item {
            border-bottom: 1px solid #cccccc;
            padding: 5px;
        }
        
        QTableWidget::item:selected {
            background-color: #0078d4;
            color: #ffffff;
        }
        
        QHeaderView::section {
            background-color: #f0f0f0;
            color: #000000;
            padding: 8px;
            border: 1px solid #cccccc;
            font-weight: bold;
        }
        
        QCheckBox {
            color: #000000;
        }
        
        QCheckBox::indicator {
            width: 16px;
            height: 16px;
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
        }
        
        QCheckBox::indicator:checked {
            background-color: #0078d4;
            border: 1px solid #005a9e;
        }
        
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: #ffffff;
            font-weight: bold;
        }
        
        QProgressBar {
            border: 1px solid #cccccc;
            border-radius: 3px;
            background-color: #ffffff;
            text-align: center;
            color: #000000;
        }
        
        QProgressBar::chunk {
            background-color: #0078d4;
            border-radius: 2px;
        }
        
        QStatusBar {
            background-color: #f0f0f0;
            color: #000000;
            border-top: 1px solid #cccccc;
        }
        
        QScrollBar:vertical {
            background-color: #f0f0f0;
            width: 15px;
            border: 1px solid #cccccc;
        }
        
        QScrollBar::handle:vertical {
            background-color: #0078d4;
            border-radius: 3px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: #106ebe;
        }
        
        QScrollBar:horizontal {
            background-color: #f0f0f0;
            height: 15px;
            border: 1px solid #cccccc;
        }
        
        QScrollBar::handle:horizontal {
            background-color: #0078d4;
            border-radius: 3px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: #106ebe;
        }
        
        QDialog {
            background-color: #ffffff;
            color: #000000;
        }
        
        QFormLayout QLabel {
            color: #000000;
        }
        """
        
        self.setStyleSheet(light_style)
        self.log_activity("Light theme applied")
    
    def on_theme_changed(self):
        """Handle theme change when checkbox is toggled."""
        if self.dark_mode_check.isChecked():
            self.set_dark_mode()
            self.theme_toggle_button.setText("☀️ Light Theme")
        else:
            self.set_light_mode()
            self.theme_toggle_button.setText("🌙 Dark Theme")
    
    def toggle_theme(self):
        """Toggle between dark and light theme."""
        current_state = self.dark_mode_check.isChecked()
        self.dark_mode_check.setChecked(not current_state)
        # The on_theme_changed method will be called automatically
    
    def update_ui(self):
        """Update the UI with current data."""
        try:
            # Update dashboard
            accounts = self.account_manager.get_accounts()
            active_accounts = [a for a in accounts if a.get("active", False)]
            
            self.active_accounts_label.setText(str(len(active_accounts)))
            self.total_accounts_label.setText(str(len(accounts)))
            
            # Update monitor status if monitor is running
            if self.monitor.is_running:
                self.monitor_status_label.setText("Running")
                self.toggle_monitor_button.setText("Stop Monitor")
            else:
                self.monitor_status_label.setText("Stopped")
                self.toggle_monitor_button.setText("Start Monitor")
            
            # Update accounts table
            self.update_accounts_table(accounts)
            
            # Update monitor logs if monitor is running
            if self.monitor.is_running:
                logs = self.monitor.get_recent_logs()
                self.update_monitor_log(logs)
                
        except Exception as e:
            self.logger.error(f"UI update error: {str(e)}")
    
    def update_accounts_table(self, accounts):
        """Update the accounts table with the current account data."""
        try:
            self.accounts_table.setRowCount(len(accounts))
            
            for row, account in enumerate(accounts):
                # Phone number
                self.accounts_table.setItem(row, 0, QTableWidgetItem(account.get("phone", "")))
                
                # Name / Username - display on same line
                name = account.get("name", "")
                username = account.get("username", "")
                
                # Format as "Name / Username" on single line
                name_username_display = ""
                
                if name and username:
                    name_username_display = f"{name} / @{username}"
                elif name:
                    name_username_display = name
                elif username:
                    name_username_display = f"@{username}"
                else:
                    name_username_display = "N/A"
                
                # Create clickable username if username exists
                if username:
                    name_username_widget = QWidget()
                    name_username_layout = QHBoxLayout(name_username_widget)  # Changed to horizontal layout
                    name_username_layout.setContentsMargins(5, 2, 5, 2)
                    
                    # Create clickable label with full text
                    name_username_label = QLabel(name_username_display)
                    name_username_label.setStyleSheet("color: blue; text-decoration: underline;")
                    name_username_label.setCursor(Qt.PointingHandCursor)
                    name_username_label.mousePressEvent = lambda e, user=username: self.open_telegram_link(user)
                    
                    name_username_layout.addWidget(name_username_label)
                    name_username_layout.addStretch()  # Push content to left
                    self.accounts_table.setCellWidget(row, 1, name_username_widget)
                else:
                    # If no username, just display as regular text item
                    self.accounts_table.setItem(row, 1, QTableWidgetItem(name_username_display))
                
                # Status
                status = "Active" if account.get("active", False) else "Inactive"
                
                # Check if account is disabled
                disabled_until = account.get("disabled_until")
                if disabled_until else 0:
                    try:
                        disabled_datetime = datetime.fromisoformat(disabled_until)
                        if disabled_datetime > datetime.now():
                            status = "Disabled"
                    except:
                        pass
                
                status_item = QTableWidgetItem(status)
                if status == "Active":
                    status_item.setForeground(QColor("green"))
                elif status == "Disabled":
                    status_item.setForeground(QColor("orange"))
                else:
                    status_item.setForeground(QColor("red"))
                    
                self.accounts_table.setItem(row, 2, status_item)
                
                # Last check
                last_check = account.get("last_check", "Never")
                self.accounts_table.setItem(row, 3, QTableWidgetItem(last_check))
                
                # Errors
                errors = account.get("errors", 0)
                error_item = QTableWidgetItem(str(errors))
                if errors > 0:
                    error_item.setForeground(QColor("red"))
                self.accounts_table.setItem(row, 4, error_item)
                
                # Group Limit
                daily_limit = account.get("daily_group_limit", 0)
                is_aged = account.get("is_aged", 0)
                
                if daily_limit else 0 > 0:
                    aged_text = "Aged" if is_aged else "New"
                    limit_text = f"{daily_limit} ({aged_text})"
                else:
                    limit_text = "Unknown"
                
                group_limit_item = QTableWidgetItem(limit_text)
                if daily_limit >= 500:
                    group_limit_item.setForeground(QColor("green"))
                elif daily_limit >= 200:
                    group_limit_item.setForeground(QColor("orange"))
                else:
                    group_limit_item.setForeground(QColor("red"))
                    
                self.accounts_table.setItem(row, 5, group_limit_item)
                
                # Actions
                actions_widget = QWidget()
                actions_layout = QHBoxLayout(actions_widget)
                actions_layout.setContentsMargins(0, 0, 0, 0)
                
                toggle_button = QPushButton("Toggle")
                fix_button = QPushButton("Fix")
                refresh_button = QPushButton("Refresh Info")
                
                # Set button styles based on status
                if status == "Active":
                    toggle_button.setText("Deactivate")
                    toggle_button.setStyleSheet("background-color: #ffeeee;")
                else:
                    toggle_button.setText("Activate")
                    toggle_button.setStyleSheet("background-color: #eeffee;")
                
                # Set up button actions
                toggle_button.clicked.connect(lambda _, acc=account: self.toggle_account(acc))
                fix_button.clicked.connect(lambda _, acc=account: self.fix_specific_account(acc))
                refresh_button.clicked.connect(lambda _, acc=account: self.refresh_specific_account_info(acc))
                
                actions_layout.addWidget(toggle_button)
                actions_layout.addWidget(fix_button)
                actions_layout.addWidget(refresh_button)
                self.accounts_table.setCellWidget(row, 6, actions_widget)
                
        except Exception as e:
            self.logger.error(f"Failed to update accounts table: {str(e)}")
    
    def update_monitor_log(self, logs):
        """Update the monitor log with recent logs."""
        try:
            if logs:
                self.monitor_log.clear()
                filtered_logs = []
                
                # Filter out redundant messages
                redundant_messages = ["No accounts were checked"]
                
                for log in logs:
                    if not any(msg in log for msg in redundant_messages):
                        filtered_logs.append(log)
                
                for log in filtered_logs:
                    self.monitor_log.append(log)
        except Exception as e:
            self.logger.error(f"Failed to update monitor log: {str(e)}")
    
    def log_activity(self, message):
        """Log an activity to the activities text box (legacy method for compatibility)."""
        # Use the signal for thread-safe logging
        self.log_activity_signal.emit(message)
    
    def toggle_account(self, account):
        """Toggle the active state of an account."""
        try:
            phone = account.get("phone", "")
            if not else 0 phone:
                return
            
            if account.get("active", False):
                self.account_manager.deactivate_account(phone)
                self.log_activity_signal.emit(f"Deactivated account: {phone}")
            else:
                self.account_manager.activate_account(phone)
                self.log_activity_signal.emit(f"Activated account: {phone}")
                
            # Update UI using signal
            self.update_ui_signal.emit()
            
        except Exception as e:
            self.logger.error(f"Failed to toggle account {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to toggle account: {str(e)}")

    def apply_monitor_settings(self):
        """Apply the monitor settings."""
        try:
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # Save filter settings
            self.settings.setValue("min_members", self.min_members_spin.value())
            self.settings.setValue("min_message_time", self.min_message_time_spin.value())
            self.settings.setValue("min_total_messages", self.min_total_messages_spin.value())
            
            # If monitor is running, restart it with new settings
            if self.monitor.is_running:
                self.stop_monitor()
                self.start_monitor()
            
            self.status_label.setText("Monitor settings applied")
            self.log_activity("Monitor settings applied")
            
        except Exception as e:
            self.logger.error(f"Failed to apply monitor settings: {str(e)}")
            self.log_activity(f"Failed to apply monitor settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to apply monitor settings: {str(e)}")
    
    def closeEvent(self, event):
        """Handle the close event."""
        # Stop timers
        if hasattr(self, 'log_refresh_timer'):
            self.log_refresh_timer.stop()
            
        # Stop the monitor
        if self.monitor.is_running:
            self.monitor.stop()
        
        # Stop any running checkers
        if self.is_checker_running:
            self.checker_should_stop = True
        if self.is_task_checker_running:
            self.task_checker_should_stop = True
        
        # Save settings
        self.save_settings()
        
        # Accept the close event
        event.accept()

    def start_checker(self):
        """Start the group/channel checker."""
        if self.is_checker_running:
            QMessageBox.information(self, "Info", "Checker is already running.")
            return
            
        # Get the list of groups/channels from the input
        group_links = self.groups_input.toPlainText().strip().split('\n')
        group_links = [link.strip() for link in group_links if link.strip()]
        
        if not group_links:
            QMessageBox.warning(self, "Warning", "Please enter at least one group or channel link.")
            return
        
        # Check for resume option
        is_resuming, final_group_links, start_index = self.check_resume_option(group_links)
        
        # Make sure we have at least one active account
        accounts = self.account_manager.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        
        if not active_accounts:
            QMessageBox.warning(self, "Warning", "Please activate at least one account before starting the checker.")
            return
        
        # Clear previous results if not resuming
        if not is_resuming:
            self.clear_results()
        
        # Reset global progress counter for this session
        with self.global_progress_lock:
            self.global_groups_processed = self.current_group_index
        
        # Set up progress tracking
        self.current_group_links = group_links
        self.total_groups = len(group_links)
        self.current_group_index = start_index
        
        # Update progress display
        self.update_progress_signal.emit(self.current_group_index, self.total_groups)
        
        # Update state and UI
        self.is_checker_running = True
        self.checker_should_stop = False
        self.start_checker_button.setEnabled(False)
        self.stop_checker_button.setEnabled(True)
        
        # Indicate we're starting the checker
        if is_resuming:
            self.status_label.setText(f"Resuming checker from group #{start_index + 1}...")
            self.log_activity(f"Resuming group/channel checker from group #{start_index + 1}...")
        else:
            self.status_label.setText("Checking groups/channels...")
            self.log_activity("Starting group/channel checker...")
        
        # Run in background thread
        threading.Thread(target=self._checker_thread, args=(final_group_links,), daemon=True).start()
    
    def stop_checker(self):
        """Stop the group/channel checker."""
        if not self.is_checker_running:
            QMessageBox.information(self, "Info", "Checker is not currently running.")
            return
        
        # Set stop flag
        self.checker_should_stop = True
        self.log_activity("Stopping group/channel checker...")
        self.status_label.setText("Stopping checker...")
    
    def clear_results(self):
        """Clear all result counters and collections."""
        with self.results_lock:
            self.valid_filtered.clear()
            self.valid_only.clear()
            self.topics_groups.clear()
            self.channels_only.clear()
            self.invalid_groups.clear()
            self.account_issues.clear()
            self.join_requests.clear()
        
        # Reset progress tracking
        self.current_group_index = 0
        self.total_groups = 0
        self.current_group_links = []
        
        # Reset global progress counter for multi-account checker
        with self.global_progress_lock:
            self.global_groups_processed = 0
        
        # Update UI counters
        self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0)
        self.update_progress_signal.emit(0, 0)
        self.log_activity("Results cleared")
    
    def save_progress(self, current_index, total, group_links):
        """Save current progress to file for crash recovery."""
        try:
            progress_data = {
                "current_index": current_index,
                "total": total,
                "timestamp": datetime.now().isoformat(),
                "group_links": group_links
            }
            
            with open(self.progress_file, "w", encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2)
                
        except Exception as e:
            self.logger.error(f"Failed to save progress: {str(e)}")
    
    def load_progress(self):
        """Load saved progress from file."""
        try:
            if not os.path.exists(self.progress_file):
                return None
                
            with open(self.progress_file, "r", encoding='utf-8') as f:
                progress_data = json.load(f)
                
            return progress_data
            
        except Exception as e:
            self.logger.error(f"Failed to load progress: {str(e)}")
            return None
    
    def clear_progress(self):
        """Clear saved progress file."""
        try:
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
        except Exception as e:
            self.logger.error(f"Failed to clear progress file: {str(e)}")
    
    def check_resume_option(self, new_group_links):
        """Check if we can resume from previous session and ask user."""
        progress_data = self.load_progress()
        
        if not progress_data:
            return False, new_group_links, 0
            
        saved_current = progress_data.get("current_index", 0)
        saved_total = progress_data.get("total", 0)
        saved_timestamp = progress_data.get("timestamp", "")
        saved_links = progress_data.get("group_links", [])
        
        if saved_current else 0 <= 0 or saved_current >= saved_total:
            return False, new_group_links, 0
            
        # Check if the new links match the saved ones (same groups)
        if saved_links == new_group_links and saved_current < len(new_group_links):
            from PyQt5.QtWidgets import QMessageBox
            
            # Format timestamp for display
            try:
                timestamp_obj = datetime.fromisoformat(saved_timestamp)
                formatted_time = timestamp_obj.strftime("%Y-%m-%d %H:%M:%S")
            except:
                formatted_time = saved_timestamp
            
            reply = QMessageBox.question(
                self,
                "Resume Previous Session?",
                f"Found previous session:\n\n"
                f"• Progress: {saved_current} / {saved_total} groups checked\n"
                f"• Time: {formatted_time}\n"
                f"• Remaining: {saved_total - saved_current} groups\n\n"
                f"Do you want to resume from where you left off?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply == QMessageBox.Yes:
                # Resume from saved position
                remaining_links = new_group_links[saved_current:]
                self.log_activity(f"📄 RESUMING: Continuing from group #{saved_current + 1}")
                self.log_activity(f"📊 PROGRESS: {saved_current} already checked, {len(remaining_links)} remaining")
                return True, remaining_links, saved_current
            else:
                # Start fresh
                self.clear_progress()
                return False, new_group_links, 0
        else:
            # Different groups, start fresh
            self.clear_progress()
            return False, new_group_links, 0
    
    def check_group_or_channel(self, link):
        """Check a single group or channel link using an active account."""
        try:
            # Get active accounts
            accounts = self.account_manager.get_accounts()
            active_accounts = [a for a in accounts if a.get("active", False)]
            
            if not active_accounts:
                return {
                    "valid": False,
                    "error_type": "account_issue",
                    "reason": "No active accounts available",
                    "type": "unknown",
                    "members": 0,
                    "last_message_age_hours": 999,
                    "total_messages": 0
                }
            
            # Use the first active account (could be improved with load balancing)
            account = active_accounts[0]
            phone = account.get("phone")
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            
            # Create client and check the group
            client = TelegramClient(api_id, api_hash, phone, logger=self.logger)
            result = client.get_entity_info(link)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error checking {link}: {str(e)}")
            return {
                "valid": False,
                "error_type": "account_issue",
                "reason": f"Checker error: {str(e)}",
                "type": "unknown",
                "members": 0,
                "last_message_age_hours": 999,
                "total_messages": 0
            }
    
    def _checker_thread(self, group_links):
        """Background thread for checking groups/channels with 3-tier error classification and progress tracking."""
        try:
            # Get filter settings
            min_members = self.settings.value("min_members", 500, type=int)
            min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
            min_total_messages = self.settings.value("min_total_messages", 100, type=int)
            
            # Initialize result categories with 3-tier classification
            valid_filtered = []      # Groups that pass all filters
            valid_only = []          # Valid groups that don't pass filters
            topics_groups = []       # Valid topic groups
            channels_only = []       # Valid channels
            invalid_groups = []      # Invalid groups (username not found, etc.)
            account_issues = []      # Account-level problems (rate limits, etc.)
            join_requests = []       # Groups that need join requests
            
            # Process each link with progress tracking
            for i, link in enumerate(group_links):
                # Check if we should stop
                if self.checker_should_stop:
                    self.log_activity_signal.emit("Group/channel checking stopped by user")
                    break
                
                # Calculate actual group number (considering resume)
                actual_group_number = self.current_group_index + i + 1
                groups_checked_so_far = self.current_group_index + i
                
                # Update progress display BEFORE processing each group
                self.update_progress_signal.emit(groups_checked_so_far, self.total_groups)
                
                # Enhanced logging with group numbers
                self.log_activity_signal.emit(f"[CHECKING] Group #{actual_group_number}: {link}")
                self.logger.info(f"Checked group {actual_group_number} of {self.total_groups}: {link}")
                
                # Save progress every 10 groups for crash recovery
                if i % 10 == 0 and i > 0:
                    self.save_progress(groups_checked_so_far, self.total_groups, self.current_group_links)
                    self.log_activity_signal.emit(f"💾 Progress auto-saved: {groups_checked_so_far} / {self.total_groups} groups checked")
                
                # Anti-flood delay: 1-2 seconds between checks
                if i > 0:  # Don't delay before the first check
                    delay = random.uniform(1.0, 2.0)
                    time.sleep(delay)
                
                # Rest cycle: pause for 5 minutes every 200 groups
                if (self.current_group_index + i) > 0 and (self.current_group_index + i) % 200 == 0:
                    self.log_activity_signal.emit("Rest cycle: pausing for 5 minutes")
                    for rest_seconds in range(300, 0, -30):  # 5 minutes
                        if self.checker_should_stop:
                            break
                        self.update_analyzing_signal.emit(f"Rest cycle: {rest_seconds//60}m {rest_seconds%60}s remaining")
                        time.sleep(30)
                    
                    if self.checker_should_stop:
                        break
                    
                    self.log_activity_signal.emit("Rest cycle completed")
                
                # Update currently analyzing label
                self.update_analyzing_signal.emit(f"Currently analyzing: Group #{actual_group_number}: {link}")
                
                # Check the link using the active account
                try:
                    result = self.check_group_or_channel(link)
                    
                    # Update progress AFTER processing each group
                    groups_completed = self.current_group_index + i + 1
                    self.update_progress_signal.emit(groups_completed, self.total_groups)
                    
                    # Log the completion with progress
                    self.logger.info(f"[INFO] Checked group {groups_completed} of {self.total_groups}: {link}")
                    
                    # Handle based on error type if invalid
                    if not result["valid"]:
                        error_type = result.get("error_type", "invalid_group")
                        
                        if error_type else 0 == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] INVALID: {link} → InvalidGroups_Channels.txt")
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            wait_seconds = result.get("wait_seconds", 0)
                            if wait_seconds else 0 > 0:
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: FloodWait → Paused {wait_seconds//60} mins → Saved to AccountIssue.txt")
                            else:
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: {result['reason']} → Saved to AccountIssue.txt")
                        elif error_type == "join_request":
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] JOIN REQUEST: {link} → Saved to JoinRequest.txt")
                        
                        # Update results in real-time
                        self.update_result_counts_signal.emit(
                            len(valid_filtered), len(valid_only), len(topics_groups), 
                            len(channels_only), len(invalid_groups), len(account_issues) + len(join_requests)
                        )
                        continue
                    
                    # Link is valid, categorize based on type and filters
                    if result["type"] == "channel":
                        channels_only.append(link)
                        self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 📺 Valid channel: {link}")
                    elif result["type"] == "topic":
                        topics_groups.append(link)
                        self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] 💬 Valid topic group: {link}")
                    elif result["type"] == "group":
                        # Check filters for groups
                        passes_filters = (
                            result["members"] >= min_members and
                            result["last_message_age_hours"] <= min_message_time_hours and
                            result["total_messages"] >= min_total_messages
                        )
                        
                        if passes_filters:
                            valid_filtered.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID: {link} → GroupsValid_Filter_On.txt")
                        else:
                            valid_only.append(link)
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ⚠️ Valid group (doesn't pass filters): {link}")
                    
                    # Update results in real-time for regular checker
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues) + len(join_requests)
                    )
                    
                except Exception as e:
                    # Handle any unexpected errors as account issues
                    account_issues.append(link)
                    self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ACCOUNT ISSUE: Error checking {link}: {str(e)} → Saved to AccountIssue.txt")
                    
                    # Update progress even for errors
                    groups_completed = self.current_group_index + i + 1
                    self.update_progress_signal.emit(groups_completed, self.total_groups)
                    
                    # Update results even for errors
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues) + len(join_requests)
                    )
            
            # Final progress update
            final_checked = self.current_group_index + len(group_links)
            self.update_progress_signal.emit(final_checked, self.total_groups)
            
            # Update result counts using signal
            self.update_result_counts_signal.emit(
                len(valid_filtered), len(valid_only), len(topics_groups), 
                len(channels_only), len(invalid_groups), len(account_issues) + len(join_requests)
            )
            
            # Save results to files if not stopped with new 3-tier system
            if not self.checker_should_stop:
                self.save_results_3tier(valid_filtered, valid_only, topics_groups, channels_only, 
                                      invalid_groups, account_issues, join_requests)
                
                # Clear progress file on successful completion
                self.clear_progress()
                self.log_activity_signal.emit("✅ Session completed successfully - progress file cleared")
            
            # Update status using signals
            self.update_analyzing_signal.emit("Currently analyzing: None")
            if self.checker_should_stop:
                self.update_status_signal.emit("Group/channel checking stopped")
                self.log_activity_signal.emit("Group/channel checking stopped")
                # Save progress for potential resume
                current_progress = self.current_group_index + len(group_links)
                self.save_progress(current_progress, self.total_groups, self.current_group_links)
                self.log_activity_signal.emit(f"💾 Progress saved: {current_progress} / {self.total_groups} groups checked")
            else:
                self.update_status_signal.emit("Group/channel checking completed")
                self.log_activity_signal.emit("Group/channel checking completed")
            
        except Exception as e:
            self.update_status_signal.emit(f"Checker error: {str(e)}")
            self.logger.error(f"Checker error: {str(e)}")
            self.log_activity_signal.emit(f"Checker failed: {str(e)}")
            self.update_analyzing_signal.emit("Currently analyzing: None")
            
            # Save progress on crash
            try:
                current_progress = self.current_group_index + len(group_links)
                self.save_progress(current_progress, self.total_groups, self.current_group_links)
                self.log_activity_signal.emit(f"💾 Progress saved due to error: {current_progress} / {self.total_groups} groups checked")
            except:
                pass
                
        finally:
            # Reset state and UI
            self.is_checker_running = False
            self.checker_should_stop = False
            QMetaObject.invokeMethod(self.start_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            QMetaObject.invokeMethod(self.stop_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, False))
    
    def _task_reassignment_monitor(self):
        """Monitor for task reassignment when accounts hit FloodWait or errors."""
        while not self.task_checker_should_stop:
            try:
                time.sleep(5)  # Check every 5 seconds
                
                # Check if there are pending groups to reassign
                with self.account_states_lock:
                    if not self.pending_groups_queue:
                        continue
                    
                    # Find available accounts (not in flood_wait, error_recovery, or failed)
                    available_accounts = []
                    for phone, state in self.account_states.items():
                        if state["status"] == "available":
                            available_accounts.append(phone)
                    
                    if available_accounts:
                        # Reassign groups to available accounts
                        groups_to_reassign = self.pending_groups_queue.copy()
                        self.pending_groups_queue.clear()
                        
                        self.log_activity_signal.emit(f"🔄 REASSIGNMENT: Found {len(available_accounts)} available accounts for {len(groups_to_reassign)} groups")
                        
                        # Distribute groups among available accounts
                        groups_per_account = max(1, len(groups_to_reassign) // len(available_accounts))
                        group_index = 0
                        
                        for account_phone in available_accounts:
                            if group_index >= len(groups_to_reassign):
                                break
                            
                            # Calculate groups for this account
                            start_index = group_index
                            end_index = min(group_index + groups_per_account, len(groups_to_reassign))
                            
                            # Add remaining groups to the last account
                            if account_phone == available_accounts[-1]:
                                end_index = len(groups_to_reassign)
                            
                            account_groups = groups_to_reassign[start_index:end_index]
                            
                            if account_groups:
                                self.log_activity_signal.emit(f"➡️ REASSIGNED: {len(account_groups)} groups to account {account_phone}")
                                
                                # Start new task thread for this account
                                task_thread = threading.Thread(
                                    target=self._enhanced_account_task_thread,
                                    args=(account_phone, account_groups, start_index),
                                    daemon=True
                                )
                                task_thread.start()
                                
                                group_index = end_index
                        
                        self.log_activity_signal.emit(f"✅ REASSIGNMENT COMPLETE: All {len(groups_to_reassign)} groups redistributed")
                    
                    else:
                        # No available accounts - check what's happening
                        status_counts = {}
                        for phone, state in self.account_states.items():
                            status = state["status"]
                            status_counts[status] = status_counts.get(status, 0) + 1
                        
                        status_summary = ", ".join([f"{count} {status}" for status, count in status_counts.items()])
                        
                        if self.pending_groups_queue:
                            self.log_activity_signal.emit(f"⏳ WAITING: {len(self.pending_groups_queue)} groups pending | Account Status: {status_summary}")
                        
                        # Check if all accounts are in permanent failure state
                        active_or_recoverable = any(
                            state["status"] in ["available", "flood_wait", "error_recovery"] 
                            for state in self.account_states.values()
                        )
                        
                        if not active_or_recoverable and self.pending_groups_queue:
                            self.log_activity_signal.emit(f"🚨 CRITICAL: All accounts failed permanently - {len(self.pending_groups_queue)} groups cannot be processed")
                            # Move pending groups to account issues
                            with self.results_lock:
                                self.account_issues.extend(self.pending_groups_queue)
                                self.pending_groups_queue.clear()
                                
                                # Update UI
                                self.update_result_counts_signal.emit(
                                    len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                                    len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                                )
                            
                            self.log_activity_signal.emit(f"📋 RECOVERY: {len(self.pending_groups_queue)} unprocessed groups moved to AccountIssue.txt")
                
            except Exception as e:
                self.logger.error(f"Task reassignment monitor error: {str(e)}")
                self.log_activity_signal.emit(f"❌ Reassignment monitor error: {str(e)}")
                
        self.log_activity_signal.emit("🛑 Task reassignment monitor stopped")
    
    def _schedule_account_retry(self, phone, wait_seconds):
        """Schedule an account retry after FloodWait or error recovery period."""
        retry_type = "FloodWait" if wait_seconds >= 300 else "Error Recovery"
        self.log_activity_signal.emit(f"⏰ RETRY SCHEDULED: Account {phone} {retry_type} - will retry in {wait_seconds}s ({wait_seconds//60}m {wait_seconds%60}s)")
        
        def retry_thread():
            try:
                # Use the actual wait time, with minimum safety margins
                if wait_seconds >= 300:  # FloodWait
                    actual_wait = max(wait_seconds, 300)  # Minimum 5 minutes for safety
                else:  # Error recovery
                    actual_wait = max(wait_seconds, 60)   # Minimum 1 minute for error recovery
                
                # Log countdown every minute for longer waits
                if actual_wait > 60:
                    remaining_time = actual_wait
                    while remaining_time > 0 and not self.task_checker_should_stop:
                        if remaining_time % 60 == 0:  # Log every minute
                            minutes = remaining_time // 60
                            self.log_activity_signal.emit(f"⏰ Account {phone} retry in {minutes} minutes")
                        time.sleep(30)  # Check every 30 seconds
                        remaining_time -= 30
                else:
                    time.sleep(actual_wait)
                
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Retry cancelled for account {phone}")
                    return
                
                # Re-enable account
                with self.account_states_lock:
                    if phone in self.account_states:
                        old_status = self.account_states[phone]["status"]
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        
                        self.log_activity_signal.emit(f"🔄 RETRY: Account {phone} recovered from {old_status} → now available")
                
                # Update account status in database
                self.account_manager.update_check_time(phone, "OK - Recovered")
                
                self.log_activity_signal.emit(f"✅ RECOVERY COMPLETE: Account {phone} is ready for new tasks")
                
            except Exception as e:
                self.logger.error(f"Retry thread error for {phone}: {str(e)}")
                self.log_activity_signal.emit(f"❌ Retry error for account {phone}: {str(e)}")
                
                # Mark as failed if recovery fails
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "failed"
        
        # Start retry thread
        threading.Thread(target=retry_thread, daemon=True).start()
    
    def update_log_display(self):
        """Update the log display based on selected filter."""
        try:
            log_type = self.log_type_combo.currentText()
            log_level = self.log_level_combo.currentText()
            
            if log_type == "Authentication":
                # Load authentication logs
                logs = read_auth_log(max_lines=1000)
            elif log_type == "Usage Checker":
                # Load usage checker logs
                logs = read_usage_checker_log(max_lines=1000)
            else:
                # Load general logs
                log_file = os.path.join("logs", "tg_checker.log")
                logs = read_log_file(log_file, max_lines=1000)
            
            # Filter by level if needed (skip for Usage Checker as it has its own format)
            if log_level != "All" and log_type != "Usage Checker":
                logs = filter_logs_by_level(logs, log_level)
            
            # Update the display
            self.log_display.clear()
            for log in logs:
                self.log_display.append(log.strip())
            
            # Scroll to bottom
            scroll_bar = self.log_display.verticalScrollBar()
            scroll_bar.setValue(scroll_bar.maximum())
            
        except Exception as e:
            self.logger.error(f"Error updating log display: {str(e)}")
            self.log_display.clear()
            self.log_display.append(f"Error loading logs: {str(e)}")

    def update_verification_status(self, message):
        """Update the status label with verification progress information."""
        try:
            QMetaObject.invokeMethod(
                self.status_label,
                "setText",
                Qt.QueuedConnection,
                Q_ARG(str, message)
            )
        except Exception as e:
            # Fall back to direct update if invokeMethod fails
            try:
                self.status_label.setText(message)
            except:
                log_auth(self_outer.logger, f"Failed to update verification status: {str(e)}", logging.ERROR)

    def refresh_account_info(self, phone):
        """Refresh account information from Telegram."""
        try:
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity(f"Account {phone} not found")
                return False
            
            # Create client and get user info
            client = TelegramClient(account.get("api_id"), account.get("api_hash"), phone)
            user_info = client.get_account_info()
            
            if user_info:
                name = user_info.get("full_name", "").strip()
                username = user_info.get("username", "").strip()
                
                # Update account info in database
                success = self.account_manager.update_account_info(phone, name=name, username=username)
                if success:
                    self.log_activity(f"Updated account info for {phone}: {name} / @{username}")
                    return True
                else:
                    self.log_activity(f"Failed to update account info for {phone}")
                    return False
            else:
                self.log_activity(f"Could not retrieve user info for {phone}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error refreshing account info for {phone}: {str(e)}")
            self.log_activity(f"Error refreshing account info for {phone}: {str(e)}")
            return False
    
    def refresh_all_accounts_info(self):
        """Refresh information for all accounts automatically."""
        self.update_status_signal.emit("Refreshing account information...")
        self.log_activity_signal.emit("Refreshing information for all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._refresh_all_accounts_thread, daemon=True).start()
    
    def _refresh_all_accounts_thread(self):
        """Background thread for refreshing all account information."""
        try:
            accounts = self.account_manager.get_accounts()
            updated_count = 0
            
            for account in accounts:
                phone = account.get("phone")
                if phone else 0:
                    # Check if account info is missing or incomplete
                    name = account.get("name", "").strip()
                    username = account.get("username", "").strip()
                    
                    if not name and not username:
                        self.log_activity_signal.emit(f"Refreshing info for {phone}...")
                        if self.refresh_account_info(phone):
                            updated_count += 1
                        time.sleep(2)  # Small delay between requests
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit(f"Account info refresh completed - {updated_count} accounts updated")
            self.log_activity_signal.emit(f"Account info refresh completed - {updated_count} accounts updated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Account info refresh error: {str(e)}")
            self.logger.error(f"Account info refresh error: {str(e)}")
            self.log_activity_signal.emit(f"Account info refresh failed: {str(e)}")

    def refresh_specific_account_info(self, account):
        """Refresh information for a specific account."""
        try:
            phone = account.get("phone", "")
            if not else 0 phone:
                return
                
            self.update_status_signal.emit(f"Refreshing info for {phone}...")
            self.log_activity_signal.emit(f"Refreshing info for {phone}...")
            
            # Run in background thread
            threading.Thread(target=self._refresh_specific_account_thread, args=(phone,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Failed to refresh account info for {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh account info: {str(e)}")
    
    def _refresh_specific_account_thread(self, phone):
        """Background thread for refreshing specific account information."""
        try:
            success = self.refresh_account_info(phone)
            
            if success:
                # Update UI using signal
                self.update_ui_signal.emit()
                self.update_status_signal.emit(f"Account info refreshed for {phone}")
            else:
                self.update_status_signal.emit(f"Failed to refresh info for {phone}")
                
        except Exception as e:
            self.logger.error(f"Error in refresh thread for {phone}: {str(e)}")
            self.update_status_signal.emit(f"Refresh error for {phone}: {str(e)}")

    def _auto_check_new_account_age(self, phone):
        """Background thread method to check age for a newly added account."""
        try:
            import asyncio
            
            # Wait a moment for the account to be fully set up
            time.sleep(3)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                log_usage_checker(self.logger, f"Checking age for newly added account: {phone}")
                success, message = loop.run_until_complete(
                    self.account_manager.check_account_age_with_bot(phone)
                )
                
                if success:
                    self.update_ui_signal.emit()
                    self.log_activity_signal.emit(f"Age check completed for new account {phone}: {message}")
                    log_usage_checker(self.logger, f"Age check successful for new account {phone}: {message}")
                else:
                    self.log_activity_signal.emit(f"Age check failed for new account {phone}: {message}")
                    log_usage_checker(self.logger, f"Age check failed for new account {phone}: {message}", logging.WARNING)
                    
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"Error in auto age check for new account {phone}: {str(e)}"
            self.logger.error(error_msg)
            self.log_activity_signal.emit(error_msg)
            log_usage_checker(self.logger, error_msg, logging.ERROR)

    def check_all_account_ages(self):
        """Manually check ages for all accounts."""
        self.update_status_signal.emit("Checking ages for all accounts...")
        self.log_activity_signal.emit("Starting manual age check for all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._check_all_account_ages_thread, daemon=True).start()
    
    def _check_all_account_ages_thread(self):
        """Background thread for checking ages of all accounts."""
        try:
            import asyncio
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                log_usage_checker(self.logger, "Starting manual age check for all accounts")
                results = loop.run_until_complete(self.account_manager.auto_check_account_ages())
                
                success_count = sum(1 for _, success, _ in results if success)
                total_count = len(results)
                
                self.update_ui_signal.emit()
                self.update_status_signal.emit(f"Age check completed: {success_count}/{total_count} successful")
                self.log_activity_signal.emit(f"Manual age check completed: {success_count}/{total_count} successful")
                log_usage_checker(self.logger, f"Manual age check completed: {success_count}/{total_count} successful")
                
            finally:
                loop.close()
                
        except Exception as e:
            self.update_status_signal.emit(f"Age check error: {str(e)}")
            self.logger.error(f"Age check error: {str(e)}")
            self.log_activity_signal.emit(f"Age check failed: {str(e)}")
            log_usage_checker(self.logger, f"Manual age check failed: {str(e)}", logging.ERROR)

    def save_results_3tier(self, valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests):
        """Save results to text files with 3-tier error classification system."""
        try:
            # Create the Results directory if it doesn't exist
            results_dir = "Results"
            os.makedirs(results_dir, exist_ok=True)
            
            # Convert to lists if needed
            valid_filtered = list(valid_filtered) if valid_filtered else []
            valid_only = list(valid_only) if valid_only else []
            topics_groups = list(topics_groups) if topics_groups else []
            channels_only = list(channels_only) if channels_only else []
            invalid_groups = list(invalid_groups) if invalid_groups else []
            account_issues = list(account_issues) if account_issues else []
            join_requests = list(join_requests) if join_requests else []
            
            # Define result file configurations with 3-tier classification
            result_files = [
                {
                    "filename": "GroupsValid_Filter_On.txt",
                    "data": valid_filtered,
                    "description": "groups (valid & pass all filters)",
                    "summary_desc": "Groups that pass all filters"
                },
                {
                    "filename": "GroupsValidOnly.txt",
                    "data": valid_only,
                    "description": "groups (valid but don't pass filters)",
                    "summary_desc": "Valid groups that don't meet filter criteria"
                },
                {
                    "filename": "TopicsGroups.txt",
                    "data": topics_groups,
                    "description": "topic groups",
                    "summary_desc": "Valid topic groups"
                },
                {
                    "filename": "Channels.txt",
                    "data": channels_only,
                    "description": "channels",
                    "summary_desc": "Valid channels"
                },
                {
                    "filename": "InvalidGroups_Channels.txt",
                    "data": invalid_groups,
                    "description": "invalid groups/channels",
                    "summary_desc": "Invalid groups (username not found, private, etc.)"
                },
                {
                    "filename": "AccountIssue.txt",
                    "data": account_issues,
                    "description": "account issues",
                    "summary_desc": "Account-level problems (rate limits, session issues, etc.)"
                },
                {
                    "filename": "JoinRequest.txt",
                    "data": join_requests,
                    "description": "join requests needed",
                    "summary_desc": "Groups that require join requests"
                }
            ]
            
            # Save each category to its own file
            files_saved = 0
            
            for file_config in result_files:
                filename = file_config["filename"]
                data = file_config["data"]
                description = file_config["description"]
                
                file_path = os.path.join(results_dir, filename)
                
                try:
                    with open(file_path, "w", encoding='utf-8', newline='') as f:
                        if data:
                            for item in data:
                                f.write(f"{item}\n")
                    
                    files_saved += 1
                    self.log_activity(f"📄 Saved {len(data)} {description} to {filename}")
                    
                    # Verify the file was created properly
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        self.log_activity(f"✅ Verified: {filename} created ({file_size} bytes)")
                    
                except Exception as file_error:
                    self.log_activity(f"❌ Failed to save {filename}: {str(file_error)}")
                    self.logger.error(f"File save error for {filename}: {str(file_error)}")
            
            # Create summary file with 3-tier classification
            total_results = len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only) + len(invalid_groups) + len(account_issues) + len(join_requests)
            summary_filename = "SUMMARY.txt"
            summary_path = os.path.join(results_dir, summary_filename)
            
            try:
                with open(summary_path, "w", encoding='utf-8', newline='') as f:
                    f.write("=== TG CHECKER RESULTS SUMMARY ===\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("=== VALID GROUPS ===\n")
                    f.write(f"Groups Valid & Filter ON: {len(valid_filtered)}\n")
                    f.write(f"Groups Valid Only: {len(valid_only)}\n")
                    f.write(f"Topics Groups Only Valid: {len(topics_groups)}\n")
                    f.write(f"Channels Only Valid: {len(channels_only)}\n")
                    f.write(f"SUBTOTAL VALID: {len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only)}\n\n")
                    f.write("=== INVALID/ISSUES ===\n")
                    f.write(f"Invalid Groups/Channels: {len(invalid_groups)}\n")
                    f.write(f"Account Issues: {len(account_issues)}\n")
                    f.write(f"Join Requests Needed: {len(join_requests)}\n")
                    f.write(f"SUBTOTAL INVALID/ISSUES: {len(invalid_groups) + len(account_issues) + len(join_requests)}\n\n")
                    f.write(f"TOTAL PROCESSED: {total_results}\n\n")
                    f.write("=== FILES CREATED ===\n")
                    for file_config in result_files:
                        f.write(f"• {file_config['filename']} - {file_config['summary_desc']}\n")
                
                files_saved += 1
                self.log_activity(f"📄 Created summary file: {summary_filename}")
                
            except Exception as summary_error:
                self.log_activity(f"❌ Failed to create summary file: {str(summary_error)}")
                self.logger.error(f"Summary file error: {str(summary_error)}")
            
            # Final summary log
            self.log_activity(f"✅ RESULTS SAVED WITH 3-TIER CLASSIFICATION!")
            self.log_activity(f"📊 Total: {total_results} groups processed")
            self.log_activity(f"🎯 Valid: {len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only)}")
            self.log_activity(f"❌ Invalid: {len(invalid_groups)}")
            self.log_activity(f"⚠️ Account Issues: {len(account_issues)}")
            self.log_activity(f"📋 Join Requests: {len(join_requests)}")
            self.log_activity(f"📁 {files_saved} files created in {results_dir}/ directory")
            
        except Exception as e:
            error_msg = f"❌ Failed to save 3-tier results: {str(e)}"
            self.logger.error(error_msg)
            self.log_activity(error_msg)

    def start_task_checker(self):
        """Start the enhanced multi-account task checker."""
        if self.is_task_checker_running:
            QMessageBox.information(self, "Info", "Task checker is already running.")
            return
            
        # Get the list of groups/channels from the input
        group_links = self.groups_input.toPlainText().strip().split('\n')
        group_links = [link.strip() for link in group_links if link.strip()]
        
        if not group_links:
            QMessageBox.warning(self, "Warning", "Please enter at least one group or channel link.")
            return
        
        # Check for resume option
        is_resuming, final_group_links, start_index = self.check_resume_option(group_links)
        
        # Make sure we have at least one active account
        accounts = self.account_manager.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        
        if not active_accounts:
            QMessageBox.warning(self, "Warning", "Please activate at least one account before starting the task checker.")
            return
        
        # Clear previous results if not resuming
        if not is_resuming:
            self.clear_results()
        
        # Reset global progress counter for this session
        with self.global_progress_lock:
            self.global_groups_processed = self.current_group_index
        
        # Set up progress tracking
        self.current_group_links = group_links
        self.total_groups = len(group_links)
        self.current_group_index = start_index
        
        # Update progress display
        self.update_progress_signal.emit(self.current_group_index, self.total_groups)
        
        # Update state and UI
        self.is_task_checker_running = True
        self.task_checker_should_stop = False
        self.start_task_checker_button.setEnabled(False)
        self.stop_task_checker_button.setEnabled(True)
        
        # Indicate we're starting the task checker
        if is_resuming:
            self.status_label.setText(f"Resuming multi-account task checker from group #{start_index + 1}...")
            self.log_activity(f"Resuming enhanced multi-account task checker from group #{start_index + 1}...")
        else:
            self.status_label.setText("Starting multi-account task checker...")
            self.log_activity("Starting enhanced multi-account task checker...")
        
        # Initialize account states
        with self.account_states_lock:
            for account in active_accounts:
                phone = account.get("phone")
                self.account_states[phone] = {
                    "status": "available",
                    "groups_remaining": [],
                    "flood_wait_until": None
                }
        
        # Distribute groups among active accounts
        groups_per_account = max(1, len(final_group_links) // len(active_accounts))
        tasks = []
        
        for i, account in enumerate(active_accounts):
            phone = account.get("phone")
            start_index_for_account = i * groups_per_account
            
            # Last account gets remaining groups
            if i == len(active_accounts) - 1:
                account_groups = final_group_links[start_index_for_account:]
            else:
                end_index = start_index_for_account + groups_per_account
                account_groups = final_group_links[start_index_for_account:end_index]
            
            if account_groups:
                self.log_activity(f"📋 Assigned {len(account_groups)} groups to account {phone}")
                
                # Create task thread for this account
                task_thread = threading.Thread(
                    target=self._enhanced_account_task_thread,
                    args=(phone, account_groups, start_index_for_account),
                    daemon=True
                )
                
                tasks.append({
                    "phone": phone,
                    "thread": task_thread,
                    "groups": account_groups
                })
                
                task_thread.start()
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_tasks_thread, args=(tasks,), daemon=True)
        monitor_thread.start()
        
        # Start reassignment monitor
        reassignment_thread = threading.Thread(target=self._task_reassignment_monitor, daemon=True)
        reassignment_thread.start()
        
        if is_resuming:
            self.log_activity(f"🚀 Task checker resumed: {len(final_group_links)} remaining groups distributed among {len(active_accounts)} accounts")
        else:
            self.log_activity(f"🚀 Task checker started: {len(final_group_links)} groups distributed among {len(active_accounts)} accounts")
    
    def stop_task_checker(self):
        """Stop the multi-account task checker."""
        if not self.is_task_checker_running:
            QMessageBox.information(self, "Info", "Task checker is not currently running.")
            return
        
        # Set stop flag
        self.task_checker_should_stop = True
        self.log_activity("Stopping multi-account task checker...")
        self.status_label.setText("Stopping task checker...")
    
    def _enhanced_account_task_thread(self, phone, group_links, start_index=0):
        """Enhanced account task thread with identical backend logic as regular checker."""
        try:
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity_signal.emit(f"❌ Account {phone} not found")
                return
            
            # Get filter settings (same as regular checker)
            min_members = self.settings.value("min_members", 500, type=int)
            min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
            min_total_messages = self.settings.value("min_total_messages", 100, type=int)
            
            local_groups_processed = 0
            consecutive_errors = 0  # Track consecutive errors for failure detection
            
            for link in group_links:
                # Check if we should stop
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Account {phone} stopping due to user request")
                    break
                
                # Check account status
                with self.account_states_lock:
                    if phone in self.account_states:
                        if self.account_states[phone]["status"] != "available":
                            self.log_activity_signal.emit(f"⏸️ Account {phone} is not available, skipping remaining groups")
                            # Add remaining groups to pending queue
                            remaining_groups = group_links[local_groups_processed:]
                            self.pending_groups_queue.extend(remaining_groups)
                            break
                
                # Anti-flood delay (same as regular checker)
                if local_groups_processed > 0:
                    delay = random.uniform(1.0, 2.0)  # Match regular checker timing
                    time.sleep(delay)
                
                # Rest cycle for long runs (every 100 groups per account)
                if local_groups_processed > 0 and local_groups_processed % 100 == 0:
                    self.log_activity_signal.emit(f"🛌 Account {phone} rest cycle: pausing for 2 minutes")
                    for rest_seconds in range(120, 0, -10):  # 2 minutes
                        if self.task_checker_should_stop:
                            break
                        time.sleep(10)
                    
                    if self.task_checker_should_stop:
                        break
                    
                    self.log_activity_signal.emit(f"✅ Account {phone} rest cycle completed")
                
                self.log_activity_signal.emit(f"🔍 Account {phone} checking: {link}")
                
                try:
                    # *** USE THE SAME BACKEND LOGIC AS REGULAR CHECKER ***
                    result = self.check_group_or_channel(link)
                    
                    # Reset consecutive error count on success
                    consecutive_errors = 0
                    
                    # Increment global progress counter (thread-safe)
                    with self.global_progress_lock:
                        self.global_groups_processed += 1
                        current_total = self.global_groups_processed
                    
                    local_groups_processed += 1
                    
                    # Update global progress display
                    self.update_progress_signal.emit(current_total, self.total_groups)
                    
                    # Log the completion with progress
                    self.logger.info(f"[INFO] Account {phone} checked group {current_total} of {self.total_groups}: {link}")
                    
                    # Process result with 3-tier classification (same as regular checker)
                    with self.results_lock:
                        if not result["valid"]:
                            error_type = result.get("error_type", "invalid_group")
                            
                            if error_type else 0 == "invalid_group":
                                self.invalid_groups.append(link)
                                self.log_activity_signal.emit(f"[{phone}] INVALID: {link} → InvalidGroups_Channels.txt")
                            elif error_type == "account_issue":
                                # Add to account issues collection
                                self.account_issues.append(link)
                                
                                # Handle FloodWait specifically
                                wait_seconds = result.get("wait_seconds", 0)
                                if wait_seconds else 0 > 0:
                                    self.log_activity_signal.emit(f"[{phone}] FLOOD WAIT: {wait_seconds}s → AccountIssue.txt → Pausing account")
                                    
                                    # Update account status
                                    with self.account_states_lock:
                                        if phone in self.account_states:
                                            self.account_states[phone]["status"] = "flood_wait"
                                            self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                    
                                    # Update account in database
                                    self.account_manager.update_check_time(phone, f"FloodWait {wait_seconds}s")
                                    
                                    # Schedule retry and add remaining groups to pending
                                    remaining_groups = group_links[local_groups_processed:]
                                    self.pending_groups_queue.extend(remaining_groups)
                                    self._schedule_account_retry(phone, wait_seconds)
                                    break
                                else:
                                    self.log_activity_signal.emit(f"[{phone}] ACCOUNT ISSUE: {result['reason']} → AccountIssue.txt")
                            elif error_type == "join_request":
                                self.join_requests.append(link)
                                self.log_activity_signal.emit(f"[{phone}] JOIN REQUEST: {link} → JoinRequest.txt")
                            
                            # Update UI in real-time
                            self.update_result_counts_signal.emit(
                                len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                                len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                            )
                            continue
                        
                        # Valid group - categorize (same logic as regular checker)
                        if result["type"] == "channel":
                            self.channels_only.append(link)
                            self.log_activity_signal.emit(f"[{phone}] 📺 Valid channel: {link}")
                        elif result["type"] == "topic":
                            self.topics_groups.append(link)
                            self.log_activity_signal.emit(f"[{phone}] 💬 Valid topic: {link}")
                        elif result["type"] == "group":
                            # Check filters (same as regular checker)
                            passes_filters = (
                                result["members"] >= min_members and
                                result["last_message_age_hours"] <= min_message_time_hours and
                                result["total_messages"] >= min_total_messages
                            )
                            
                            if passes_filters:
                                self.valid_filtered.append(link)
                                self.log_activity_signal.emit(f"[{phone}] ✅ Valid group (filtered): {link}")
                            else:
                                self.valid_only.append(link)
                                self.log_activity_signal.emit(f"[{phone}] ⚠️ Valid group (no filter): {link}")
                        
                        # Update UI in real-time
                        self.update_result_counts_signal.emit(
                            len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                            len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                        )
                
                except Exception as e:
                    # Handle unexpected errors (same as regular checker)
                    consecutive_errors += 1
                    
                    # Increment global progress counter even for errors (thread-safe)
                    with self.global_progress_lock:
                        self.global_groups_processed += 1
                        current_total = self.global_groups_processed
                    
                    local_groups_processed += 1
                    
                    # Update progress display for errors too
                    self.update_progress_signal.emit(current_total, self.total_groups)
                    
                    # Log the error with progress
                    self.logger.info(f"[INFO] Account {phone} checked group {current_total} of {self.total_groups}: {link}")
                    
                    # Classify as account issue (same as regular checker)
                    with self.results_lock:
                        self.account_issues.append(link)
                        self.log_activity_signal.emit(f"[{phone}] ACCOUNT ISSUE: Error checking {link}: {str(e)} → AccountIssue.txt")
                        
                        # Update UI
                        self.update_result_counts_signal.emit(
                            len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                            len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                        )
                    
                    # If too many consecutive errors, pause account
                    if consecutive_errors >= 5:
                        self.log_activity_signal.emit(f"⚠️ Account {phone} has {consecutive_errors} consecutive errors - pausing for 5 minutes")
                        
                        # Mark account as temporarily unavailable
                        with self.account_states_lock:
                            if phone in self.account_states:
                                self.account_states[phone]["status"] = "error_recovery"
                        
                        # Add remaining groups to pending queue
                        remaining_groups = group_links[local_groups_processed:]
                        self.pending_groups_queue.extend(remaining_groups)
                        
                        # Schedule recovery after 5 minutes
                        self._schedule_account_retry(phone, 300)  # 5 minutes
                        break
                    
                    continue
            
            # Mark account as completed
            with self.account_states_lock:
                if phone in self.account_states and self.account_states[phone]["status"] == "available":
                    self.account_states[phone]["status"] = "completed"
            
            self.log_activity_signal.emit(f"✅ Account {phone} completed: {local_groups_processed} groups processed")
            
        except Exception as e:
            self.log_activity_signal.emit(f"❌ Account {phone} task error: {str(e)}")
            self.logger.error(f"Task thread error for {phone}: {str(e)}")
            
            # Mark account as failed
            with self.account_states_lock:
                if phone in self.account_states:
                    self.account_states[phone]["status"] = "failed"
    
    def _monitor_tasks_thread(self, tasks):
        """Monitor the task threads and update UI when all are done."""
        try:
            # Wait for all tasks to complete
            for task in tasks:
                task["thread"].join()
            
            # Update the UI with results using signals
            with self.results_lock:
                self.update_result_counts_signal.emit(
                    len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                    len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                )
                
                # Save results to files if not stopped
                if not self.task_checker_should_stop:
                    # Use 3-tier system for task checker too
                    self.save_results_3tier(
                        self.valid_filtered.copy(), 
                        self.valid_only.copy(), 
                        self.topics_groups.copy(), 
                        self.channels_only.copy(), 
                        self.invalid_groups.copy(),
                        self.account_issues.copy(),  # Now properly tracked
                        self.join_requests.copy()   # Now properly tracked
                    )
            
            # Update status using signals
            self.update_analyzing_signal.emit("Currently analyzing: None")
            if self.task_checker_should_stop:
                self.update_status_signal.emit("Task-based checking stopped")
                self.log_activity_signal.emit("All tasks stopped")
            else:
                self.update_status_signal.emit("Task-based checking completed")
                self.log_activity_signal.emit("All tasks completed")
            
            # Update accounts table to reflect any changes in status
            self.update_ui_signal.emit()
            
        except Exception as e:
            self.logger.error(f"Task monitoring error: {str(e)}")
            self.log_activity_signal.emit(f"Task monitoring error: {str(e)}")
            self.update_status_signal.emit(f"Task error: {str(e)}")
        finally:
            # Reset state and UI
            self.is_task_checker_running = False
            self.task_checker_should_stop = False
            QMetaObject.invokeMethod(self.start_task_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            QMetaObject.invokeMethod(self.stop_task_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, False))
    
    def _task_reassignment_monitor(self):
        """Monitor for task reassignment when accounts hit FloodWait or errors."""
        while not self.task_checker_should_stop:
            try:
                time.sleep(5)  # Check every 5 seconds
                
                # Check if there are pending groups to reassign
                with self.account_states_lock:
                    if not self.pending_groups_queue:
                        continue
                    
                    # Find available accounts (not in flood_wait, error_recovery, or failed)
                    available_accounts = []
                    for phone, state in self.account_states.items():
                        if state["status"] == "available":
                            available_accounts.append(phone)
                    
                    if available_accounts:
                        # Reassign groups to available accounts
                        groups_to_reassign = self.pending_groups_queue.copy()
                        self.pending_groups_queue.clear()
                        
                        self.log_activity_signal.emit(f"🔄 REASSIGNMENT: Found {len(available_accounts)} available accounts for {len(groups_to_reassign)} groups")
                        
                        # Distribute groups among available accounts
                        groups_per_account = max(1, len(groups_to_reassign) // len(available_accounts))
                        group_index = 0
                        
                        for account_phone in available_accounts:
                            if group_index >= len(groups_to_reassign):
                                break
                            
                            # Calculate groups for this account
                            start_index = group_index
                            end_index = min(group_index + groups_per_account, len(groups_to_reassign))
                            
                            # Add remaining groups to the last account
                            if account_phone == available_accounts[-1]:
                                end_index = len(groups_to_reassign)
                            
                            account_groups = groups_to_reassign[start_index:end_index]
                            
                            if account_groups:
                                self.log_activity_signal.emit(f"➡️ REASSIGNED: {len(account_groups)} groups to account {account_phone}")
                                
                                # Start new task thread for this account
                                task_thread = threading.Thread(
                                    target=self._enhanced_account_task_thread,
                                    args=(account_phone, account_groups, start_index),
                                    daemon=True
                                )
                                task_thread.start()
                                
                                group_index = end_index
                        
                        self.log_activity_signal.emit(f"✅ REASSIGNMENT COMPLETE: All {len(groups_to_reassign)} groups redistributed")
                    
                    else:
                        # No available accounts - check what's happening
                        status_counts = {}
                        for phone, state in self.account_states.items():
                            status = state["status"]
                            status_counts[status] = status_counts.get(status, 0) + 1
                        
                        status_summary = ", ".join([f"{count} {status}" for status, count in status_counts.items()])
                        
                        if self.pending_groups_queue:
                            self.log_activity_signal.emit(f"⏳ WAITING: {len(self.pending_groups_queue)} groups pending | Account Status: {status_summary}")
                        
                        # Check if all accounts are in permanent failure state
                        active_or_recoverable = any(
                            state["status"] in ["available", "flood_wait", "error_recovery"] 
                            for state in self.account_states.values()
                        )
                        
                        if not active_or_recoverable and self.pending_groups_queue:
                            self.log_activity_signal.emit(f"🚨 CRITICAL: All accounts failed permanently - {len(self.pending_groups_queue)} groups cannot be processed")
                            # Move pending groups to account issues
                            with self.results_lock:
                                self.account_issues.extend(self.pending_groups_queue)
                                self.pending_groups_queue.clear()
                                
                                # Update UI
                                self.update_result_counts_signal.emit(
                                    len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                                    len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                                )
                            
                            self.log_activity_signal.emit(f"📋 RECOVERY: {len(self.pending_groups_queue)} unprocessed groups moved to AccountIssue.txt")
                
            except Exception as e:
                self.logger.error(f"Task reassignment monitor error: {str(e)}")
                self.log_activity_signal.emit(f"❌ Reassignment monitor error: {str(e)}")
                
        self.log_activity_signal.emit("🛑 Task reassignment monitor stopped")
    
    def _schedule_account_retry(self, phone, wait_seconds):
        """Schedule an account retry after FloodWait or error recovery period."""
        retry_type = "FloodWait" if wait_seconds >= 300 else "Error Recovery"
        self.log_activity_signal.emit(f"⏰ RETRY SCHEDULED: Account {phone} {retry_type} - will retry in {wait_seconds}s ({wait_seconds//60}m {wait_seconds%60}s)")
        
        def retry_thread():
            try:
                # Use the actual wait time, with minimum safety margins
                if wait_seconds >= 300:  # FloodWait
                    actual_wait = max(wait_seconds, 300)  # Minimum 5 minutes for safety
                else:  # Error recovery
                    actual_wait = max(wait_seconds, 60)   # Minimum 1 minute for error recovery
                
                # Log countdown every minute for longer waits
                if actual_wait > 60:
                    remaining_time = actual_wait
                    while remaining_time > 0 and not self.task_checker_should_stop:
                        if remaining_time % 60 == 0:  # Log every minute
                            minutes = remaining_time // 60
                            self.log_activity_signal.emit(f"⏰ Account {phone} retry in {minutes} minutes")
                        time.sleep(30)  # Check every 30 seconds
                        remaining_time -= 30
                else:
                    time.sleep(actual_wait)
                
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Retry cancelled for account {phone}")
                    return
                
                # Re-enable account
                with self.account_states_lock:
                    if phone in self.account_states:
                        old_status = self.account_states[phone]["status"]
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        
                        self.log_activity_signal.emit(f"🔄 RETRY: Account {phone} recovered from {old_status} → now available")
                
                # Update account status in database
                self.account_manager.update_check_time(phone, "OK - Recovered")
                
                self.log_activity_signal.emit(f"✅ RECOVERY COMPLETE: Account {phone} is ready for new tasks")
                
            except Exception as e:
                self.logger.error(f"Retry thread error for {phone}: {str(e)}")
                self.log_activity_signal.emit(f"❌ Retry error for account {phone}: {str(e)}")
                
                # Mark as failed if recovery fails
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "failed"
        
        # Start retry thread
        threading.Thread(target=retry_thread, daemon=True).start()
    
    def update_log_display(self):
        """Update the log display based on selected filter."""
        try:
            log_type = self.log_type_combo.currentText()
            log_level = self.log_level_combo.currentText()
            
            if log_type == "Authentication":
                # Load authentication logs
                logs = read_auth_log(max_lines=1000)
            elif log_type == "Usage Checker":
                # Load usage checker logs
                logs = read_usage_checker_log(max_lines=1000)
            else:
                # Load general logs
                log_file = os.path.join("logs", "tg_checker.log")
                logs = read_log_file(log_file, max_lines=1000)
            
            # Filter by level if needed (skip for Usage Checker as it has its own format)
            if log_level != "All" and log_type != "Usage Checker":
                logs = filter_logs_by_level(logs, log_level)
            
            # Update the display
            self.log_display.clear()
            for log in logs:
                self.log_display.append(log.strip())
            
            # Scroll to bottom
            scroll_bar = self.log_display.verticalScrollBar()
            scroll_bar.setValue(scroll_bar.maximum())
            
        except Exception as e:
            self.logger.error(f"Error updating log display: {str(e)}")
            self.log_display.clear()
            self.log_display.append(f"Error loading logs: {str(e)}")

    def test_progress_signals(self):
        """Test method to verify progress signals are working."""
        try:
            self.log_activity("🧪 Testing progress signals...")
            
            # Test progress signal
            for i in range(1, 6):
                self.update_progress_signal.emit(i, 5)
                self.log_activity(f"Progress test: {i}/5")
                time.sleep(1)
            
            self.log_activity("✅ Progress signal test completed")
            
        except Exception as e:
            self.logger.error(f"Progress signal test failed: {str(e)}")
            self.log_activity(f"❌ Progress signal test failed: {str(e)}")

    def open_telegram_link(self, username):
        """Open Telegram link when username is clicked."""
        try:
            import webbrowser
            telegram_url = f"https://t.me/{username}"
            webbrowser.open(telegram_url)
            self.log_activity_signal.emit(f"Opened Telegram link: {telegram_url}")
        except Exception as e:
            self.logger.error(f"Failed to open Telegram link for {username}: {str(e)}")
            self.log_activity_signal.emit(f"Failed to open Telegram link for {username}")
    
    def update_monitor_log(self, logs):
        """Update the monitor log with recent logs."""
        try:
            if logs:
                self.monitor_log.clear()
                filtered_logs = []
                
                # Filter out redundant messages
                redundant_messages = ["No accounts were checked"]
                
                for log in logs:
                    if not any(msg in log for msg in redundant_messages):
                        filtered_logs.append(log)
                
                for log in filtered_logs:
                    self.monitor_log.append(log)
        except Exception as e:
            self.logger.error(f"Failed to update monitor log: {str(e)}")

def main():
    app = QApplication(sys.argv)
    window = TGCheckerApp()
    window.show()
    
    # Set up a graceful exit
    app.aboutToQuit.connect(lambda: window.monitor.stop() if window.monitor.is_running else None)
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 