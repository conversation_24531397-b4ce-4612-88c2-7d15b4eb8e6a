
import os
import sys
import json
import time
import re
import asyncio
import logging
import sqlite3
import random
from datetime import datetime
from PyQt5 import QtCore, QtWidgets
from PyQt5.QtWidgets import QMessageBox, QTableWidgetItem, QFileDialog, QMainWindow
from PyQt5.QtCore import QThread, pyqtSignal, QTimer, Qt
import traceback

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("logs/tg_checker.log"), logging.StreamHandler()],
)
logger = logging.getLogger("tg_checker")

# Create logs directory if it doesn't exist
os.makedirs("logs", exist_ok=True)
os.makedirs("data", exist_ok=True)

class ForwarderDatabase:
    """Database for managing forwarding tasks and settings."""
    
    def __init__(self, db_path="data/forwarder.db"):
        self.db_path = db_path
        self.init_db()
    
    def init_db(self):
        """Initialize the database with required tables."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout to prevent database locked errors
            conn.execute("PRAGMA busy_timeout = 30000")
            
            # Create tasks table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS forwarder_tasks (
                    id TEXT PRIMARY KEY,
                    name TEXT,
                    account TEXT,
                    message_link TEXT,
                    target_groups TEXT,
                    source_group TEXT,
                    status TEXT DEFAULT 'ready',
                    last_run TEXT,
                    current_index INTEGER DEFAULT 0,
                    total_groups INTEGER DEFAULT 0,
                    processed_groups INTEGER DEFAULT 0,
                    failed_groups INTEGER DEFAULT 0,
                    delay INTEGER DEFAULT 0,
                    message_filter TEXT DEFAULT '',
                    is_active INTEGER DEFAULT 1
                )
            """)
            
            # Create settings table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS forwarder_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT
                )
            """)
            
            # Create task_groups table for storing target groups for each task
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS task_groups (
                    task_id TEXT,
                    group_link TEXT,
                    processed INTEGER DEFAULT 0,
                    result TEXT,
                    last_processed TEXT,
                    PRIMARY KEY (task_id, group_link)
                )
            """)
            
            conn.commit()
        except Exception as e:
            logger.error(f"Error initializing forwarder database: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def create_task(self, name, source_group, target_groups, message_filter=None, delay=0):
        """Create a new forwarding task."""
        conn = None
        try:
            task_id = f"task_{int(time.time())}_{random.randint(1000, 9999)}"
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout
            conn.execute("PRAGMA busy_timeout = 30000")
            
            # Insert task
            cursor.execute(
                "INSERT INTO forwarder_tasks (id, name, source_group, target_groups, status, message_filter, delay) VALUES (?, ?, ?, ?, ?, ?, ?)",
                (task_id, name, source_group, json.dumps(target_groups), "ready", message_filter or "", delay)
            )
            
            # Insert target groups
            for group in target_groups:
                cursor.execute(
                    "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                    (task_id, group, 0)
                )
            
            conn.commit()
            return task_id
        except Exception as e:
            logger.error(f"Error creating forwarder task: {str(e)}")
            if conn:
                conn.rollback()
            return None
        finally:
            if conn:
                conn.close()
    
    def get_task(self, task_id):
        """Get a task by ID."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout
            conn.execute("PRAGMA busy_timeout = 30000")
            
            cursor.execute("SELECT * FROM forwarder_tasks WHERE id = ?", (task_id,))
            task = cursor.fetchone()
            
            if task:
                columns = [column[0] for column in cursor.description]
                task_dict = dict(zip(columns, task))
                
                # Parse JSON fields
                if task_dict.get("target_groups"):
                    try:
                        task_dict["target_groups"] = json.loads(task_dict["target_groups"])
                    except:
                        task_dict["target_groups"] = []
                
                return task_dict
            return None
        except Exception as e:
            logger.error(f"Error getting forwarder task: {str(e)}")
            return None
        finally:
            if conn:
                conn.close()
    
    def update_task(self, task_id, **kwargs):
        """Update a task with the given parameters."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout
            conn.execute("PRAGMA busy_timeout = 30000")
            
            # Build the update query
            update_fields = []
            update_values = []
            
            for key, value in kwargs.items():
                # Convert JSON fields
                if key == "target_groups" and isinstance(value, list):
                    value = json.dumps(value)
                
                update_fields.append(f"{key} = ?")
                update_values.append(value)
            
            if not update_fields:
                return False
            
            query = f"UPDATE forwarder_tasks SET {', '.join(update_fields)} WHERE id = ?"
            update_values.append(task_id)
            
            cursor.execute(query, update_values)
            conn.commit()
            
            return cursor.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating forwarder task: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()
    
    def get_setting(self, key, default=None):
        """Get a setting value by key."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout
            conn.execute("PRAGMA busy_timeout = 30000")
            
            cursor.execute("SELECT value FROM forwarder_settings WHERE key = ?", (key,))
            result = cursor.fetchone()
            
            if result:
                return result[0]
            return default
        except Exception as e:
            logger.error(f"Error getting forwarder setting: {str(e)}")
            return default
        finally:
            if conn:
                conn.close()
    
    def update_setting(self, key, value):
        """Update or insert a setting."""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Set busy timeout
            conn.execute("PRAGMA busy_timeout = 30000")
            
            # Check if setting exists
            cursor.execute("SELECT 1 FROM forwarder_settings WHERE key = ?", (key,))
            exists = cursor.fetchone() is not None
            
            if exists:
                cursor.execute("UPDATE forwarder_settings SET value = ? WHERE key = ?", (value, key))
            else:
                cursor.execute("INSERT INTO forwarder_settings (key, value) VALUES (?, ?)", (key, value))
            
            conn.commit()
            return True
        except Exception as e:
            logger.error(f"Error updating forwarder setting: {str(e)}")
            if conn:
                conn.rollback()
            return False
        finally:
            if conn:
                conn.close()

class TelegramForwarder(QThread):
    """Handles forwarding messages between Telegram groups."""
    
    log_activity_signal = pyqtSignal(str)
    progress_signal = pyqtSignal(dict)
    
    def __init__(self, client=None):
        super().__init__()
        self.client = client
        self.db = ForwarderDatabase()
        self.current_task = None
        self.running = False
        self.paused = False
        self.last_processed_index = 0
    
    def set_task(self, task):
        """Set the current task to process."""
        self.current_task = task
        self.last_processed_index = task.get('current_index', 0) if task else 0
    
    def run_task(self, task_id):
        """Run a forwarding task by ID."""
        task = self.db.get_task(task_id)
        if task:
            self.set_task(task)
            return self.process_task()
        return {"success": False, "error": "Task not found"}
    
    async def process_task(self):
        """Process the current forwarding task."""
        if not self.current_task:
            self.log_activity_signal.emit("No task selected")
            return {"success": False, "error": "No task selected"}
        
        task_id = self.current_task.get('id')
        source_group = self.current_task.get('source_group')
        target_groups = self.current_task.get('target_groups', [])
        
        if not source_group or not target_groups:
            self.log_activity_signal.emit("Invalid task configuration")
            return {"success": False, "error": "Invalid task configuration"}
        
        self.running = True
        self.paused = False
        
        # Update task status
        self.db.update_task(task_id, status="running", last_run=datetime.now().isoformat())
        
        # Get task progress
        start_index = self.last_processed_index
        total_groups = len(target_groups)
        
        self.log_activity_signal.emit(f"Starting forwarding from {source_group} to {total_groups} groups")
        
        try:
            # Process groups from last index
            for i in range(start_index, total_groups):
                if not self.running or self.paused:
                    break
                
                target_group = target_groups[i]
                self.log_activity_signal.emit(f"Forwarding to group {i+1}/{total_groups}: {target_group}")
                
                # Update progress
                self.db.update_task(task_id, current_index=i)
                self.last_processed_index = i
                
                # Simulate forwarding (replace with actual implementation)
                await asyncio.sleep(2)  # Simulate processing time
                
                # Update progress signal
                progress = {
                    "task_id": task_id,
                    "current": i + 1,
                    "total": total_groups,
                    "target": target_group
                }
                self.progress_signal.emit(progress)
            
            # Update task as completed if not paused
            if self.running and not self.paused:
                self.db.update_task(task_id, status="completed", current_index=total_groups)
                self.log_activity_signal.emit(f"Task completed: {task_id}")
                return {"success": True}
            else:
                self.db.update_task(task_id, status="paused")
                self.log_activity_signal.emit(f"Task paused: {task_id}")
                return {"success": True, "paused": True}
        except Exception as e:
            error_msg = f"Error processing task: {str(e)}"
            self.log_activity_signal.emit(error_msg)
            self.db.update_task(task_id, status="error")
            return {"success": False, "error": error_msg}
        finally:
            self.running = False
    
    def stop(self):
        """Stop the current task."""
        self.running = False
        self.log_activity_signal.emit("Task stopped")
    
    def pause(self):
        """Pause the current task."""
        self.paused = True
        self.log_activity_signal.emit("Task paused")
    
    def resume(self):
        """Resume the current task."""
        self.paused = False
        self.log_activity_signal.emit("Task resumed")
    
    def create_forwarder_task(self, name, source_group, target_groups, message_filter=None, delay=0):
        """Create a new forwarding task."""
        try:
            task_id = self.db.create_task(name, source_group, target_groups, message_filter, delay)
            return {"success": True, "task_id": task_id}
        except Exception as e:
            return {"success": False, "error": str(e)}

class MainWindow(QMainWindow):
    """Main application window."""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TG Checker with Forwarder")
        self.resize(1000, 800)
        
        # Initialize components
        self.init_ui()
        
        # Show a notification that the application is working
        QMessageBox.information(self, "TG Checker", "Application started successfully with forwarding functionality!")
    
    def init_ui(self):
        """Initialize the user interface."""
        # Create a central widget
        central_widget = QtWidgets.QWidget()
        self.setCentralWidget(central_widget)
        
        # Create layout
        layout = QtWidgets.QVBoxLayout(central_widget)
        
        # Add a label
        label = QtWidgets.QLabel("TG Checker with Forwarding is working properly!")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # Add a button
        button = QtWidgets.QPushButton("Close")
        button.clicked.connect(self.close)
        layout.addWidget(button)

# Main application entry point
if __name__ == "__main__":
    # Create application
    app = QtWidgets.QApplication(sys.argv)
    
    # Create and show the main window
    window = MainWindow()
    window.show()
    
    # Start the application event loop
    sys.exit(app.exec_())
