#!/usr/bin/env python3
"""
Script to update the Group Limit column in the TG Checker application.
This script replaces the old account age/fixed limits logic with new real-time
Telegram-based status tracking.
"""

import re

def main():
    # Read the original file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Define pattern to match the old Group Limit section
    old_pattern = re.compile(
        r'# Group Limit\s+daily_limit = account\.get\("daily_group_limit", 0\).*?'
        r'self\.accounts_table\.setItem\(row, 5, group_limit_item\)',
        re.DOTALL
    )
    
    # The new implementation to replace the old one
    new_implementation = '''# Group Limit - Real-time Telegram-based status tracking
                account_status = account.get("status", "").lower()
                disabled_until = account.get("disabled_until")
                
                # Generate group limit text based on real-time Telegram status
                if "banned" in account_status:
                    # Display for permanently restricted accounts
                    limit_text = "Banned"
                    group_limit_item = QTableWidgetItem(limit_text)
                    group_limit_item.setForeground(QColor("red"))
                elif disabled_until:
                    # Display for limited accounts with known reset time
                    try:
                        disabled_datetime = datetime.fromisoformat(disabled_until)
                        if disabled_datetime > datetime.now():
                            # Calculate remaining time for real-time countdown
                            remaining_time = disabled_datetime - datetime.now()
                            hours, remainder = divmod(remaining_time.total_seconds(), 3600)
                            minutes, _ = divmod(remainder, 60)
                            
                            # Format the countdown text
                            if hours > 0:
                                limit_text = f"Limited (resets in {int(hours)}h {int(minutes)}m)"
                            else:
                                limit_text = f"Limited (resets in {int(minutes)}m)"
                            
                            group_limit_item = QTableWidgetItem(limit_text)
                            group_limit_item.setForeground(QColor("orange"))
                        else:
                            # Limit expired
                            limit_text = "Active"
                            group_limit_item = QTableWidgetItem(limit_text)
                            group_limit_item.setForeground(QColor("green"))
                    except:
                        # Fallback if date parsing fails
                        limit_text = "Limited"
                        group_limit_item = QTableWidgetItem(limit_text)
                        group_limit_item.setForeground(QColor("orange"))
                elif "limit" in account_status:
                    # Limited account without known reset time
                    limit_text = "Limited"
                    group_limit_item = QTableWidgetItem(limit_text)
                    group_limit_item.setForeground(QColor("orange"))
                else:
                    # For active accounts that are not limited or banned
                    if status == "Active":
                        limit_text = "Active"
                        group_limit_item = QTableWidgetItem(limit_text)
                        group_limit_item.setForeground(QColor("green"))
                    else:
                        limit_text = "Inactive"
                        group_limit_item = QTableWidgetItem(limit_text)
                        group_limit_item.setForeground(QColor("gray"))
                    
                self.accounts_table.setItem(row, 5, group_limit_item)'''
    
    # Replace the old implementation with the new one
    updated_content = old_pattern.sub(new_implementation, content)
    
    # Check if any replacement was made
    if content == updated_content:
        print("No changes were made - pattern not found.")
        return
    
    # Write the updated content back to the file
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(updated_content)
    
    print("Successfully updated Group Limit column logic in main.py")
    print("The old account age/fixed limits logic has been replaced with real-time Telegram-based status tracking.")

if __name__ == "__main__":
    main() 