def fix_for_loop_indentation():
    print("Fixing for loop indentation in auto_refresh_missing_account_info method...")
    
    # Read the file content
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    # Find the auto_refresh_missing_account_info method
    method_start = None
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            method_start = i
            break
    
    if method_start is None:
        print("Method not found")
        return
    
    # Get the base indentation for the method
    base_indent = " " * (len(lines[method_start]) - len(lines[method_start].lstrip()))
    try_indent = base_indent + "    "  # Inside try block
    block_indent = try_indent + "    "  # Inside for loop
    nested_indent = block_indent + "    "  # Nested blocks
    
    # Find the for loop and fix all indentation within it
    for_loop_start = None
    for i in range(method_start, len(lines)):
        if "for account in accounts:" in lines[i]:
            for_loop_start = i
            # Fix the for loop line itself if needed
            if not lines[i].startswith(try_indent):
                lines[i] = try_indent + lines[i].lstrip()
            break
    
    if for_loop_start is None:
        print("For loop not found")
        return
    
    # Fix indentation of all lines in the for loop
    i = for_loop_start + 1
    inside_nested_block = False
    fixes_made = 0
    
    while i < len(lines):
        line = lines[i].rstrip()
        if not line:  # Skip empty lines
            i += 1
            continue
            
        # Check if we've exited the for loop (dedent or reached end of try block)
        if line.lstrip().startswith("if accounts_needing_refresh:") or line.lstrip().startswith("except "):
            break
            
        # Determine correct indentation level
        if line.lstrip().startswith("if ") or line.lstrip().startswith("elif ") or line.lstrip().startswith("else:"):
            # This is a control structure line - should be at block level
            correct_indent = block_indent
            inside_nested_block = True if line.lstrip().endswith(":") else False
        elif inside_nested_block:
            # Inside a nested if/elif/else block
            correct_indent = nested_indent
        else:
            # Regular line inside for loop
            correct_indent = block_indent
            
        # Apply correct indentation
        if not line.startswith(correct_indent) and line.strip():
            lines[i] = correct_indent + line.lstrip()
            fixes_made += 1
            print(f"Fixed indentation on line {i+1}")
            
        i += 1
    
    # Write back the fixed content
    if fixes_made > 0:
        with open("main.py", "w", encoding="utf-8") as file:
            file.writelines(lines)
        print(f"Applied {fixes_made} indentation fixes to the for loop")
    else:
        print("No indentation issues found in the for loop")

if __name__ == "__main__":
    fix_for_loop_indentation() 