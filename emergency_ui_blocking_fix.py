#!/usr/bin/env python3
"""
🚨 EMERGENCY UI BLOCKING FIX
===========================

This script immediately fixes the UI blocking by replacing the problematic
refresh_joining_tasks() calls with non-blocking versions.
"""

import re

def apply_emergency_fix():
    """Apply emergency fix to eliminate UI blocking."""
    try:
        # Read main.py
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("🚨 Applying EMERGENCY UI blocking fix...")
        
        # Replace the blocking refresh_joining_tasks() call in start_joining_task
        old_refresh_call = "            # Refresh task data after reset\n            self.refresh_joining_tasks()"
        new_refresh_call = "            # Refresh task data after reset (non-blocking)\n            self._refresh_joining_tasks_background()"
        
        if old_refresh_call in content:
            content = content.replace(old_refresh_call, new_refresh_call)
            print("   ✅ Fixed blocking refresh in start_joining_task")
        
        # Add non-blocking refresh method if not present
        if "_refresh_joining_tasks_background" not in content:
            refresh_method = '''
    def _refresh_joining_tasks_background(self):
        """Non-blocking version of refresh_joining_tasks."""
        def background_refresh():
            try:
                conn = sqlite3.connect(self.joining_db_path, timeout=30)
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM joining_tasks ORDER BY created_at DESC")
                tasks = cursor.fetchall()
                
                conn.close()
                
                # Convert to dictionary format
                task_dict = {}
                for task in tasks:
                    task_dict[task[0]] = {
                        'id': task[0],
                        'name': task[1],
                        'account_phone': task[2],
                        'group_links': task[3],
                        'status': task[4],
                        'current_index': task[5],
                        'total_groups': task[6],
                        'successful_joins': task[7],
                        'failed_joins': task[8],
                        'created_at': task[9],
                        'updated_at': task[10],
                        'settings': task[11],
                        'shareable_folder_name': task[12] if len(task) > 12 else '',
                        'shareable_folder_enabled': task[13] if len(task) > 13 else 0
                    }
                
                # Update UI in main thread using QTimer
                def update_ui():
                    try:
                        self.joining_tasks = task_dict
                        self.update_joining_tasks_table(list(task_dict.values()))
                    except Exception as e:
                        print(f"UI update error: {e}")
                
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(0, update_ui)
                
            except Exception as e:
                print(f"Background refresh error: {e}")
        
        # Start in background thread
        import threading
        threading.Thread(target=background_refresh, daemon=True, name="RefreshBg").start()
'''
            
            # Add the method before the last class method
            insert_pos = content.rfind("    def ")
            if insert_pos > 0:
                # Find the end of the previous method
                method_end = content.find("\n    def ", insert_pos + 10)
                if method_end == -1:
                    method_end = len(content) - 1000  # Near end of file
                
                content = content[:method_end] + refresh_method + content[method_end:]
                print("   ✅ Added non-blocking refresh method")
            
        # Write the updated content
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        print("✅ Emergency UI blocking fix applied!")
        return True
        
    except Exception as e:
        print(f"❌ Emergency fix failed: {e}")
        return False

def kill_any_running_process():
    """Kill any running TG Checker process to prevent conflicts."""
    import subprocess
    import psutil
    
    try:
        print("🔍 Checking for running TG Checker processes...")
        
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                cmdline = proc.info['cmdline']
                if cmdline and any('main.py' in arg for arg in cmdline):
                    print(f"   Found running process: PID {proc.info['pid']}")
                    proc.terminate()
                    print(f"   ✅ Terminated process {proc.info['pid']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass
                
    except Exception as e:
        print(f"⚠️ Could not check processes: {e}")

def main():
    """Apply emergency fix immediately."""
    print("🚨 EMERGENCY UI BLOCKING FIX")
    print("=" * 35)
    print()
    
    # Kill any running processes first
    kill_any_running_process()
    
    # Apply the fix
    if apply_emergency_fix():
        print("\n🎉 EMERGENCY FIX APPLIED!")
        print("=" * 30)
        print("✅ The UI blocking should now be eliminated")
        print("🚀 Restart your TG Checker and test the continue button")
        print("   It should respond instantly without freezing")
    else:
        print("\n❌ Emergency fix failed")

if __name__ == "__main__":
    main() 