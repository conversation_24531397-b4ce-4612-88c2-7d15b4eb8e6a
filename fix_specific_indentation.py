import os
import re
import ast

def fix_specific_indentation():
    """Fix specific indentation issues in main.py"""
    print("Fixing specific indentation issues in main.py...")
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
    except Exception as e:
        print(f"Error reading main.py: {str(e)}")
        return False
    
    # Make a backup
    with open("main.py.specific.bak", "w", encoding="utf-8") as f:
        f.writelines(lines)
    
    # Detect and fix the problematic SQL statement block
    # This typically involves cursor.execute with triple-quoted strings
    fixed_lines = []
    i = 0
    while i < len(lines):
        line = lines[i]
        
        # Look for "cursor.execute" followed by triple quotes
        if "cursor.execute" in line and ("'''" in line or '"""' in line):
            # Get current indentation
            indent = len(line) - len(line.lstrip())
            
            # Add this line
            fixed_lines.append(line)
            i += 1
            
            # Process the multi-line SQL string
            sql_lines = []
            triple_quote_type = "'''" if "'''" in line else '"""'
            end_of_sql = False
            
            while i < len(lines) and not end_of_sql:
                sql_line = lines[i]
                
                # Check if this line contains the closing triple quote
                if triple_quote_type in sql_line:
                    end_of_sql = True
                
                # Use consistent indentation for SQL lines
                # We add 4 spaces to the base indent
                stripped_sql = sql_line.lstrip()
                if stripped_sql:
                    sql_lines.append(' ' * (indent + 4) + stripped_sql)
                else:
                    sql_lines.append('\n')
                
                i += 1
            
            # Add the SQL lines
            fixed_lines.extend(sql_lines)
        else:
            # Process normal line
            fixed_lines.append(line)
            i += 1
    
    # Write fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.writelines(fixed_lines)
    
    # Verify if the fix worked
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        ast.parse(content)
        print("✓ Indentation fixed successfully")
        return True
    except SyntaxError as e:
        print(f"✗ Syntax error still exists: {str(e)}")
        
        # If syntax error persists, try a more aggressive approach
        try:
            # Simplistic approach: normalize all indentation to multiples of 4 spaces
            with open("main.py", "r", encoding="utf-8") as f:
                lines = f.readlines()
            
            for i in range(len(lines)):
                stripped = lines[i].lstrip()
                if stripped:
                    indent = len(lines[i]) - len(stripped)
                    if indent % 4 != 0:
                        new_indent = round(indent / 4) * 4
                        lines[i] = ' ' * new_indent + stripped
            
            # Remove any standalone 'try:' without a body
            i = 0
            while i < len(lines) - 1:
                if lines[i].strip() == 'try:' and not lines[i+1].strip():
                    lines[i] = '# Removed empty try: block\n'
                    i += 2
                    while i < len(lines) and (lines[i].strip().startswith('except') or not lines[i].strip()):
                        lines[i] = '# Removed associated except\n' if lines[i].strip() else lines[i]
                        i += 1
                else:
                    i += 1
            
            with open("main.py", "w", encoding="utf-8") as f:
                f.writelines(lines)
            
            print("Applied aggressive indentation normalization")
            
            # Check one more time
            with open("main.py", "r", encoding="utf-8") as f:
                content = f.read()
            ast.parse(content)
            print("✓ Syntax is now valid after aggressive fix")
            return True
        except SyntaxError as e:
            print(f"✗ Aggressive fix failed, syntax error still exists: {str(e)}")
            
            # Last resort: simple whitespace normalization
            with open("main.py", "r", encoding="utf-8") as f:
                content = f.read()
            
            # Replace all weird indentation with standard 4-space indents
            lines = content.split('\n')
            for i in range(len(lines)):
                if lines[i].strip():
                    leading_spaces = len(lines[i]) - len(lines[i].lstrip())
                    spaces_in_units = leading_spaces // 4
                    lines[i] = '    ' * spaces_in_units + lines[i].lstrip()
            
            with open("main.py", "w", encoding="utf-8") as f:
                f.write('\n'.join(lines))
            
            print("Applied simple whitespace normalization as last resort")
            return False
    
    return False

if __name__ == "__main__":
    fix_specific_indentation() 