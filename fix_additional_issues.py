import re
import os
import sys

def fix_additional_issues(filename="main_fixed.py"):
    """Fix additional syntax issues in the fixed file."""
    # Create a backup
    backup_file = f"{filename}.bak"
    if not os.path.exists(backup_file):
        import shutil
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix specific issues
    content = fix_line_56(content)  # Invalid colon after f-string
    
    # Save to a new file
    final_file = "main_final.py"
    with open(final_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed additional issues and saved to {final_file}")
    return final_file

def fix_line_56(content):
    """Fix line 56 with invalid colon after f-string."""
    # Find the problematic line around line 56
    lines = content.split('\n')
    
    for i in range(50, 60):
        if i < len(lines):
            # Look for a line with f-string and ends with a colon
            if "self.logger.info(f" in lines[i] and lines[i].strip().endswith(':'):
                # Remove the colon at the end
                lines[i] = lines[i].rstrip(':')
                print(f"Fixed invalid colon on line {i+1}: {lines[i]}")
    
    # Search for any similar patterns more broadly
    for i in range(len(lines)):
        if "self.logger" in lines[i] and ".info(f" in lines[i] and lines[i].strip().endswith(':'):
            lines[i] = lines[i].rstrip(':')
            print(f"Fixed invalid colon on line {i+1}: {lines[i]}")
    
    return '\n'.join(lines)

if __name__ == "__main__":
    final_file = fix_additional_issues()
    
    # Create a batch file to run the final fixed application
    batch_content = """@echo off
echo Running TG Checker application with all issues fixed...
python main_final.py
pause
"""
    
    with open("run_final_app.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Jarandni programi chakkrawi tawaw...
echo Hamu kishakanmon charesar kird.
python main_final.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Bo zanini ziatr sairi faily log bka.
    pause
)
pause
"""
    
    with open("run_final_app_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print("Created batch files to run the final fixed application.")
    print(f"You can now run the fixed file: python {final_file}")
    print("Or use one of the batch files: run_final_app.bat or run_final_app_kurdish.bat") 