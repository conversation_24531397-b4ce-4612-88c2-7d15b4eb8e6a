#!/usr/bin/env python
"""
This script fixes the db_path issues in all methods in the main.py file.
"""

import os
import re
import shutil
from datetime import datetime

def create_backup(file_path):
    """Create a backup of the file."""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        return False
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_{timestamp}"
    
    try:
        shutil.copy2(file_path, backup_path)
        print(f"Created backup at {backup_path}")
        return True
    except Exception as e:
        print(f"Error creating backup: {str(e)}")
        return False

def fix_db_path_in_method(content, method_name, db_path_reference):
    """Fix db_path reference in a specific method."""
    pattern = f"def {method_name}\\([^)]*\\):[^\\n]*\\n(?:\\s+[^\\n]*\\n)*\\s+(?:with self\\.db_lock:\\s*\\n\\s+)?conn = sqlite3\\.connect\\({db_path_reference}\\)"
    
    replacement_template = f"""def {method_name}(self{{params}}):{{docstring}}
        try:
            with self.db_lock:
                # Get db_path safely
                if not hasattr(self, 'account_manager') or not hasattr(self.account_manager, 'db_path'):
                    if hasattr(self, 'db_path'):
                        db_path = self.db_path
                    else:
                        db_path = "tg_checker.db"
                        # Set the db_path attribute for future use
                        self.db_path = db_path
                else:
                    db_path = self.account_manager.db_path
                
                conn = sqlite3.connect(db_path)"""
    
    # Find all occurrences of the pattern
    matches = re.finditer(pattern, content, re.MULTILINE | re.DOTALL)
    
    # Process each match
    for match in matches:
        # Extract parameters and docstring
        full_match = match.group(0)
        params_match = re.search(r"def " + method_name + r"\(([^)]*)\)", full_match)
        params = params_match.group(1) if params_match else "self"
        params = params.replace("self", "", 1).strip()  # Remove 'self' since we add it in the template
        params = ", " + params if params else ""
        
        # Check for docstring
        docstring_match = re.search(r"def " + method_name + r"\([^)]*\):([^\\n]*\\n\\s+\"\"\"[^\"]*\"\"\")", full_match, re.DOTALL)
        docstring = docstring_match.group(1) if docstring_match else "\n        "
        
        # Create the replacement with extracted parameters and docstring
        replacement = replacement_template.format(params=params, docstring=docstring)
        
        # Replace the occurrence
        content = content.replace(full_match, replacement)
    
    return content

def apply_fixes(file_path):
    """Apply all fixes to the file."""
    if not os.path.exists(file_path):
        print(f"Error: File {file_path} not found")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # List of methods with db_path references
        methods_to_fix = [
            ("load_forwarder_settings", "self.account_manager.db_path"),
            ("save_forwarder_settings", "self.account_manager.db_path"),
            ("update_task_table", "self.account_manager.db_path"),
            ("create_new_task", "self.account_manager.db_path"),
            ("save_new_task", "self.account_manager.db_path"),
            ("start_forwarding_task", "self.account_manager.db_path"),
            ("stop_forwarding_task", "self.account_manager.db_path"),
            ("delete_forwarding_task", "self.account_manager.db_path"),
            ("start_all_forwarding_tasks", "self.account_manager.db_path"),
            ("stop_all_forwarding_tasks", "self.account_manager.db_path"),
            ("continue_all_forwarding_tasks", "self.account_manager.db_path"),
            ("update_task_status", "self.account_manager.db_path"),
            ("add_groups_to_task", "self.account_manager.db_path"),
            ("change_task_message", "self.account_manager.db_path"),
            ("edit_account_forwarder_settings", "self.account_manager.db_path"),
            ("save_account_forwarder_settings", "self.account_manager.db_path"),
            ("validate_session_accounts", "self.db_path"),
        ]
        
        # Apply fixes for each method
        for method_name, db_path_reference in methods_to_fix:
            print(f"Fixing {method_name}...")
            content = fix_db_path_in_method(content, method_name, db_path_reference)
        
        # Write the fixed content back to the file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Successfully applied fixes to {file_path}")
        return True
    
    except Exception as e:
        print(f"Error applying fixes: {str(e)}")
        return False

if __name__ == "__main__":
    file_path = "main.py"
    
    # Create a backup first
    if create_backup(file_path):
        # Apply fixes
        apply_fixes(file_path)
    else:
        print("Aborting fix operation due to backup failure") 