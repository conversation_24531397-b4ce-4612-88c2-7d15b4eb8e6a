@echo off
chcp 65001 > nul
echo TG Checker - ڕێبەری چاککردن / Fix Guides
echo ===================================================
echo 1. Database Fix Guide (چاککردنی داتابەیس)
echo 2. Complete Solution Guide (چارەسەری تەواو)
echo 3. Exit (دەرچوون)
echo.
choice /C 123 /N /M "Select option/هەڵبژاردەیەک هەڵبژێرە (1-3): "

if errorlevel 3 goto End
if errorlevel 2 goto CompleteSolution
if errorlevel 1 goto DatabaseFix

:DatabaseFix
echo Opening Database Fix Guide...
start "" notepad TG_CHECKER_DATABASE_FIX_GUIDE.md
goto Menu

:CompleteSolution
echo Opening Complete Solution Guide...
start "" notepad TG_CHECKER_COMPLETE_SOLUTION.md
goto Menu

:Menu
echo.
echo 1. View the other guide (سەیرکردنی ڕێبەرەکەی تر)
echo 2. Exit (دەرچوون)
echo.
choice /C 12 /N /M "Select option/هەڵبژاردەیەک هەڵبژێرە (1-2): "

if errorlevel 2 goto End
if errorlevel 1 goto ToggleGuide

:ToggleGuide
if exist LAST_VIEWED_GUIDE.tmp (
    set /p last_guide=<LAST_VIEWED_GUIDE.tmp
    if "%last_guide%"=="DATABASE" (
        echo Opening Complete Solution Guide...
        start "" notepad TG_CHECKER_COMPLETE_SOLUTION.md
        echo COMPLETE>LAST_VIEWED_GUIDE.tmp
    ) else (
        echo Opening Database Fix Guide...
        start "" notepad TG_CHECKER_DATABASE_FIX_GUIDE.md
        echo DATABASE>LAST_VIEWED_GUIDE.tmp
    )
) else (
    echo Opening Complete Solution Guide...
    start "" notepad TG_CHECKER_COMPLETE_SOLUTION.md
    echo COMPLETE>LAST_VIEWED_GUIDE.tmp
)
goto Menu

:End
if exist LAST_VIEWED_GUIDE.tmp del LAST_VIEWED_GUIDE.tmp
exit 