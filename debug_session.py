import os
import sys
import logging
from PyQt5.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QDialog, QVBoxLayout, QLabel, QPushButton
from PyQt5.QtCore import Qt

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("session_debug")

def list_session_files():
    """List all session files in the sessions directory."""
    if not os.path.exists("sessions"):
        logger.error("Sessions directory does not exist!")
        return []
    
    sessions = [f for f in os.listdir("sessions") if f.endswith(".session")]
    logger.info(f"Found {len(sessions)} session files: {sessions}")
    return sessions

def check_account_database():
    """Check the database for accounts."""
    try:
        import sqlite3
        conn = sqlite3.connect("tg_checker.db")
        cursor = conn.cursor()
        
        # Check if accounts table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='accounts'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            logger.error("Accounts table does not exist in database!")
            return 0
        
        # Count accounts
        cursor.execute("SELECT COUNT(*) FROM accounts")
        count = cursor.fetchone()[0]
        logger.info(f"Found {count} accounts in database")
        
        # List account phone numbers
        cursor.execute("SELECT phone FROM accounts")
        phones = [row[0] for row in cursor.fetchall()]
        logger.info(f"Account phone numbers: {phones}")
        
        conn.close()
        return count
    except Exception as e:
        logger.error(f"Error checking database: {str(e)}")
        return 0

def check_imports():
    """Check if all required imports are available."""
    missing_imports = []
    
    try:
        import PyQt5
        logger.info(f"PyQt5 version: {PyQt5.QtCore.QT_VERSION_STR}")
    except ImportError:
        missing_imports.append("PyQt5")
    
    try:
        import telethon
        logger.info(f"Telethon version: {telethon.__version__}")
    except ImportError:
        missing_imports.append("telethon")
    
    if missing_imports:
        logger.error(f"Missing imports: {', '.join(missing_imports)}")
        return False
    
    logger.info("All required imports are available")
    return True

class DebugWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TG Checker Debug")
        self.setGeometry(100, 100, 600, 400)
        
        # Create central widget and layout
        central_widget = QDialog()
        layout = QVBoxLayout(central_widget)
        
        # Add debug info
        layout.addWidget(QLabel("<h2>TG Checker Debug Information</h2>"))
        
        # Check imports
        imports_ok = check_imports()
        layout.addWidget(QLabel(f"<b>Required imports:</b> {'OK' if imports_ok else 'MISSING'}"))
        
        # Check sessions
        sessions = list_session_files()
        layout.addWidget(QLabel(f"<b>Session files:</b> {len(sessions)}"))
        for session in sessions:
            layout.addWidget(QLabel(f"• {session}"))
        
        # Check database
        accounts = check_account_database()
        layout.addWidget(QLabel(f"<b>Database accounts:</b> {accounts}"))
        
        # Exit button
        exit_button = QPushButton("Close")
        exit_button.clicked.connect(self.close)
        layout.addWidget(exit_button)
        
        self.setCentralWidget(central_widget)

def main():
    app = QApplication(sys.argv)
    window = DebugWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 