#!/usr/bin/env python3
"""
Fix for the continue_all_joining_tasks function that's causing crashes.
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_continue_all_joining_tasks():
    """Fix the continue_all_joining_tasks function that's causing crashes."""
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix the indentation in the continue_all_joining_tasks function
    # The code has a logical error where the loop over tasks is outside the loop body
    fixed_content = content.replace(
        "def continue_all_joining_tasks(self):\n        \"\"\"🚀 CRASH FIX: Continue all paused joining tasks with staggered resume logic.\"\"\"\n        try:\n            # 🔧 SMART RESUME DETECTION: Find all incomplete tasks (even if marked \"completed\")\n            resumable_tasks = []\n            for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n            \n            # Parse group links",
        "def continue_all_joining_tasks(self):\n        \"\"\"🚀 CRASH FIX: Continue all paused joining tasks with staggered resume logic.\"\"\"\n        try:\n            # 🔧 SMART RESUME DETECTION: Find all incomplete tasks (even if marked \"completed\")\n            resumable_tasks = []\n            for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n                \n                # Parse group links"
    )
    
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(fixed_content)
    
    print("✅ Fixed continue_all_joining_tasks function")

def main():
    print("🚨 EMERGENCY FIX: Resolving continue_all_joining_tasks crash...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fix_continue_all_joining_tasks()
    
    print("\n✅ Fix applied successfully!")
    print("You can now restart the TG Checker application.")

if __name__ == "__main__":
    main() 