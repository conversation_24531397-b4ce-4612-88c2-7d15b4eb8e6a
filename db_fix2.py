"""
Fix SQLite set_busy_timeout compatibility issue in main.py - Version 2
"""
import re

def fix_sqlite_timeout():
    """Fix the 'set_busy_timeout' compatibility issue in main.py"""
    print("Making a backup of main.py...")
    with open("main.py", "r", encoding="utf-8") as file:
        content = file.read()
    
    with open("main.py.bak_db2", "w", encoding="utf-8") as backup:
        backup.write(content)
    
    # Find all occurrences of set_busy_timeout
    pattern = r'conn\.set_busy_timeout\((\d+)\)'
    matches = list(re.finditer(pattern, content))
    
    print(f"Found {len(matches)} occurrences of set_busy_timeout")
    
    # Manual replacement to avoid escape sequence issues
    lines = content.split('\n')
    for match in reversed(matches):  # Process in reverse to keep line numbers valid
        # Get the line number of the match
        line_num = content[:match.start()].count('\n')
        timeout_value = match.group(1)
        
        # Replace the line
        old_line = lines[line_num]
        new_line = old_line.replace(
            f"conn.set_busy_timeout({timeout_value})",
            f"conn.execute(\"PRAGMA busy_timeout = {timeout_value}\")"
        )
        lines[line_num] = new_line
        
        print(f"Replaced line {line_num+1}: {old_line.strip()} -> {new_line.strip()}")
    
    # Write the fixed content back
    with open("main.py", "w", encoding="utf-8") as file:
        file.write('\n'.join(lines))
    
    print("Fixed SQLite compatibility issue in main.py")

if __name__ == "__main__":
    fix_sqlite_timeout() 