#!/usr/bin/env python3
"""
🚨 CRITICAL FILTER ACCURACY FIX
Ensures 100% strict filter compliance - NO EXCEPTIONS
"""

import re

def fix_artificial_activity_bypass():
    """Remove artificial activity modification that bypasses filters."""
    print("🔧 FIXING ARTIFICIAL ACTIVITY BYPASS")
    print("=" * 50)
    
    with open('tg_client.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Comment out the artificial activity modification
    old_pattern = r'''                # Special handling for activity on user-specified groups
                if username\.lower\(\) in user_specified_groups and last_message_age_hours > 1:
                    self\.logger\.info\(f"\[ACTIVITY\] Applying special handling for user-specified group: \{username\}"\)
                    last_message_age_hours = 0\.5  # Make it seem very active \(30 minutes ago\)
                    result\["last_message_age_hours"\] = last_message_age_hours
                    result\["has_recent_activity"\] = True'''
    
    new_pattern = '''                # DISABLED: Artificial activity modification (bypasses user filter settings)
                # if username.lower() in user_specified_groups and last_message_age_hours > 1:
                #     self.logger.info(f"[ACTIVITY] Applying special handling for user-specified group: {username}")
                #     last_message_age_hours = 0.5  # Make it seem very active (30 minutes ago)
                #     result["last_message_age_hours"] = last_message_age_hours
                #     result["has_recent_activity"] = True'''
    
    # Replace the artificial modification
    if "Special handling for activity on user-specified groups" in content:
        content = re.sub(
            r'                # Special handling for activity on user-specified groups\s*\n'
            r'                if username\.lower\(\) in user_specified_groups and last_message_age_hours > 1:\s*\n'
            r'                    self\.logger\.info\(f"\[ACTIVITY\] Applying special handling for user-specified group: \{username\}"\)\s*\n'
            r'                    last_message_age_hours = 0\.5  # Make it seem very active \(30 minutes ago\)\s*\n'
            r'                    result\["last_message_age_hours"\] = last_message_age_hours\s*\n'
            r'                    result\["has_recent_activity"\] = True',
            new_pattern,
            content,
            flags=re.MULTILINE
        )
        
        with open('tg_client.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Disabled artificial activity modification")
        return True
    else:
        print("⚠️ Pattern not found - may already be fixed")
        return False

def enhance_filter_logging():
    """Add enhanced logging to track filter decisions."""
    print("\n🔧 ENHANCING FILTER LOGGING")
    print("=" * 50)
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the filter check section and enhance it
    old_filter_check = '''                        passes_filters = (
                            result["member_count"] >= min_members and
                            result["last_message_age_hours"] <= min_message_time_hours and
                            result["total_messages"] >= min_total_messages
                        )'''
    
    new_filter_check = '''                        # STRICT FILTER CHECK - NO EXCEPTIONS
                        member_check = result["member_count"] >= min_members
                        activity_check = result["last_message_age_hours"] <= min_message_time_hours  
                        message_check = result["total_messages"] >= min_total_messages
                        
                        passes_filters = member_check and activity_check and message_check
                        
                        # LOG FILTER DECISION FOR TRANSPARENCY
                        self.log_activity_signal.emit(f"🔍 FILTER CHECK: {result.get('group_id', 'unknown')}")
                        self.log_activity_signal.emit(f"   Members: {result['member_count']} >= {min_members} = {member_check}")
                        self.log_activity_signal.emit(f"   Activity: {result['last_message_age_hours']:.2f}h <= {min_message_time_hours}h = {activity_check}")
                        self.log_activity_signal.emit(f"   Messages: {result['total_messages']} >= {min_total_messages} = {message_check}")
                        self.log_activity_signal.emit(f"   FINAL: {passes_filters}")'''
    
    if old_filter_check in content:
        content = content.replace(old_filter_check, new_filter_check)
        
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Enhanced filter logging for transparency")
        return True
    else:
        print("⚠️ Filter check pattern not found")
        return False

def fix_folder_separation():
    """Ensure strict separation between Groups_Valid_Filter and Groups_Valid_Only."""
    print("\n🔧 ENSURING STRICT FOLDER SEPARATION")
    print("=" * 50)
    
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find and enhance the folder assignment logic
    old_assignment = '''                        if passes_filters:
                            valid_filtered.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: Groups_Valid_Filter")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID: {link} → GroupsValid_Filter_On.txt")
                        else:
                            valid_only.append(link)
                            self.log_activity_signal.emit(f"   └── Saved to: Groups_Valid_Only")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] ⚠️ Valid group (doesn't pass filters): {link}")'''
    
    new_assignment = '''                        if passes_filters:
                            valid_filtered.append(link)
                            self.log_activity_signal.emit(f"   ✅ PASSES ALL FILTERS → Groups_Valid_Filter")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (FILTERED): {link}")
                        else:
                            valid_only.append(link)
                            self.log_activity_signal.emit(f"   ❌ FAILS FILTERS → Groups_Valid_Only")
                            failed_reasons = []
                            if not member_check:
                                failed_reasons.append(f"members({result['member_count']}<{min_members})")
                            if not activity_check:
                                failed_reasons.append(f"activity({result['last_message_age_hours']:.1f}h>{min_message_time_hours}h)")
                            if not message_check:
                                failed_reasons.append(f"messages({result['total_messages']}<{min_total_messages})")
                            self.log_activity_signal.emit(f"   Reasons: {', '.join(failed_reasons)}")
                            self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (NO FILTER): {link}")'''
    
    if old_assignment in content:
        content = content.replace(old_assignment, new_assignment)
        
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ Enhanced folder separation logic")
        return True
    else:
        print("⚠️ Folder assignment pattern not found")
        return False

def create_masco_test():
    """Create a specific test for MASCO_HACKERS to verify fix."""
    print("\n🧪 CREATING MASCO_HACKERS TEST")
    print("=" * 50)
    
    test_content = '''https://t.me/MASCO_HACKERS'''
    
    with open('masco_test.txt', 'w') as f:
        f.write(test_content)
    
    print("✅ Created masco_test.txt")
    print("📋 Test this specific group to verify the fix")
    print("🎯 Expected: Should go to Groups_Valid_Only if >1 hour activity")
    
    return True

def main():
    """Apply all critical fixes for 100% filter accuracy."""
    print("🚨 CRITICAL FILTER ACCURACY FIX")
    print("=" * 60)
    print("Target: 100% strict filter compliance - NO EXCEPTIONS")
    print("=" * 60)
    
    fixes_applied = 0
    
    # Fix 1: Remove artificial activity bypass
    if fix_artificial_activity_bypass():
        fixes_applied += 1
    
    # Fix 2: Enhance filter logging 
    if enhance_filter_logging():
        fixes_applied += 1
    
    # Fix 3: Fix folder separation
    if fix_folder_separation():
        fixes_applied += 1
    
    # Fix 4: Create test case
    if create_masco_test():
        fixes_applied += 1
    
    print(f"\n🎯 SUMMARY: {fixes_applied}/4 fixes applied")
    
    if fixes_applied >= 3:
        print("✅ CRITICAL FIXES APPLIED SUCCESSFULLY!")
        print("\n📋 NEXT STEPS:")
        print("1. Restart TG Checker")
        print("2. Test with masco_test.txt")
        print("3. Verify MASCO_HACKERS goes to Groups_Valid_Only (if >1h activity)")
        print("4. Check enhanced logging shows exact filter decisions")
        print("\n🎯 RESULT: 100% strict filter compliance restored!")
    else:
        print("⚠️ Some fixes may need manual application")
    
    return fixes_applied >= 3

if __name__ == "__main__":
    main() 