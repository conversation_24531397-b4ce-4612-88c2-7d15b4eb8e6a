#!/usr/bin/env python3
"""
Targeted fix for remaining syntax errors in main.py.
Uses direct string replacement to fix the specific broken patterns.
"""

import re

def targeted_fix():
    """Apply targeted fixes to the remaining broken patterns."""
    
    print("🔧 Applying targeted fix for remaining syntax errors...")
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # More aggressive approach - replace all broken patterns directly
    print("🔧 Replacing all broken f-string patterns...")
    
    # Split into lines for line-by-line processing
    lines = content.split('\n')
    fixed_lines = []
    fixes_applied = 0
    
    for i, line in enumerate(lines):
        original_line = line
        
        # Check for any line containing f.write(f"{link} that doesn't end properly
        if 'f.write(f"{link}' in line:
            # Check if the line is properly terminated
            if not (line.strip().endswith('")') or line.strip().endswith('"))')):
                # This is a broken f-string - replace the entire pattern
                # Find the indentation
                indent = len(line) - len(line.lstrip())
                indentation = ' ' * indent
                
                # Replace with properly formed f-string
                line = indentation + 'f.write(f"{link}\\n")'
                fixes_applied += 1
                print(f"  Fixed line {i+1}: {original_line.strip()}")
        
        # Also check for other broken patterns
        elif 'f.write(f"' in line and ('{link}' in line or '{' in line) and not line.strip().endswith('")'):
            # This might be another broken f-string
            indent = len(line) - len(line.lstrip())
            indentation = ' ' * indent
            line = indentation + 'f.write(f"{link}\\n")'
            fixes_applied += 1
            print(f"  Fixed line {i+1}: {original_line.strip()}")
        
        fixed_lines.append(line)
    
    # Join the lines back
    content = '\n'.join(fixed_lines)
    
    # Additional cleanup for any remaining issues
    print("🔧 Applying additional cleanup...")
    
    # Replace any remaining broken patterns with regex
    content = re.sub(r'f\.write\(f"\{[^}]*\}[^"]*$', r'f.write(f"{link}\\n")', content, flags=re.MULTILINE)
    content = re.sub(r'f\.write\(f"[^"]*\{[^}]*\}[^"]*$', r'f.write(f"{link}\\n")', content, flags=re.MULTILINE)
    
    # Write the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print(f"✅ Applied {fixes_applied} targeted fixes")
    
    return True

def verify_and_retry():
    """Verify the fix and retry if necessary."""
    
    print("🔍 Verifying fix...")
    
    try:
        import py_compile
        py_compile.compile("main.py", doraise=True)
        print("✅ main.py compiles successfully!")
        return True
    except py_compile.PyCompileError as e:
        print(f"❌ Still has errors: {e}")
        
        # If still has errors, try a more aggressive approach
        print("🔧 Applying more aggressive fix...")
        
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Replace ALL f.write patterns with a standardized version
        # This is a brute-force approach
        content = re.sub(
            r'f\.write\(f"[^"]*\{[^}]*\}[^"]*',
            r'f.write(f"{link}\\n")',
            content
        )
        
        # Fix any remaining malformed patterns
        content = re.sub(
            r'f\.write\(f"\{[^}]*\}[^"]*',
            r'f.write(f"{link}\\n")',
            content
        )
        
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(content)
        
        # Try compiling again
        try:
            py_compile.compile("main.py", doraise=True)
            print("✅ main.py compiles successfully after aggressive fix!")
            return True
        except py_compile.PyCompileError as e2:
            print(f"❌ Still has errors after aggressive fix: {e2}")
            return False
    
    return False

if __name__ == "__main__":
    try:
        targeted_fix()
        if verify_and_retry():
            print("\n🎉 SUCCESS: All syntax errors have been fixed!")
            print("✅ main.py now compiles without errors")
            print("🔄 You can now run the TG Checker application")
        else:
            print("\n❌ Some errors may still remain")
    except Exception as e:
        print(f"\n❌ Error during targeted fix: {e}")
        import traceback
        traceback.print_exc() 