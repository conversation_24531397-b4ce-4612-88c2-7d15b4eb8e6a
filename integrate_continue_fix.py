#!/usr/bin/env python3
"""
🎯 AUTOMATIC INTEGRATION: Continue Button Thread Fix
===================================================

This script automatically patches your main.py file to fix the continue button blocking issue.
Run this script once to apply the fix permanently.
"""

import os
import re
import shutil
from datetime import datetime

def create_backup(file_path):
    """Create a backup of the original file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{file_path}.backup_continue_fix_{timestamp}"
    shutil.copy2(file_path, backup_path)
    return backup_path

def patch_main_file():
    """Patch the main.py file with the continue button fix."""
    main_file = "main.py"
    
    if not os.path.exists(main_file):
        print(f"❌ {main_file} not found in current directory")
        return False
    
    print(f"📁 Found {main_file}")
    
    # Create backup
    backup_path = create_backup(main_file)
    print(f"💾 Backup created: {backup_path}")
    
    # Read the main file
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if already patched
    if "apply_continue_button_patch" in content:
        print("✅ Continue button fix already applied!")
        return True
    
    # Read the patch content
    patch_file = "main_continue_button_patch.py"
    if not os.path.exists(patch_file):
        print(f"❌ Patch file {patch_file} not found")
        return False
    
    with open(patch_file, 'r', encoding='utf-8') as f:
        patch_content = f.read()
    
    # Extract the actual patch code (everything after the docstring)
    patch_lines = patch_content.split('\n')
    start_index = 0
    for i, line in enumerate(patch_lines):
        if line.strip() == '"""' and i > 5:  # Find end of docstring
            start_index = i + 1
            break
    
    actual_patch = '\n'.join(patch_lines[start_index:])
    
    # Add the patch to the end of main.py
    patched_content = content + "\n\n" + "# " + "="*60 + "\n"
    patched_content += "# CONTINUE BUTTON THREAD FIX - AUTOMATICALLY APPLIED\n"
    patched_content += "# " + "="*60 + "\n\n"
    patched_content += actual_patch
    
    # Find the TGCheckerApp.__init__ method and add the patch call
    init_pattern = r"(class TGCheckerApp.*?def __init__\(self.*?\):.*?super\(\).__init__\(\))"
    
    def add_patch_call(match):
        init_code = match.group(1)
        # Add the patch call after super().__init__()
        if "apply_continue_button_patch(self)" not in init_code:
            init_code += "\n        \n        # Apply continue button thread fix\n        apply_continue_button_patch(self)"
        return init_code
    
    # Apply the patch call
    patched_content = re.sub(init_pattern, add_patch_call, patched_content, flags=re.DOTALL)
    
    # Write the patched file
    with open(main_file, 'w', encoding='utf-8') as f:
        f.write(patched_content)
    
    print("✅ Main.py successfully patched!")
    print("🚀 Continue button fix integrated!")
    
    return True

def verify_patch():
    """Verify that the patch was applied correctly."""
    main_file = "main.py"
    
    with open(main_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("Patch function exists", "def apply_continue_button_patch" in content),
        ("Patch call added", "apply_continue_button_patch(self)" in content),
        ("Background executor", "_bg_executor" in content),
        ("Non-blocking continue", "continue_all_joining_tasks_non_blocking" in content),
    ]
    
    print("\n🔍 Verifying patch...")
    all_passed = True
    
    for check_name, passed in checks:
        status = "✅" if passed else "❌"
        print(f"   {status} {check_name}")
        if not passed:
            all_passed = False
    
    if all_passed:
        print("\n🎉 Patch verification successful!")
        print("   The continue button will now respond instantly without blocking the UI.")
    else:
        print("\n⚠️ Some verification checks failed.")
    
    return all_passed

def main():
    """Main integration function."""
    print("🚀 Continue Button Thread Fix - Auto Integration")
    print("=" * 55)
    print()
    
    try:
        if patch_main_file():
            if verify_patch():
                print("\n🎯 INTEGRATION COMPLETE!")
                print("=" * 30)
                print("✅ Your TG Checker now has:")
                print("   • Instant continue button response")
                print("   • No more UI freezing")
                print("   • Background threading for all operations")
                print("   • Thread-safe UI updates")
                print()
                print("🚀 Restart your TG Checker to see the improvements!")
                return True
            else:
                print("\n⚠️ Patch applied but verification failed.")
                print("You may need to manually check the integration.")
        else:
            print("\n❌ Failed to apply patch.")
            
    except Exception as e:
        print(f"\n💥 Error during integration: {e}")
        print("Please check the error and try again.")
    
    return False

if __name__ == "__main__":
    main() 