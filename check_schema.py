import sqlite3
import os
import json
from datetime import datetime

def check_database_schema(db_path, db_name="Database"):
    """Check the schema of a database and report any issues"""
    print(f"\n=== Checking {db_name} Schema ({db_path}) ===")
    
    if not os.path.exists(db_path):
        print(f"✗ {db_name} file does not exist at path: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        table_names = [t[0] for t in tables]
        
        print(f"Found {len(table_names)} tables: {', '.join(table_names)}")
        
        # Check schema for each table
        for table in table_names:
            print(f"\nTable: {table}")
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            col_info = []
            for col in columns:
                col_id, name, type_, notnull, default_val, pk = col
                col_info.append({
                    "name": name,
                    "type": type_,
                    "notnull": bool(notnull),
                    "pk": bool(pk)
                })
            
            print(f"  Columns: {len(col_info)}")
            for col in col_info:
                print(f"    - {col['name']} ({col['type']})")
                if col['pk']:
                    print(f"      Primary Key: Yes")
                if col['notnull']:
                    print(f"      Not Null: Yes")
        
        conn.close()
        return True
    except Exception as e:
        print(f"✗ Error checking {db_name} schema: {str(e)}")
        return False

def check_for_specific_issues():
    """Check for specific issues we've fixed in the past"""
    print("\n=== Checking for Previously Fixed Issues ===")
    
    # Check for forwarder table structures
    forwarder_db = 'data/forwarder.db'
    if os.path.exists(forwarder_db):
        try:
            conn = sqlite3.connect(forwarder_db)
            cursor = conn.cursor()
            
            # Check for target_groups column in forwarder_tasks
            cursor.execute("PRAGMA table_info(forwarder_tasks)")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            required_columns = ['target_groups', 'source_group', 'name', 'status', 'current_index']
            missing_columns = [col for col in required_columns if col not in column_names]
            
            if missing_columns:
                print(f"✗ Missing columns in forwarder_tasks: {', '.join(missing_columns)}")
            else:
                print(f"✓ All required columns exist in forwarder_tasks")
            
            conn.close()
        except Exception as e:
            print(f"✗ Error checking forwarder_tasks table: {str(e)}")
    else:
        print(f"✗ Forwarder database not found: {forwarder_db}")

def check_forwarding_methods():
    """Check the presence of key forwarding methods in main.py"""
    print("\n=== Checking Forwarding Methods in main.py ===")
    
    if not os.path.exists("main.py"):
        print("✗ main.py not found")
        return
    
    required_methods = [
        "def set_task(self, task)",
        "def run_task(self, task_id)",
        "async def process_task(self)",
        "def create_forwarder_task("
    ]
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        for method in required_methods:
            if method in content:
                print(f"✓ Found method: {method}")
            else:
                print(f"✗ Missing method: {method}")
    except Exception as e:
        print(f"✗ Error checking main.py: {str(e)}")

def check_if_statements():
    """Check if the incomplete if statements have been fixed"""
    print("\n=== Checking Fixed If Statements ===")
    
    if not os.path.exists("main.py"):
        print("✗ main.py not found")
        return
    
    problematic_patterns = [
        "if t\n",
        "if task\n",
        "0ask else 0",
        " ask else 0"
    ]
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        for pattern in problematic_patterns:
            if pattern in content:
                print(f"✗ Found problematic pattern: '{pattern}'")
            else:
                print(f"✓ No problematic pattern: '{pattern}'")
    except Exception as e:
        print(f"✗ Error checking if statements: {str(e)}")

def run_schema_checks():
    """Run all schema and code checks"""
    print("=== Starting Database Schema and Code Checks ===")
    print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Working directory: {os.getcwd()}")
    
    # Create data directory if it doesn't exist (for sample DBs)
    os.makedirs('data', exist_ok=True)
    
    # Run schema checks
    main_db_path = "data/database.db"
    forwarder_db_path = "data/forwarder.db"
    
    # Sample databases might be created if they don't exist
    if not os.path.exists(main_db_path):
        print(f"Note: {main_db_path} does not exist, application may create it when needed")
    
    if not os.path.exists(forwarder_db_path):
        print(f"Note: {forwarder_db_path} does not exist, application may create it when needed")
    
    # Check for code issues
    check_forwarding_methods()
    check_if_statements()
    
    print("\n=== Schema Check Summary ===")
    print("All checks completed. See above for any issues found.")
    print(f"Check finished at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    run_schema_checks() 