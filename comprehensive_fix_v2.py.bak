import re
import os
import sys
import ast
import traceback

def check_syntax(filename):
    """Check Python file for syntax errors."""
    print(f"Checking syntax in {filename}...")
    try:
        # Try different encodings
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                with open(filename, 'r', encoding=encoding) as file:
                    content = file.read()
                ast.parse(content)
                return []
            except UnicodeDecodeError:
                continue
            except SyntaxError as e:
                return [(e.lineno, str(e))]
        
        # If we get here, none of the encodings worked
        return [(0, f"Cannot decode file with any of the attempted encodings")]
    except Exception as e:
        return [(0, f"Error parsing file: {str(e)}")]

def check_indentation(filename):
    """Check file for consistent indentation."""
    print(f"Checking indentation in {filename}...")
    issues = []
    
    # Try different encodings
    content = None
    lines = None
    for encoding in ['utf-8', 'latin-1', 'cp1252']:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                lines = file.readlines()
            break
        except UnicodeDecodeError:
            continue
    
    if lines is None:
        return [(0, f"Cannot decode file with any of the attempted encodings")]
    
    # Check for mixed tabs and spaces
    has_tabs = any('\t' in line for line in lines)
    has_spaces = any('    ' in line for line in lines)
    if has_tabs and has_spaces:
        issues.append((0, "Mixed tabs and spaces for indentation"))
    
    # Check for consistent indentation patterns
    for i, line in enumerate(lines):
        stripped = line.lstrip()
        if not stripped or stripped.startswith('#'):
            continue
        
        indent = len(line) - len(stripped)
        if indent > 0 and indent % 4 != 0:
            issues.append((i+1, f"Inconsistent indentation: {indent} spaces"))
    
    return issues

def read_file_with_fallback_encoding(filename):
    """Read a file with fallback encodings if utf-8 fails."""
    for encoding in ['utf-8', 'latin-1', 'cp1252']:
        try:
            with open(filename, 'r', encoding=encoding) as file:
                return file.read(), encoding
        except UnicodeDecodeError:
            continue
    return None, None

def check_if_expressions(filename):
    """Check for incomplete if expressions."""
    print(f"Checking conditional expressions in {filename}...")
    issues = []
    
    content, encoding = read_file_with_fallback_encoding(filename)
    if content is None:
        return [(0, f"Cannot decode file with any of the attempted encodings")]
    
    # Look for potential incomplete if expressions
    pattern = r'(\w+\s*=\s*\w+\.get\([^)]+\)\s+if\s+\w+)(?!\s+else)'
    for match in re.finditer(pattern, content):
        line_number = content[:match.start()].count('\n') + 1
        issues.append((line_number, f"Possible incomplete if expression: {match.group(0)}"))
    
    # Look for malformed if expressions
    pattern = r'if\s+\w+\s+else\s+\d+\w+'  # Matches patterns like "if task else 0ask"
    for match in re.finditer(pattern, content):
        line_number = content[:match.start()].count('\n') + 1
        issues.append((line_number, f"Malformed if-else expression: {match.group(0)}"))
    
    return issues

def check_try_blocks(filename):
    """Check for proper try-except blocks."""
    print(f"Checking try-except blocks in {filename}...")
    issues = []
    
    try:
        lines = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                with open(filename, 'r', encoding=encoding) as file:
                    lines = file.readlines()
                break
            except UnicodeDecodeError:
                continue
        
        if lines is None:
            return [(0, f"Cannot decode file with any of the attempted encodings")]
        
        in_try = False
        try_line = 0
        has_except = False
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('try:'):
                in_try = True
                try_line = i + 1
                has_except = False
            elif in_try and stripped.startswith('except'):
                has_except = True
            elif in_try and stripped.startswith('finally:'):
                if not has_except:
                    issues.append((try_line, "Try block with finally but no except clause"))
            elif (in_try and not has_except and 
                  (stripped.startswith('def ') or stripped.startswith('class ') or i == len(lines) - 1)):
                issues.append((try_line, "Try block without except clause"))
                in_try = False
    except Exception as e:
        issues.append((0, f"Error checking try blocks: {str(e)}"))
    
    return issues

def check_for_loops(filename):
    """Check for proper indentation after for loops."""
    print(f"Checking for loops in {filename}...")
    issues = []
    
    try:
        lines = None
        for encoding in ['utf-8', 'latin-1', 'cp1252']:
            try:
                with open(filename, 'r', encoding=encoding) as file:
                    lines = file.readlines()
                break
            except UnicodeDecodeError:
                continue
        
        if lines is None:
            return [(0, f"Cannot decode file with any of the attempted encodings")]
        
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped.startswith('for ') and stripped.endswith(':'):
                # Check next non-empty line for proper indentation
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    j += 1
                
                if j < len(lines):
                    current_indent = len(line) - len(line.lstrip())
                    next_indent = len(lines[j]) - len(lines[j].lstrip())
                    if next_indent <= current_indent:
                        issues.append((i+1, f"For loop without properly indented block"))
    except Exception as e:
        issues.append((0, f"Error checking for loops: {str(e)}"))
    
    return issues

def fix_issues(filename, issues):
    """Fix identified issues in the file."""
    if not issues:
        print(f"No issues to fix in {filename}")
        return
    
    print(f"Fixing {len(issues)} issues in {filename}...")
    
    # Skip binary files or files with encoding issues
    content, encoding = read_file_with_fallback_encoding(filename)
    if content is None:
        print(f"Skipping {filename} due to encoding issues")
        return
    
    # Make a backup of the original file
    backup_file = f"{filename}.bak"
    os.system(f'copy "{filename}" "{backup_file}"')
    
    # Fix incomplete if expressions
    content = re.sub(r'(\w+\s*=\s*\w+\.get\([^)]+\)\s+if\s+\w+)(?!\s+else)', r'\1 else 0', content)
    
    # Fix malformed if expressions like "if task else 0ask else 0"
    content = re.sub(r'if\s+(\w+)\s+else\s+0\w+\s+else\s+0', r'if \1 else 0', content)
    
    # Fix any remaining "ask else 0" fragments
    content = content.replace("0ask else 0", "0")
    content = content.replace("0 ask else 0", "0")
    content = content.replace("if task else 0ask", "if task else 0")
    
    # Write fixed content back to file
    with open(filename, 'w', encoding=encoding) as file:
        file.write(content)
    
    print(f"Fixed issues in {filename}")

def fix_main_py():
    """Specifically fix known issues in main.py."""
    print("Applying specific fixes to main.py...")
    
    filename = "main.py"
    content, encoding = read_file_with_fallback_encoding(filename)
    if content is None:
        print(f"Cannot read main.py with any of the attempted encodings")
        return
    
    # Make a backup
    backup_file = f"{filename}.bak"
    os.system(f'copy "{filename}" "{backup_file}"')
    
    # Fix both occurrences of the incomplete if statement
    content = re.sub(
        r"self\.last_processed_index = task\.get\('current_index', 0\) if t",
        "self.last_processed_index = task.get('current_index', 0) if task else 0",
        content
    )
    
    # Fix any remaining "ask else 0" fragments
    content = content.replace("0ask else 0", "0")
    content = content.replace("0 ask else 0", "0")
    content = content.replace("if task else 0ask", "if task else 0")
    
    # Fix indentation in auto_refresh_missing_account_info
    lines = content.split('\n')
    in_auto_refresh = False
    auto_refresh_indent = 0
    fixed_lines = []
    
    for line in lines:
        if "def auto_refresh_missing_account_info" in line:
            in_auto_refresh = True
            auto_refresh_indent = len(line) - len(line.lstrip())
            fixed_lines.append(line)
        elif in_auto_refresh:
            if line.strip() and not line.isspace():
                stripped_line = line.lstrip()
                # Check if this line starts a new method
                if line.strip().startswith("def ") and len(line) - len(line.lstrip()) == auto_refresh_indent:
                    in_auto_refresh = False
                    fixed_lines.append(line)
                else:
                    # Fix indentation for lines inside auto_refresh_missing_account_info
                    current_indent = len(line) - len(line.lstrip())
                    if "try:" in line.strip():
                        # This is the try line
                        fixed_lines.append(" " * (auto_refresh_indent + 4) + stripped_line)
                    elif "for account_" in line.strip():
                        # This is the for loop line inside try
                        fixed_lines.append(" " * (auto_refresh_indent + 8) + stripped_line)
                    elif "if account_" in line.strip() and current_indent <= auto_refresh_indent + 8:
                        # This is the if statement inside the for loop
                        fixed_lines.append(" " * (auto_refresh_indent + 12) + stripped_line)
                    elif current_indent <= auto_refresh_indent + 12 and in_auto_refresh:
                        # These are the lines inside the if statement
                        fixed_lines.append(" " * (auto_refresh_indent + 16) + stripped_line)
                    else:
                        fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        else:
            fixed_lines.append(line)
    
    # Write the fixed content back
    with open(filename, 'w', encoding=encoding) as file:
        file.write('\n'.join(fixed_lines))
    
    print("Applied specific fixes to main.py")

def fix_all_syntax_issues():
    """Fix all syntax issues in Python files."""
    print("Starting comprehensive code inspection and fixes...")
    
    # First, apply specific fixes to main.py
    fix_main_py()
    
    # Get list of Python files
    python_files = [file for file in os.listdir() if file.endswith('.py') and file != "main.py"]
    
    for filename in python_files:
        # Skip files that are likely to cause issues
        if filename in ['fix.py', 'fix_both_literals.py']:
            print(f"Skipping potentially problematic file: {filename}")
            continue
            
        all_issues = []
        
        try:
            # Collect issues from different checks
            all_issues.extend(check_syntax(filename))
            all_issues.extend(check_indentation(filename))
            all_issues.extend(check_if_expressions(filename))
            all_issues.extend(check_try_blocks(filename))
            all_issues.extend(check_for_loops(filename))
            
            # Sort issues by line number
            all_issues.sort(key=lambda x: x[0])
            
            # Print issues
            if all_issues:
                print(f"\nIssues in {filename}:")
                for line, issue in all_issues:
                    print(f"  Line {line}: {issue}")
                
                # Fix issues
                fix_issues(filename, all_issues)
            else:
                print(f"\nNo issues found in {filename}")
        except Exception as e:
            print(f"Error processing {filename}: {str(e)}")
    
    print("\nAll files checked and fixed.")

def test_application():
    """Test the application to ensure it runs without errors."""
    print("\nTesting application startup...")
    try:
        # Run the application in the background
        os.system("start /B python main.py")
        print("Application started successfully")
        return True
    except Exception as e:
        print(f"Error starting application: {str(e)}")
        return False

if __name__ == "__main__":
    fix_all_syntax_issues()
    test_application() 