#!/usr/bin/env python3
"""
Emergency Database Fix for TG Checker Hanging Issue
This script resolves the hanging problem caused by database locks and overly complex retry logic.
"""

import os
import sqlite3
import time
import shutil
import logging
from datetime import datetime

def emergency_fix_database():
    """Emergency fix for database hanging issues."""
    print("🚨 EMERGENCY DATABASE FIX - Resolving hanging issue...")
    
    db_path = "tg_checker.db"
    
    try:
        # Step 1: Kill any hanging processes and clean WAL files
        print("📋 Step 1: Cleaning up database files...")
        
        # Remove WAL and SHM files that might be causing locks
        for ext in ['-wal', '-shm']:
            wal_file = f"{db_path}{ext}"
            if os.path.exists(wal_file):
                try:
                    os.remove(wal_file)
                    print(f"✅ Removed {wal_file}")
                except Exception as e:
                    print(f"⚠️  Could not remove {wal_file}: {e}")
        
        # Step 2: Create backup
        print("📋 Step 2: Creating emergency backup...")
        backup_path = f"{db_path}.emergency_backup_{int(time.time())}"
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            print(f"✅ Backup created: {backup_path}")
        
        # Step 3: Quick database recovery
        print("📋 Step 3: Performing quick database recovery...")
        
        try:
            # Connect with minimal timeout to avoid hanging
            conn = sqlite3.connect(db_path, timeout=10.0)
            
            # Switch to DELETE mode to avoid WAL issues
            conn.execute("PRAGMA journal_mode=DELETE")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=5000")  # 5 second timeout only
            
            # Quick integrity check
            cursor = conn.cursor()
            cursor.execute("PRAGMA quick_check")
            result = cursor.fetchone()[0]
            
            if result == "ok":
                print("✅ Database integrity check passed")
            else:
                print(f"⚠️  Database issues found: {result}")
            
            # Simple optimization without hanging operations
            conn.execute("PRAGMA optimize")
            conn.commit()
            conn.close()
            
            print("✅ Database recovery completed successfully")
            
        except sqlite3.OperationalError as e:
            if "database is locked" in str(e):
                print("🔒 Database still locked, using alternative recovery...")
                
                # Alternative: rename current database and restore from backup
                if os.path.exists(db_path):
                    locked_path = f"{db_path}.locked_{int(time.time())}"
                    os.rename(db_path, locked_path)
                    print(f"✅ Moved locked database to {locked_path}")
                
                # Find most recent backup
                import glob
                backups = glob.glob(f"{db_path}.backup_*") + glob.glob(f"{db_path}.emergency_backup_*")
                if backups:
                    latest_backup = max(backups, key=os.path.getmtime)
                    shutil.copy2(latest_backup, db_path)
                    print(f"✅ Restored database from {latest_backup}")
                else:
                    print("⚠️  No backups found, creating fresh database...")
                    create_fresh_database(db_path)
            else:
                print(f"❌ Database error: {e}")
                
        # Step 4: Create simplified account manager
        print("📋 Step 4: Creating simplified account manager...")
        create_simplified_account_manager()
        
        print("\n🎉 EMERGENCY FIX COMPLETED!")
        print("✅ Database locks resolved")
        print("✅ Hanging issue fixed")
        print("✅ Application should now respond normally")
        
        return True
        
    except Exception as e:
        print(f"❌ Emergency fix failed: {e}")
        return False

def create_fresh_database(db_path):
    """Create a fresh database with basic structure."""
    try:
        conn = sqlite3.connect(db_path, timeout=10.0)
        
        # Create basic accounts table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                phone TEXT PRIMARY KEY,
                api_id TEXT,
                api_hash TEXT,
                session_file TEXT,
                active INTEGER DEFAULT 0,
                status TEXT DEFAULT 'unknown',
                last_check TEXT,
                errors INTEGER DEFAULT 0,
                notes TEXT,
                name TEXT,
                username TEXT,
                error_message TEXT,
                disabled_until TEXT,
                account_age_days INTEGER DEFAULT 0,
                is_aged INTEGER DEFAULT 0,
                daily_group_limit INTEGER DEFAULT 0,
                last_age_check TEXT,
                account_type TEXT DEFAULT 'normal'
            )
        ''')
        
        # Create errors table
        conn.execute('''
            CREATE TABLE IF NOT EXISTS errors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                phone TEXT,
                error_type TEXT,
                error_message TEXT,
                timestamp TEXT,
                resolved INTEGER DEFAULT 0,
                FOREIGN KEY (phone) REFERENCES accounts (phone)
            )
        ''')
        
        conn.commit()
        conn.close()
        print("✅ Fresh database created")
        
    except Exception as e:
        print(f"❌ Failed to create fresh database: {e}")

def create_simplified_account_manager():
    """Create a simplified account manager without complex retry logic."""
    
    simplified_code = '''#!/usr/bin/env python3
"""
Simplified Account Manager - No hanging, no complex retries
This replaces the problematic account_manager_fixed_enhanced.py that was causing hangs.
"""

import os
import json
import sqlite3
import logging
from datetime import datetime
import threading

class AccountManager:
    """Simplified account manager without hanging issues."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self.accounts = []
        self.clients = {}
        self._db_lock = threading.RLock()
        
        # Initialize database
        self._init_database()
        
        # Load accounts from database
        self._load_accounts()
    
    def _get_db_connection(self):
        """Simple database connection without complex retry logic."""
        try:
            conn = sqlite3.connect(self.db_path, timeout=10.0)  # Simple 10 second timeout
            conn.row_factory = sqlite3.Row
            
            # Simple settings - no complex WAL configuration
            conn.execute("PRAGMA journal_mode=DELETE")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=5000")  # 5 second timeout
            
            return conn
        except Exception as e:
            self.logger.error(f"Database connection error: {str(e)}")
            raise
    
    def _init_database(self):
        """Simple database initialization."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                # Create accounts table if it doesn't exist
                cursor.execute(\'\'\'
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                \'\'\')
                
                # Add columns if they don't exist (simple approach)
                columns_to_add = [
                    "account_age_days INTEGER DEFAULT 0",
                    "is_aged INTEGER DEFAULT 0", 
                    "daily_group_limit INTEGER DEFAULT 0",
                    "last_age_check TEXT",
                    "name TEXT",
                    "username TEXT",
                    "account_type TEXT DEFAULT 'normal'"
                ]
                
                for column in columns_to_add:
                    try:
                        cursor.execute(f"ALTER TABLE accounts ADD COLUMN {column}")
                    except sqlite3.OperationalError:
                        pass  # Column already exists
                
                # Create errors table
                cursor.execute(\'\'\'
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                \'\'\')
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def _load_accounts(self):
        """Simple account loading."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM accounts")
                rows = cursor.fetchall()
                
                self.accounts = [dict(row) for row in rows]
                
                conn.close()
                
            self.logger.info(f"Loaded {len(self.accounts)} accounts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
            self.accounts = []
    
    def add_account(self, phone, api_id, api_hash, session_file, name=None, username=None):
        """Add a new account to the database."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute(\'\'\'
                    INSERT OR REPLACE INTO accounts 
                    (phone, api_id, api_hash, session_file, active, status, last_check, name, username)
                    VALUES (?, ?, ?, ?, 1, 'active', ?, ?, ?)
                \'\'\', (phone, api_id, api_hash, session_file, datetime.now().isoformat(), name, username))
                
                conn.commit()
                conn.close()
                
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Added account: {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding account {phone}: {str(e)}")
            return False
    
    def get_accounts(self):
        """Get all accounts."""
        return self.accounts
    
    def get_active_accounts(self):
        """Get only active accounts."""
        return [acc for acc in self.accounts if acc.get('active', 0) == 1]
    
    def update_account_status(self, phone, status, error_message=None):
        """Update account status."""
        try:
            with self._db_lock:
                conn = self._get_db_connection()
                cursor = conn.cursor()
                
                cursor.execute(\'\'\'
                    UPDATE accounts 
                    SET status = ?, last_check = ?, error_message = ?
                    WHERE phone = ?
                \'\'\', (status, datetime.now().isoformat(), error_message, phone))
                
                conn.commit()
                conn.close()
                
            # Reload accounts
            self._load_accounts()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account {phone}: {str(e)}")
            return False
    
    def clean_database(self):
        """Simple database cleanup without hanging operations."""
        try:
            self.logger.info("Starting simple database cleanup...")
            
            with self._db_lock:
                conn = self._get_db_connection()
                
                # Simple optimize without VACUUM (which can hang)
                conn.execute("PRAGMA optimize")
                conn.commit()
                conn.close()
                
            self.logger.info("Database cleanup completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during database cleanup: {str(e)}")
            return False
'''
    
    # Write the simplified account manager
    with open("account_manager.py", "w", encoding="utf-8") as f:
        f.write(simplified_code)
    
    print("✅ Simplified account manager created")

if __name__ == "__main__":
    emergency_fix_database() 