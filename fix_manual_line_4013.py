#!/usr/bin/env python3
"""
Script to fix the specific syntax error at line 4013 in main.py
"""

import os
import re
import shutil

def fix_line_4013():
    """Fix the syntax error at line 4013 (else: without try block)"""
    input_file = "main.py"
    output_file = "main_fixed.py"
    
    # Create a backup if it doesn't exist
    backup_file = f"{input_file}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Find the problematic area around line 4013
    start_line = max(0, 4013 - 20)
    end_line = min(len(lines), 4013 + 20)
    
    print(f"Examining code around line 4013:")
    for i in range(start_line, end_line):
        print(f"{i+1}: {lines[i].rstrip()}")
    
    # Find the "else:" at line 4013
    if len(lines) >= 4013 and "else:" in lines[4013-1]:
        print(f"Found 'else:' at line 4013: {lines[4013-1].strip()}")
        
        # Look backwards for indentation patterns to figure out what structure this else belongs to
        current_indent = len(lines[4013-1]) - len(lines[4013-1].lstrip())
        
        # Look for the closest if statement with the same indentation level
        matching_if_line = None
        for i in range(4013-2, max(0, 4013-50), -1):
            line = lines[i]
            line_indent = len(line) - len(line.lstrip())
            
            # If we find a line with less indentation, we've gone too far out
            if line_indent < current_indent and line.strip():
                break
                
            # Check for if statement at same indentation level
            if line_indent == current_indent and line.lstrip().startswith("if "):
                matching_if_line = i
                break
        
        if matching_if_line is not None:
            print(f"Found matching 'if' statement at line {matching_if_line+1}: {lines[matching_if_line].strip()}")
            # This else might belong to an if statement, which is valid syntax
            # The issue might be indentation or there's a missing "except" if this was meant to be part of a try block
            
            # Check if there's a try block before this if statement
            try_line = None
            for i in range(matching_if_line-1, max(0, matching_if_line-20), -1):
                if lines[i].lstrip().startswith("try:"):
                    try_line = i
                    break
            
            if try_line is not None:
                print(f"Found 'try:' at line {try_line+1}: {lines[try_line].strip()}")
                # There is a try block before this if-else, check if there's an except block
                
                has_except = False
                for i in range(try_line+1, 4013):
                    if lines[i].lstrip().startswith("except"):
                        has_except = True
                        break
                
                if not has_except:
                    print("This appears to be a try block without an except clause.")
                    print("Converting 'else:' to 'except Exception as e:'")
                    
                    # Replace the else with except
                    lines[4013-1] = lines[4013-1].replace("else:", "except Exception as e:")
                    
                    # Add a logging statement in the except block
                    next_line = lines[4013] if 4013 < len(lines) else ""
                    next_indent = len(next_line) - len(next_line.lstrip()) if next_line else current_indent + 4
                    
                    # Insert a logging statement if the next line doesn't have more indentation
                    if len(next_line) - len(next_line.lstrip()) <= current_indent:
                        indent_str = ' ' * (current_indent + 4)
                        lines.insert(4013, f"{indent_str}self.logger.error(f\"Error: {{str(e)}}\")\n")
                        print(f"Added error logging statement after the except")
            else:
                # No try block found, this else might be orphaned or belong to the if
                # Let's check the structure further
                
                # Look for any unclosed parentheses or other syntax issues
                print("No matching try block found. Let's fix the structure:")
                
                # Check what's inside the else block to decide how to fix it
                else_content = []
                i = 4013
                else_indent = len(lines[4013-1]) - len(lines[4013-1].lstrip())
                
                while i < len(lines) and (not lines[i].strip() or len(lines[i]) - len(lines[i].lstrip()) > else_indent):
                    else_content.append(lines[i])
                    i += 1
                
                if else_content:
                    print(f"Found else block content ({len(else_content)} lines)")
                    
                    # Two options:
                    # 1. This else might need to be wrapped in a try-except block
                    # 2. This else might need to be part of the if statement
                    
                    # For now, let's assume option 1 is more likely given the error
                    indent_str = ' ' * else_indent
                    
                    # Insert a try before the else
                    lines.insert(4013-1, f"{indent_str}try:\n")
                    
                    # Replace the else with except
                    lines[4013] = lines[4013].replace("else:", "except Exception as e:")
                    
                    # Add logging in the except block if needed
                    if not else_content or len(else_content[0]) - len(else_content[0].lstrip()) <= else_indent:
                        except_indent = ' ' * (else_indent + 4)
                        lines.insert(4013+1, f"{except_indent}self.logger.error(f\"Error: {{str(e)}}\")\n")
                    
                    print(f"Added try before the else and converted else to except")
                else:
                    print("Empty else block. Converting to pass in an except block")
                    lines[4013-1] = lines[4013-1].replace("else:", "except Exception as e:")
                    indent_str = ' ' * (current_indent + 4)
                    lines.insert(4013, f"{indent_str}pass  # Handle exception\n")
        else:
            print("No matching if statement found with the same indentation.")
            print("This appears to be an orphaned else statement.")
            
            # Fix by either:
            # 1. Converting to an except
            # 2. Removing it
            # Let's convert to an except, which is safer
            
            # Look backwards for a try block
            try_line = None
            for i in range(4013-2, max(0, 4013-50), -1):
                if "try:" in lines[i]:
                    try_line = i
                    break
            
            if try_line is not None:
                print(f"Found 'try:' at line {try_line+1}. Converting 'else:' to 'except Exception as e:'")
                lines[4013-1] = lines[4013-1].replace("else:", "except Exception as e:")
                
                # Add a logging statement in the except block
                indent = len(lines[4013-1]) - len(lines[4013-1].lstrip())
                indent_str = ' ' * (indent + 4)
                
                # Check if next line has proper indentation for except block
                next_line = lines[4013] if 4013 < len(lines) else ""
                if not next_line.strip() or len(next_line) - len(next_line.lstrip()) <= indent:
                    lines.insert(4013, f"{indent_str}self.logger.error(f\"Error: {{str(e)}}\")\n")
            else:
                print("No try block found. Adding a try block before this else")
                indent = len(lines[4013-1]) - len(lines[4013-1].lstrip())
                indent_str = ' ' * indent
                
                # Add a try block before the else
                lines.insert(4013-1, f"{indent_str}try:\n")
                lines.insert(4013, f"{indent_str}    pass  # Add your code here\n")
                
                # Convert else to except
                lines[4013+1] = lines[4013+1].replace("else:", "except Exception as e:")
    else:
        print(f"Could not find 'else:' at line 4013. The file may have changed.")
        return False
    
    # Write the fixed content
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print(f"Fixed the syntax error and saved to {output_file}")
    print(f"Please review the changes and then replace main.py with the fixed version if it looks good.")
    return True

if __name__ == "__main__":
    success = fix_line_4013()
    
    if success:
        print("You can now run the fixed version with: python main_fixed.py")
        
        # Create a simple batch file to run the fixed version
        with open("run_fixed.bat", "w") as f:
            f.write("""@echo off
echo Running TG Checker with fixed syntax...
python main_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
        
        print("Or run the batch file: run_fixed.bat")
    else:
        print("Fix operation failed or wasn't needed.") 