import re
import os
import sys
import shutil

def fix_all_problems(filename="main.py"):
    """Fix all indentation and syntax issues in the main.py file."""
    # Create a backup
    backup_file = f"{filename}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix each identified problem
    content = fix_line_576(content)     # Expected indented block - start_monitor
    content = fix_line_2676(content)    # 'for' statement indentation error
    content = fix_line_2677(content)    # 'if' statement indentation error
    content = fix_line_3013_3014(content)  # Expected expression and Unexpected indentation
    content = fix_line_3062(content)    # Try statement must have at least one except or finally clause
    content = fix_line_3073_3074(content)  # Unexpected indentation
    content = fix_line_3086(content)    # Expected expression
    
    # Save to a new file
    fixed_file = "main_fixed.py"
    with open(fixed_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed all problems and saved to {fixed_file}")
    return fixed_file

def fix_line_576(content):
    """Fix indentation issue around line 576 - start_monitor not indented."""
    lines = content.split('\n')
    
    # Find the lines around position 575-576
    for i in range(570, 580):
        if i < len(lines):
            if "if self.settings.value(\"auto_start_monitor\"" in lines[i]:
                # Found the if statement, make sure the next line is properly indented
                if i+1 < len(lines) and "self.start_monitor()" in lines[i+1]:
                    # Get the indentation of the if statement
                    if_indent = re.match(r"(\s*)", lines[i]).group(1)
                    # Set proper indentation for the start_monitor line
                    lines[i+1] = if_indent + "    " + lines[i+1].lstrip()
                    
                    # Rejoin content
                    return '\n'.join(lines)
    
    # If specific pattern not found, try a more general approach
    pattern = r"(\s+)if self\.settings\.value\(.*\):\s*\n(\s*)self\.start_monitor\(\)"
    fixed_content = re.sub(pattern, lambda m: f"{m.group(1)}if self.settings.value(\"auto_start_monitor\", False, type=bool):\n{m.group(1)}    self.start_monitor()", content)
    
    return fixed_content

def fix_line_2676(content):
    """Fix the indentation error after 'for' statement on line 2676."""
    # Find the for statement
    pattern = r"(\s+)for\s+.*:\s*\n(\s*)([^\s])"
    
    # Find line 2676 approximately
    line_start = 0
    line_count = 1
    while line_count < 2670 and line_start != -1:
        line_start = content.find('\n', line_start) + 1
        line_count += 1
    
    # Get a chunk of content around line 2676
    line_end = line_start
    for _ in range(20):  # Get about 20 lines
        next_end = content.find('\n', line_end + 1)
        if next_end == -1:
            break
        line_end = next_end
    
    chunk = content[line_start:line_end]
    
    # Look for 'for' statement followed by unindented line
    match = re.search(r"(\s+)(for\s+.*:)\s*\n(\s*)([^\s])", chunk)
    if match:
        indent = match.group(1)
        for_statement = match.group(2)
        next_indent = match.group(3)
        next_line = match.group(4)
        
        # Only fix if the next line doesn't have enough indentation
        if len(next_indent) <= len(indent):
            fixed_chunk = chunk.replace(
                f"{indent}{for_statement}\n{next_indent}{next_line}",
                f"{indent}{for_statement}\n{indent}    {next_line}"
            )
            return content.replace(chunk, fixed_chunk)
    
    # If not found in the chunk, try searching the whole content
    return re.sub(
        pattern,
        lambda m: f"{m.group(1)}for{m.group(2)[3:]}:\n{m.group(1)}    {m.group(3)}",
        content
    )

def fix_line_2677(content):
    """Fix indentation error after 'if' statement on line 2677."""
    # Find line position
    line_start = 0
    line_count = 1
    while line_count < 2675 and line_start != -1:
        line_start = content.find('\n', line_start) + 1
        line_count += 1
    
    # Get a chunk around line 2677
    line_end = line_start
    for _ in range(20):  # Get about 20 lines
        next_end = content.find('\n', line_end + 1)
        if next_end == -1:
            break
        line_end = next_end
    
    chunk = content[line_start:line_end]
    
    # Look for 'if' statement followed by unindented line
    match = re.search(r"(\s+)(if\s+.*:)\s*\n(\s*)([^\s])", chunk)
    if match:
        indent = match.group(1)
        if_statement = match.group(2)
        next_indent = match.group(3)
        next_line = match.group(4)
        
        # Only fix if the next line doesn't have enough indentation
        if len(next_indent) <= len(indent):
            fixed_chunk = chunk.replace(
                f"{indent}{if_statement}\n{next_indent}{next_line}",
                f"{indent}{if_statement}\n{indent}    {next_line}"
            )
            return content.replace(chunk, fixed_chunk)
    
    # If not found in the chunk, try a general approach
    pattern = r"(\s+)(if\s+.*:)\s*\n(\s*)([^\s])"
    return re.sub(
        pattern,
        lambda m: f"{m.group(1)}{m.group(2)}\n{m.group(1)}    {m.group(4)}",
        content
    )

def fix_line_3013_3014(content):
    """Fix expected expression and unexpected indentation around lines 3013-3014."""
    # Find the else statement that might be misaligned
    pattern = r"(\s+)(def\s+.*\):\s*\n(?:.*\n)*?)\s*(else):\s*\n(\s+)"
    
    # Find a properly indented line before it to get correct indentation
    match = re.search(pattern, content)
    if match:
        indent = match.group(1)  # Original function indentation
        function_def = match.group(2)
        else_keyword = match.group(3)
        content_after_else = match.group(4)
        
        # Fix the else indentation - ensure it has same indentation as def + 4 spaces
        fixed_content = content.replace(
            f"{function_def}{else_keyword}:",
            f"{function_def}{indent}    {else_keyword}:"
        )
        return fixed_content
    
    # If not found with the specific pattern, try a more general approach
    return re.sub(
        r"(\n)(\s*)(else|elif|except|finally)([^\n]*)\n(\s+)",
        lambda m: f"{m.group(1)}{' ' * (len(m.group(5)) - 4)}{m.group(3)}{m.group(4)}\n{m.group(5)}", 
        content
    )

def fix_line_3062(content):
    """Fix try statement without except/finally around line 3062."""
    # Find try statements without except/finally
    pattern = r"(\s+)(try:\s*\n(?:.*\n)*?)(?!\1\s*except|\1\s*finally)"
    
    # Find the problematic try block
    match = re.search(pattern, content)
    if match:
        indent = match.group(1)
        try_block = match.group(2)
        
        # Add an except block
        fixed_content = content.replace(
            f"{indent}try:{try_block}",
            f"{indent}try:{try_block}{indent}except Exception as e:\n{indent}    self.log_activity(f\"Error: {{str(e)}}\")\n"
        )
        return fixed_content
    
    return content

def fix_line_3073_3074(content):
    """Fix unexpected indentation around lines 3073-3074."""
    # Find lines that might be misaligned
    lines = content.split('\n')
    
    # Check lines around 3073-3074
    for i in range(3070, 3080):
        if i < len(lines) and i > 0:
            current_line = lines[i]
            prev_line = lines[i-1]
            
            # Look for lines that should be indented but aren't
            if prev_line.strip().endswith(":") and current_line.strip() and not re.match(r"\s+", current_line):
                # Get the indentation of the previous line
                prev_indent = re.match(r"(\s*)", prev_line).group(1)
                # Fix indentation
                lines[i] = prev_indent + "    " + current_line.lstrip()
    
    return '\n'.join(lines)

def fix_line_3086(content):
    """Fix expected expression issues around line 3086."""
    # Look for incomplete statements that are missing a colon
    lines = content.split('\n')
    
    # Check lines around 3086
    for i in range(3080, 3090):
        if i < len(lines):
            if re.match(r"\s+(if|else|elif|except|finally|while|for|try)\s.*[^:]\s*$", lines[i]):
                # Add missing colon
                lines[i] = lines[i].rstrip() + ":"
    
    # Join back lines
    content_with_fixed_lines = '\n'.join(lines)
    
    # General pattern for finding incomplete statements
    pattern = r"(\s+)(if|else|elif|except|finally|while|for|try)\s+([^:\n]*[^\s:\n])(\s*)$"
    
    # Apply general fixes for any remaining incomplete statements
    return re.sub(
        pattern,
        lambda m: f"{m.group(1)}{m.group(2)} {m.group(3)}:{m.group(4)}",
        content_with_fixed_lines,
        flags=re.MULTILINE
    )

if __name__ == "__main__":
    fixed_file = fix_all_problems()
    
    # Create a batch file to run the fixed application
    batch_content = """@echo off
echo Running fixed TG Checker application...
python main_fixed.py
pause
"""
    
    with open("run_fixed_app.bat", "w") as f:
        f.write(batch_content)
    
    print("Created run_fixed_app.bat to run the fixed application.")
    print(f"You can now run the fixed file: python {fixed_file} or use the batch file.") 