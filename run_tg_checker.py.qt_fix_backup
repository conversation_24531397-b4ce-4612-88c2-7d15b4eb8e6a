import sys
import os
import traceback
import logging
import subprocess
from datetime import datetime

# Set up logging
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"tg_checker_run_{datetime.now().strftime('%Y-%m-%d_%H-%M-%S')}.log")
logging.basicConfig(
    filename=log_file,
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def run_application():
    """Run the TG Checker application with error handling."""
    try:
        logging.info("Starting TG Checker application...")
        
        # Try to use the most recent fixed file
        app_files = [
            "main_indentation_fixed.py",    # Most recent specific fix
            "main_specific_fixed.py",       # Specific fixes
            "main_comprehensive_fixed.py",  # General fixes
            "main_fixed_final.py",          # Clean implementation
            "main.py"                       # Original file as fallback
        ]
        
        app_file = None
        for file in app_files:
            if os.path.exists(file):
                app_file = file
                logging.info(f"Found {app_file}, will use this file")
                break
        
        if not app_file:
            logging.error("No valid application file found")
            print("Error: No valid application file found")
            return 1
        
        logging.info(f"Running {app_file}...")
        
        # Run the application using subprocess to capture output
        process = subprocess.Popen(
            [sys.executable, app_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # Wait for the process to complete or user to close it
        stdout, stderr = process.communicate()
        
        # Log the output
        if stdout:
            logging.info(f"Application stdout: {stdout}")
        
        # Log any errors
        if stderr:
            logging.error(f"Application stderr: {stderr}")
        
        # Log exit code
        logging.info(f"Application exited with code: {process.returncode}")
        
        return process.returncode
        
    except Exception as e:
        logging.error(f"Error running TG Checker: {str(e)}")
        logging.error(traceback.format_exc())
        print(f"Error running TG Checker: {str(e)}")
        print("See log file for details:", log_file)
        return 1

if __name__ == "__main__":
    print("Starting TG Checker application...")
    print(f"Logging to: {log_file}")
    
    try:
        exit_code = run_application()
        
        if exit_code != 0:
            print(f"Application exited with code: {exit_code}")
            print("Check the log file for details:", log_file)
        
    except KeyboardInterrupt:
        print("\nApplication stopped by user.")
        logging.info("Application stopped by user (KeyboardInterrupt)")
    
    print("Exiting run script.") 