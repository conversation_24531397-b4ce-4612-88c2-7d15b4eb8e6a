#!/usr/bin/env python3
"""
Script to fix duplicated code blocks in main.py
"""

import re

def main():
    # Read the original file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 1. Remove the duplicate Enabled/Disabled status block
    duplicate_status_pattern = r'''# Enabled/Disabled status \(separate from Telegram status\)
                enabled_status = "Enabled" if account\.get\("active", False\) else "Disabled"
                enabled_item = QTableWidgetItem\(enabled_status\)
                
                if enabled_status == "Enabled":
                    enabled_item\.setForeground\(QColor\("green"\)\)
                else:
                    enabled_item\.setForeground\(QColor\("red"\)\)
                
                self\.accounts_table\.setItem\(row, 2, enabled_item\)'''
    
    # Replace with empty string to remove the duplicated block
    content = re.sub(duplicate_status_pattern, '', content, flags=re.DOTALL)
    
    # 2. Remove the duplicate toggle button creation block
    duplicate_button_pattern = r'''# Create buttons with specific colors as requested
                if account\.get\("active", False\):
                    toggle_button = QPushButton\("Disable"\)
                    toggle_button\.setStyleSheet\("background-color: #ffaaaa; color: black;"\)
                else:
                    toggle_button = QPushButton\("Enable"\)
                    toggle_button\.setStyleSheet\("background-color: #88cc88; color: black;"\)

'''
    
    # Replace with empty string to remove the duplicated block
    content = re.sub(duplicate_button_pattern, '', content, flags=re.DOTALL)
    
    # Write the updated content
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Duplicated code blocks have been removed.")

if __name__ == "__main__":
    main() 