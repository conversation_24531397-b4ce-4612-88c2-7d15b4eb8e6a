#!/usr/bin/env python3
"""
START TG CHECKER - CRASH PROOF EDITION
This script starts TG Checker with comprehensive protection against crashes and UI freezing.

🛡️ PROTECTIONS INCLUDED:
- Emergency UI freeze fix (non-blocking joining operations)
- Comprehensive crash prevention system
- Global exception handling
- Resource management and cleanup
- Database connection safety
- Async operation protection
"""

import sys
import os
import traceback
import time
from PyQt5.QtWidgets import QApplication, QMessageBox

def show_startup_info():
    """Show startup information"""
    print("🚀 TG CHECKER - CRASH PROOF EDITION")
    print("=" * 60)
    print("🛡️ PROTECTIONS ACTIVE:")
    print("   ✅ UI Freeze Prevention")
    print("   ✅ Crash Prevention System")
    print("   ✅ Emergency Exception Handling")
    print("   ✅ Resource Management")
    print("   ✅ Database Safety")
    print("   ✅ Async Operation Protection")
    print("=" * 60)

def apply_comprehensive_fixes(window):
    """Apply all fixes to prevent crashes and freezing"""
    fixes_applied = []
    
    # 1. Apply UI Freeze Fix
    try:
        from emergency_ui_freeze_fix import apply_emergency_ui_freeze_fix
        ui_fix = apply_emergency_ui_freeze_fix(window)
        fixes_applied.append("✅ UI Freeze Fix")
        print("✅ UI Freeze Fix applied - joining operations are now non-blocking")
    except Exception as e:
        fixes_applied.append(f"❌ UI Freeze Fix failed: {e}")
        print(f"⚠️ UI Freeze Fix failed: {e}")
    
    # 2. Apply Crash Prevention
    try:
        from crash_prevention_system import apply_crash_prevention
        crash_handler = apply_crash_prevention(window)
        fixes_applied.append("✅ Crash Prevention System")
        print("✅ Crash Prevention System applied - TG Checker is now crash-proof")
    except Exception as e:
        fixes_applied.append(f"❌ Crash Prevention failed: {e}")
        print(f"⚠️ Crash Prevention failed: {e}")
    
    # 3. Apply Additional Safety Measures
    try:
        apply_additional_safety_measures(window)
        fixes_applied.append("✅ Additional Safety Measures")
        print("✅ Additional safety measures applied")
    except Exception as e:
        fixes_applied.append(f"❌ Additional Safety failed: {e}")
        print(f"⚠️ Additional safety measures failed: {e}")
    
    return fixes_applied

def apply_additional_safety_measures(window):
    """Apply additional safety measures"""
    
    # Set up memory monitoring
    import gc
    gc.set_debug(gc.DEBUG_UNCOLLECTABLE)
    
    # Enable automatic garbage collection
    gc.enable()
    
    # Set up periodic cleanup
    from PyQt5.QtCore import QTimer
    cleanup_timer = QTimer()
    cleanup_timer.timeout.connect(lambda: gc.collect())
    cleanup_timer.start(60000)  # Clean up every minute
    window.cleanup_timer = cleanup_timer
    
    # Set lower thread limits to prevent resource exhaustion
    import threading
    threading.stack_size(32768)  # 32KB stack size instead of default
    
    print("🧹 Automatic cleanup and resource management enabled")

def main():
    """Main startup function with comprehensive protection"""
    
    show_startup_info()
    
    try:
        # Create Qt application with error handling
        app = QApplication(sys.argv)
        app.setQuitOnLastWindowClosed(True)
        
        print("📥 Importing TG Checker...")
        
        # Import with protection
        try:
            from main import TGCheckerApp
        except ImportError as e:
            print(f"❌ Failed to import TG Checker: {e}")
            print("❌ Make sure main.py is in the same directory")
            input("Press Enter to exit...")
            sys.exit(1)
        
        print("🏗️ Creating TG Checker window...")
        
        # Create window with protection
        try:
            window = TGCheckerApp()
        except Exception as e:
            print(f"❌ Failed to create TG Checker window: {e}")
            print(f"❌ Error details: {traceback.format_exc()}")
            
            # Show error dialog
            try:
                QMessageBox.critical(None, "TG Checker Startup Error", 
                    f"Failed to create TG Checker window:\n\n{e}\n\nCheck console for details.")
            except:
                pass
            
            input("Press Enter to exit...")
            sys.exit(1)
        
        print("🛡️ Applying comprehensive protection...")
        
        # Apply all fixes
        fixes_applied = apply_comprehensive_fixes(window)
        
        # Show fix results
        print("=" * 60)
        print("🛡️ PROTECTION STATUS:")
        for fix in fixes_applied:
            print(f"   {fix}")
        print("=" * 60)
        
        print("🖥️ Showing TG Checker window...")
        window.show()
        
        # Final success message
        print("🎉 TG CHECKER STARTED SUCCESSFULLY!")
        print("=" * 60)
        print("🔥 CRITICAL INFORMATION:")
        print("   🎯 Joining operations are NON-BLOCKING")
        print("   🛡️ Crash prevention is ACTIVE")
        print("   ⚡ UI will remain RESPONSIVE")
        print("   🚀 You can now click 'Start All Tasks' safely!")
        print("=" * 60)
        
        # Run the application
        return app.exec_()
        
    except Exception as error:
        print(f"🚨 CRITICAL STARTUP ERROR: {error}")
        print(f"🚨 Full traceback: {traceback.format_exc()}")
        
        # Try to show error dialog
        try:
            QMessageBox.critical(None, "Critical TG Checker Error", 
                f"Critical startup error:\n\n{error}\n\nTG Checker cannot start. Check console for full details.")
        except:
            pass
        
        print("🚨 TG Checker failed to start - check the error above")
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Startup interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"\n🚨 UNEXPECTED ERROR: {e}")
        print(f"🚨 Traceback: {traceback.format_exc()}")
        input("Press Enter to exit...")
        sys.exit(1) 