#!/usr/bin/env python3
"""
PATCH_SAVE_FUNCTION - Fix for TG Checker Main Save Function
Patches the save_results_to_exact_folders function in main.py
"""

import os
import re
from datetime import datetime

def patch_main_py():
    """Patch the main.py file to fix the filter logic and save function"""
    
    print("Starting to patch main.py file...")
    
    # Read the file
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix the filter logic - Change the strict requirement for activity
    old_filter_logic = """                        # STRICT FILTER CHECK - NO EXCEPTIONS
                        member_check = result["member_count"] >= min_members
                        activity_check = result["last_message_age_hours"] <= min_message_time_hours  
                        message_check = result["total_messages"] >= min_total_messages
                        
                        passes_filters = member_check and activity_check and message_check"""
    
    new_filter_logic = """                        # UPDATED FILTER CHECK - Only members and total messages matter
                        member_check = result["member_count"] >= min_members
                        message_check = result["total_messages"] >= min_total_messages
                        
                        # Activity is logged but NOT used in filter decision
                        activity_check = result["last_message_age_hours"] <= min_message_time_hours  
                        
                        # CRITICAL FIX: Updated filter logic to ONLY check member count and total messages
                        # Activity time is NOT a filter criterion as per user requirements
                        passes_filters = member_check and message_check"""
    
    # Replace the filter logic
    patched_content = content.replace(old_filter_logic, new_filter_logic)
    
    # Also update the filter failure messages to be accurate
    old_failure_message = """                            if passes_filters:
                                valid_filtered.append(link)
                                self.log_activity_signal.emit(f"   ✅ PASSES ALL FILTERS → Groups_Valid_Filter")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (FILTERED): {link}")
                            else:
                                valid_only.append(link)
                                self.log_activity_signal.emit(f"   ❌ FAILS FILTERS → Groups_Valid_Only")
                                failed_reasons = []
                                if not member_check:
                                    failed_reasons.append(f"members({result['member_count']}<{min_members})")
                                if not activity_check:
                                    failed_reasons.append(f"activity({result['last_message_age_hours']:.1f}h>{min_message_time_hours}h)")
                                if not message_check:
                                    failed_reasons.append(f"messages({result['total_messages']}<{min_total_messages})")
                                self.log_activity_signal.emit(f"   Reasons: {', '.join(failed_reasons)}")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (NO FILTER): {link}")"""
    
    new_failure_message = """                            if passes_filters:
                                valid_filtered.append(link)
                                self.log_activity_signal.emit(f"   ✅ PASSES ALL FILTERS → Groups_Valid_Filter")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (FILTERED): {link}")
                            else:
                                valid_only.append(link)
                                self.log_activity_signal.emit(f"   ❌ FAILS FILTERS → Groups_Valid_Only")
                                failed_reasons = []
                                if not member_check:
                                    failed_reasons.append(f"members({result['member_count']}<{min_members})")
                                # Activity is no longer a filter criterion, but still log it for information
                                # if not activity_check:
                                #     failed_reasons.append(f"activity({result['last_message_age_hours']:.1f}h>{min_message_time_hours}h)")
                                if not message_check:
                                    failed_reasons.append(f"messages({result['total_messages']}<{min_total_messages})")
                                self.log_activity_signal.emit(f"   Reasons: {', '.join(failed_reasons)}")
                                self.log_activity_signal.emit(f"[GROUP #{actual_group_number}] VALID (NO FILTER): {link}")"""
    
    # Replace the failure message
    patched_content = patched_content.replace(old_failure_message, new_failure_message)
    
    # Create improved save function
    improved_save_function = """
    def save_results_to_exact_folders(self, valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests):
        \"\"\"Save results to EXACT folder paths as specified by user with 100% reliability.\"\"\"
        try:
            # Create exact folder structure as requested
            base_path = "Results"
            
            # EXACT folder structure as specified by user
            required_folders = {
                "Groups_Valid_Filter": os.path.join(base_path, "Groups_Valid_Filter"),
                "Groups_Valid_Only": os.path.join(base_path, "Groups_Valid_Only"),
                "Topics_Groups_Only_Valid": os.path.join(base_path, "Topics_Groups_Only_Valid"), 
                "Channels_Only_Valid": os.path.join(base_path, "Channels_Only_Valid"),
                "Invalid_Groups_Channels": os.path.join(base_path, "Invalid_Groups_Channels"),
                "Account_Issues": os.path.join(base_path, "Account_Issues")
            }
            
            # Create all required directories with verification
            for folder_name, folder_path in required_folders.items():
                try:
                    os.makedirs(folder_path, exist_ok=True)
                    if os.path.exists(folder_path):
                        self.log_activity_signal.emit(f"📁 ✅ Verified: {folder_name}")
                    else:
                        raise Exception(f"Failed to create {folder_path}")
                except Exception as e:
                    self.logger.error(f"⚠️ Failed to create {folder_path}: {e}")
                    self.log_activity_signal.emit(f"⚠️ Failed to create {folder_path}: {e}")
                    return False
            
            # Convert to lists and get exact counts
            valid_filtered_list = list(valid_filtered) if valid_filtered else []
            valid_only_list = list(valid_only) if valid_only else []
            topics_groups_list = list(topics_groups) if topics_groups else []
            channels_only_list = list(channels_only) if channels_only else []
            invalid_groups_list = list(invalid_groups) if invalid_groups else []
            account_issues_list = list(account_issues) if account_issues else []
            
            # Define exact filenames
            filenames = {
                "Groups_Valid_Filter": "GroupsValidFilter.txt",
                "Groups_Valid_Only": "GroupsValidOnly.txt",
                "Topics_Groups_Only_Valid": "TopicsGroups.txt", 
                "Channels_Only_Valid": "Channels.txt",
                "Invalid_Groups_Channels": "InvalidGroups.txt",
                "Account_Issues": "AccountIssues.txt"
            }
            
            # Create save configuration
            save_configs = [
                {
                    "data": valid_filtered_list,
                    "path": os.path.join(required_folders["Groups_Valid_Filter"], filenames["Groups_Valid_Filter"]),
                    "description": "Valid groups that passed filters (Filter ON)"
                },
                {
                    "data": valid_only_list,
                    "path": os.path.join(required_folders["Groups_Valid_Only"], filenames["Groups_Valid_Only"]),
                    "description": "Valid groups (no filter applied)"
                },
                {
                    "data": topics_groups_list,
                    "path": os.path.join(required_folders["Topics_Groups_Only_Valid"], filenames["Topics_Groups_Only_Valid"]),
                    "description": "Valid topic groups"
                },
                {
                    "data": channels_only_list,
                    "path": os.path.join(required_folders["Channels_Only_Valid"], filenames["Channels_Only_Valid"]),
                    "description": "Valid channels"
                },
                {
                    "data": invalid_groups_list,
                    "path": os.path.join(required_folders["Invalid_Groups_Channels"], filenames["Invalid_Groups_Channels"]),
                    "description": "Invalid groups/channels"
                },
                {
                    "data": account_issues_list,
                    "path": os.path.join(required_folders["Account_Issues"], filenames["Account_Issues"]),
                    "description": "Account issues"
                }
            ]
            
            # IMPROVED - Save each category with direct file open/write/close
            total_saved = 0
            for config in save_configs:
                data = config["data"]
                file_path = config["path"]
                description = config["description"]
                
                try:
                    with open(file_path, "w", encoding="utf-8") as f:
                        if data:
                            for item in data:
                                f.write(f"{item}\\n")
                            
                    total_saved += len(data)
                    # Verify the file was created
                    if os.path.exists(file_path):
                        self.log_activity_signal.emit(f"✅ Saved {len(data)} {description} to {os.path.basename(file_path)}")
                    else:
                        self.log_activity_signal.emit(f"⚠️ Warning: File not created for {description}")
                        
                except Exception as e:
                    self.logger.error(f"Error saving {description}: {e}")
                    self.log_activity_signal.emit(f"❌ Error saving {description}: {e}")
            
            # Create verification file
            verification_path = os.path.join(base_path, "VERIFICATION.txt")
            
            try:
                with open(verification_path, "w", encoding="utf-8") as f:
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    f.write(f"SAVE VERIFICATION\\n")
                    f.write(f"Timestamp: {timestamp}\\n")
                    f.write(f"Total saved: {total_saved}\\n\\n")
                    
                    for config in save_configs:
                        data = config["data"]
                        f.write(f"{config['description']}: {len(data)}\\n")
                
                self.log_activity_signal.emit(f"✅ Created verification file")
            except Exception as e:
                self.logger.error(f"Could not create verification file: {e}")
            
            self.log_activity_signal.emit(f"✅ Successfully saved {total_saved} items")
            return True
            
        except Exception as e:
            self.logger.error(f"Critical save error: {str(e)}")
            self.log_activity_signal.emit(f"❌ CRITICAL: Save failed: {str(e)}")
            return False
    """
    
    # Find the position of the save_results_to_exact_folders method
    pattern = r'def save_results_to_exact_folders\(.*?\):'
    match = re.search(pattern, patched_content, re.DOTALL)
    
    if match:
        method_start = match.start()
        
        # Find method end (next def or class)
        next_method = re.search(r'\n\s*def [a-zA-Z_]+\(.*?\):', patched_content[method_start+1:], re.DOTALL)
        if next_method:
            method_end = method_start + next_method.start() + 1
        else:
            # Assume the method extends to the end of the file
            method_end = len(patched_content)
        
        # Get current method content
        current_method = patched_content[method_start:method_end]
        
        # Replace the method
        patched_content = patched_content.replace(current_method, improved_save_function)
        print("✅ Replaced save_results_to_exact_folders method")
    else:
        # Method not found, append it to the class
        class_pattern = r'class TGCheckerApp\(QMainWindow\):'
        class_match = re.search(class_pattern, patched_content)
        if class_match:
            class_start = class_match.start()
            patched_content = patched_content[:class_start] + improved_save_function + "\n\n" + patched_content[class_start:]
            print("✅ Added save_results_to_exact_folders method")
        else:
            print("❌ Could not find TGCheckerApp class to add method")
    
    # Create backup of original file
    backup_path = f'main.py.backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
    with open(backup_path, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"✅ Created backup of original file at {backup_path}")
    
    # Write the patched content
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(patched_content)
    print("✅ Successfully patched main.py")
    
    return True

if __name__ == "__main__":
    print("=== PATCH SAVE FUNCTION ===")
    success = patch_main_py()
    if success:
        print("✅ Patch applied successfully")
    else:
        print("❌ Patch failed") 