﻿#!/usr/bin/env python3
"""
Utility script to apply performance manager patches to main.py
"""

import os
import re
import shutil
from datetime import datetime

def backup_main():
    """Create a backup of main.py"""
    backup_name = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2("main.py", backup_name)
    print(f"✅ Created backup: {backup_name}")
    return backup_name

def apply_performance_patches():
    """Apply performance manager patches to main.py"""
    
    print("🚀 Applying performance manager patches to main.py...")
    
    # Read the current main.py
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Patch 1: Add performance imports at the top
    import_patch = '''
# Import performance management
try:
    from performance_integration import integrate_performance_manager
    PERFORMANCE_MANAGER_AVAILABLE = True
except ImportError:
    PERFORMANCE_MANAGER_AVAILABLE = False
    print("Performance manager not available, using basic threading")
'''
    
    # Find the position after the logger import
    logger_import_pattern = r'(from logger import setup_logger[^\n]*\n)'
    if re.search(logger_import_pattern, content):
        content = re.sub(logger_import_pattern, r'\1' + import_patch, content, count=1)
        print("✅ Added performance manager imports")
    else:
        print("❌ Could not find logger import location")
    
    # Patch 2: Add performance manager initialization in __init__
    init_patch = '''
        # Initialize performance manager for high concurrency
        self.performance_integration = None
        if PERFORMANCE_MANAGER_AVAILABLE:
            try:
                self.performance_integration = integrate_performance_manager(self, self.logger)
                self.logger.info("🚀 High-performance concurrency system activated")
            except Exception as e:
                self.logger.warning(f"Failed to initialize performance manager: {e}")
'''
    
    # Find the pattern in the last TGCheckerApp class (around line 5438)
    # Look for the monitor initialization in the last occurrence
    monitor_pattern = r'(        self\.monitor = Monitor\(self\.account_manager, self\.logger\)\n)'
    
    # Find all matches and replace the last one
    matches = list(re.finditer(monitor_pattern, content))
    if matches:
        last_match = matches[-1]
        # Insert the performance manager code after the monitor initialization
        before = content[:last_match.end()]
        after = content[last_match.end():]
        content = before + init_patch + after
        print("✅ Added performance manager initialization")
    else:
        print("❌ Could not find monitor initialization location")
    
    # Patch 3: Add performance stats tab
    stats_tab_method = '''
    def create_performance_tab(self):
        """Create the performance monitoring tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Performance overview
        perf_group = QGroupBox("Performance Statistics")
        perf_layout = QFormLayout()
        
        self.memory_usage_label = QLabel("0 MB")
        self.cpu_usage_label = QLabel("0%")
        self.active_tasks_label = QLabel("0")
        self.total_threads_label = QLabel("0")
        
        perf_layout.addRow("Memory Usage:", self.memory_usage_label)
        perf_layout.addRow("CPU Usage:", self.cpu_usage_label)
        perf_layout.addRow("Active Tasks:", self.active_tasks_label)
        perf_layout.addRow("Total Threads:", self.total_threads_label)
        
        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)
        
        # Task distribution
        tasks_group = QGroupBox("Task Distribution")
        tasks_layout = QVBoxLayout()
        
        self.task_stats_text = QTextEdit()
        self.task_stats_text.setMaximumHeight(200)
        self.task_stats_text.setReadOnly(True)
        tasks_layout.addWidget(self.task_stats_text)
        
        # Refresh button
        refresh_btn = QPushButton("Refresh Stats")
        refresh_btn.clicked.connect(self.refresh_performance_stats)
        tasks_layout.addWidget(refresh_btn)
        
        # Optimize button
        optimize_btn = QPushButton("Optimize Resources")
        optimize_btn.clicked.connect(self.optimize_performance)
        tasks_layout.addWidget(optimize_btn)
        
        tasks_group.setLayout(tasks_layout)
        layout.addWidget(tasks_group)
        
        layout.addStretch()
        return tab
        
    def refresh_performance_stats(self):
        """Refresh performance statistics display."""
        if self.performance_integration:
            try:
                stats = self.performance_integration.get_performance_statistics()
                
                # Update labels
                self.memory_usage_label.setText(f"{stats['current_memory_mb']:.1f} MB")
                self.cpu_usage_label.setText(f"{stats['cpu_percent']:.1f}%")
                
                active_tasks = sum(s['active_tasks'] for s in stats['task_stats'].values())
                self.active_tasks_label.setText(str(active_tasks))
                
                # Update task stats
                task_details = []
                for task_type, task_stats in stats['task_stats'].items():
                    task_details.append(f"{task_type.title()}: {task_stats['active_tasks']} active, "
                                      f"{task_stats['queued_tasks']} queued, "
                                      f"{task_stats['max_workers']} max workers")
                
                self.task_stats_text.setPlainText("\\n".join(task_details))
                
            except Exception as e:
                self.task_stats_text.setPlainText(f"Error getting stats: {e}")
        else:
            self.task_stats_text.setPlainText("Performance manager not available")
    
    def optimize_performance(self):
        """Trigger performance optimization."""
        if self.performance_integration:
            try:
                self.performance_integration.optimize_performance()
                self.log_activity("🔧 Performance optimization triggered")
                self.refresh_performance_stats()
            except Exception as e:
                self.log_activity(f"❌ Performance optimization failed: {e}")
        else:
            self.log_activity("❌ Performance manager not available")
'''
    
    # Find the end of the TGCheckerApp class methods and add the performance tab method
    # Look for the pattern before the main() function
    class_end_pattern = r'(\n\ndef main\(\):)'
    if re.search(class_end_pattern, content):
        content = re.sub(class_end_pattern, stats_tab_method + r'\1', content, count=1)
        print("✅ Added performance statistics tab")
    else:
        print("❌ Could not find class end location")
    
    # Patch 4: Add performance tab to the UI setup
    ui_patch = '''        self.performance_tab = self.create_performance_tab()
        '''
    
    # Find where tabs are created and add performance tab
    tabs_pattern = r'(        self\.settings_tab = self\.create_settings_tab\(\)\n)'
    if re.search(tabs_pattern, content):
        content = re.sub(tabs_pattern, r'\1' + ui_patch, content, count=1)
        print("✅ Added performance tab creation")
    else:
        print("❌ Could not find tabs creation location")
    
    # Add the performance tab to the tab widget
    tab_add_patch = '''        self.tabs.addTab(self.performance_tab, "Performance")
        '''
    
    tab_add_pattern = r'(        self\.tabs\.addTab\(self\.settings_tab, "Settings"\)\n)'
    if re.search(tab_add_pattern, content):
        content = re.sub(tab_add_pattern, r'\1' + tab_add_patch, content, count=1)
        print("✅ Added performance tab to UI")
    else:
        print("❌ Could not find tab addition location")
    
    # Write the patched content back
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ Successfully applied all performance patches!")
    print("🚀 The application now supports high-performance concurrency for 100+ accounts")

def main():
    """Main function to apply patches"""
    if not os.path.exists("main.py"):
        print("❌ main.py not found in current directory")
        return
    
    if not os.path.exists("performance_manager.py"):
        print("❌ performance_manager.py not found - please ensure it's in the current directory")
        return
        
    if not os.path.exists("performance_integration.py"):
        print("❌ performance_integration.py not found - please ensure it's in the current directory")
        return
    
    # Create backup
    backup_file = backup_main()
    
    try:
        # Apply patches
        apply_performance_patches()
        
        print(f"""
🎉 Performance patches applied successfully!

📊 New Features Added:
• High-performance concurrency manager
• 50 workers for joining operations
• 30 workers for checking operations  
• 20 workers for forwarding operations
• Automatic resource monitoring
• Memory leak prevention
• Database connection pooling
• Real-time performance statistics

🚀 The tool can now handle 100+ accounts simultaneously without freezing!

📋 To test the improvements:
1. Run the application: python main.py
2. Go to the new "Performance" tab to monitor stats
3. Try bulk operations with multiple accounts
4. Monitor memory and CPU usage in real-time

💾 Backup created: {backup_file}
   (Restore with: cp {backup_file} main.py)
""")
        
    except Exception as e:
        print(f"❌ Error applying patches: {e}")
        print(f"💾 Restoring from backup: {backup_file}")
        shutil.copy2(backup_file, "main.py")

if __name__ == "__main__":
    main()
