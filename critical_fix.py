# CRITICAL FIX SCRIPT FOR TG CHECKER
# This script contains all the missing functions and fixes for the issues

import asyncio
import re
from telethon.tl.types import Channel, Chat
from telethon.errors import *

class TGCheckerFixes:
    """Critical fixes for TG Checker tool"""
    
    async def verify_real_membership(self, client, group_link):
        """Accurately check if the client is a real member of the group/channel."""
        try:
            link = group_link.strip()
            entity = None
            
            # Try to resolve entity safely - ONLY for valid usernames
            try:
                if link.startswith('https://t.me/joinchat/'):
                    pass  # Can't verify invite links without joining
                elif link.startswith('https://t.me/'):
                    username = link.split('/')[-1]
                    if not username.startswith('+') and re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
                        entity = await client.get_entity(username)
                elif link.startswith('@'):
                    username = link[1:]
                    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
                        entity = await client.get_entity(username)
                else:
                    if re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', link):
                        entity = await client.get_entity(link)
            except:
                pass
            
            # Check dialogs for membership - this is the REAL verification
            try:
                dialogs = await client.get_dialogs()
                
                if entity:
                    for dialog in dialogs:
                        if hasattr(dialog.entity, 'id') and dialog.entity.id == entity.id:
                            return True
                
                # Fallback: check dialogs for matching username
                link_clean = link.lower().replace('@', '').replace('https://t.me/', '')
                for dialog in dialogs:
                    ent = dialog.entity
                    if hasattr(ent, 'username') and ent.username:
                        if ent.username.lower() == link_clean:
                            return True
            except:
                pass
            
            return False
        except:
            return False

    async def attempt_join_with_retry(self, client, group_link, task_id, max_retries=3, log_signal=None):
        """Try to join a group up to max_retries times before giving up."""
        for attempt in range(max_retries):
            try:
                # Yield to UI before each attempt - CRITICAL for responsiveness
                await asyncio.sleep(0.05)
                
                result = await self.validate_and_join_group_safe(client, group_link, task_id)
                
                # Success or already member - no need to retry
                if result in ("success", "already_member"):
                    if attempt > 0 and log_signal:
                        log_signal.emit("info", task_id, 
                            f"✅ Retry {attempt + 1} succeeded for: {group_link}")
                    return result
                
                # Flood wait - let caller handle it
                if "flood" in result.lower():
                    return result
                
                # Permanent errors - don't retry (THIS PREVENTS RESOLVE USERNAME SPAM)
                permanent_errors = [
                    "invalid_username_format", "username_not_found", "invalid_invite_link",
                    "invalid_invite_hash", "username_resolve_blocked", "admin_required",
                    "private_channel", "invalid_or_expired", "username_resolve_error",
                    "invalid_length", "invalid_start", "invalid_format", "invalid_underscore",
                    "consecutive_underscore", "suspicious_pattern", "url_like", "only_numbers",
                    "username_invalid_length", "username_invalid_start", "username_invalid_format",
                    "username_not_username_link", "username_validation_error", "no_username_found"
                ]
                if any(error in result for error in permanent_errors):
                    if attempt == 0 and log_signal:  # Only log on first attempt for permanent errors
                        log_signal.emit("warning", task_id, 
                            f"⚠️ Permanent error, skipping retries: {result}")
                    return result
                
                # Temporary errors - retry with backoff
                if attempt < max_retries - 1:
                    if log_signal:
                        log_signal.emit("warning", task_id, 
                            f"⚠️ Attempt {attempt + 1} failed: {result}, retrying...")
                    await asyncio.sleep(2 ** attempt)  # Exponential backoff
                else:
                    if log_signal:
                        log_signal.emit("error", task_id, 
                            f"❌ All {max_retries} attempts failed for: {group_link}")
                    
            except Exception as e:
                error_str = str(e).lower()
                if "resolveusernamerequest" in error_str:
                    return "username_resolve_blocked"
                    
                if attempt < max_retries - 1:
                    if log_signal:
                        log_signal.emit("warning", task_id, 
                            f"⚠️ Attempt {attempt + 1} error: {str(e)}, retrying...")
                    await asyncio.sleep(2 ** attempt)
                else:
                    return f"retry_failed: {str(e)}"
        
        return result

    def validate_username_strict(self, username):
        """Strict username validation to prevent API spam"""
        if not username or not isinstance(username, str):
            return False, "empty_username"
        
        username = username.strip().lstrip('@')
        
        # Check length: 5-32 characters
        if len(username) < 5 or len(username) > 32:
            return False, "invalid_length"
        
        # Must start with a letter
        if not username[0].isalpha():
            return False, "invalid_start"
        
        # Only alphanumeric and underscore allowed
        if not re.match(r'^[a-zA-Z][a-zA-Z0-9_]{4,31}$', username):
            return False, "invalid_format"
        
        # Can't start or end with underscore
        if username.startswith('_') or username.endswith('_'):
            return False, "invalid_underscore"
        
        # No consecutive underscores
        if '__' in username:
            return False, "consecutive_underscore"
        
        # Check for obviously invalid patterns
        username_lower = username.lower()
        invalid_patterns = [
            'admin', 'support', 'help', 'official', 'bot', 'channel',
            'group', 'telegram', 'tg', 'www', 'http', 'https', 'test',
            'null', 'undefined', 'spam', 'fake'
        ]
        
        if any(pattern in username_lower for pattern in invalid_patterns):
            return False, "suspicious_pattern"
        
        # Additional checks
        if username_lower.startswith(('http', 'www', 'ftp', 'mailto')):
            return False, "url_like"
        
        if username.isdigit():
            return False, "only_numbers"
        
        return True, "valid"

    async def run_join_loop_with_ui_yield(self, group_links, client, task_id, task, log_signal, progress_signal):
        """Main join loop with proper UI yielding and error handling"""
        successful_joins = task.get('successful_joins', 0)
        failed_joins = task.get('failed_joins', 0)
        current_index = task.get('current_index', 0)
        
        for i in range(current_index, len(group_links)):
            # CRITICAL: Check if task is still active
            if hasattr(self, 'active_joining_tasks') and task_id not in getattr(self, 'active_joining_tasks', {}):
                log_signal.emit("info", task_id, "Task stopped by user")
                break
            
            group_link = group_links[i].strip()
            if not group_link:
                continue
            
            # CRITICAL: UI yield before each group
            await asyncio.sleep(0.05)
            
            try:
                log_signal.emit("info", task_id, f"Attempting to join: {group_link}")
                
                # Check if already member first
                is_already_member = await self.verify_real_membership(client, group_link)
                if is_already_member:
                    successful_joins += 1
                    log_signal.emit("info", task_id, f"✅ Already verified member: {group_link}")
                    continue
                
                # Attempt to join with retry logic
                result = await self.attempt_join_with_retry(client, group_link, task_id, 3, log_signal)
                
                if result == "success":
                    # CRITICAL: Verify the join actually worked
                    await asyncio.sleep(3)  # Wait for join to process
                    is_actually_joined = await self.verify_real_membership(client, group_link)
                    
                    if is_actually_joined:
                        successful_joins += 1
                        log_signal.emit("success", task_id, f"✅ VERIFIED joined: {group_link}")
                    else:
                        failed_joins += 1
                        log_signal.emit("error", task_id, f"❌ Join reported success but VERIFICATION FAILED: {group_link}")
                elif result == "already_member":
                    # Double-check this claim
                    is_verified_member = await self.verify_real_membership(client, group_link)
                    if is_verified_member:
                        successful_joins += 1
                        log_signal.emit("info", task_id, f"✅ Verified already member: {group_link}")
                    else:
                        failed_joins += 1
                        log_signal.emit("error", task_id, f"❌ Claimed member but VERIFICATION FAILED: {group_link}")
                else:
                    failed_joins += 1
                    log_signal.emit("error", task_id, f"❌ Failed to join: {group_link} ({result})")
                
                # Update progress every 5 groups for UI responsiveness
                if i % 5 == 0:
                    progress_signal.emit(task_id, "running", i + 1, successful_joins, failed_joins)
                    await asyncio.sleep(0.1)  # Extra UI yield
                
            except Exception as e:
                failed_joins += 1
                log_signal.emit("error", task_id, f"❌ Error joining {group_link}: {str(e)}")
                await asyncio.sleep(0.05)  # UI yield on error
        
        return successful_joins, failed_joins

# INSTRUCTIONS TO FIX MAIN.PY:
# 1. Add these methods to your TGCheckerApp class
# 2. Replace _verify_real_membership calls with self.verify_real_membership
# 3. Replace _attempt_join_with_retry calls with self.attempt_join_with_retry
# 4. Use run_join_loop_with_ui_yield for the main join loop
# 5. Add await asyncio.sleep(0.05) after every group processing 