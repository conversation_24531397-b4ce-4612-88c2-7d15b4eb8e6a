def fix_indentation():
    print("Fixing indentation error in main.py...")
    
    # Read the file content
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    # Look for the try block near line 5498 and fix indentation
    try_line_index = 5497  # This is the line number for "try:" (1-indexed)
    indentation_fix_needed = False
    
    # Check if the try line exists
    if try_line_index < len(lines):
        try_line = lines[try_line_index]
        # Check if it's a try line
        if "try:" in try_line:
            # Get the indentation level from the try line
            indentation = " " * (len(try_line) - len(try_line.lstrip()))
            # Add proper indentation to the next line if needed
            next_line_index = try_line_index + 1
            if next_line_index < len(lines):
                next_line = lines[next_line_index]
                if not next_line.startswith(indentation + "    "):
                    # Add proper indentation (4 spaces after the try line's indentation)
                    lines[next_line_index] = indentation + "    " + next_line.lstrip()
                    indentation_fix_needed = True
                    print(f"Fixed indentation on line {next_line_index + 1}")
    
    # If we found and fixed the indentation issue, write the fixed content back
    if indentation_fix_needed:
        with open("main.py", "w", encoding="utf-8") as file:
            file.writelines(lines)
        print("Indentation fix applied successfully.")
    else:
        print("No indentation issues found at the specified location.")

if __name__ == "__main__":
    fix_indentation() 