#!/usr/bin/env python3
"""
EMERGENCY FIX: Resolves critical issues with the joining task system in TG Checker
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_continue_all_joining_tasks():
    """Fix the continue_all_joining_tasks function that's causing crashes."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the continue_all_joining_tasks function
        if "def continue_all_joining_tasks(self):" in content:
            # Fix the indentation issue in the loop
            fixed_content = content.replace(
                "for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n            \n            # Parse group links",
                "for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n                \n                # Parse group links"
            )
            
            # Write the fixed content back to the file
            with open("main.py", "w", encoding="utf-8") as f:
                f.write(fixed_content)
            
            print("✅ Fixed continue_all_joining_tasks function")
            return True
        else:
            print("⚠️ Could not find continue_all_joining_tasks function")
            return False
    except Exception as e:
        print(f"❌ Error fixing continue_all_joining_tasks: {str(e)}")
        return False

def fix_start_joining_task():
    """Fix the start_joining_task function to properly handle resuming tasks."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the start_joining_task function
        if "def start_joining_task(self, task_id):" in content and "# ALWAYS reset task progress when manually starting" in content:
            # Replace the always-reset logic with smarter resume detection
            start_idx = content.find("def start_joining_task(self, task_id):")
            reset_idx = content.find("# ALWAYS reset task progress when manually starting", start_idx)
            
            if reset_idx > 0:
                # Find the end of the reset block
                reset_end_idx = content.find("self.reset_joining_task_progress(task_id)", reset_idx)
                next_line_idx = content.find("\n", reset_end_idx + 10)
                
                if reset_end_idx > 0 and next_line_idx > 0:
                    # Build the replacement text
                    new_code = """            # Check if we're continuing a task or starting fresh
            task = self.joining_tasks[task_id]
            
            # Get actual progress
            import json
            try:
                group_links = json.loads(task['group_links']) if isinstance(task['group_links'], str) else task['group_links']
                total_groups = len(group_links)
                current_index = task['current_index']
                
                # Detect if we're continuing a task
                if (task['status'] == 'paused' or task['status'] == 'running') and current_index > 0 and current_index < total_groups:
                    self.logger.info(f"Continuing joining task from index {current_index}/{total_groups}: {task_id}")
                    self.log_joining_message("info", task_id, f"🔄 Continuing from group #{current_index+1} ({current_index}/{total_groups})")
                else:
                    # Reset task progress for fresh start
                    self.logger.info(f"Resetting joining task progress for fresh start: {task_id}")
                    self.log_joining_message("info", task_id, "🔄 Resetting progress counters for clean start")
                    self.reset_joining_task_progress(task_id)
            except:
                # If any error in parsing, default to reset for safety
                self.logger.info(f"Resetting joining task progress (parse error): {task_id}")
                self.log_joining_message("info", task_id, "🔄 Resetting progress counters (parse error)")
                self.reset_joining_task_progress(task_id)"""
                    
                    # Replace the old code with the new code
                    fixed_content = content[:reset_idx] + new_code + content[next_line_idx:]
                    
                    # Write the fixed content back to the file
                    with open("main.py", "w", encoding="utf-8") as f:
                        f.write(fixed_content)
                    
                    print("✅ Fixed start_joining_task function")
                    return True
                else:
                    print("⚠️ Could not find reset block in start_joining_task function")
                    return False
            else:
                print("⚠️ Could not find reset comment in start_joining_task function")
                return False
        else:
            print("⚠️ Could not find start_joining_task function")
            return False
    except Exception as e:
        print(f"❌ Error fixing start_joining_task: {str(e)}")
        return False

def main():
    print("🚨 EMERGENCY FIX: Resolving joining task crashes and progress issues...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fixed_continue = fix_continue_all_joining_tasks()
    fixed_start = fix_start_joining_task()
    
    if fixed_continue or fixed_start:
        print("\n✅ Emergency fixes applied successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fixes were applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 