"""
Group checker module for TG Checker.
This module handles the checking of Telegram groups for various criteria.
"""

import os
import asyncio
import logging
from datetime import datetime, timedelta
import json
from telethon.tl.functions.channels import GetFullChannelRequest
from telethon.errors import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ChannelPrivateError

from language_detector import detect_language_batch
from settings import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()

class GroupChecker:
    """Class to check Telegram groups against various criteria."""
    
    def __init__(self, client):
        self.client = client
        self.results = {
            "good_groups": [],
            "channels": [],
            "topics": [],
            "broke_groups": [],
            "non_filter_groups": []
        }
        
        # Load settings
        self.min_members = settings.get("min_members", 500)
        self.max_last_message_hours = settings.get("max_last_message_hours", 1)
        self.min_total_messages = settings.get("min_total_messages", 100)
        self.check_post_frequency = settings.get("check_post_frequency", True)
        self.check_real_users = settings.get("check_real_users", True)
        self.public_groups_only = settings.get("public_groups_only", True)
        self.language_filter = settings.get("language_filter", "all")
        self.wait_time = settings.get("wait_time_after_check", 2)
        self.history_limit = settings.get("history_message_limit", 100)
    
    async def check_group(self, group_link):
        """Check a Telegram group for various criteria."""
        try:
            # Clean the group link
            group_link = group_link.strip()
            if not group_link.startswith('https://t.me/'):
                group_link = f"https://t.me/{group_link.lstrip('@')}"

            # Extract username from the link
            username = group_link.split('t.me/')[-1].split('?')[0].split('/')[0]
            if not username:
                raise ValueError("Invalid group link format")
            
            logger.info(f"Checking group: {username}")
            
            # Get group entity
            try:
                group = await self.client.get_entity(username)
            except ValueError:
                logger.error(f"Username not found: {username}")
                return {
                    "group_id": username,
                    "title": username,
                    "status": "error",
                    "error": "Username not found"
                }

            if not group:
                logger.error(f"Group not found: {username}")
                return {
                    "group_id": username,
                    "title": username,
                    "status": "error",
                    "error": "Group not found"
                }

            # Get member count
            member_count = 0
            try:
                # Try with GetFullChannelRequest
                full_channel = await self.client(GetFullChannelRequest(channel=group))
                if hasattr(full_channel, 'full_chat') and hasattr(full_channel.full_chat, 'participants_count'):
                    member_count = full_channel.full_chat.participants_count
                    logger.info(f"Got {member_count} members for {username}")
            except Exception as e:
                logger.warning(f"Could not get member count: {str(e)}")
                member_count = 500  # Default to avoid false negatives

            # Get basic info
            result = {
                "group_id": username,
                "title": getattr(group, 'title', username),
                "username": getattr(group, 'username', None),
                "member_count": member_count,
                "is_public": getattr(group, 'username', None) is not None,
                "status": "good",
                "last_message_date": None,
                "post_frequency": 0,
                "real_users_percentage": 0,
                "language": "unknown",
                "is_topic": hasattr(group, 'forum') and group.forum,
                "is_channel": hasattr(group, 'broadcast') and group.broadcast,
                "total_messages": 0
            }

            # Check if this is a topics group or channel
            if result["is_topic"]:
                logger.info(f"Group {username} is a topics group")
                result["status"] = "topic"
                self.results["topics"].append(result)
            elif result["is_channel"]:
                logger.info(f"Group {username} is a channel")
                result["status"] = "channel"
                self.results["channels"].append(result)
            else:
                # Get last messages for various checks
                try:
                    messages = await self.client.get_messages(group, limit=self.history_limit)
                    result["total_messages"] = len(messages)
                    
                    if messages and messages[0]:
                        result["last_message_date"] = messages[0].date.isoformat()

                        # Calculate post frequency
                        if len(messages) >= 2 and self.check_post_frequency:
                            time_diff = (messages[0].date - messages[-1].date).total_seconds()
                            if time_diff > 0:
                                result["post_frequency"] = len(messages) / (time_diff / 86400)  # posts per day

                        # Check real users percentage
                        if self.check_real_users:
                            real_users = 0
                            total_checked = min(self.history_limit, len(messages))
                            for msg in messages[:total_checked]:
                                if msg and msg.from_id and not hasattr(msg.from_id, 'channel_id'):
                                    real_users += 1
                            result["real_users_percentage"] = (real_users / total_checked) * 100 if total_checked > 0 else 0

                        # Detect language
                        if self.language_filter != "all":
                            result["language"] = detect_language_batch(messages)

                except Exception as e:
                    logger.warning(f"Could not get messages for {username}: {str(e)}")

                # Apply filters for regular groups
                if not self._apply_filters(result):
                    return result
                    
                # Add to good groups if it passes all filters
                self.results["good_groups"].append(result)
            
            # Wait to avoid rate limits
            await asyncio.sleep(self.wait_time)
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking group {group_link}: {str(e)}")
            error_result = {
                "group_id": group_link,
                "title": group_link,
                "status": "error",
                "error": str(e)
            }
            self.results["broke_groups"].append(error_result)
            return error_result
    
    def _apply_filters(self, result):
        """Apply filters to determine if group passes criteria."""
        # Filter by member count
        if result["member_count"] < self.min_members:
            result["status"] = "invalid"
            result["error"] = f"Group has too few members: {result['member_count']} < {self.min_members}"
            self.results["non_filter_groups"].append(result)
            return False
            
        # Filter by public/private
        if self.public_groups_only and not result["is_public"]:
            result["status"] = "invalid"
            result["error"] = "Group is private"
            self.results["non_filter_groups"].append(result)
            return False
            
        # Filter by last message time
        if result["last_message_date"]:
            try:
                last_message = datetime.fromisoformat(result["last_message_date"].replace('Z', '+00:00'))
                hours_since_last_message = (datetime.now() - last_message.replace(tzinfo=None)).total_seconds() / 3600
                if hours_since_last_message > self.max_last_message_hours:
                    result["status"] = "invalid"
                    result["error"] = f"Group has old messages: {hours_since_last_message:.1f} hours old"
                    self.results["non_filter_groups"].append(result)
                    return False
            except Exception as e:
                logger.warning(f"Could not parse last message date: {str(e)}")
        
        # Filter by total messages
        if result["total_messages"] < self.min_total_messages:
            result["status"] = "invalid" 
            result["error"] = f"Group has too few messages: {result['total_messages']} < {self.min_total_messages}"
            self.results["non_filter_groups"].append(result)
            return False
            
        # Filter by language
        if self.language_filter != "all" and result["language"] != self.language_filter:
            result["status"] = "invalid"
            result["error"] = f"Group language ({result['language']}) does not match filter ({self.language_filter})"
            self.results["non_filter_groups"].append(result)
            return False
            
        # All filters passed
        return True
    
    async def check_groups(self, group_links):
        """Check multiple groups."""
        results = []
        
        for group_link in group_links:
            try:
                result = await self.check_group(group_link)
                results.append(result)
            except FloodWaitError as e:
                logger.warning(f"Hit rate limit, waiting {e.seconds} seconds")
                await asyncio.sleep(e.seconds)
                # Try again
                result = await self.check_group(group_link)
                results.append(result)
            except Exception as e:
                logger.error(f"Error checking group {group_link}: {str(e)}")
        
        return results
    
    async def check_groups_batch(self, group_links, batch_size=10):
        """Check groups in batches to avoid rate limits."""
        all_results = []
        
        # Process in batches
        for i in range(0, len(group_links), batch_size):
            batch = group_links[i:i+batch_size]
            
            # Create tasks for each group in the batch
            tasks = [self.check_group(group) for group in batch]
            
            # Wait for all tasks to complete
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.error(f"Error in batch processing: {str(result)}")
                    continue
                all_results.append(result)
            
            # Wait between batches
            await asyncio.sleep(self.wait_time * 2)
        
        return all_results
    
    def save_results(self, output_dir="Results"):
        """Save results to files."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Save categories to specific files
        if self.results["good_groups"]:
            with open(os.path.join(output_dir, "GroupsVaild_Filter_On.txt"), 'w', encoding='utf-8') as f:
                for group in self.results["good_groups"]:
                    f.write(f"https://t.me/{group['group_id']}\n")
            logger.info(f"Saved {len(self.results['good_groups'])} groups to GroupsVaild_Filter_On.txt")
            
        if self.results["non_filter_groups"]:
            with open(os.path.join(output_dir, "GroupsVaildOnly.txt"), 'w', encoding='utf-8') as f:
                for group in self.results["non_filter_groups"]:
                    f.write(f"https://t.me/{group['group_id']}\n")
            logger.info(f"Saved {len(self.results['non_filter_groups'])} groups to GroupsVaildOnly.txt")
            
        if self.results["topics"]:
            with open(os.path.join(output_dir, "TopicsGroups.txt"), 'w', encoding='utf-8') as f:
                for group in self.results["topics"]:
                    f.write(f"https://t.me/{group['group_id']}\n")
            logger.info(f"Saved {len(self.results['topics'])} groups to TopicsGroups.txt")
            
        if self.results["channels"]:
            with open(os.path.join(output_dir, "Channels.txt"), 'w', encoding='utf-8') as f:
                for group in self.results["channels"]:
                    f.write(f"https://t.me/{group['group_id']}\n")
            logger.info(f"Saved {len(self.results['channels'])} groups to Channels.txt")
            
        if self.results["broke_groups"]:
            with open(os.path.join(output_dir, "InvalidGroups_Channels.txt"), 'w', encoding='utf-8') as f:
                for group in self.results["broke_groups"]:
                    f.write(f"https://t.me/{group['group_id']}\n")
            logger.info(f"Saved {len(self.results['broke_groups'])} groups to InvalidGroups_Channels.txt")
        
        # Save summary
        summary = {category: len(groups) for category, groups in self.results.items()}
        with open(os.path.join(output_dir, "summary.json"), 'w') as f:
            json.dump(summary, f, indent=2)
        
        return summary
    
    def get_results(self):
        """Get the current results."""
        return self.results
    
    def clear_results(self):
        """Clear all results."""
        self.results = {
            "good_groups": [],
            "channels": [],
            "topics": [],
            "broke_groups": [],
            "non_filter_groups": []
        } 