def fix_all_errors():
    print("Running comprehensive fix for all syntax and indentation errors...")
    
    # Read the entire file
    with open("main.py", "r", encoding="utf-8") as file:
        lines = file.readlines()
    
    # Fix 1: Incomplete if expression in TelegramForwarder.set_task methods
    fixed_if_count = 0
    for i, line in enumerate(lines):
        if "self.last_processed_index = task.get('current_index', 0) if t" in line:
            lines[i] = line.replace("if t", "if task else 0")
            fixed_if_count += 1
            print(f"Fixed incomplete if expression on line {i+1}")
    
    print(f"Fixed {fixed_if_count} incomplete if expressions")
    
    # Fix 2: Properly indent all code blocks in auto_refresh_missing_account_info method
    method_start = None
    method_end = None
    base_indent = None
    
    # Find the method
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            method_start = i
            base_indent = " " * (len(line) - len(line.lstrip()))
            break
    
    if method_start is None:
        print("Error: auto_refresh_missing_account_info method not found")
        return
    
    # Find the end of the method (next def at same indentation)
    for i in range(method_start + 1, len(lines)):
        if lines[i].startswith(base_indent + "def "):
            method_end = i
            break
    
    if method_end is None:
        # If no next method found, go until the end of the class
        for i in range(method_start + 1, len(lines)):
            if lines[i].strip() and len(lines[i]) - len(lines[i].lstrip()) < len(base_indent):
                method_end = i
                break
    
    if method_end is None:
        method_end = len(lines)  # End of file
    
    # Fix indentation of the method
    fixed_indent_count = 0
    inside_try = False
    inside_for_loop = False
    inside_if_block = False
    
    for i in range(method_start + 1, method_end):
        line = lines[i].rstrip()
        if not line:  # Skip empty lines
            continue
        
        # Calculate correct indentation
        correct_indent = base_indent + "    "  # Default is method level + 1
        
        # Handle try-except blocks
        if "try:" in line and line.strip() == "try:":
            inside_try = True
        elif "except" in line and ":" in line and line.strip().startswith("except"):
            inside_try = False
            
        # Inside try block
        if inside_try:
            correct_indent = base_indent + "    "  # Method level + 1
            
            # Check for for loops
            if "for " in line and line.strip().startswith("for ") and line.strip().endswith(":"):
                inside_for_loop = True
                # The for loop line itself stays at try block indentation
            elif inside_for_loop:
                correct_indent = base_indent + "        "  # Method level + 2
                
                # Check for if/elif/else blocks
                if (line.strip().startswith("if ") or 
                    line.strip().startswith("elif ") or 
                    line.strip().startswith("else:")) and ":" in line:
                    inside_if_block = True
                elif inside_if_block:
                    correct_indent = base_indent + "            "  # Method level + 3
                
                # Check if we're exiting the for loop
                if line.strip().startswith("if accounts_needing_refresh:"):
                    inside_for_loop = False
                    inside_if_block = False
                    correct_indent = base_indent + "    "  # Back to try block level
        
        # Apply correct indentation if needed
        current_indent = " " * (len(line) - len(line.lstrip()))
        if current_indent != correct_indent and line.strip():
            lines[i] = correct_indent + line.lstrip() + "\n"
            fixed_indent_count += 1
            print(f"Fixed indentation on line {i+1}")
    
    print(f"Fixed {fixed_indent_count} indentation issues")
    
    # Write back all fixes
    with open("main.py", "w", encoding="utf-8") as file:
        file.writelines(lines)
    
    print("All fixes applied successfully!")

if __name__ == "__main__":
    fix_all_errors() 