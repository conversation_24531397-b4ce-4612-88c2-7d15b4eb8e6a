@echo off
echo Starting TG Checker...

:: Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH. Please install Python 3.8+
    pause
    exit /b 1
)

:: Check if the virtual environment exists, if not create it
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo Failed to create virtual environment
        pause
        exit /b 1
    )
)

:: Activate the virtual environment and install dependencies
echo Activating virtual environment...
call venv\Scripts\activate.bat

:: Install required packages if not already installed
echo Checking and installing dependencies...
pip install -r requirements.txt

:: Run the application
echo Starting application...
python main.py

:: Deactivate the virtual environment when done
call venv\Scripts\deactivate.bat

pause 