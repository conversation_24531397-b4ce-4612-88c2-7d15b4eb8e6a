"""
CRITICAL NETWORK CONNECTION FIX
Addresses urgent network connectivity and resource management issues causing crashes
"""

import asyncio
import threading
import time
import logging
import weakref
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Optional
import telethon
from telethon import Telegram<PERSON>lient
from telethon.errors import (
    FloodWaitError, SessionPasswordNeededError, PhoneCodeInvalidError,
    ChannelPrivateError, ChatAdminRequiredError, InviteHashExpiredError,
    InviteHashInvalidError, UserAlreadyParticipantError, PeerFloodError,
    UserBannedInChannelError, ChatWriteForbiddenError, ChannelsTooMuchError,
    UserNotMutualContactError, ConnectionError, TimeoutError, RPCError
)

class CriticalNetworkFix:
    """Critical fix for network connectivity and performance issues."""
    
    def __init__(self, main_app):
        self.main_app = main_app
        self.logger = logging.getLogger(__name__)
        
        # Network configuration
        self.MAX_RETRIES = 3  # Limit retries to prevent infinite loops
        self.RETRY_DELAY = 10  # Wait 10 seconds between retries
        self.CONNECTION_TIMEOUT = 30  # 30 second connection timeout
        self.REQUEST_TIMEOUT = 15  # 15 second request timeout
        
        # Connection pool management
        self.client_pool = {}
        self.client_locks = {}
        self.connection_attempts = {}
        
        # High-performance executor with proper resource limits
        self.executor = ThreadPoolExecutor(
            max_workers=20,  # Reduced from 50 to prevent overload
            thread_name_prefix="NetworkSafeJoin"
        )
        
        # Flood wait tracking (per account)
        self.flood_waits = {}
        self.flood_wait_lock = threading.Lock()
        
        # Resource monitoring
        self.active_connections = 0
        self.max_concurrent_connections = 10
        self.connection_lock = threading.Lock()
        
        print("🔥 CRITICAL NETWORK FIX INITIALIZED - Connection management active")
    
    def apply_fix_to_main_app(self):
        """Apply critical fixes to the main application."""
        try:
            # Store original methods
            self.main_app._original_start_joining_task = self.main_app.start_joining_task
            self.main_app._original_stop_joining_task = self.main_app.stop_joining_task
            
            # Replace with network-safe versions
            self.main_app.start_joining_task = self.start_joining_task_network_safe
            self.main_app.stop_joining_task = self.stop_joining_task_network_safe
            
            # Add resource monitoring
            self.main_app._monitor_resources = self._monitor_resources
            self.main_app._get_network_status = self._get_network_status
            
            # Initialize network monitoring
            self._start_resource_monitor()
            
            print("✅ Critical network fixes applied to main application")
            
        except Exception as e:
            print(f"❌ Failed to apply network fixes: {e}")
    
    def start_joining_task_network_safe(self, task_id: str):
        """Network-safe joining task with proper error handling and resource limits."""
        try:
            if task_id not in self.main_app.joining_tasks:
                self.main_app.log_joining_message("error", task_id, "Task not found")
                return
            
            task = self.main_app.joining_tasks[task_id]
            phone = task['account_phone']
            
            # Check network connectivity first
            if not self._check_network_connectivity():
                self.main_app.log_joining_message("error", task_id, 
                    "❌ Network connectivity issues detected - please check internet connection")
                return
            
            # Check resource limits
            with self.connection_lock:
                if self.active_connections >= self.max_concurrent_connections:
                    self.main_app.log_joining_message("warning", task_id, 
                        f"⏳ Max connections reached ({self.max_concurrent_connections}) - queuing task")
                    # Queue for later execution
                    threading.Timer(30, lambda: self.start_joining_task_network_safe(task_id)).start()
                    return
            
            # Check flood wait
            if self._is_in_flood_wait(phone):
                remaining = self._get_flood_wait_remaining(phone)
                self.main_app.log_joining_message("warning", task_id, 
                    f"⏳ Account {phone} in flood wait for {remaining}s - will auto-retry")
                
                # Schedule retry after flood wait
                threading.Timer(remaining, lambda: self.start_joining_task_network_safe(task_id)).start()
                return
            
            # Submit to resource-managed executor
            future = self.executor.submit(self._run_network_safe_task, task)
            
            # Track active task
            if not hasattr(self.main_app, 'active_safe_tasks'):
                self.main_app.active_safe_tasks = {}
            self.main_app.active_safe_tasks[task_id] = future
            
            # Update UI safely
            self._safe_ui_update(task_id, status='running')
            
            self.main_app.log_joining_message("info", task_id, 
                f"🌐 Network-safe task started: {task['name']}")
            
        except Exception as e:
            self.logger.error(f"Network-safe task start failed {task_id}: {e}")
            self.main_app.log_joining_message("error", task_id, f"Start failed: {e}")
    
    def _run_network_safe_task(self, task):
        """Run task with comprehensive network error handling."""
        task_id = task['id']
        account_phone = task['account_phone']
        
        try:
            # Increment active connections
            with self.connection_lock:
                self.active_connections += 1
            
            # Create isolated async environment
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Run with network safety
                result = loop.run_until_complete(self._join_groups_network_safe(task))
                return result
            finally:
                loop.close()
                
        except Exception as e:
            self._safe_log("error", task_id, f"Network task failed: {e}")
            self._safe_ui_update(task_id, status='failed')
            return 'failed'
        finally:
            # Decrement active connections
            with self.connection_lock:
                self.active_connections -= 1
            
            # Cleanup
            if hasattr(self.main_app, 'active_safe_tasks') and task_id in self.main_app.active_safe_tasks:
                del self.main_app.active_safe_tasks[task_id]
    
    async def _join_groups_network_safe(self, task):
        """Join groups with comprehensive network error handling."""
        task_id = task['id']
        account_phone = task['account_phone']
        group_links = [link.strip() for link in task['group_links'].split('\n') if link.strip()]
        total_groups = len(group_links)
        current_index = task.get('current_index', 0)
        successful_joins = 0
        failed_joins = 0
        skipped_count = 0
        
        client = None
        
        try:
            # Get client with network safety
            client = await self._get_safe_client(account_phone)
            if not client:
                self._safe_log("error", task_id, "❌ Failed to create network-safe client")
                return 'failed'
            
            self._safe_log("info", task_id, f"🌐 Network-safe client connected - processing {total_groups} groups")
            
            # Process groups with network error handling
            for i in range(current_index, total_groups):
                group_link = group_links[i]
                current_index = i + 1
                
                # Check if task should stop
                if hasattr(self.main_app, 'active_safe_tasks') and task_id not in self.main_app.active_safe_tasks:
                    self._safe_log("info", task_id, "🛑 Task stopped by user")
                    break
                
                try:
                    result = await self._join_single_group_network_safe(client, group_link, task_id, account_phone)
                    
                    if result == 'success':
                        successful_joins += 1
                        self._safe_log("success", task_id, f"✅ Joined: {group_link}")
                    elif result == 'already_joined':
                        skipped_count += 1
                        self._safe_log("info", task_id, f"⏭️ Already joined: {group_link}")
                    elif result == 'flood_wait':
                        self._safe_log("warning", task_id, f"⏳ Flood wait triggered - pausing {account_phone}")
                        return 'flood_wait'
                    elif result == 'network_error':
                        # Network error - retry after delay
                        self._safe_log("warning", task_id, f"🌐 Network error: {group_link} - will retry")
                        await asyncio.sleep(self.RETRY_DELAY)
                        continue
                    else:
                        failed_joins += 1
                        
                except Exception as e:
                    failed_joins += 1
                    self._safe_log("error", task_id, f"❌ Error: {group_link} - {e}")
                
                # Update progress every 3 groups
                if i % 3 == 0:
                    self._safe_ui_update(
                        task_id,
                        status='running',
                        current_index=current_index,
                        successful_joins=successful_joins,
                        failed_joins=failed_joins
                    )
                
                # Smart delay with network awareness
                if i < total_groups - 1:
                    await self._apply_network_aware_delay(task)
            
            # Final results
            self._safe_ui_update(
                task_id,
                status='completed',
                current_index=current_index,
                successful_joins=successful_joins,
                failed_joins=failed_joins
            )
            
            self._safe_log("success", task_id, 
                f"🎯 NETWORK-SAFE COMPLETED! Joined: {successful_joins} | Failed: {failed_joins} | Skipped: {skipped_count}")
            
            return 'completed'
            
        except Exception as e:
            self._safe_log("error", task_id, f"Network task failed: {e}")
            self._safe_ui_update(task_id, status='failed')
            return 'failed'
        finally:
            if client:
                try:
                    await client.disconnect()
                except:
                    pass
    
    async def _get_safe_client(self, phone: str) -> Optional[TelegramClient]:
        """Get Telegram client with network safety and connection pooling."""
        try:
            # Check if we already have a working client
            if phone in self.client_pool:
                client = self.client_pool[phone]
                if client and client.is_connected():
                    return client
                else:
                    # Remove dead client
                    del self.client_pool[phone]
            
            # Create new client with network safety
            api_id = 20272919
            api_hash = "7d9cc3ad9b5b7e2b1c48c79e3eefee6d"
            session_path = f"sessions/{phone}.session"
            
            client = TelegramClient(
                session_path, 
                api_id, 
                api_hash,
                timeout=self.CONNECTION_TIMEOUT,
                request_retries=self.MAX_RETRIES,
                retry_delay=self.RETRY_DELAY
            )
            
            # Connection with timeout and retries
            for attempt in range(self.MAX_RETRIES):
                try:
                    await asyncio.wait_for(client.connect(), timeout=self.CONNECTION_TIMEOUT)
                    
                    if client.is_connected():
                        self.client_pool[phone] = client
                        return client
                    else:
                        raise ConnectionError(f"Client connection failed for {phone}")
                        
                except (ConnectionError, TimeoutError, OSError) as e:
                    if attempt < self.MAX_RETRIES - 1:
                        self._safe_log("warning", "", f"Connection attempt {attempt + 1} failed for {phone}: {e}")
                        await asyncio.sleep(self.RETRY_DELAY)
                    else:
                        self._safe_log("error", "", f"All connection attempts failed for {phone}: {e}")
                        raise
                        
        except Exception as e:
            self._safe_log("error", "", f"Failed to create safe client for {phone}: {e}")
            return None
    
    async def _join_single_group_network_safe(self, client, group_link: str, task_id: str, phone: str):
        """Join single group with comprehensive network error handling."""
        try:
            # Network-aware request with timeout
            try:
                result = await asyncio.wait_for(
                    client(telethon.functions.messages.ImportChatInviteRequest(
                        hash=group_link.split('/')[-1]
                    )),
                    timeout=self.REQUEST_TIMEOUT
                )
                return 'success'
                
            except asyncio.TimeoutError:
                self._safe_log("warning", task_id, f"⏱️ Request timeout: {group_link}")
                return 'network_error'
                
            except (ConnectionError, OSError) as e:
                if "1232" in str(e) or "network location" in str(e).lower():
                    self._safe_log("warning", task_id, f"🌐 Network unreachable: {group_link}")
                    return 'network_error'
                else:
                    raise
                    
        except FloodWaitError as e:
            # Handle flood wait per account
            self._set_flood_wait(phone, e.seconds)
            return 'flood_wait'
            
        except UserAlreadyParticipantError:
            return 'already_joined'
            
        except (ChannelPrivateError, InviteHashExpiredError, InviteHashInvalidError):
            return 'invalid_link'
            
        except (PeerFloodError, ChannelsTooMuchError, UserBannedInChannelError):
            return 'account_limited'
            
        except Exception as e:
            self._safe_log("error", task_id, f"Join error: {group_link} - {e}")
            return 'failed'
    
    def _check_network_connectivity(self) -> bool:
        """Check basic network connectivity."""
        try:
            import socket
            # Try to connect to Google DNS
            socket.create_connection(("*******", 53), timeout=5)
            return True
        except OSError:
            return False
    
    def _set_flood_wait(self, phone: str, seconds: int):
        """Set flood wait for specific account."""
        with self.flood_wait_lock:
            self.flood_waits[phone] = time.time() + seconds
    
    def _is_in_flood_wait(self, phone: str) -> bool:
        """Check if account is in flood wait."""
        with self.flood_wait_lock:
            if phone in self.flood_waits:
                return time.time() < self.flood_waits[phone]
            return False
    
    def _get_flood_wait_remaining(self, phone: str) -> int:
        """Get remaining flood wait time."""
        with self.flood_wait_lock:
            if phone in self.flood_waits:
                remaining = self.flood_waits[phone] - time.time()
                return max(0, int(remaining))
            return 0
    
    async def _apply_network_aware_delay(self, task):
        """Apply delay that's aware of network conditions."""
        try:
            import json
            settings = json.loads(task.get('settings', '{}'))
            delay_min = settings.get('delay_min', 30)
            delay_max = settings.get('delay_max', 90)
            
            # Adjust delay based on network conditions
            if self.active_connections > 5:
                # Increase delay if many connections active
                delay_min *= 1.5
                delay_max *= 1.5
            
            import random
            delay = random.randint(int(delay_min), int(delay_max))
            
            # Use chunked sleep for responsiveness
            chunk_size = 2
            elapsed = 0
            
            while elapsed < delay:
                await asyncio.sleep(min(chunk_size, delay - elapsed))
                elapsed += chunk_size
                
        except Exception:
            # Fallback delay
            await asyncio.sleep(30)
    
    def stop_joining_task_network_safe(self, task_id: str):
        """Network-safe task stopping."""
        try:
            if hasattr(self.main_app, 'active_safe_tasks') and task_id in self.main_app.active_safe_tasks:
                future = self.main_app.active_safe_tasks[task_id]
                future.cancel()
                del self.main_app.active_safe_tasks[task_id]
            
            self._safe_ui_update(task_id, status='stopped')
            self._safe_log("info", task_id, "🛑 Network-safe task stopped")
            
        except Exception as e:
            self._safe_log("error", task_id, f"Stop failed: {e}")
    
    def _safe_log(self, level: str, task_id: str, message: str):
        """Safe logging that won't crash."""
        try:
            if hasattr(self.main_app, 'log_joining_message'):
                self.main_app.log_joining_message(level, task_id, message)
            else:
                print(f"[{level.upper()}] {task_id}: {message}")
        except:
            print(f"[{level.upper()}] {task_id}: {message}")
    
    def _safe_ui_update(self, task_id: str, **kwargs):
        """Safe UI update that won't crash."""
        try:
            if hasattr(self.main_app, '_queue_ui_update_optimized'):
                self.main_app._queue_ui_update_optimized(task_id, **kwargs)
            elif hasattr(self.main_app, 'update_joining_task_status'):
                self.main_app.update_joining_task_status(task_id, **kwargs)
            else:
                print(f"UI Update {task_id}: {kwargs}")
        except:
            print(f"UI Update {task_id}: {kwargs}")
    
    def _start_resource_monitor(self):
        """Start background resource monitoring."""
        def monitor_loop():
            while True:
                try:
                    # Monitor network and resources
                    import psutil
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory_info = psutil.virtual_memory()
                    
                    if cpu_percent > 80:
                        print(f"⚠️ High CPU usage: {cpu_percent}%")
                    
                    if memory_info.percent > 80:
                        print(f"⚠️ High memory usage: {memory_info.percent}%")
                    
                    # Clean up dead connections
                    self._cleanup_dead_connections()
                    
                    time.sleep(30)  # Check every 30 seconds
                    
                except Exception as e:
                    print(f"Resource monitor error: {e}")
                    time.sleep(60)
        
        threading.Thread(target=monitor_loop, daemon=True, name="ResourceMonitor").start()
    
    def _cleanup_dead_connections(self):
        """Clean up dead connections from the pool."""
        try:
            dead_clients = []
            for phone, client in self.client_pool.items():
                if not client or not client.is_connected():
                    dead_clients.append(phone)
            
            for phone in dead_clients:
                del self.client_pool[phone]
                
        except Exception as e:
            print(f"Connection cleanup error: {e}")
    
    def _monitor_resources(self):
        """Get current resource status."""
        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'active_connections': self.active_connections,
                'client_pool_size': len(self.client_pool),
                'flood_waits': len(self.flood_waits)
            }
        except:
            return {'error': 'Unable to get resource info'}
    
    def _get_network_status(self):
        """Get network connectivity status."""
        return {
            'connectivity': self._check_network_connectivity(),
            'active_connections': self.active_connections,
            'max_connections': self.max_concurrent_connections,
            'client_pool_size': len(self.client_pool)
        }


def apply_critical_network_fix(main_app):
    """Apply critical network fix to main application."""
    try:
        # Create and apply the fix
        network_fix = CriticalNetworkFix(main_app)
        network_fix.apply_fix_to_main_app()
        
        # Store reference for later use
        main_app._network_fix = network_fix
        
        print("🔥 CRITICAL NETWORK FIX APPLIED SUCCESSFULLY!")
        print("✅ Network error handling active")
        print("✅ Connection pooling active") 
        print("✅ Resource monitoring active")
        print("✅ Flood wait management active")
        
        return True
        
    except Exception as e:
        print(f"❌ FAILED TO APPLY NETWORK FIX: {e}")
        return False


if __name__ == "__main__":
    print("Critical Network Connection Fix - Ready for application") 