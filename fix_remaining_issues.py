import re
import os
import shutil

def fix_remaining_issues(filename="main.py"):
    """Fix all remaining issues shown in the problems panel."""
    # Create a backup
    backup_file = f"{filename}.remaining_bak"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.readlines()
    
    # Fix line 576 - Expected indented block
    for i in range(570, 580):
        if i < len(content) and "if self.settings.value(\"auto_start_monitor\"" in content[i]:
            # Fix indentation of the next line
            indent = ' ' * (content[i].find('if'))
            content[i+1] = indent + "    self.start_monitor()\n"
            print(f"Fixed indentation on line {i+1}")
    
    # Fix line 3013-3014 - Expected expression & Unexpected indentation
    for i in range(3010, 3020):
        if i < len(content) and i+1 < len(content):
            if "else:" in content[i] and not content[i].startswith(' '):
                # Find appropriate indentation level
                for j in range(max(i-10, 0), i):
                    if "if " in content[j]:
                        indent = ' ' * (content[j].find('if') - 4)
                        content[i] = indent + "            else:\n"
                        content[i+1] = indent + "                " + content[i+1].lstrip()
                        print(f"Fixed lines {i+1}-{i+2}")
                        break
    
    # Fix line 3062 - Try statement must have at least one except or finally clause
    for i in range(3060, 3070):
        if i < len(content) and "try:" in content[i]:
            # Find the end of try block
            try_indent = ' ' * content[i].find('try')
            try_end = i + 1
            while try_end < len(content):
                if content[try_end].strip() and len(content[try_end]) - len(content[try_end].lstrip()) <= len(try_indent):
                    break
                try_end += 1
            
            # Check if there's an except after the try block
            has_except = False
            for j in range(i+1, min(try_end+1, len(content))):
                if "except" in content[j]:
                    has_except = True
                    break
            
            if not has_except:
                # Add except block
                content.insert(try_end, try_indent + "    self.logger.error(f\"Error: {str(e)}\")\n")
                content.insert(try_end, try_indent + "except Exception as e:\n")
                print(f"Added except block after try on line {i+1}")
    
    # Fix lines 3073-3074 - Unexpected indentation & Expected expression
    for i in range(3070, 3080):
        if i < len(content) and content[i].strip() and not content[i].startswith(' '):
            # This line should be indented - find appropriate level
            indent = "            "
            content[i] = indent + content[i].lstrip()
            print(f"Fixed indentation on line {i+1}")
            
            # If this is a method call that needs a variable 'e', make sure it's defined
            if "e" in content[i] and "Exception as e" not in ''.join(content[max(i-5, 0):i]):
                # We need to wrap this in a try-except
                indent_level = len(indent)
                try_indent = ' ' * (indent_level - 4)
                
                # Insert try before this line
                content.insert(i, try_indent + "try:\n")
                
                # Make sure the indentation is correct for the current line
                content[i+1] = indent + content[i+1].lstrip()
                
                # Insert except after this line
                content.insert(i+2, try_indent + "except Exception as e:\n")
                content.insert(i+3, indent + "pass\n")
                
                print(f"Added try-except with 'e' variable around line {i+1}")
    
    # Fix line 3086 - Expected expression
    for i in range(3080, 3090):
        if i < len(content) and "else" in content[i] and not content[i].strip().endswith(':'):
            content[i] = content[i].rstrip() + ":\n"
            print(f"Added missing colon on line {i+1}")
    
    # Fix undefined variable 'self_outer' on line 2981
    for i in range(2975, 2985):
        if i < len(content) and "self_outer" in content[i]:
            # Replace with 'self'
            content[i] = content[i].replace("self_outer", "self")
            print(f"Fixed undefined variable 'self_outer' on line {i+1}")
    
    # Fix undefined 'e' variables on lines 3074, 3075
    for i in range(3070, 3080):
        if i < len(content) and "e" in content[i] and "Exception as e" not in ''.join(content[max(i-5, 0):i]):
            # Check if this is within a try block
            in_try_block = False
            for j in range(max(i-10, 0), i):
                if "try:" in content[j]:
                    in_try_block = True
                    break
            
            if not in_try_block:
                # Modify to use a safe default error message
                content[i] = content[i].replace("str(e)", "\"Unknown error\"")
                print(f"Fixed undefined 'e' variable on line {i+1}")
    
    # Save the fixed content
    output_file = "main_fully_fixed.py"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(content)
    
    print(f"Fixed all remaining issues and saved to {output_file}")
    return output_file

if __name__ == "__main__":
    output_file = fix_remaining_issues()
    
    # Create batch files
    batch_content = """@echo off
echo TG Checker - All issues fixed!
echo Running completely fixed application...
python main_fully_fixed.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
"""
    
    with open("run_fully_fixed.bat", "w") as f:
        f.write(batch_content)
    
    # Create Kurdish version
    batch_content_kurdish = """@echo off
echo TG Checker - Hamu helakan chareserkrawn!
echo Jarandni bernameyek bi tawawi chareserkraw...
python main_fully_fixed.py
if %errorlevel% neq 0 (
    echo Helayek ruida! Sairi failakani log bka bo zaniari ziatr.
    pause
)
pause
"""
    
    with open("run_fully_fixed_kurdish.bat", "w") as f:
        f.write(batch_content_kurdish)
    
    print(f"Created batch files to run the fully fixed application")
    print(f"You can now run: python {output_file}")
    print(f"Or use the batch files: run_fully_fixed.bat or run_fully_fixed_kurdish.bat") 