#!/usr/bin/env python
import sys
import os
import logging
import asyncio
from tg_client import TelegramClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("tg_filter_test")

async def test_group_filtering():
    """Test if the group filtering is working correctly."""
    # Sample credentials - replace with actual values if needed
    api_id = 611335  # Default placeholder
    api_hash = "session_placeholder"  # Default placeholder
    
    # Find first available session file
    session_files = os.listdir("sessions")
    session_files = [f for f in session_files if f.endswith('.session')]
    
    if not session_files:
        logger.error("No session files found in the 'sessions' directory.")
        return
    
    # Get phone from session file name
    phone = session_files[0].replace('.session', '')
    
    # Create client
    client = TelegramClient(api_id, api_hash, phone, f"sessions/{phone}")
    
    # Test groups
    test_groups = [
        "https://t.me/THE_SELLERS_MARKET",
        "https://t.me/BuysellZone1",
        "https://t.me/EscrowersCommunity",
        "https://t.me/Bineros_Full"
    ]
    
    # Connect and check each group
    connected = await client.connect()
    if not connected:
        logger.error(f"Failed to connect with account {phone}")
        return
    
    try:
        for group_link in test_groups:
            logger.info(f"Testing group: {group_link}")
            result = await client.get_group_info(group_link)
            
            # Log the result
            if "valid" in result and result["valid"]:
                group_type = result.get("type", "unknown")
                
                # Check filter data
                member_count = result.get("member_count", 0)
                last_message_age = result.get("last_message_age_hours", 999)
                total_messages = result.get("total_messages", 0)
                
                logger.info(f"Group {group_link} - Type: {group_type}")
                logger.info(f"Filter data: Members: {member_count}, Activity: {last_message_age:.2f}h, Messages: {total_messages}")
                
                if group_type == "valid_filtered":
                    logger.info(f"✅ SUCCESS: Group {group_link} correctly classified as VALID FILTERED")
                else:
                    logger.error(f"❌ FAILURE: Group {group_link} was not classified as VALID FILTERED")
                    logger.info(f"It was classified as: {group_type}")
            else:
                error = result.get("error", "Unknown error")
                error_type = result.get("error_type", "unknown")
                logger.error(f"❌ Group {group_link} could not be accessed: {error} (Type: {error_type})")
    finally:
        # Disconnect
        await client.disconnect()
        logger.info("Test completed.")

if __name__ == "__main__":
    # Run the test
    asyncio.run(test_group_filtering())
    print("\nTesting completed. Check the logs above for results.")
    print("If groups are correctly identified as 'valid_filtered', the fix was successful.") 