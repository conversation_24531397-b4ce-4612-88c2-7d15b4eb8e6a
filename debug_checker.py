#!/usr/bin/env python3
"""
Debug script for TG Checker tool
This will help identify why the checker is stuck or not processing groups.
"""

import sys
import os
import time
import threading
from PyQt5.QtWidgets import QApplication

def debug_checker():
    """Debug the checker tool to identify blocking issues."""
    print("🔧 TG Checker Debug Tool")
    print("=" * 50)
    
    # Check if main.py can be imported
    try:
        print("🔍 Testing main.py import...")
        sys.path.append('.')
        import main
        print("✅ main.py imported successfully")
    except Exception as e:
        print(f"❌ Error importing main.py: {e}")
        return
    
    # Test account manager
    try:
        print("🔍 Testing account manager...")
        from account_manager_fixed_enhanced import AccountManager
        am = AccountManager()
        accounts = am.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        print(f"✅ Account manager working - {len(active_accounts)} active accounts")
        
        if not active_accounts:
            print("⚠️ WARNING: No active accounts found!")
            print("   This will prevent the checker from working.")
        else:
            for i, acc in enumerate(active_accounts[:3]):  # Show first 3
                phone = acc.get('phone', 'Unknown')
                print(f"   Account {i+1}: {phone}")
    except Exception as e:
        print(f"❌ Account manager error: {e}")
    
    # Test TelegramClient import
    try:
        print("🔍 Testing TelegramClient import...")
        from tg_client_fixed import TelegramClient
        print("✅ TelegramClient imported successfully")
    except Exception as e:
        print(f"❌ TelegramClient import error: {e}")
    
    # Test database access
    try:
        print("🔍 Testing database access...")
        import sqlite3
        if os.path.exists('tg_checker.db'):
            conn = sqlite3.connect('tg_checker.db', timeout=5.0)
            cursor = conn.cursor()
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = cursor.fetchall()
            conn.close()
            print(f"✅ Database accessible - {len(tables)} tables found")
        else:
            print("⚠️ WARNING: tg_checker.db not found")
    except Exception as e:
        print(f"❌ Database error: {e}")
    
    # Test group processing simulation
    try:
        print("🔍 Testing group processing simulation...")
        test_links = [
            "https://t.me/test_group1",
            "https://t.me/test_group2", 
            "https://t.me/test_group3"
        ]
        
        print(f"   Processing {len(test_links)} test links...")
        for i, link in enumerate(test_links):
            print(f"   [{i+1}/{len(test_links)}] Simulating check: {link}")
            time.sleep(0.1)  # Simulate processing time
        print("✅ Group processing simulation completed")
    except Exception as e:
        print(f"❌ Group processing simulation error: {e}")
    
    # Test threading
    try:
        print("🔍 Testing threading capability...")
        
        def test_thread():
            print("   Background thread started successfully")
            time.sleep(1)
            print("   Background thread completed")
        
        thread = threading.Thread(target=test_thread, daemon=True)
        thread.start()
        thread.join(timeout=3)
        print("✅ Threading working correctly")
    except Exception as e:
        print(f"❌ Threading error: {e}")
    
    print("\n🎯 DEBUG RECOMMENDATIONS:")
    print("=" * 50)
    
    # Check for common issues
    if not active_accounts:
        print("❌ CRITICAL: No active accounts found")
        print("   → Solution: Go to Accounts tab and activate at least one account")
    
    if not os.path.exists('tg_checker.db'):
        print("❌ CRITICAL: Database file missing")
        print("   → Solution: Run the app once to create initial database")
    
    print("✅ DEBUGGING STEPS TO TRY:")
    print("   1. Check console output when clicking Start Checker")
    print("   2. Verify accounts are properly activated")
    print("   3. Test with just 1-2 groups first")
    print("   4. Check if sync is working (should not say 'disabled')")
    print("   5. Look for error messages in logs/")
    
    print("\n📋 LIVE TESTING:")
    print("   Add this to a test group list:")
    print("   https://t.me/telegram")
    print("   https://t.me/durov")
    print("   (These are public channels that should always work)")
    
    print("\n🚀 If checker still stuck, check:")
    print("   - Windows Defender/Antivirus blocking")
    print("   - Network connectivity")
    print("   - Telegram API limits")

if __name__ == "__main__":
    debug_checker()
    input("\nPress Enter to exit...") 