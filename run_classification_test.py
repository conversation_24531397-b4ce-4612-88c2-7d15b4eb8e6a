#!/usr/bin/env python3
"""
🎯 FOCUSED CLASSIFICATION TEST
Runs TG Checker on our 4 test groups and analyzes the results
"""

import subprocess
import time
import os
import shutil
from datetime import datetime

# Test groups to verify
TEST_GROUPS = [
    "https://t.me/hyipinformer_com",
    "https://t.me/islamic_hacker_army", 
    "https://t.me/imperiamarket",
    "https://t.me/infocoindogroup"
]

# Expected classifications
EXPECTED = {
    "hyipinformer_com": "Groups_Valid_Only",
    "islamic_hacker_army": "Groups_Valid_Only",
    "imperiamarket": "Groups_Valid_Filter", 
    "infocoindogroup": "Groups_Valid_Filter"
}

def backup_and_clear_results():
    """Backup existing results and clear for fresh test."""
    print("🧹 Preparing fresh test environment...")
    
    # Backup existing results
    if os.path.exists("Results"):
        backup_name = f"Results_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.move("Results", backup_name)
        print(f"   ✅ Backed up existing results to: {backup_name}")
    
    # Create fresh Results folders
    os.makedirs("Results", exist_ok=True)
    for folder in ["Groups_Valid_Filter", "Groups_Valid_Only", "Topics_Groups_Only_Valid", 
                   "Channels_Only_Valid", "Invalid_Groups_Channels", "Account_Issues"]:
        os.makedirs(f"Results/{folder}", exist_ok=True)
    
    print("   ✅ Created fresh Results folder structure")

def create_test_input():
    """Create test input file with our 4 groups."""
    test_content = "\n".join(TEST_GROUPS)
    with open("test_input.txt", "w") as f:
        f.write(test_content)
    print(f"📝 Created test_input.txt with {len(TEST_GROUPS)} groups")

def check_results():
    """Check where each group ended up."""
    print("\n🔍 CLASSIFICATION RESULTS:")
    print("=" * 50)
    
    results = {}
    
    # Check each classification folder
    folders_to_check = {
        "Groups_Valid_Filter": "Groups_Valid_Filter",
        "Groups_Valid_Only": "Groups_Valid_Only", 
        "Topics_Groups_Only_Valid": "Topics_Groups_Only_Valid",
        "Channels_Only_Valid": "Channels_Only_Valid",
        "Invalid_Groups_Channels": "Invalid_Groups_Channels",
        "Account_Issues": "Account_Issues"
    }
    
    for folder_name, display_name in folders_to_check.items():
        folder_path = f"Results/{folder_name}"
        if os.path.exists(folder_path):
            files = os.listdir(folder_path)
            if files:
                print(f"\n📂 {display_name}:")
                for file in files:
                    if file.endswith('.txt'):
                        try:
                            with open(os.path.join(folder_path, file), 'r') as f:
                                content = f.read().strip()
                                if content:
                                    print(f"   📄 {file}:")
                                    for line in content.split('\n'):
                                        if line.strip() and 'https://t.me/' in line:
                                            username = line.replace('https://t.me/', '').strip()
                                            results[username] = display_name
                                            print(f"      • {username}")
                        except Exception as e:
                            print(f"   ❌ Error reading {file}: {e}")
    
    # Analyze results
    print(f"\n🎯 CLASSIFICATION ANALYSIS:")
    print("=" * 50)
    
    correct = 0
    total = len(EXPECTED)
    
    for group_url in TEST_GROUPS:
        username = group_url.replace('https://t.me/', '')
        expected = EXPECTED[username]
        actual = results.get(username, "NOT_FOUND")
        
        if actual == expected:
            print(f"✅ {username}: {actual} (CORRECT)")
            correct += 1
        else:
            print(f"❌ {username}: Expected {expected}, Got {actual}")
    
    accuracy = (correct / total) * 100
    print(f"\n📊 ACCURACY: {correct}/{total} ({accuracy:.1f}%)")
    
    if accuracy == 100:
        print("🎉 PERFECT CLASSIFICATION! All groups correctly classified.")
    else:
        print("⚠️  Classification needs improvement.")
    
    return accuracy

def main():
    """Run the focused classification test."""
    print("🎯 FOCUSED CLASSIFICATION TEST")
    print("=" * 50)
    
    # Step 1: Prepare environment
    backup_and_clear_results()
    
    # Step 2: Create test input
    create_test_input()
    
    # Step 3: Instructions for user
    print(f"\n📋 NEXT STEPS:")
    print(f"1. ✅ Test environment prepared")
    print(f"2. ✅ Test input file created: test_input.txt")
    print(f"3. 🔄 NOW: Open TG Checker GUI and:")
    print(f"   • Load test_input.txt as your groups")
    print(f"   • Make sure filter settings are: Min Members=500, Max Activity=1h, Min Messages=100")
    print(f"   • Click 'Start Checking'")
    print(f"   • Wait for completion")
    print(f"4. ⏳ THEN: Run 'python run_classification_test.py check' to analyze results")
    
    print(f"\n🎯 Expected Results:")
    for group_url in TEST_GROUPS:
        username = group_url.replace('https://t.me/', '')
        expected = EXPECTED[username]
        print(f"   • {username} → {expected}")

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "check":
        check_results()
    else:
        main() 