import os
import json
import sqlite3
import logging
from datetime import datetime
import asyncio
import threading
import time
from contextlib import contextmanager
from tg_client import TelegramClient

def fix_db_lock(db_path):
    """Utility function to fix database lock issues by creating a backup and recovering if necessary."""
    try:
        # First try to create a backup of the current database
        backup_path = f"{db_path}.backup_{int(datetime.now().timestamp())}"
        import shutil
        
        # Only backup if the database file exists
        if os.path.exists(db_path):
            shutil.copy2(db_path, backup_path)
            logging.info(f"Created database backup at {backup_path}")
            
            try:
                # Try to connect with DELETE journal mode
                conn = sqlite3.connect(db_path, timeout=60.0)
                
                # Try to switch to DELETE journal mode
                conn.execute("PRAGMA journal_mode=DELETE")
                conn.commit()
                
                # Run VACUUM to compact the database
                conn.execute("VACUUM")
                conn.commit()
                
                # Switch back to WAL mode
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA busy_timeout=60000")
                conn.commit()
                
                conn.close()
                logging.info("Database cleanup completed successfully")
                return True
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e):
                    logging.warning("Database still locked during fix attempt, trying alternative method")
                    # Database is still locked, try alternative approach
                    try:
                        # Wait a bit for locks to clear
                        time.sleep(2)
                        
                        # Delete the current database and restore from backup
                        if os.path.exists(db_path):
                            os.remove(db_path)
                            time.sleep(1)
                            
                        # Restore from backup
                        shutil.copy2(backup_path, db_path)
                        logging.info(f"Restored database from backup after lock issues")
                        return True
                    except Exception as restore_err:
                        logging.error(f"Failed to restore database from backup: {str(restore_err)}")
                else:
                    logging.error(f"Database error during fix attempt: {str(e)}")
        else:
            logging.warning(f"Database file {db_path} not found, can't fix locks")
            return False
            
    except Exception as e:
        logging.error(f"Error fixing database lock: {str(e)}")
        return False
        
    return False

class AccountManager:
    """Manages Telegram accounts for the application."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self.accounts = []
        self.clients = {}
        
        # Add database connection lock for thread safety
        self._db_lock = threading.RLock()
        
        # Initialize database
        self._init_database()
        
        # Load accounts from database
        self._load_accounts()
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 5
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                with self._db_lock:
                    conn = sqlite3.connect(self.db_path, timeout=60.0)  # Increased timeout from 30 to 60 seconds
                    conn.row_factory = sqlite3.Row
                    # Enable WAL mode for better concurrent access
                    conn.execute("PRAGMA journal_mode=WAL")
                    # Set busy timeout
                    conn.execute("PRAGMA busy_timeout=60000")  # Increased from 30000 to 60000ms
                    # Disable synchronous writes for better performance (still safe with WAL)
                    conn.execute("PRAGMA synchronous=NORMAL")
                    yield conn
                    return  # Successfully got connection, exit retry loop
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    # Database is locked, retry after delay
                    self.logger.warning(f"Database is locked, retrying in {retry_delay} seconds (attempt {attempt+1}/{max_retries})")
                    if conn:
                        try:
                            conn.close()
                        except:
                            pass
                    conn = None
                    time.sleep(retry_delay)
                    # Increase retry delay for next attempt (exponential backoff)
                    retry_delay *= 1.5
                    
                    # On the second attempt, try to fix database locks
                    if attempt == 1:
                        try:
                            fix_db_lock(self.db_path)
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database locks: {str(fix_err)}")
                    
                    continue
                else:
                    # Other error or max retries reached
                    if conn:
                        try:
                            conn.rollback()
                        except:
                            pass
                    self.logger.error(f"Database error after {attempt+1} attempts: {str(e)}")
                    raise
            except Exception as e:
                if conn:
                    try:
                        conn.rollback()
                    except:
                        pass
                self.logger.error(f"Database error: {str(e)}")
                raise
            finally:
                if conn and attempt == max_retries - 1:
                    try:
                        conn.close()
                    except:
                        pass
    
    def _init_database(self):
        """Initialize the SQLite database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create accounts table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS accounts (
                        phone TEXT PRIMARY KEY,
                        api_id TEXT,
                        api_hash TEXT,
                        session_file TEXT,
                        active INTEGER DEFAULT 0,
                        status TEXT DEFAULT 'unknown',
                        last_check TEXT,
                        errors INTEGER DEFAULT 0,
                        notes TEXT,
                        name TEXT,
                        username TEXT,
                        error_message TEXT,
                        disabled_until TEXT,
                        account_age_days INTEGER DEFAULT 0,
                        is_aged INTEGER DEFAULT 0,
                        daily_group_limit INTEGER DEFAULT 0,
                        last_age_check TEXT,
                        account_type TEXT DEFAULT 'normal'
                    )
                ''')
                
                # Add new columns to existing accounts table if they don't exist
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_age_days INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN is_aged INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN daily_group_limit INTEGER DEFAULT 0")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN last_age_check TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add missing columns that were causing errors
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN name TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                    
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN username TEXT")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Add account_type column for session vs normal accounts
                try:
                    cursor.execute("ALTER TABLE accounts ADD COLUMN account_type TEXT DEFAULT 'normal'")
                except sqlite3.OperationalError:
                    pass  # Column already exists
                
                # Create errors table if it doesn't exist
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS errors (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        phone TEXT,
                        error_type TEXT,
                        error_message TEXT,
                        timestamp TEXT,
                        resolved INTEGER DEFAULT 0,
                        FOREIGN KEY (phone) REFERENCES accounts (phone)
                    )
                ''')
                
                # Create blacklist table for permanently deleted accounts
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS deleted_accounts (
                        phone TEXT PRIMARY KEY,
                        deleted_at TEXT,
                        reason TEXT DEFAULT 'user_deleted'
                    )
                ''')
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def _load_accounts(self):
        """Load accounts from the database."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM accounts")
                rows = cursor.fetchall()
                
                self.accounts = [dict(row) for row in rows]
                
            self.logger.info(f"Loaded {len(self.accounts)} accounts from database")
            
        except Exception as e:
            self.logger.error(f"Error loading accounts: {str(e)}")
    
    def get_accounts(self):
        """Get all accounts."""
        return self.accounts
    
    def get_active_accounts(self):
        """Get only active accounts."""
        return [acc for acc in self.accounts if acc.get("active", 0) == 1]
    
    def get_account(self, phone):
        """Get a specific account by phone number."""
        for account in self.accounts:
            if account.get("phone") == phone:
                return account
        return None
    
    def add_account(self, phone, api_id, api_hash, active=False, account_type="normal"):
        """Add a new account to the database."""
        try:
            # Check if account already exists
            existing_account = self.get_account(phone)
            if existing_account:
                # Update existing account instead of raising error
                self.logger.info(f"Updating existing account {phone}")
                return self.update_account_credentials(phone, api_id, api_hash, account_type)
            
            # Use multiple retries for database operations
            max_retries = 3
            retry_delay = 1.0
            
            for attempt in range(max_retries):
                try:
                    with self._get_db_connection() as conn:
                        cursor = conn.cursor()
                        
                        # Set session file path based on phone number (remove + sign)
                        clean_phone = phone.replace('+', '')
                        session_file = f"sessions/{clean_phone}"
                        
                        # Ensure sessions directory exists
                        os.makedirs("sessions", exist_ok=True)
                        
                        cursor.execute(
                            "INSERT INTO accounts (phone, api_id, api_hash, session_file, active, status, last_check, account_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                            (phone, api_id, api_hash, session_file, 1 if active else 0, "new", datetime.now().isoformat(), account_type)
                        )
                        
                        conn.commit()
                    
                    # Reload accounts
                    self._load_accounts()
                    
                    self.logger.info(f"Added {account_type} account {phone}")
                    return True
                    
                except sqlite3.OperationalError as e:
                    if "database is locked" in str(e) and attempt < max_retries - 1:
                        self.logger.warning(f"Database locked when adding account {phone}, retrying ({attempt+1}/{max_retries})...")
                        time.sleep(retry_delay)
                        retry_delay *= 1.5
                        
                        # On the second attempt, try to fix database locks
                        if attempt == 1:
                            try:
                                fix_db_lock(self.db_path)
                            except Exception as fix_err:
                                self.logger.error(f"Failed to fix database locks: {str(fix_err)}")
                        
                        continue
                    else:
                        self.logger.error(f"Database error adding account {phone}: {str(e)}")
                        raise
                except Exception as e:
                    self.logger.error(f"Error adding account {phone}: {str(e)}")
                    raise
                
        except Exception as e:
            self.logger.error(f"Error adding account {phone}: {str(e)}")
            return False
    
    def update_account_credentials(self, phone, api_id, api_hash, account_type="normal"):
        """Update account credentials and type."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET api_id = ?, api_hash = ?, account_type = ?, status = ? WHERE phone = ?",
                    (api_id, api_hash, account_type, "updated", phone)
                )
                
                conn.commit()
            
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Updated credentials for {account_type} account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account credentials {phone}: {str(e)}")
            return False
    
    def remove_account(self, phone):
        """Remove an account from the database and add to blacklist."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Add to blacklist first
                cursor.execute(
                    "INSERT OR REPLACE INTO deleted_accounts (phone, deleted_at, reason) VALUES (?, ?, ?)",
                    (phone, datetime.now().isoformat(), "user_deleted")
                )
                
                # Delete errors for this account
                cursor.execute("DELETE FROM errors WHERE phone = ?", (phone,))
                
                # Delete the account
                cursor.execute("DELETE FROM accounts WHERE phone = ?", (phone,))
                
                conn.commit()
            
            # Reload accounts
            self._load_accounts()
            
            self.logger.info(f"Removed account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing account {phone}: {str(e)}")
            return False
    
    def is_account_blacklisted(self, phone):
        """Check if an account is in the blacklist (permanently deleted)."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT phone FROM deleted_accounts WHERE phone = ?", (phone,))
                result = cursor.fetchone()
                return result is not None
        except Exception as e:
            self.logger.error(f"Error checking blacklist for {phone}: {str(e)}")
            return False
    
    def get_blacklisted_accounts(self):
        """Get all blacklisted (permanently deleted) accounts."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT phone, deleted_at, reason FROM deleted_accounts ORDER BY deleted_at DESC")
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
        except Exception as e:
            self.logger.error(f"Error getting blacklisted accounts: {str(e)}")
            return []
    
    def remove_from_blacklist(self, phone):
        """Remove an account from the blacklist (allow re-import)."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("DELETE FROM deleted_accounts WHERE phone = ?", (phone,))
                conn.commit()
            
            self.logger.info(f"Removed {phone} from blacklist")
            return True
        except Exception as e:
            self.logger.error(f"Error removing {phone} from blacklist: {str(e)}")
            return False
    
    def activate_account(self, phone):
        """Activate an account (allows multiple active accounts)."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Simply activate the specified account without deactivating others
            cursor.execute(
                "UPDATE accounts SET active = 1 WHERE phone = ?",
                (phone,)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["active"] = 1
            
            self.logger.info(f"Activated account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error activating account {phone}: {str(e)}")
            return False
    
    def deactivate_account(self, phone):
        """Deactivate an account."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "UPDATE accounts SET active = 0 WHERE phone = ?",
                (phone,)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["active"] = 0
            
            self.logger.info(f"Deactivated account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deactivating account {phone}: {str(e)}")
            return False
    
    def activate_all(self):
        """Activate all accounts."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Activate all accounts
            cursor.execute("UPDATE accounts SET active = 1")
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                account["active"] = 1
            
            self.logger.info("Activated all accounts")
            return True
            
        except Exception as e:
            self.logger.error(f"Error activating all accounts: {str(e)}")
            return False
    
    def deactivate_all(self):
        """Deactivate all accounts."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("UPDATE accounts SET active = 0")
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                account["active"] = 0
            
            self.logger.info("Deactivated all accounts")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deactivating all accounts: {str(e)}")
            return False
    
    def update_account_status(self, phone, status, error_count=None):
        """Update the status of an account."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            if error_count is not None:
                cursor.execute(
                    "UPDATE accounts SET status = ?, last_check = ?, errors = ? WHERE phone = ?",
                    (status, datetime.now().isoformat(), error_count, phone)
                )
            else:
                cursor.execute(
                    "UPDATE accounts SET status = ?, last_check = ? WHERE phone = ?",
                    (status, datetime.now().isoformat(), phone)
                )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["status"] = status
                    account["last_check"] = datetime.now().isoformat()
                    if error_count is not None:
                        account["errors"] = error_count
            
            self.logger.info(f"Updated status for account {phone} to {status}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account {phone} status: {str(e)}")
            return False
    
    def log_error(self, phone, error_type, error_message):
        """Log an error for an account."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "INSERT INTO errors (phone, error_type, error_message, timestamp) VALUES (?, ?, ?, ?)",
                (phone, error_type, error_message, datetime.now().isoformat())
            )
            
            # Increment error count for the account
            cursor.execute(
                "UPDATE accounts SET errors = errors + 1 WHERE phone = ?",
                (phone,)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["errors"] = account.get("errors", 0) + 1
            
            self.logger.warning(f"Logged error for account {phone}: {error_type}: {error_message}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error logging error for account {phone}: {str(e)}")
            return False
    
    def resolve_errors(self, phone):
        """Mark all errors for an account as resolved."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute(
                "UPDATE errors SET resolved = 1 WHERE phone = ?",
                (phone,)
            )
            
            cursor.execute(
                "UPDATE accounts SET errors = 0 WHERE phone = ?",
                (phone,)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["errors"] = 0
            
            self.logger.info(f"Resolved all errors for account {phone}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error resolving errors for account {phone}: {str(e)}")
            return False
    
    def sync_accounts(self):
        """Synchronize accounts with the database."""
        try:
            # Simply reload accounts from the database
            self._load_accounts()
            return True
        except Exception as e:
            self.logger.error(f"Error syncing accounts: {str(e)}")
            return False
    
    async def check_account(self, phone):
        """Check if an account is working and update its status."""
        try:
            account = self.get_account(phone)
            if not account:
                return False
            
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            session_file = account.get("session_file")
            account_type = account.get("account_type", "normal")
            
            # Handle legacy session-imported accounts with invalid placeholders
            if account_type == "session" and (api_id == "session_import" or api_hash == "session_import"):
                # Try to extract real API credentials from session file
                real_api_id, real_api_hash = self._extract_api_credentials_from_session(session_file)
                
                if real_api_id and real_api_hash:
                    # Update the account with real credentials
                    self.update_account_credentials(phone, real_api_id, real_api_hash, "session")
                    api_id = real_api_id
                    api_hash = real_api_hash
                    self.logger.info(f"Extracted and updated API credentials for session account {phone}")
                else:
                    # Use default credentials for session-only accounts
                    self.update_account_credentials(phone, "611335", "session_placeholder", "session")
                    api_id = "611335"
                    api_hash = "session_placeholder"
                    self.logger.info(f"Updated legacy session account {phone} with default credentials")
            
            # Create client and test connection
            client = TelegramClient(api_id, api_hash, phone, session_file, self.logger)
            
            # Test the account
            if await client.connect():
                await client.disconnect()
                self.update_account_status(phone, "active")
                self.logger.info(f"Account {phone} is working")
                return True
            else:
                self.update_account_status(phone, "inactive")
                self.logger.warning(f"Account {phone} connection failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Error checking account {phone}: {str(e)}")
            self.update_account_status(phone, "error")
            return False
    
    def _extract_api_credentials_from_session(self, session_file):
        """Extract API credentials from a Telethon session file."""
        try:
            if not session_file or not os.path.exists(session_file):
                return None, None
                
            if session_file.endswith('.session'):
                import sqlite3
                conn = sqlite3.connect(session_file, timeout=5)
                cursor = conn.cursor()
                
                # Try to get API ID from sessions table
                cursor.execute("SELECT api_id FROM sessions LIMIT 1")
                result = cursor.fetchone()
                api_id = result[0] if result else None
                
                conn.close()
                
                # We can get API ID but not API Hash from session files
                # Return what we can extract
                return api_id, None
                
        except Exception as e:
            self.logger.debug(f"Could not extract API credentials from session {session_file}: {str(e)}")
            
        return None, None
    
    async def check_all_accounts(self):
        """Check all active accounts."""
        active_accounts = self.get_active_accounts()
        results = []
        
        for account in active_accounts:
            phone = account.get("phone")
            result = await self.check_account(phone)
            results.append((phone, result))
            
            # Wait a bit to avoid rate limits
            await asyncio.sleep(1)
        
        return results
    
    async def fix_account(self, phone):
        """Try to fix common issues with an account."""
        account = self.get_account(phone)
        if not account:
            self.logger.error(f"Account {phone} not found for fixing")
            return False
            
        try:
            # First check the account to identify issues
            check_result = await self.check_account(phone)
            
            if check_result:
                # Account is already working fine
                self.logger.info(f"Account {phone} is already working fine")
                return True
                
            # Try to fix by clearing errors
            self.resolve_errors(phone)
            
            # Try checking the account again
            check_result = await self.check_account(phone)
            
            if check_result:
                self.logger.info(f"Fixed account {phone} by clearing errors")
                return True
                
            # More complex fixes could be implemented here
            
            self.logger.warning(f"Could not automatically fix account {phone}")
            return False
            
        except Exception as e:
            self.logger.error(f"Error fixing account {phone}: {str(e)}")
            return False
    
    async def fix_all_accounts(self):
        """Try to fix all accounts with issues."""
        accounts_with_issues = [acc for acc in self.accounts if acc.get("errors", 0) > 0]
        results = []
        
        for account in accounts_with_issues:
            phone = account.get("phone")
            result = await self.fix_account(phone)
            results.append((phone, result))
            
            # Wait a bit between accounts
            await asyncio.sleep(1)
        
        return results
    
    def backup_database(self, backup_path=None):
        """Create a backup of the database."""
        try:
            import shutil
            
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_dir = "backups"
                os.makedirs(backup_dir, exist_ok=True)
                backup_path = os.path.join(backup_dir, f"backup_{timestamp}.db")
            
            shutil.copy2(self.db_path, backup_path)
            
            self.logger.info(f"Created database backup at {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"Database backup error: {str(e)}")
            return None
    
    def update_account_info(self, phone, name=None, username=None):
        """Update account name and username."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            updates = []
            params = []
            
            if name is not None:
                updates.append("name = ?")
                params.append(name)
                
            if username is not None:
                updates.append("username = ?")
                params.append(username)
                
            if not updates:
                conn.close()
                return False, "No updates provided"
                
            params.append(phone)
            
            cursor.execute(
                f'UPDATE accounts SET {", ".join(updates)} WHERE phone = ?',
                tuple(params)
            )
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Updated account info for: {phone}")
            return True, "Account info updated successfully"
            
        except Exception as e:
            self.logger.error(f"Failed to update account info: {str(e)}")
            return False, str(e)
            
    def update_account_error(self, phone, error_message=None, disabled_until=None):
        """Update account error status."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            updates = []
            params = []
            
            if error_message is not None:
                updates.append("error_message = ?")
                params.append(error_message)
                
            if disabled_until is not None:
                updates.append("disabled_until = ?")
                params.append(disabled_until)
                
            if not updates:
                conn.close()
                return False, "No updates provided"
                
            # Increment error count
            updates.append("errors = errors + 1")
            
            params.append(phone)
            
            cursor.execute(
                f'UPDATE accounts SET {", ".join(updates)} WHERE phone = ?',
                tuple(params)
            )
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Updated error status for: {phone}")
            return True, "Account error status updated successfully"
            
        except Exception as e:
            self.logger.error(f"Failed to update account error status: {str(e)}")
            return False, str(e)
    
    async def check_account_age_with_bot(self, phone):
        """Check account age using @TGDNAbot."""
        account = self.get_account(phone)
        if not account:
            return False, "Account not found"
        
        client = None
        try:
            from logger import log_usage_checker
            
            # Create client for the account
            api_id = account.get("api_id")
            api_hash = account.get("api_hash")
            session_file = account.get("session_file")
            
            client = TelegramClient(api_id, api_hash, phone, session_file, self.logger)
            
            # Connect and check authorization with timeout
            try:
                connected = await asyncio.wait_for(client.connect(), timeout=15)
                if not connected:
                    log_usage_checker(self.logger, f"Failed to connect client for age check: {phone}", logging.ERROR)
                    return False, "Failed to connect client"
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Connection timeout for age check: {phone}", logging.ERROR)
                return False, "Connection timeout"
            
            try:
                authorized = await asyncio.wait_for(client.check_authorization(), timeout=10)
                if not authorized:
                    log_usage_checker(self.logger, f"Client not authorized for age check: {phone}", logging.ERROR)
                    return False, "Client not authorized"
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Authorization check timeout for age check: {phone}", logging.ERROR)
                return False, "Authorization timeout"
            
            # Get the @TGDNAbot entity with timeout
            try:
                bot_entity = await asyncio.wait_for(
                    client.client.get_entity("@TGDNAbot"), 
                    timeout=10
                )
                log_usage_checker(self.logger, f"Found @TGDNAbot entity for age check: {phone}")
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Timeout finding @TGDNAbot for {phone}", logging.ERROR)
                return False, "Timeout finding @TGDNAbot"
            except Exception as e:
                log_usage_checker(self.logger, f"Failed to find @TGDNAbot: {str(e)}", logging.ERROR)
                return False, f"Failed to find @TGDNAbot: {str(e)}"
            
            # Send /start command to the bot with timeout
            try:
                await asyncio.wait_for(
                    client.client.send_message(bot_entity, "/start"), 
                    timeout=10
                )
                log_usage_checker(self.logger, f"Sent /start command to @TGDNAbot for {phone}")
                
                # Wait a bit for the bot to respond
                await asyncio.sleep(3)
                
                # Get messages from the bot with timeout
                messages = await asyncio.wait_for(
                    client.client.get_messages(bot_entity, limit=5), 
                    timeout=10
                )
                
                # Parse the response for age information
                age_days = None
                is_aged = False
                
                for message in messages:
                    if message.message:
                        text = message.message.lower()
                        
                        # Look for age patterns in the response
                        import re
                        
                        # Pattern for "X days old" or "X months old" or "X years old"
                        days_match = re.search(r'(\d+)\s*days?\s*old', text)
                        months_match = re.search(r'(\d+)\s*months?\s*old', text)
                        years_match = re.search(r'(\d+)\s*years?\s*old', text)
                        
                        if days_match:
                            age_days = int(days_match.group(1))
                        elif months_match:
                            age_days = int(months_match.group(1)) * 30  # Approximate
                        elif years_match:
                            age_days = int(years_match.group(1)) * 365  # Approximate
                        
                        # Check if account is considered aged
                        if any(keyword in text for keyword in ['aged', 'old account', 'mature']):
                            is_aged = True
                        elif any(keyword in text for keyword in ['new', 'young', 'fresh']):
                            is_aged = False
                        
                        if age_days is not None:
                            break
                
                # If we couldn't parse the age, set defaults
                if age_days is None:
                    # Check for aged status from text patterns
                    if any('aged' in msg.message.lower() or 'old' in msg.message.lower() 
                           for msg in messages if msg.message):
                        age_days = 365  # Default to 1 year for aged accounts
                        is_aged = True
                    else:
                        age_days = 30   # Default to 30 days for new accounts
                        is_aged = False
                
                # Update account age information
                success = self.update_account_age(phone, age_days, is_aged)
                
                if success:
                    # Calculate and log the daily group limit
                    daily_limit = self.calculate_daily_group_limit(age_days, is_aged)
                    aged_status = "Yes" if is_aged else "No"
                    
                    log_usage_checker(
                        self.logger, 
                        f"Account: {phone} | Aged: {aged_status} | Group Limit: {daily_limit}"
                    )
                    
                    return True, f"Age check completed: {age_days} days, aged: {is_aged}"
                else:
                    return False, "Failed to update account age information"
                
            except asyncio.TimeoutError:
                log_usage_checker(self.logger, f"Timeout communicating with @TGDNAbot for {phone}", logging.ERROR)
                return False, "Timeout communicating with bot"
            except Exception as e:
                log_usage_checker(self.logger, f"Error communicating with @TGDNAbot for {phone}: {str(e)}", logging.ERROR)
                return False, f"Error communicating with bot: {str(e)}"
            
        except Exception as e:
            log_usage_checker(self.logger, f"Age check error for {phone}: {str(e)}", logging.ERROR)
            return False, str(e)
        finally:
            # Always disconnect the client
            if client:
                try:
                    await client.disconnect()
                except Exception as e:
                    log_usage_checker(self.logger, f"Error disconnecting client for {phone}: {str(e)}", logging.WARNING)
    
    def update_account_age(self, phone, age_days, is_aged):
        """Update account age information in the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Calculate daily group limit
            daily_limit = self.calculate_daily_group_limit(age_days, is_aged)
            
            cursor.execute(
                """UPDATE accounts SET 
                   account_age_days = ?, 
                   is_aged = ?, 
                   daily_group_limit = ?, 
                   last_age_check = ? 
                   WHERE phone = ?""",
                (age_days, 1 if is_aged else 0, daily_limit, datetime.now().isoformat(), phone)
            )
            
            conn.commit()
            conn.close()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["account_age_days"] = age_days
                    account["is_aged"] = 1 if is_aged else 0
                    account["daily_group_limit"] = daily_limit
                    account["last_age_check"] = datetime.now().isoformat()
            
            self.logger.info(f"Updated age info for account {phone}: {age_days} days, aged: {is_aged}, limit: {daily_limit}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating account age for {phone}: {str(e)}")
            return False
    
    def calculate_daily_group_limit(self, age_days, is_aged):
        """Calculate daily group checking limit based on account age."""
        # Base limits
        if is_aged or age_days >= 365:  # 1 year or explicitly aged
            base_limit = 800
        elif age_days >= 180:  # 6 months
            base_limit = 600
        elif age_days >= 90:   # 3 months
            base_limit = 400
        elif age_days >= 30:   # 1 month
            base_limit = 200
        else:  # New accounts
            base_limit = 100
        
        # Add bonus based on specific age
        age_bonus = min(age_days // 30 * 10, 200)  # 10 extra per month, max 200
        
        total_limit = base_limit + age_bonus
        
        # Cap at reasonable maximum
        return min(total_limit, 1000)
    
    def get_accounts_needing_age_check(self):
        """Get accounts that need age checking."""
        accounts_needing_check = []
        
        for account in self.accounts:
            last_check = account.get("last_age_check")
            age_days = account.get("account_age_days", 0)
            
            # Check if account needs age verification
            if not last_check or age_days == 0:
                accounts_needing_check.append(account)
            else:
                # Check if it's been more than 30 days since last check
                try:
                    last_check_date = datetime.fromisoformat(last_check)
                    if (datetime.now() - last_check_date).days > 30:
                        accounts_needing_check.append(account)
                except:
                    # Invalid date format, needs recheck
                    accounts_needing_check.append(account)
        
        return accounts_needing_check
    
    async def auto_check_account_ages(self):
        """Automatically check ages for accounts that need it."""
        from logger import log_usage_checker
        
        accounts_needing_check = self.get_accounts_needing_age_check()
        
        if not accounts_needing_check:
            log_usage_checker(self.logger, "All accounts have up-to-date age information")
            return []
        
        log_usage_checker(self.logger, f"Starting auto age check for {len(accounts_needing_check)} accounts")
        
        results = []
        for account in accounts_needing_check:
            phone = account.get("phone")
            if phone:
                log_usage_checker(self.logger, f"Checking age for account: {phone}")
                
                try:
                    success, message = await self.check_account_age_with_bot(phone)
                    results.append((phone, success, message))
                    
                    # Wait between requests to avoid rate limits
                    await asyncio.sleep(5)
                    
                except Exception as e:
                    error_msg = f"Error checking age for {phone}: {str(e)}"
                    log_usage_checker(self.logger, error_msg, logging.ERROR)
                    results.append((phone, False, error_msg))
        
        log_usage_checker(self.logger, f"Completed auto age check for {len(accounts_needing_check)} accounts")
        return results

    def update_check_time(self, phone, status="OK"):
        """Update the last check time for an account."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET last_check = ?, status = ? WHERE phone = ?",
                    (datetime.now().isoformat(), status, phone)
                )
                
                conn.commit()
            
            # Update local accounts list
            for account in self.accounts:
                if account.get("phone") == phone:
                    account["last_check"] = datetime.now().isoformat()
                    account["status"] = status
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating check time for {phone}: {str(e)}")
            return False
    
    def update_disabled_until(self, phone, disabled_until):
        """Update the disabled_until timestamp for an account."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "UPDATE accounts SET disabled_until = ? WHERE phone = ?",
                    (disabled_until, phone)
                )
                
                conn.commit()
            
            # Reload accounts
            self._load_accounts()
            
            return True
        except Exception as e:
            self.logger.error(f"Error updating disabled_until for account {phone}: {str(e)}")
            return False

    def clean_database(self):
        """Clean and optimize the database to resolve locking issues."""
        try:
            self.logger.info("Starting database cleanup...")
            
            # Close all active connections first
            self.clients = {}
            
            # First attempt to fix any database locks
            result = fix_db_lock(self.db_path)
            if result:
                self.logger.info("Database cleaned and optimized successfully")
            else:
                self.logger.warning("Initial database cleanup failed, trying alternative method")
                
                # Try to make a backup anyway
                try:
                    backup_path = f"{self.db_path}.backup_{int(datetime.now().timestamp())}"
                    import shutil
                    if os.path.exists(self.db_path):
                        shutil.copy2(self.db_path, backup_path)
                        self.logger.info(f"Created database backup at {backup_path}")
                except Exception as backup_err:
                    self.logger.error(f"Failed to create database backup: {str(backup_err)}")
            
            # Reload accounts
            self._load_accounts()
            
            return True
        except Exception as e:
            self.logger.error(f"Error during database cleanup: {str(e)}")
            return False 