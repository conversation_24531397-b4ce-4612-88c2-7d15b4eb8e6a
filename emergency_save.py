
def emergency_save_results(valid_filtered, valid_only, topics_groups, channels_only, invalid_groups):
    """Emergency save function that ACTUALLY writes files"""
    import os
    
    # Create folders
    folders = [
        "Results/Groups_Valid_Filter",
        "Results/Groups_Valid_Only",
        "Results/Topics_Groups_Only_Valid", 
        "Results/Channels_Only_Valid",
        "Results/Invalid_Groups_Channels"
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)
    
    # Save files
    saves = [
        (valid_filtered, "Results/Groups_Valid_Filter/GroupsValidFilter.txt"),
        (valid_only, "Results/Groups_Valid_Only/GroupsValidOnly.txt"),
        (topics_groups, "Results/Topics_Groups_Only_Valid/TopicsGroups.txt"),
        (channels_only, "Results/Channels_Only_Valid/Channels.txt"),
        (invalid_groups, "Results/Invalid_Groups_Channels/InvalidGroups.txt")
    ]
    
    for data, filepath in saves:
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                if data:
                    for item in data:
                        f.write(str(item) + '\n')
            print(f"✅ Saved: {filepath} ({len(data) if data else 0} items)")
        except Exception as e:
            print(f"❌ Error saving {filepath}: {e}")
