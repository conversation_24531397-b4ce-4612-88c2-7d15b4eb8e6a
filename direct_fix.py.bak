#!/usr/bin/env python3
"""
Direct fix for specific lines in main.py that are causing errors
"""

import os
import shutil
import re

def direct_fix():
    """Apply direct fixes to the specific lines in main.py causing errors"""
    print("=== Applying direct fixes to main.py ===")
    
    # Create a backup of the original file
    input_file = "main.py"
    backup_file = f"{input_file}.bak_direct"
    
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file content
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Fix 1: Fix the indentation error at line 659
    print("\nFixing indentation error at line 659...")
    
    # Look for the problematic line
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info" in line:
            print(f"Found problematic method at line {i+1}")
            
            # Fix the indentation by setting it to exactly 4 spaces
            lines[i] = "    def auto_refresh_missing_account_info(self):\n"
            
            # Also fix the indentation of the method body
            j = i + 1
            while j < len(lines) and (not lines[j].strip() or len(lines[j]) - len(lines[j].lstrip()) > 4):
                if lines[j].strip():  # Only process non-empty lines
                    # Set indentation to 8 spaces (4 for class + 4 for method body)
                    lines[j] = "        " + lines[j].lstrip()
                j += 1
            
            print(f"Fixed indentation for method body from line {i+1} to {j}")
            break
    
    # Fix 2: Fix the syntax error at line 4013 (expected 'except' or 'finally' block)
    print("\nFixing syntax error at line 4013...")
    
    # Look for problematic try-else blocks
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        if line == "try:":
            # Found a try block, check if it has a matching except
            try_line = i
            try_indent = len(lines[i]) - len(lines[i].lstrip())
            indent_str = " " * try_indent
            
            # Look for except, finally, or else at the same indentation level
            j = i + 1
            has_except = False
            has_else = False
            else_line = -1
            
            while j < len(lines):
                if not lines[j].strip():  # Skip empty lines
                    j += 1
                    continue
                
                curr_indent = len(lines[j]) - len(lines[j].lstrip())
                curr_line = lines[j].lstrip()
                
                if curr_indent == try_indent:
                    if curr_line.startswith(("except", "finally")):
                        has_except = True
                        break
                    elif curr_line == "else:":
                        has_else = True
                        else_line = j
                        break
                elif curr_indent < try_indent and curr_line:
                    # We've reached code at a lower indentation level
                    break
                
                j += 1
            
            # If we found an else without an except, insert an except block
            if has_else and not has_except and else_line != -1:
                except_line = f"{indent_str}except Exception as e:\n{indent_str}    print(f\"Error: {{e}}\")\n"
                lines.insert(else_line, except_line)
                print(f"Added except block before else at line {else_line+1}")
                
                # Skip ahead past the inserted line
                i = else_line + 1
        
        i += 1
    
    # Write the fixed content back to the file
    with open(input_file, 'w', encoding='utf-8') as f:
        f.writelines(lines)
    
    print("\nAll fixes applied to main.py")
    print("You can now run: python main.py")
    
    # Create a simple batch file for convenience
    with open("run_fixed.bat", "w") as f:
        f.write("""@echo off
echo Running TG Checker with all fixes applied...
python main.py
if %errorlevel% neq 0 (
    echo An error occurred! Check the logs for details.
    pause
)
pause
""")
    
    print("Created batch file: run_fixed.bat")
    
    # Create Kurdish version
    with open("run_fixed_kurdish.bat", "w") as f:
        f.write("""@echo off
echo TG Checker - Barnama ba hamw charasarkanu...
python main.py
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created Kurdish batch file: run_fixed_kurdish.bat")
    
    return True

def fix_if_statement():
    print("Creating a more direct fix for the incomplete if statements")
    
    # Read the entire file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix pattern: replace the incomplete if statement with the complete one
    fixed_content = re.sub(
        r"self\.last_processed_index = task\.get\('current_index', 0\) if t",
        "self.last_processed_index = task.get('current_index', 0) if tas else 0",
        content
    )
    
    # Fix any stray "ask else 0" fragments
    fixed_content = fixed_content.replace("ask else 0", "")
    
    # Write back the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(fixed_content)
    
    print("Fixed incomplete if statements")

if __name__ == "__main__":
    direct_fix()
    fix_if_statement() 