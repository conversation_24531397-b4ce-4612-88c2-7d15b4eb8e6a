#!/usr/bin/env python3
"""
Quick fix for syntax error in main.py
Fixes unterminated string literals in file writing sections.
"""

import re

def fix_syntax_error():
    """Fix the syntax error in main.py."""
    
    print("🔧 Fixing syntax error in main.py...")
    
    # Read the file
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix the malformed f-string with newlines
    # Replace f.write(f"{link}\n") patterns that got broken
    content = re.sub(
        r'f\.write\(f"\{link\}\s*"\s*\)',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Fix any other malformed newline patterns
    content = re.sub(
        r'f\.write\(f"\{([^}]+)\}\s*\n"\s*\)',
        r'f.write(f"{\1}\n")',
        content
    )
    
    # Fix broken multi-line strings
    content = re.sub(
        r'f\.write\(f"\{([^}]+)\}\s*\n"\s*\)',
        r'f.write(f"{\1}\n")',
        content
    )
    
    # More comprehensive fix for broken f-strings
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Fix specific pattern that's causing the error
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Replace the specific broken pattern we found
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Another approach - fix all broken f-string patterns
    broken_patterns = [
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
        r'f\.write\(f"\{link\}\\\n"\s*\)',
        r'f\.write\(f"\{link\}\s*\\n"\s*\)',
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
    ]
    
    for pattern in broken_patterns:
        content = re.sub(pattern, r'f.write(f"{link}\n")', content)
    
    # Manual fix for the specific error pattern
    content = re.sub(
        r'f\.write\(f"\{link\}\s*\n"\s*\)',
        r'f.write(f"{link}\n")',
        content
    )
    
    # Let's just do a direct replacement for the exact broken pattern
    content = content.replace('f.write(f"{link}\n")', 'f.write(f"{link}\\n")')
    
    # Write the fixed content
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(content)
    
    print("✅ Syntax error fixed!")
    return True

if __name__ == "__main__":
    try:
        fix_syntax_error()
        print("✅ Syntax fix completed!")
    except Exception as e:
        print(f"❌ Error fixing syntax: {e}")
        import traceback
        traceback.print_exc() 