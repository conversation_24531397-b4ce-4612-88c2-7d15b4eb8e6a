"""
Fix incomplete if statement in TelegramForwarder.set_task method
"""
import re

def fix_if_statement(file_path="main.py"):
    print("Fixing incomplete if statements in TelegramForwarder.set_task...")
    
    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix the first occurrence at line ~808
    pattern1 = r"self\.last_processed_index = task\.get\('current_index', 0\) if t"
    replacement1 = "self.last_processed_index = task.get('current_index', 0) if tas else 0"
    
    # Fix the second occurrence at line ~2820
    pattern2 = r"self\.last_processed_index = task\.get\('current_index', 0\) if t\n"
    replacement2 = "self.last_processed_index = task.get('current_index', 0) if tas else 0\n"
    
    # Replace any "ask else 0" strings that might be floating around
    pattern3 = r"ask else 0"
    replacement3 = ""
    
    # Apply the fixes
    content = re.sub(pattern1, replacement1, content)
    content = re.sub(pattern2, replacement2, content)
    content = re.sub(pattern3, replacement3, content)
    
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(content)
    
    print("Fixed incomplete if statements in TelegramForwarder.set_task")

if __name__ == "__main__":
    fix_if_statement() 