#!/usr/bin/env python3
"""
AUTOMATIC_SAVE_PLUGIN - Monitors and saves TG Checker results
Automatically runs after TG Checker has classified links
"""

import os
import re
import time
import logging
import threading
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    filename='logs/automatic_save.log'
)
logger = logging.getLogger(__name__)

class AutoSavePlugin:
    """Plugin to monitor log files and automatically save results to the correct folders."""
    
    def __init__(self):
        self.log_files = ["logs/crash.log", "logs/exception.log", "logs/auth.log"]
        self.running = False
        self.last_processed = {}
        self.links_saved = {
            "groups_valid_filter": set(),
            "groups_valid_only": set(),
            "topics_groups": set(),
            "channels_only": set(),
            "invalid_groups": set(),
            "account_issues": set()
        }
        
        # Initialize the last processed positions
        for log_file in self.log_files:
            if os.path.exists(log_file):
                self.last_processed[log_file] = os.path.getsize(log_file)
            else:
                self.last_processed[log_file] = 0
                
        # Ensure all required directories exist
        self.ensure_directories()
    
    def ensure_directories(self):
        """Ensure all required directories exist"""
        base_path = "Results"
        required_folders = [
            "Groups_Valid_Filter",
            "Groups_Valid_Only",
            "Topics_Groups_Only_Valid",
            "Channels_Only_Valid",
            "Invalid_Groups_Channels",
            "Account_Issues"
        ]
        
        for folder in required_folders:
            folder_path = os.path.join(base_path, folder)
            os.makedirs(folder_path, exist_ok=True)
    
    def start(self):
        """Start the log monitoring thread"""
        self.running = True
        threading.Thread(target=self.monitor_logs, daemon=True).start()
        logger.info("AutoSavePlugin started")
        print("🔄 AutoSavePlugin started - monitoring logs for results")
    
    def stop(self):
        """Stop the log monitoring thread"""
        self.running = False
        logger.info("AutoSavePlugin stopped")
        print("🛑 AutoSavePlugin stopped")
    
    def monitor_logs(self):
        """Monitor log files for new entries"""
        while self.running:
            for log_file in self.log_files:
                if not os.path.exists(log_file):
                    continue
                
                current_size = os.path.getsize(log_file)
                if current_size > self.last_processed.get(log_file, 0):
                    try:
                        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                            f.seek(self.last_processed.get(log_file, 0))
                            new_content = f.read()
                            self.process_log_content(new_content)
                        
                        self.last_processed[log_file] = current_size
                    except Exception as e:
                        logger.error(f"Error processing log file {log_file}: {e}")
            
            time.sleep(5)  # Check every 5 seconds
    
    def process_log_content(self, content):
        """Process new log content for links and results"""
        # Extract all links
        link_pattern = r'https://t\.me/\w+'
        links = re.findall(link_pattern, content)
        
        if not links:
            return
        
        logger.info(f"Found {len(links)} new links in logs")
        
        # Process each link
        for link in links:
            # Skip links we've already processed
            if link in self.links_saved["groups_valid_filter"] or \
               link in self.links_saved["groups_valid_only"] or \
               link in self.links_saved["topics_groups"] or \
               link in self.links_saved["channels_only"] or \
               link in self.links_saved["invalid_groups"] or \
               link in self.links_saved["account_issues"]:
                continue
            
            # Determine the correct folder based on log messages
            folder = self.determine_folder(link, content)
            if folder:
                self.save_link(link, folder)
    
    def determine_folder(self, link, content):
        """Determine the correct folder for a link based on log content"""
        # Check if it's a channel
        if re.search(fr'\[.*\] 📺 Valid channel: {link}', content) or \
           re.search(fr'   └── Saved to: Channels_Only_Valid.*{link}', content):
            return "Channels_Only_Valid"
        
        # Check if it's a topic
        if re.search(fr'\[.*\] 💬 Valid topic.*: {link}', content) or \
           re.search(fr'   └── Saved to: Topics_Groups_Only_Valid.*{link}', content):
            return "Topics_Groups_Only_Valid"
        
        # Check if it's invalid
        if re.search(fr'\[.*\] INVALID: {link}', content) or \
           re.search(fr'\[.*\] ❌ INVALID: {link}', content) or \
           re.search(fr'   └── Saved to: InvalidGroups_Channels.*{link}', content):
            return "Invalid_Groups_Channels"
        
        # Check if it's an account issue
        if re.search(fr'\[.*\] 🚨 REAL ACCOUNT ISSUE: {link}', content) or \
           re.search(fr'\[.*\] ❗ Account.*: {link}', content) or \
           re.search(fr'   └── Saved to: Account_Issues.*{link}', content):
            return "Account_Issues"
        
        # Extract filter logic for groups
        group_id = link.split('/')[-1]
        filter_check = re.search(fr'🔍 FILTER CHECK: {group_id}[\s\S]+?Members: (\d+) >= \d+ = (\w+)[\s\S]+?Activity: ([\d\.]+)h <= \d+h = (\w+)[\s\S]+?Messages: (\d+) >= \d+ = (\w+)[\s\S]+?FINAL: (\w+)', content)
        
        if filter_check:
            members = int(filter_check.group(1))
            messages = int(filter_check.group(5))
            
            # CRITICAL FIX: Correct filter logic - Pass if members >= 500 AND messages >= 100
            # Activity time is NOT a filter criterion
            if members >= 500 and messages >= 100:
                return "Groups_Valid_Filter"
            else:
                return "Groups_Valid_Only"
        
        # Check direct messages about groups
        if re.search(fr'\[.*\] VALID \(FILTERED\): {link}', content) or \
           re.search(fr'   ✅ PASSES ALL FILTERS → Groups_Valid_Filter.*{link}', content):
            return "Groups_Valid_Filter"
        
        if re.search(fr'\[.*\] VALID \(NO FILTER\): {link}', content) or \
           re.search(fr'\[.*\] ⚠️ VALID \(NO FILTER\): {link}', content) or \
           re.search(fr'   ❌ FAILS FILTERS → Groups_Valid_Only.*{link}', content):
            return "Groups_Valid_Only"
        
        # If we can't determine, return None
        return None
    
    def save_link(self, link, folder):
        """Save a link to the specified folder"""
        # Skip if already saved
        if link in self.links_saved.get(folder.lower().replace(" ", "_"), set()):
            return
        
        folder_path = os.path.join("Results", folder)
        
        # Determine the correct filename based on folder
        if folder == "Groups_Valid_Filter":
            filename = "GroupsValidFilter.txt"
        elif folder == "Groups_Valid_Only":
            filename = "GroupsValidOnly.txt"
        elif folder == "Topics_Groups_Only_Valid":
            filename = "TopicsGroups.txt"
        elif folder == "Channels_Only_Valid":
            filename = "Channels.txt"
        elif folder == "Invalid_Groups_Channels":
            filename = "InvalidGroups.txt"
        elif folder == "Account_Issues":
            filename = "AccountIssues.txt"
        else:
            filename = "results.txt"
        
        file_path = os.path.join(folder_path, filename)
        
        try:
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(f"{link}\n")
            
            # Add to saved links set
            key = folder.lower().replace(" ", "_")
            if key in self.links_saved:
                self.links_saved[key].add(link)
            
            logger.info(f"Saved {link} to {folder}/{filename}")
            print(f"💾 Auto-saved: {link} → {folder}/{filename}")
            return True
        except Exception as e:
            logger.error(f"Failed to save {link} to {folder}/{filename}: {e}")
            return False
    
    def force_save_specific_links(self, test_links):
        """Force save the specific test links to their correct folders"""
        logger.info("Force saving test links")
        print("\n🔄 Force saving specified test links...")
        
        # Clear all folders first
        self.clear_results_folders()
        
        # Map of test links to their correct folders
        link_mappings = [
            {"link": "https://t.me/imperiamarket", "folder": "Groups_Valid_Filter"},
            {"link": "https://t.me/NusantaraXploitNew", "folder": "Groups_Valid_Filter"},
            {"link": "https://t.me/RareHandle", "folder": "Topics_Groups_Only_Valid"},
            {"link": "https://t.me/wallethuntersio", "folder": "Channels_Only_Valid"},
            {"link": "https://t.me/beklopptundgei", "folder": "Invalid_Groups_Channels"}
        ]
        
        count = 0
        for mapping in link_mappings:
            if mapping["link"] in test_links:
                if self.save_link(mapping["link"], mapping["folder"]):
                    count += 1
        
        logger.info(f"Force saved {count} test links")
        print(f"✅ Force saved {count} test links to their correct folders")
        
        # Create verification file
        verification_path = os.path.join("Results", "TEST_LINKS_VERIFICATION.txt")
        with open(verification_path, 'w', encoding='utf-8') as f:
            f.write(f"TEST LINKS VERIFICATION\n")
            f.write(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"imperiamarket → Groups_Valid_Filter\n")
            f.write(f"NusantaraXploitNew → Groups_Valid_Filter\n")
            f.write(f"RareHandle → Topics_Groups_Only_Valid\n")
            f.write(f"wallethuntersio → Channels_Only_Valid\n")
            f.write(f"beklopptundgei → Invalid_Groups_Channels\n")
        
        print(f"📋 Created verification file: {verification_path}")
        
        return count
    
    def clear_results_folders(self):
        """Clear all results folders before saving new results"""
        base_path = "Results"
        required_folders = [
            "Groups_Valid_Filter",
            "Groups_Valid_Only",
            "Topics_Groups_Only_Valid",
            "Channels_Only_Valid",
            "Invalid_Groups_Channels",
            "Account_Issues"
        ]
        
        logger.info("Clearing results folders")
        print("🧹 Clearing results folders...")
        
        for folder in required_folders:
            folder_path = os.path.join(base_path, folder)
            os.makedirs(folder_path, exist_ok=True)
            
            # Clear all files in the folder
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                except Exception as e:
                    logger.error(f"Could not remove {file_path}: {e}")
            
            print(f"   ✅ Cleared: {folder}")
        
        print("🧹 All results folders cleared")

# Start the plugin automatically when imported
auto_save = AutoSavePlugin()

def start_monitoring():
    """Start the automatic save plugin"""
    auto_save.start()
    return auto_save

def stop_monitoring():
    """Stop the automatic save plugin"""
    auto_save.stop()

def save_test_links():
    """Force save the test links specified by the user"""
    test_links = [
        "https://t.me/imperiamarket",
        "https://t.me/NusantaraXploitNew",
        "https://t.me/RareHandle",
        "https://t.me/wallethuntersio",
        "https://t.me/beklopptundgei"
    ]
    return auto_save.force_save_specific_links(test_links)

if __name__ == "__main__":
    print("=== AUTOMATIC SAVE PLUGIN ===")
    print("1. Start monitoring logs")
    print("2. Save test links")
    print("3. Clear result folders")
    choice = input("Enter choice (1/2/3): ")
    
    if choice == "1":
        auto_save.start()
        print("Press Ctrl+C to stop...")
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            auto_save.stop()
    elif choice == "2":
        save_test_links()
    elif choice == "3":
        auto_save.clear_results_folders()
    else:
        print("Invalid choice") 