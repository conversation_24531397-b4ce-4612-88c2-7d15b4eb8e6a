@echo off
echo TG Checker Indentation Fix
echo ===================================
echo This will fix indentation issues in main.py
echo Please wait, the process is starting...
echo.

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.7 or later and try again.
    pause
    exit /b 1
)

REM Run the indentation fix script
python fix_indentation.py

echo.
if %errorlevel% equ 0 (
    echo All fixes have been applied successfully!
    echo You can now run TG Checker using the Run_Fixed_TG_Checker.bat file.
) else (
    echo An error occurred during the fix process.
    echo Please check the log messages above for details.
)

pause 