2025-06-13 18:53:12 - EXCEPTION - WARNING - Exception in Test Context: ValueError - Test exception for logging
2025-06-14 07:09:17 - EXCEPTION - ERROR - 
=== HANDLED EXCEPTION ===
Time: 2025-06-14 07:09:17
Context: Test Context
Exception Type: ValueError
Exception Message: Test exception for logging
Stack Trace:
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\TG Checker\TG PY\test_stability_fixes.py", line 52, in test_crash_logger
    raise ValueError("Test exception for logging")
ValueError: Test exception for logging

========================

