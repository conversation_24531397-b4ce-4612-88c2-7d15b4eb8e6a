import sys
import os
import logging
import json
import asyncio
import sqlite3
import threading
import time
import random
import uuid
import re
from datetime import datetime, timed<PERSON><PERSON>
from contextlib import contextmanager
from PyQt5.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout,
                           QHBoxLayout, QLabel, QPushButton, QTextEdit, QTableWidget,
                           QTableWidgetItem, QComboBox, QProgressBar, QMessageBox,
                           QSystemTrayIcon, QMenu, QAction, QHeaderView, QCheckBox,
                           QLineEdit, QGroupBox, QFormLayout, QSpinBox, QFileDialog, QDialog,
                           QListWidget, QListWidgetItem, QScrollArea, QSplitter, QFrame, 
                           QToolButton)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, QSettings, QMetaObject, Q_ARG, pyqtSlot, QSize
from PyQt5.QtGui import QIcon, QFont, QPixmap, QTextCursor, QColor
from telethon.errors import (SessionPasswordNeededError, PhoneCodeInvalidError, FloodWaitError,
                           ChatAdminRequiredError, ChannelPrivateError, UserBannedInChannelError,
                           PeerIdInvalidError, MessageIdInvalidError, UserDeactivatedBanError,
                           AuthKeyDuplicatedError, ChatWriteForbiddenError, SlowModeWaitError,
                           MessageTooLongError, ChatGuestSendForbiddenError)
from telethon.tl.types import InputPeerChannel, InputPeerUser, InputPeerChat
from telethon.tl.functions.channels import JoinChannelRequest, GetFullChannelRequest
from telethon.tl.functions.messages import GetHistoryRequest

# Import custom modules
from tg_client import TelegramClient
from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

class ForwarderDatabase:
    """Database manager for the Telegram forwarder module."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self._db_lock = threading.RLock()
        self._busy_handler_calls = 0
        self._max_busy_retries = 25
        
        # Initialize database tables
        self._init_database()
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 5
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = self._dict_factory
                
                # Set custom busy handler
                # Fixed set_busy_timeout compatibility
                conn.execute("PRAGMA busy_timeout = 10000")  # 10 seconds
                
                # Return connection to the caller
                yield conn
                
                # If we got here, everything worked fine
                break
                
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    self.logger.warning(f"Database locked, retrying in {retry_delay}s (attempt {attempt+1}/{max_retries})")
                    time.sleep(retry_delay * (1.5 ** attempt))
                    
                    # Try fixing the database if this is the 3rd attempt
                    if attempt == 2:
                        self.logger.warning("Attempting database recovery during locked state")
                        try:
                            self._fix_db_lock()
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database lock: {str(fix_err)}")
                else:
                    raise
            except Exception as e:
                self.logger.error(f"Database connection error: {str(e)}")
                raise
            finally:
                # Always close the connection if it was opened
                if conn:
                    conn.close()
    
    def _dict_factory(self, cursor, row):
        """Convert row to dictionary."""
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d
    
    def _fix_db_lock(self):
        """Try to fix locked database by creating a temporary connection."""
        try:
            # Close existing connections by connecting briefly
            temp_conn = sqlite3.connect(self.db_path, timeout=20)
            temp_conn.close()
            self.logger.info("Fixed database lock by establishing temporary connection")
        except Exception as e:
            self.logger.error(f"Failed to fix database lock: {str(e)}")
            
    def _init_database(self):
        """Initialize database tables for forwarder functionality."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create tasks table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT,
                        account TEXT,
                        message_link TEXT,
                        target_groups TEXT,
                        part2_message_link TEXT,
                        part2_target_groups TEXT,
                        status TEXT DEFAULT 'pending',
                        current_index INTEGER DEFAULT 0,
                        error_message TEXT,
                        last_processed_time TEXT,
                        created_at TEXT,
                        intervalMin INTEGER DEFAULT 20,
                        intervalMax INTEGER DEFAULT 25,
                        afterEachSecond INTEGER DEFAULT 360,
                        randomSleepTimeMin INTEGER DEFAULT 30,
                        randomSleepTimeMax INTEGER DEFAULT 60,
                        customReplyMessage TEXT
                    )
                ''')
                
                # Create task_groups table for storing target groups for each task
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create part2_task_groups table for storing part 2 target groups
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS part2_task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Initialize default settings if they don't exist
                default_settings = {
                    'useRandomSleepTime': 'true',
                    'logFailedGroups': 'true',
                    'floodwait': 'true',
                    'replyMessage': 'true',
                    'message': 'Hi! This is an advertising bot. @vipstore. DMs won\'t be seen here'
                }
                
                for key, value in default_settings.items():
                    cursor.execute(
                        "INSERT OR IGNORE INTO forwarder_settings (key, value) VALUES (?, ?)",
                        (key, value)
                    )
                
                conn.commit()
                
                self.logger.info("Initialized forwarder database tables")
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            task_id = task_data.get('id')
            name = task_data.get('name')
            account = task_data.get('account')
            message_link = task_data.get('message_link')
            target_groups = task_data.get('target_groups', [])
            part2_message_link = task_data.get('part2_message_link')
            part2_target_groups = task_data.get('part2_target_groups', [])
            
            # Get account-specific settings if available, or use defaults
            intervalMin = task_data.get('intervalMin', 20)
            intervalMax = task_data.get('intervalMax', 25)
            afterEachSecond = task_data.get('afterEachSecond', 360)
            randomSleepTimeMin = task_data.get('randomSleepTimeMin', 30)
            randomSleepTimeMax = task_data.get('randomSleepTimeMax', 60)
            customReplyMessage = task_data.get('customReplyMessage')
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Insert task
                cursor.execute(
                    """
                    INSERT INTO forwarder_tasks (
                        id, name, account, message_link, target_groups,
                        part2_message_link, part2_target_groups, status,
                        created_at, intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        task_id, name, account, message_link, ','.join(target_groups),
                        part2_message_link, ','.join(part2_target_groups), 'pending',
                        datetime.now().isoformat(), intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    )
                )
                
                # Insert target groups
                for group in target_groups:
                    cursor.execute(
                        "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                # Insert part 2 target groups if available
                for group in part2_target_groups:
                    cursor.execute(
                        "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                conn.commit()
                
            self.logger.info(f"Created forwarder task: {name} for account {account}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating forwarder task: {str(e)}")
            return False
    
    def update_task(self, task_id, task_data):
        """Update an existing task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # First, update the task details
                update_fields = []
                update_values = []
                
                for field, value in task_data.items():
                    if field in ['target_groups', 'part2_target_groups'] and isinstance(value, list):
                        update_fields.append(f"{field} = ?")
                        update_values.append(','.join(value))
                    elif field not in ['id', 'task_id']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                if update_fields:
                    cursor.execute(
                        f"UPDATE forwarder_tasks SET {', '.join(update_fields)} WHERE id = ?",
                        update_values + [task_id]
                    )
                
                # If target groups are being updated, handle them separately
                if 'target_groups' in task_data and isinstance(task_data['target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['target_groups']:
                        cursor.execute(
                            "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                # Same for part 2 target groups
                if 'part2_target_groups' in task_data and isinstance(task_data['part2_target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['part2_target_groups']:
                        cursor.execute(
                            "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder task: {str(e)}")
            return False
    
    def delete_task(self, task_id):
        """Delete a forwarding task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Delete from main tasks table
                cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_id,))
                
                # Delete associated groups
                cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                
                conn.commit()
                
            self.logger.info(f"Deleted forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting forwarder task: {str(e)}")
            return False
    
    def get_all_tasks(self):
        """Get all forwarding tasks."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks ORDER BY created_at DESC")
                tasks = cursor.fetchall()
                
                # For each task, get its target groups
                for task in tasks:
                    # Get regular target groups
                    cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task['id'],))
                    task['groups'] = cursor.fetchall()
                    
                    # Get part 2 target groups
                    cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task['id'],))
                    task['part2_groups'] = cursor.fetchall()
                
                return tasks
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder tasks: {str(e)}")
            return []
    
    def get_task(self, task_id):
        """Get a specific task by ID."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    return None
                
                # Get target groups
                cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task_id,))
                task['groups'] = cursor.fetchall()
                
                # Get part 2 target groups
                cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task_id,))
                task['part2_groups'] = cursor.fetchall()
                
                return task
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder task: {str(e)}")
            return None
    
    def update_task_status(self, task_id, status, current_index=None, error_message=None):
        """Update the status of a task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                update_values = [status, datetime.now().isoformat()]
                update_sql = "UPDATE forwarder_tasks SET status = ?, last_processed_time = ?"
                
                if current_index is not None:
                    update_sql += ", current_index = ?"
                    update_values.append(current_index)
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE id = ?"
                update_values.append(task_id)
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            self.logger.info(f"Updated task {task_id} status to {status}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating task status: {str(e)}")
            return False
    
    def update_group_status(self, task_id, group_link, processed, error_message=None, is_part2=False):
        """Update the processed status for a group."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                table = "part2_task_groups" if is_part2 else "task_groups"
                
                update_values = [processed]
                update_sql = f"UPDATE {table} SET processed = ?"
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE task_id = ? AND group_link = ?"
                update_values.extend([task_id, group_link])
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating group status: {str(e)}")
            return False
    
    def get_settings(self):
        """Get all forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT key, value FROM forwarder_settings")
                settings = {row['key']: row['value'] for row in cursor.fetchall()}
                
                return settings
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder settings: {str(e)}")
            return {}
    
    def update_setting(self, key, value):
        """Update a specific setting."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "INSERT OR REPLACE INTO forwarder_settings (key, value) VALUES (?, ?)",
                    (key, value)
                )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder setting: {key} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder setting: {str(e)}")
            return False

class ForwarderManager:
    """Manager for coordinating multiple forwarding tasks and accounts."""
    
    def __init__(self, account_manager, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.account_manager = account_manager
        self.forwarder_db = ForwarderDatabase(logger=self.logger)
        self.active_forwarders = {}  # task_id -> forwarder
        self.tasks_lock = threading.RLock()
        self._is_running = False
        self._should_stop = False
        self._task_completion_callbacks = []
    
    def add_task_completion_callback(self, callback):
        """Add a callback function to be called when a task completes."""
        if callback not in self._task_completion_callbacks:
            self._task_completion_callbacks.append(callback)
    
    def remove_task_completion_callback(self, callback):
        """Remove a task completion callback."""
        if callback in self._task_completion_callbacks:
            self._task_completion_callbacks.remove(callback)
    
    def _notify_task_completion(self, task_id, success, error=None):
        """Notify all registered callbacks about task completion."""
        for callback in self._task_completion_callbacks:
            try:
                callback(task_id, success, error)
            except Exception as e:
                self.logger.error(f"Error in task completion callback: {str(e)}")
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            # Generate task ID if not provided
            if 'id' not in task_data:
                task_data['id'] = str(uuid.uuid4())
            
            # Validate required fields
            required_fields = ['name', 'account', 'message_link']
            for field in required_fields:
                if field not in task_data or not task_data[field]:
                    raise ValueError(f"Missing required field: {field}")
            
            # Ensure we have at least one target group
            if ('target_groups' not in task_data or not task_data['target_groups']) and \
               ('groups' not in task_data or not task_data['groups']):
                raise ValueError("At least one target group is required")
            
            # If groups is provided as a list but target_groups is not, convert it
            if 'groups' in task_data and isinstance(task_data['groups'], list) and \
               ('target_groups' not in task_data or not isinstance(task_data['target_groups'], list)):
                task_data['target_groups'] = task_data['groups']
            
            # Create the task in the database
            success = self.forwarder_db.create_task(task_data)
            if success:
                self.logger.info(f"Created forwarding task: {task_data['name']}")
                return task_data['id']
            else:
                raise ValueError("Failed to create task in database")
        
        except Exception as e:
            self.logger.error(f"Error creating forwarding task: {str(e)}")
            raise
    
    def update_task(self, task_id, task_data):
        """Update an existing task."""
        try:
            # Ensure the task exists
            existing_task = self.forwarder_db.get_task(task_id)
            if not existing_task:
                raise ValueError(f"Task {task_id} not found")
            
            # Update the task
            success = self.forwarder_db.update_task(task_id, task_data)
            if success:
                self.logger.info(f"Updated forwarding task: {task_id}")
                return True
            else:
                raise ValueError(f"Failed to update task {task_id}")
        
        except Exception as e:
            self.logger.error(f"Error updating forwarding task: {str(e)}")
            raise
    
    def delete_task(self, task_id):
        """Delete a forwarding task."""
        try:
            # First, stop the task if it's running
            self.stop_task(task_id)
            
            # Delete from database
            success = self.forwarder_db.delete_task(task_id)
            if success:
                self.logger.info(f"Deleted forwarding task: {task_id}")
                return True
            else:
                raise ValueError(f"Failed to delete task {task_id}")
        
        except Exception as e:
            self.logger.error(f"Error deleting forwarding task: {str(e)}")
            raise
    
    def get_all_tasks(self):
        """Get all forwarding tasks."""
        try:
            return self.forwarder_db.get_all_tasks()
        except Exception as e:
            self.logger.error(f"Error getting all forwarding tasks: {str(e)}")
            return []
    
    def get_task(self, task_id):
        """Get a specific task by ID."""
        try:
            return self.forwarder_db.get_task(task_id)
        except Exception as e:
            self.logger.error(f"Error getting forwarding task {task_id}: {str(e)}")
            return None
    
    def start_task(self, task_id, progress_callback=None, error_callback=None):
        """Start a specific forwarding task."""
        try:
            with self.tasks_lock:
                # Check if the task is already running
                if task_id in self.active_forwarders:
                    self.logger.warning(f"Task {task_id} is already running")
                    return False
                
                # Get the task from the database
                task = self.forwarder_db.get_task(task_id)
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                # Get the account for this task
                account_phone = task.get('account')
                if not account_phone:
                    raise ValueError(f"No account specified for task {task_id}")
                
                # Get account details from account manager
                account = self._get_account_by_phone(account_phone)
                if not account:
                    raise ValueError(f"Account {account_phone} not found")
                
                # Create a forwarder instance
                forwarder = TelegramForwarder(account, task, self.forwarder_db, self.logger)
                
                # Set callbacks
                forwarder.set_callbacks(progress_callback, error_callback)
                
                # Store the forwarder
                self.active_forwarders[task_id] = forwarder
                
                # Update task status to pending
                self.forwarder_db.update_task_status(task_id, 'pending')
                
                # Start the task in a background thread
                threading.Thread(
                    target=self._run_task_thread,
                    args=(task_id, forwarder),
                    daemon=True
                ).start()
                
                self.logger.info(f"Started forwarding task: {task_id}")
                return True
        
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error starting forwarding task {task_id}: {error_msg}")
            
            # Update task status to error
            try:
                self.forwarder_db.update_task_status(task_id, 'error', error_message=error_msg)
            except:
                pass
            
            # Notify callbacks about error
            if error_callback:
                try:
                    error_callback(task_id, error_msg, True)
                except:
                    pass
            
            return False
    
    def _run_task_thread(self, task_id, forwarder):
        """Run a task in a background thread."""
        success = False
        error = None
        
        try:
            # Run the task
            success = forwarder.run_task()
            
            # Log result
            if success:
                self.logger.info(f"Task {task_id} completed successfully")
            else:
                self.logger.warning(f"Task {task_id} completed with errors")
                
        except Exception as e:
            error = str(e)
            self.logger.error(f"Error running task {task_id}: {error}")
            
            # Update task status to error
            try:
                self.forwarder_db.update_task_status(task_id, 'error', error_message=error)
            except:
                pass
        
        finally:
            # Clean up
            with self.tasks_lock:
                if task_id in self.active_forwarders:
                    del self.active_forwarders[task_id]
            
            # Notify about task completion
            self._notify_task_completion(task_id, success, error)
    
    def stop_task(self, task_id):
        """Stop a specific forwarding task."""
        try:
            with self.tasks_lock:
                # Check if the task is running
                if task_id not in self.active_forwarders:
                    self.logger.warning(f"Task {task_id} is not running")
                    return False
                
                # Get the forwarder
                forwarder = self.active_forwarders[task_id]
                
                # Stop the forwarder
                forwarder.stop()
                
                # Update task status to stopped
                self.forwarder_db.update_task_status(task_id, 'stopped')
                
                self.logger.info(f"Stopped forwarding task: {task_id}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error stopping forwarding task {task_id}: {str(e)}")
            return False
    
    def pause_task(self, task_id):
        """Pause a specific forwarding task."""
        # Currently the same as stop, but we update the status to paused
        try:
            result = self.stop_task(task_id)
            if result:
                self.forwarder_db.update_task_status(task_id, 'paused')
                self.logger.info(f"Paused forwarding task: {task_id}")
            return result
        except Exception as e:
            self.logger.error(f"Error pausing forwarding task {task_id}: {str(e)}")
            return False
    
    def resume_task(self, task_id, progress_callback=None, error_callback=None):
        """Resume a paused forwarding task."""
        # Similar to start_task, but we don't reset the current_index
        try:
            return self.start_task(task_id, progress_callback, error_callback)
        except Exception as e:
            self.logger.error(f"Error resuming forwarding task {task_id}: {str(e)}")
            return False
    
    def is_task_running(self, task_id):
        """Check if a task is currently running."""
        with self.tasks_lock:
            return task_id in self.active_forwarders
    
    def _get_account_by_phone(self, phone):
        """Get account details by phone number."""
        try:
            accounts = self.account_manager.get_accounts()
            for account in accounts:
                if account.get('phone') == phone:
                    return account
            return None
        except Exception as e:
            self.logger.error(f"Error getting account by phone: {str(e)}")
            return None
    
    def update_settings(self, settings):
        """Update forwarder settings."""
        try:
            for key, value in settings.items():
                self.forwarder_db.update_setting(key, value)
            self.logger.info("Updated forwarder settings")
            return True
        except Exception as e:
            self.logger.error(f"Error updating forwarder settings: {str(e)}")
            return False
    
    def get_settings(self):
        """Get current forwarder settings."""
        try:
            return self.forwarder_db.get_settings()
        except Exception as e:
            self.logger.error(f"Error getting forwarder settings: {str(e)}")
            return {}

class TelegramForwarder:
    """Core forwarding engine for sending messages to multiple groups."""
    
    def __init__(self, account, task=None, forwarder_db=None, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.account = account
        self.task = task
        self.forwarder_db = forwarder_db
        self.client = None
        self.is_running = False
        self.should_stop = False
        self.progress_callback = None
        self.error_callback = None
        self.last_processed_index = 0
        self.message_to_forward = None
        self.part2_message_to_forward = None
    
    def set_callbacks(self, progress_callback=None, error_callback=None):
        """Set callback functions for progress and error reporting."""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
    
    def set_task(self, task):
        """Set the current task to process."""
        self.task = task
        self.last_processed_index = task.get('current_index', 0) if t

    
    def run_task(self):

    
        """Run the forwarding task in a synchronous way (wrapper around process_task)."""

    
        import asyncio

    
        

    
        # Reset state

    
        self.should_stop = False

    
        

    
        # Create new event loop for this thread

    
        loop = asyncio.new_event_loop()

    
        asyncio.set_event_loop(loop)

    
        

    
        try:

    
            # Run the async task in the loop

    
            return loop.run_until_complete(self.process_task())

    
        except Exception as e:

    
            self._report_error(f"Error in run_task: {str(e)}", fatal=True)

    
            return False

    
        finally:

    
            loop.close()
ask else 0
    
    def _report_progress(self, group_link, current, total, status="processing", error=None):
        """Report progress to the callback if available."""
        if self.progress_callback:
            self.progress_callback(self.task['id'], group_link, current, total, status, error)
    
    def _report_error(self, error_message, fatal=False):
        """Report errors to the error callback if available."""
        if self.error_callback:
            self.error_callback(self.task['id'], error_message, fatal)
        self.logger.error(f"Forwarder error for task {self.task['id'] if self.task else 'N/A'}: {error_message}")
    
    async def _create_client(self):
        """Create and connect the Telegram client."""
        try:
            from telethon import TelegramClient as TelethonClient
            
            # Get API credentials from account
            api_id = self.account.get("api_id")
            api_hash = self.account.get("api_hash")
            phone = self.account.get("phone")
            
            if not api_id or not api_hash or not phone:
                raise ValueError("Missing API credentials or phone number")
            
            # Ensure sessions directory exists
            os.makedirs("sessions", exist_ok=True)
            
            # Create the client
            session_path = f"sessions/{phone}"
            self.client = TelethonClient(session_path, api_id, api_hash)
            
            # Connect with timeout
            await asyncio.wait_for(self.client.connect(), timeout=30)
            
            # Check authorization
            if not await self.client.is_user_authorized():
                raise ValueError(f"Account {phone} is not authorized")
            
            self.logger.info(f"Connected to Telegram with account {phone}")
            return True
            
        except Exception as e:
            self._report_error(f"Failed to create client: {str(e)}", fatal=True)
            return False
    
    async def _parse_message_link(self, message_link):
        """Parse a message link to extract channel/chat ID and message ID."""
        try:
            # Format: https://t.me/c/**********/123 or https://t.me/username/123
            if not message_link:
                raise ValueError("Message link is empty")
            
            # Remove any query parameters
            message_link = message_link.split('?')[0]
            
            # Extract channel/username and message ID
            parts = message_link.strip('/').split('/')
            
            if len(parts) < 2:
                raise ValueError(f"Invalid message link format: {message_link}")
            
            # Last part is always the message ID
            message_id = int(parts[-1])
            
            # Second to last part is the channel ID or username
            channel_part = parts[-2]
            
            # Check if it's a private channel (with c/ prefix)
            is_private = channel_part == 'c'
            
            if is_private:
                # For private channels, format is t.me/c/channelid/messageid
                if len(parts) < 3:
                    raise ValueError(f"Invalid private channel link: {message_link}")
                channel_id = int(parts[-3])
                return {'channel_id': channel_id, 'message_id': message_id, 'is_private': True}
            else:
                # For public channels/chats, format is t.me/username/messageid
                username = channel_part
                return {'username': username, 'message_id': message_id, 'is_private': False}
            
        except ValueError as ve:
            raise ve
        except Exception as e:
            raise ValueError(f"Failed to parse message link: {str(e)}")
    
    async def _get_message(self, message_info):
        """Retrieve a message from a channel/chat based on parsed message link."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            if message_info.get('is_private', False):
                # Private channel with channel ID
                channel_id = message_info.get('channel_id')
                message_id = message_info.get('message_id')
                
                # Create InputPeerChannel for private channels
                entity = InputPeerChannel(channel_id, 0)  # access_hash 0 means we need to resolve it
                
                try:
                    # Try to get the actual entity
                    entity = await self.client.get_entity(entity)
                except:
                    # If that fails, try to get it from the peer database
                    entity = await self.client.get_input_entity(channel_id)
                
            else:
                # Public channel with username
                username = message_info.get('username')
                message_id = message_info.get('message_id')
                
                # Get the entity
                entity = await self.client.get_entity(username)
            
            # Get the message
            message = await self.client.get_messages(entity, ids=message_id)
            
            if not message:
                raise ValueError(f"Message with ID {message_id} not found")
            
            return message
            
        except Exception as e:
            error_msg = str(e)
            if "ChatAdminRequired" in error_msg:
                raise ValueError("Admin rights required to access this channel")
            elif "FloodWait" in error_msg:
                # Extract the wait time from error message
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            else:
                raise ValueError(f"Failed to get message: {error_msg}")
    
    async def _join_group(self, group_link):
        """Join a group or channel if not already a member."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            # Handle different link formats
            group_link = group_link.strip()
            
            # Extract username/invite hash from link
            if '//t.me/' in group_link:
                parts = group_link.split('t.me/')
                if len(parts) < 2:
                    raise ValueError(f"Invalid group link format: {group_link}")
                group_id = parts[1].strip('/')
            elif 't.me/' in group_link:
                parts = group_link.split('t.me/')
                if len(parts) < 2:
                    raise ValueError(f"Invalid group link format: {group_link}")
                group_id = parts[1].strip('/')
            elif '@' in group_link:
                group_id = group_link.lstrip('@')
            else:
                group_id = group_link
                
            # For invite links (t.me/joinchat/...)
            if 'joinchat' in group_id:
                invite_parts = group_id.split('joinchat/')
                if len(invite_parts) < 2:
                    raise ValueError(f"Invalid invite link: {group_link}")
                invite_hash = invite_parts[1].strip('/')
                
                # Join via invite link
                await self.client(functions.messages.ImportChatInviteRequest(hash=invite_hash))
                return True
            else:
                # Regular channel/group join
                try:
                    # Try to get entity first to check if we're already a member
                    entity = await self.client.get_entity(group_id)
                    
                    # If we got here, we're already a member or it's a public channel
                    # Try joining anyway to ensure membership
                    await self.client(JoinChannelRequest(entity))
                    return True
                except Exception as e:
                    error_msg = str(e)
                    if "Chat admin required" in error_msg or "Admin rights required" in error_msg:
                        # We're already a member but don't have admin rights, which is fine
                        return True
                    else:
                        # Try joining by username/id
                        await self.client(JoinChannelRequest(group_id))
                        return True
            
        except Exception as e:
            error_msg = str(e)
            if "The channel is private" in error_msg or "The user has been banned" in error_msg:
                raise ValueError("Cannot join private channel or user is banned")
            elif "FloodWait" in error_msg:
                # Extract wait time
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            elif "already in this channel" in error_msg or "already a participant" in error_msg:
                # Already a member, which is fine
                return True
            else:
                raise ValueError(f"Failed to join group: {error_msg}")
    
    async def _forward_message(self, message, target_group, reply_message=None):
        """Forward a message to a target group with optional reply text."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            # First join the target group if needed
            await self._join_group(target_group)
            
            # Get the target entity
            target_entity = await self.client.get_entity(target_group)
            
            # Forward the message
            forwarded_msg = await self.client.forward_messages(
                entity=target_entity,
                messages=message,
                silent=False  # Allow notifications
            )
            
            # If there's a reply message to add
            if reply_message and forwarded_msg:
                # Add a reply to the forwarded message
                await self.client.send_message(
                    entity=target_entity,
                    message=reply_message,
                    reply_to=forwarded_msg
                )
            
            # Return success
            return True
            
        except Exception as e:
            error_msg = str(e)
            if "ChatAdminRequired" in error_msg or "Chat admin required" in error_msg:
                raise ValueError("Admin rights required to post in this group")
            elif "CHAT_WRITE_FORBIDDEN" in error_msg or "not allowed to send" in error_msg:
                raise ValueError("Not allowed to post in this group")
            elif "UserBannedInChannel" in error_msg:
                raise ValueError("User is banned in this channel")
            elif "ChannelPrivate" in error_msg:
                raise ValueError("Channel is private")
            elif "FloodWait" in error_msg:
                # Extract wait time
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            else:
                raise ValueError(f"Failed to forward message: {error_msg}")
    
    def _human_sleep(self, min_seconds, max_seconds):
        """Simulate human-like delay between actions."""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
        return delay
    
    async def _init_forwarding_session(self):
        """Initialize a forwarding session by parsing message links and preparing messages."""
        try:
            # Get settings from database
            if self.forwarder_db:
                settings = self.forwarder_db.get_settings()
            else:
                settings = {}
            
            # Check if task has required fields
            if not self.task:
                raise ValueError("No task specified")
            
            if not self.task.get('message_link'):
                raise ValueError("No message link specified")
            
            # Parse message link
            message_info = await self._parse_message_link(self.task['message_link'])
            
            # Get the message to forward
            self.message_to_forward = await self._get_message(message_info)
            
            if not self.message_to_forward:
                raise ValueError("Failed to retrieve message to forward")
            
            # If there's a part 2 message, get that as well
            if self.task.get('part2_message_link'):
                part2_message_info = await self._parse_message_link(self.task['part2_message_link'])
                self.part2_message_to_forward = await self._get_message(part2_message_info)
            
            # Prepare custom reply message if needed
            self.use_reply_message = settings.get('replyMessage', 'true').lower() == 'true'
            
            if self.use_reply_message:
                # Use task-specific reply message if available, otherwise use global setting
                self.reply_message = self.task.get('customReplyMessage')
                
                if not self.reply_message:
                    self.reply_message = settings.get('message', 'Hi! This is an advertising bot.')
            else:
                self.reply_message = None
            
            return True
            
        except Exception as e:
            self._report_error(f"Failed to initialize forwarding: {str(e)}", fatal=True)
            return False
    
    async def process_task(self):
        """Process a forwarding task by sending messages to all target groups."""
        if self.is_running:
            self._report_error("Task is already running", fatal=False)
            return False
        
        self.is_running = True
        self.should_stop = False
        success = False
        
        try:
            if not self.task:
                raise ValueError("No task specified")
            
            # Connect to Telegram
            if not await self._create_client():
                raise ValueError("Failed to connect to Telegram")
            
            # Initialize the forwarding session
            if not await self._init_forwarding_session():
                raise ValueError("Failed to initialize forwarding session")
            
            # Get target groups
            target_groups = self.task.get('target_groups', '').split(',') if self.task.get('target_groups') else []
            
            if not target_groups:
                target_groups = []
                # Check if we have groups in the task_groups table
                if self.forwarder_db and self.task.get('id'):
                    task = self.forwarder_db.get_task(self.task['id'])
                    if task and 'groups' in task:
                        target_groups = [group['group_link'] for group in task['groups']]
            
            if not target_groups:
                raise ValueError("No target groups specified")
            
            # Part 2 target groups if available
            part2_target_groups = self.task.get('part2_target_groups', '').split(',') if self.task.get('part2_target_groups') else []
            
            if not part2_target_groups and self.task.get('id') and self.forwarder_db:
                task = self.forwarder_db.get_task(self.task['id'])
                if task and 'part2_groups' in task:
                    part2_target_groups = [group['group_link'] for group in task['part2_groups']]
            
            # Update task status to running
            if self.forwarder_db and self.task.get('id'):
                self.forwarder_db.update_task_status(self.task['id'], 'running')
            
            # Get forwarding settings
            interval_min = self.task.get('intervalMin', 20)
            interval_max = self.task.get('intervalMax', 25)
            after_each_break = self.task.get('afterEachSecond', 360)
            random_sleep_min = self.task.get('randomSleepTimeMin', 30)
            random_sleep_max = self.task.get('randomSleepTimeMax', 60)
            
            # Process target groups from the last processed index
            total_groups = len(target_groups)
            processed_count = 0
            
            for i in range(self.last_processed_index, total_groups):
                if self.should_stop:
                    self._report_error("Task stopped by user", fatal=False)
                    break
                
                group_link = target_groups[i].strip()
                if not group_link:
                    continue
                
                self._report_progress(group_link, i + 1, total_groups, "processing")
                
                try:
                    # Forward the message
                    await self._forward_message(self.message_to_forward, group_link, self.reply_message)
                    
                    # Update group status in database
                    if self.forwarder_db and self.task.get('id'):
                        self.forwarder_db.update_group_status(self.task['id'], group_link, 1)
                    
                    # Log success
                    self.logger.info(f"Successfully forwarded message to {group_link}")
                    self._report_progress(group_link, i + 1, total_groups, "success")
                    processed_count += 1
                    
                except Exception as e:
                    error_msg = str(e)
                    
                    # Check for FloodWait
                    if "FloodWait" in error_msg:
                        # Extract wait time
                        match = re.search(r'wait (\d+)', error_msg)
                        if match:
                            wait_seconds = int(match.group(1))
                            # Update task status with wait time
                            if self.forwarder_db and self.task.get('id'):
                                self.forwarder_db.update_task_status(
                                    self.task['id'], 
                                    'paused', 
                                    current_index=i,
                                    error_message=f"FloodWait: {wait_seconds} seconds"
                                )
                                
                    # Log error
                    self.logger.error(f"Forward error: {error_msg}")
                    self._report_progress(group_link, i + 1, total_groups, "error", error_msg)
                    
                    # Update group status in database with error
                    if self.forwarder_db and self.task.get('id'):
                        self.forwarder_db.update_group_status(
                            self.task['id'], 
                            group_link, 
                            0, 
                            error_message=error_msg
                        )
                
                # Sleep between forwarding to avoid rate limits
                sleep_time = random.uniform(interval_min, interval_max)
                self.logger.info(f"Sleeping for {sleep_time:.2f} seconds before next group")
                self._human_sleep(sleep_time, sleep_time)
                
                # Update last processed index
                self.last_processed_index = i + 1
                
                # After each X groups, take a longer break
                if processed_count > 0 and processed_count % 10 == 0:
                    rand_break = random.uniform(random_sleep_min, random_sleep_max)
                    self.logger.info(f"Taking a longer break for {rand_break:.2f} seconds after {processed_count} groups")
                    self._human_sleep(rand_break, rand_break)
            
            # Process part 2 message if available
            if self.part2_message_to_forward and part2_target_groups:
                self.logger.info(f"Processing part 2 message to {len(part2_target_groups)} groups")
                
                # Similar logic for part 2 groups...
                # (Omitted for brevity)
            
            # Update task status to completed if we reached the end
            if self.last_processed_index >= total_groups:
                if self.forwarder_db and self.task.get('id'):
                    self.forwarder_db.update_task_status(
                        self.task['id'], 
                        'completed',
                        error_message=None
                    )
                success = True
            
            return success
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error processing task: {error_msg}")
            
            # Update task status to error
            if self.forwarder_db and self.task.get('id'):
                self.forwarder_db.update_task_status(
                    self.task['id'], 
                    'error',
                    error_message=error_msg
                )
            
            self._report_error(error_msg, fatal=True)
            return False
            
        finally:
            self.is_running = False
            
            # Close client if open
            if self.client:
                try:
                    self.client.disconnect()
                except Exception:
                        pass
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")
                self.log_activity_signal.emit("Task error")
                self.log_activity_signal.emit("Task monitoring error")

# Import custom modules
from tg_client import TelegramClient
from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

class ForwarderDatabase:
    """Database manager for the Telegram forwarder module."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self._db_lock = threading.RLock()
        self._busy_handler_calls = 0
        self._max_busy_retries = 25
        
        # Initialize database tables
        self._init_database()
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 5
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = self._dict_factory
                
                # Set custom busy handler
                # Fixed set_busy_timeout compatibility
                conn.execute("PRAGMA busy_timeout = 10000")  # 10 seconds
                
                # Return connection to the caller
                yield conn
                
                # If we got here, everything worked fine
                break
                
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    self.logger.warning(f"Database locked, retrying in {retry_delay}s (attempt {attempt+1}/{max_retries})")
                    time.sleep(retry_delay * (1.5 ** attempt))
                    
                    # Try fixing the database if this is the 3rd attempt
                    if attempt == 2:
                        self.logger.warning("Attempting database recovery during locked state")
                        try:
                            self._fix_db_lock()
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database lock: {str(fix_err)}")
                else:
                    raise
            except Exception as e:
                self.logger.error(f"Database connection error: {str(e)}")
                raise
            finally:
                # Always close the connection if it was opened
                if conn:
                    conn.close()
    
    def _dict_factory(self, cursor, row):
        """Convert row to dictionary."""
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d
    
    def _fix_db_lock(self):
        """Try to fix locked database by creating a temporary connection."""
        try:
            # Close existing connections by connecting briefly
            temp_conn = sqlite3.connect(self.db_path, timeout=20)
            temp_conn.close()
            self.logger.info("Fixed database lock by establishing temporary connection")
        except Exception as e:
            self.logger.error(f"Failed to fix database lock: {str(e)}")
            
    def _init_database(self):
        """Initialize database tables for forwarder functionality."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create tasks table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT,
                        account TEXT,
                        message_link TEXT,
                        target_groups TEXT,
                        part2_message_link TEXT,
                        part2_target_groups TEXT,
                        status TEXT DEFAULT 'pending',
                        current_index INTEGER DEFAULT 0,
                        error_message TEXT,
                        last_processed_time TEXT,
                        created_at TEXT,
                        intervalMin INTEGER DEFAULT 20,
                        intervalMax INTEGER DEFAULT 25,
                        afterEachSecond INTEGER DEFAULT 360,
                        randomSleepTimeMin INTEGER DEFAULT 30,
                        randomSleepTimeMax INTEGER DEFAULT 60,
                        customReplyMessage TEXT
                    )
                ''')
                
                # Create task_groups table for storing target groups for each task
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create part2_task_groups table for storing part 2 target groups
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS part2_task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Initialize default settings if they don't exist
                default_settings = {
                    'useRandomSleepTime': 'true',
                    'logFailedGroups': 'true',
                    'floodwait': 'true',
                    'replyMessage': 'true',
                    'message': 'Hi! This is an advertising bot. @vipstore. DMs won\'t be seen here'
                }
                
                for key, value in default_settings.items():
                    cursor.execute(
                        "INSERT OR IGNORE INTO forwarder_settings (key, value) VALUES (?, ?)",
                        (key, value)
                    )
                
                conn.commit()
                
                self.logger.info("Initialized forwarder database tables")
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            task_id = task_data.get('id')
            name = task_data.get('name')
            account = task_data.get('account')
            message_link = task_data.get('message_link')
            target_groups = task_data.get('target_groups', [])
            part2_message_link = task_data.get('part2_message_link')
            part2_target_groups = task_data.get('part2_target_groups', [])
            
            # Get account-specific settings if available, or use defaults
            intervalMin = task_data.get('intervalMin', 20)
            intervalMax = task_data.get('intervalMax', 25)
            afterEachSecond = task_data.get('afterEachSecond', 360)
            randomSleepTimeMin = task_data.get('randomSleepTimeMin', 30)
            randomSleepTimeMax = task_data.get('randomSleepTimeMax', 60)
            customReplyMessage = task_data.get('customReplyMessage')
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Insert task
                cursor.execute(
                    """
                    INSERT INTO forwarder_tasks (
                        id, name, account, message_link, target_groups,
                        part2_message_link, part2_target_groups, status,
                        created_at, intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        task_id, name, account, message_link, ','.join(target_groups),
                        part2_message_link, ','.join(part2_target_groups), 'pending',
                        datetime.now().isoformat(), intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    )
                )
                
                # Insert target groups
                for group in target_groups:
                    cursor.execute(
                        "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                # Insert part 2 target groups if available
                for group in part2_target_groups:
                    cursor.execute(
                        "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                conn.commit()
                
            self.logger.info(f"Created forwarder task: {name} for account {account}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating forwarder task: {str(e)}")
            return False
    
    def update_task(self, task_id, task_data):
        """Update an existing task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # First, update the task details
                update_fields = []
                update_values = []
                
                for field, value in task_data.items():
                    if field in ['target_groups', 'part2_target_groups'] and isinstance(value, list):
                        update_fields.append(f"{field} = ?")
                        update_values.append(','.join(value))
                    elif field not in ['id', 'task_id']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                if update_fields:
                    cursor.execute(
                        f"UPDATE forwarder_tasks SET {', '.join(update_fields)} WHERE id = ?",
                        update_values + [task_id]
                    )
                
                # If target groups are being updated, handle them separately
                if 'target_groups' in task_data and isinstance(task_data['target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['target_groups']:
                        cursor.execute(
                            "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                # Same for part 2 target groups
                if 'part2_target_groups' in task_data and isinstance(task_data['part2_target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['part2_target_groups']:
                        cursor.execute(
                            "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder task: {str(e)}")
            return False
    
    def delete_task(self, task_id):
        """Delete a forwarding task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Delete from main tasks table
                cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_id,))
                
                # Delete associated groups
                cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                
                conn.commit()
                
            self.logger.info(f"Deleted forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting forwarder task: {str(e)}")
            return False
    
    def get_all_tasks(self):
        """Get all forwarding tasks."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks ORDER BY created_at DESC")
                tasks = cursor.fetchall()
                
                # For each task, get its target groups
                for task in tasks:
                    # Get regular target groups
                    cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task['id'],))
                    task['groups'] = cursor.fetchall()
                    
                    # Get part 2 target groups
                    cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task['id'],))
                    task['part2_groups'] = cursor.fetchall()
                
                return tasks
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder tasks: {str(e)}")
            return []
    
    def get_task(self, task_id):
        """Get a specific task by ID."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    return None
                
                # Get target groups
                cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task_id,))
                task['groups'] = cursor.fetchall()
                
                # Get part 2 target groups
                cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task_id,))
                task['part2_groups'] = cursor.fetchall()
                
                return task
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder task: {str(e)}")
            return None
    
    def update_task_status(self, task_id, status, current_index=None, error_message=None):
        """Update the status of a task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                update_values = [status, datetime.now().isoformat()]
                update_sql = "UPDATE forwarder_tasks SET status = ?, last_processed_time = ?"
                
                if current_index is not None:
                    update_sql += ", current_index = ?"
                    update_values.append(current_index)
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE id = ?"
                update_values.append(task_id)
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            self.logger.info(f"Updated task {task_id} status to {status}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating task status: {str(e)}")
            return False
    
    def update_group_status(self, task_id, group_link, processed, error_message=None, is_part2=False):
        """Update the processed status for a group."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                table = "part2_task_groups" if is_part2 else "task_groups"
                
                update_values = [processed]
                update_sql = f"UPDATE {table} SET processed = ?"
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE task_id = ? AND group_link = ?"
                update_values.extend([task_id, group_link])
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating group status: {str(e)}")
            return False
    
    def get_settings(self):
        """Get all forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT key, value FROM forwarder_settings")
                settings = {row['key']: row['value'] for row in cursor.fetchall()}
                
                return settings
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder settings: {str(e)}")
            return {}
    
    def update_setting(self, key, value):
        """Update a specific setting."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "INSERT OR REPLACE INTO forwarder_settings (key, value) VALUES (?, ?)",
                    (key, value)
                )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder setting: {key} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder setting: {str(e)}")
            return False

class ForwarderManager:
    """Manager for coordinating multiple forwarding tasks and accounts."""
    
    def __init__(self, account_manager, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.account_manager = account_manager
        self.forwarder_db = ForwarderDatabase(logger=self.logger)
        self.active_forwarders = {}  # task_id -> forwarder
        self.tasks_lock = threading.RLock()
        self._is_running = False
        self._should_stop = False
        self._task_completion_callbacks = []
    
    def add_task_completion_callback(self, callback):
        """Add a callback function to be called when a task completes."""
        if callback not in self._task_completion_callbacks:
            self._task_completion_callbacks.append(callback)
    
    def remove_task_completion_callback(self, callback):
        """Remove a task completion callback."""
        if callback in self._task_completion_callbacks:
            self._task_completion_callbacks.remove(callback)
    
    def _notify_task_completion(self, task_id, success, error=None):
        """Notify all registered callbacks about task completion."""
        for callback in self._task_completion_callbacks:
            try:
                callback(task_id, success, error)
            except Exception as e:
                self.logger.error(f"Error in task completion callback: {str(e)}")
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            # Generate task ID if not provided
            if 'id' not in task_data:
                task_data['id'] = str(uuid.uuid4())
            
            # Validate required fields
            required_fields = ['name', 'account', 'message_link']
            for field in required_fields:
                if field not in task_data or not task_data[field]:
                    raise ValueError(f"Missing required field: {field}")
            
            # Ensure we have at least one target group
            if ('target_groups' not in task_data or not task_data['target_groups']) and \
               ('groups' not in task_data or not task_data['groups']):
                raise ValueError("At least one target group is required")
            
            # If groups is provided as a list but target_groups is not, convert it
            if 'groups' in task_data and isinstance(task_data['groups'], list) and \
               ('target_groups' not in task_data or not isinstance(task_data['target_groups'], list)):
                task_data['target_groups'] = task_data['groups']
            
            # Create the task in the database
            success = self.forwarder_db.create_task(task_data)
            if success:
                self.logger.info(f"Created forwarding task: {task_data['name']}")
                return task_data['id']
            else:
                raise ValueError("Failed to create task in database")
        
        except Exception as e:
            self.logger.error(f"Error creating forwarding task: {str(e)}")
            raise
    
    def update_task(self, task_id, task_data):
        """Update an existing task."""
        try:
            # Ensure the task exists
            existing_task = self.forwarder_db.get_task(task_id)
            if not existing_task:
                raise ValueError(f"Task {task_id} not found")
            
            # Update the task
            success = self.forwarder_db.update_task(task_id, task_data)
            if success:
                self.logger.info(f"Updated forwarding task: {task_id}")
                return True
            else:
                raise ValueError(f"Failed to update task {task_id}")
        
        except Exception as e:
            self.logger.error(f"Error updating forwarding task: {str(e)}")
            raise
    
    def delete_task(self, task_id):
        """Delete a forwarding task."""
        try:
            # First, stop the task if it's running
            self.stop_task(task_id)
            
            # Delete from database
            success = self.forwarder_db.delete_task(task_id)
            if success:
                self.logger.info(f"Deleted forwarding task: {task_id}")
                return True
            else:
                raise ValueError(f"Failed to delete task {task_id}")
        
        except Exception as e:
            self.logger.error(f"Error deleting forwarding task: {str(e)}")
            raise
    
    def get_all_tasks(self):
        """Get all forwarding tasks."""
        try:
            return self.forwarder_db.get_all_tasks()
        except Exception as e:
            self.logger.error(f"Error getting all forwarding tasks: {str(e)}")
            return []
    
    def get_task(self, task_id):
        """Get a specific task by ID."""
        try:
            return self.forwarder_db.get_task(task_id)
        except Exception as e:
            self.logger.error(f"Error getting forwarding task {task_id}: {str(e)}")
            return None
    
    def start_task(self, task_id, progress_callback=None, error_callback=None):
        """Start a specific forwarding task."""
        try:
            with self.tasks_lock:
                # Check if the task is already running
                if task_id in self.active_forwarders:
                    self.logger.warning(f"Task {task_id} is already running")
                    return False
                
                # Get the task from the database
                task = self.forwarder_db.get_task(task_id)
                if not task:
                    raise ValueError(f"Task {task_id} not found")
                
                # Get the account for this task
                account_phone = task.get('account')
                if not account_phone:
                    raise ValueError(f"No account specified for task {task_id}")
                
                # Get account details from account manager
                account = self._get_account_by_phone(account_phone)
                if not account:
                    raise ValueError(f"Account {account_phone} not found")
                
                # Create a forwarder instance
                forwarder = TelegramForwarder(account, task, self.forwarder_db, self.logger)
                
                # Set callbacks
                forwarder.set_callbacks(progress_callback, error_callback)
                
                # Store the forwarder
                self.active_forwarders[task_id] = forwarder
                
                # Update task status to pending
                self.forwarder_db.update_task_status(task_id, 'pending')
                
                # Start the task in a background thread
                threading.Thread(
                    target=self._run_task_thread,
                    args=(task_id, forwarder),
                    daemon=True
                ).start()
                
                self.logger.info(f"Started forwarding task: {task_id}")
                return True
        
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error starting forwarding task {task_id}: {error_msg}")
            
            # Update task status to error
            try:
                self.forwarder_db.update_task_status(task_id, 'error', error_message=error_msg)
            except:
                pass
            
            # Notify callbacks about error
            if error_callback:
                try:
                    error_callback(task_id, error_msg, True)
                except:
                    pass
            
            return False
    
    def _run_task_thread(self, task_id, forwarder):
        """Run a task in a background thread."""
        success = False
        error = None
        
        try:
            # Run the task
            success = forwarder.run_task()
            
            # Log result
            if success:
                self.logger.info(f"Task {task_id} completed successfully")
            else:
                self.logger.warning(f"Task {task_id} completed with errors")
                
        except Exception as e:
            error = str(e)
            self.logger.error(f"Error running task {task_id}: {error}")
            
            # Update task status to error
            try:
                self.forwarder_db.update_task_status(task_id, 'error', error_message=error)
            except:
                pass
        
        finally:
            # Clean up
            with self.tasks_lock:
                if task_id in self.active_forwarders:
                    del self.active_forwarders[task_id]
            
            # Notify about task completion
            self._notify_task_completion(task_id, success, error)
    
    def stop_task(self, task_id):
        """Stop a specific forwarding task."""
        try:
            with self.tasks_lock:
                # Check if the task is running
                if task_id not in self.active_forwarders:
                    self.logger.warning(f"Task {task_id} is not running")
                    return False
                
                # Get the forwarder
                forwarder = self.active_forwarders[task_id]
                
                # Stop the forwarder
                forwarder.stop()
                
                # Update task status to stopped
                self.forwarder_db.update_task_status(task_id, 'stopped')
                
                self.logger.info(f"Stopped forwarding task: {task_id}")
                return True
        
        except Exception as e:
            self.logger.error(f"Error stopping forwarding task {task_id}: {str(e)}")
            return False
    
    def pause_task(self, task_id):
        """Pause a specific forwarding task."""
        # Currently the same as stop, but we update the status to paused
        try:
            result = self.stop_task(task_id)
            if result:
                self.forwarder_db.update_task_status(task_id, 'paused')
                self.logger.info(f"Paused forwarding task: {task_id}")
            return result
        except Exception as e:
            self.logger.error(f"Error pausing forwarding task {task_id}: {str(e)}")
            return False
    
    def resume_task(self, task_id, progress_callback=None, error_callback=None):
        """Resume a paused forwarding task."""
        # Similar to start_task, but we don't reset the current_index
        try:
            return self.start_task(task_id, progress_callback, error_callback)
        except Exception as e:
            self.logger.error(f"Error resuming forwarding task {task_id}: {str(e)}")
            return False
    
    def is_task_running(self, task_id):
        """Check if a task is currently running."""
        with self.tasks_lock:
            return task_id in self.active_forwarders
    
    def _get_account_by_phone(self, phone):
        """Get account details by phone number."""
        try:
            accounts = self.account_manager.get_accounts()
            for account in accounts:
                if account.get('phone') == phone:
                    return account
            return None
        except Exception as e:
            self.logger.error(f"Error getting account by phone: {str(e)}")
            return None
    
    def update_settings(self, settings):
        """Update forwarder settings."""
        try:
            for key, value in settings.items():
                self.forwarder_db.update_setting(key, value)
            self.logger.info("Updated forwarder settings")
            return True
        except Exception as e:
            self.logger.error(f"Error updating forwarder settings: {str(e)}")
            return False
    
    def get_settings(self):
        """Get current forwarder settings."""
        try:
            return self.forwarder_db.get_settings()
        except Exception as e:
            self.logger.error(f"Error getting forwarder settings: {str(e)}")
            return {}

class TelegramForwarder:
    """Core forwarding engine for sending messages to multiple groups."""
    
    def __init__(self, account, task=None, forwarder_db=None, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.account = account
        self.task = task
        self.forwarder_db = forwarder_db
        self.client = None
        self.is_running = False
        self.should_stop = False
        self.progress_callback = None
        self.error_callback = None
        self.last_processed_index = 0
        self.message_to_forward = None
        self.part2_message_to_forward = None
    
    def set_callbacks(self, progress_callback=None, error_callback=None):
        """Set callback functions for progress and error reporting."""
        self.progress_callback = progress_callback
        self.error_callback = error_callback
    
    def set_task(self, task):
        """Set the current task to process."""
        self.task = task
        self.last_processed_index = task.get('current_index', 0) if t

    
    def run_task(self):

    
        """Run the forwarding task in a synchronous way (wrapper around process_task)."""

    
        import asyncio

    
        

    
        # Reset state

    
        self.should_stop = False

    
        

    
        # Create new event loop for this thread

    
        loop = asyncio.new_event_loop()

    
        asyncio.set_event_loop(loop)

    
        

    
        try:

    
            # Run the async task in the loop

    
            return loop.run_until_complete(self.process_task())

    
        except Exception as e:

    
            self._report_error(f"Error in run_task: {str(e)}", fatal=True)

    
            return False

    
        finally:

    
            loop.close()
ask else 0
    
    def _report_progress(self, group_link, current, total, status="processing", error=None):
        """Report progress to the callback if available."""
        if self.progress_callback:
            self.progress_callback(self.task['id'], group_link, current, total, status, error)
    
    def _report_error(self, error_message, fatal=False):
        """Report errors to the error callback if available."""
        if self.error_callback:
            self.error_callback(self.task['id'], error_message, fatal)
        self.logger.error(f"Forwarder error for task {self.task['id'] if self.task else 'N/A'}: {error_message}")
    
    async def _create_client(self):
        """Create and connect the Telegram client."""
        try:
            from telethon import TelegramClient as TelethonClient
            
            # Get API credentials from account
            api_id = self.account.get("api_id")
            api_hash = self.account.get("api_hash")
            phone = self.account.get("phone")
            
            if not api_id or not api_hash or not phone:
                raise ValueError("Missing API credentials or phone number")
            
            # Ensure sessions directory exists
            os.makedirs("sessions", exist_ok=True)
            
            # Create the client
            session_path = f"sessions/{phone}"
            self.client = TelethonClient(session_path, api_id, api_hash)
            
            # Connect with timeout
            await asyncio.wait_for(self.client.connect(), timeout=30)
            
            # Check authorization
            if not await self.client.is_user_authorized():
                raise ValueError(f"Account {phone} is not authorized")
            
            self.logger.info(f"Connected to Telegram with account {phone}")
            return True
            
        except Exception as e:
            self._report_error(f"Failed to create client: {str(e)}", fatal=True)
            return False
    
    async def _parse_message_link(self, message_link):
        """Parse a message link to extract channel/chat ID and message ID."""
        try:
            # Format: https://t.me/c/**********/123 or https://t.me/username/123
            if not message_link:
                raise ValueError("Message link is empty")
            
            # Remove any query parameters
            message_link = message_link.split('?')[0]
            
            # Extract channel/username and message ID
            parts = message_link.strip('/').split('/')
            
            if len(parts) < 2:
                raise ValueError(f"Invalid message link format: {message_link}")
            
            # Last part is always the message ID
            message_id = int(parts[-1])
            
            # Second to last part is the channel ID or username
            channel_part = parts[-2]
            
            # Check if it's a private channel (with c/ prefix)
            is_private = channel_part == 'c'
            
            if is_private:
                # For private channels, format is t.me/c/channelid/messageid
                if len(parts) < 3:
                    raise ValueError(f"Invalid private channel link: {message_link}")
                channel_id = int(parts[-3])
                return {'channel_id': channel_id, 'message_id': message_id, 'is_private': True}
            else:
                # For public channels/chats, format is t.me/username/messageid
                username = channel_part
                return {'username': username, 'message_id': message_id, 'is_private': False}
            
        except ValueError as ve:
            raise ve
        except Exception as e:
            raise ValueError(f"Failed to parse message link: {str(e)}")
    
    async def _get_message(self, message_info):
        """Retrieve a message from a channel/chat based on parsed message link."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            if message_info.get('is_private', False):
                # Private channel with channel ID
                channel_id = message_info.get('channel_id')
                message_id = message_info.get('message_id')
                
                # Create InputPeerChannel for private channels
                entity = InputPeerChannel(channel_id, 0)  # access_hash 0 means we need to resolve it
                
                try:
                    # Try to get the actual entity
                    entity = await self.client.get_entity(entity)
                except:
                    # If that fails, try to get it from the peer database
                    entity = await self.client.get_input_entity(channel_id)
                
            else:
                # Public channel with username
                username = message_info.get('username')
                message_id = message_info.get('message_id')
                
                # Get the entity
                entity = await self.client.get_entity(username)
            
            # Get the message
            message = await self.client.get_messages(entity, ids=message_id)
            
            if not message:
                raise ValueError(f"Message with ID {message_id} not found")
            
            return message
            
        except Exception as e:
            error_msg = str(e)
            if "ChatAdminRequired" in error_msg:
                raise ValueError("Admin rights required to access this channel")
            elif "FloodWait" in error_msg:
                # Extract the wait time from error message
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            else:
                raise ValueError(f"Failed to get message: {error_msg}")
    
    async def _join_group(self, group_link):
        """Join a group or channel if not already a member."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            # Handle different link formats
            group_link = group_link.strip()
            
            # Extract username/invite hash from link
            if '//t.me/' in group_link:
                parts = group_link.split('t.me/')
                if len(parts) < 2:
                    raise ValueError(f"Invalid group link format: {group_link}")
                group_id = parts[1].strip('/')
            elif 't.me/' in group_link:
                parts = group_link.split('t.me/')
                if len(parts) < 2:
                    raise ValueError(f"Invalid group link format: {group_link}")
                group_id = parts[1].strip('/')
            elif '@' in group_link:
                group_id = group_link.lstrip('@')
            else:
                group_id = group_link
                
            # For invite links (t.me/joinchat/...)
            if 'joinchat' in group_id:
                invite_parts = group_id.split('joinchat/')
                if len(invite_parts) < 2:
                    raise ValueError(f"Invalid invite link: {group_link}")
                invite_hash = invite_parts[1].strip('/')
                
                # Join via invite link
                await self.client(functions.messages.ImportChatInviteRequest(hash=invite_hash))
                return True
            else:
                # Regular channel/group join
                try:
                    # Try to get entity first to check if we're already a member
                    entity = await self.client.get_entity(group_id)
                    
                    # If we got here, we're already a member or it's a public channel
                    # Try joining anyway to ensure membership
                    await self.client(JoinChannelRequest(entity))
                    return True
                except Exception as e:
                    error_msg = str(e)
                    if "Chat admin required" in error_msg or "Admin rights required" in error_msg:
                        # We're already a member but don't have admin rights, which is fine
                        return True
                    else:
                        # Try joining by username/id
                        await self.client(JoinChannelRequest(group_id))
                        return True
            
        except Exception as e:
            error_msg = str(e)
            if "The channel is private" in error_msg or "The user has been banned" in error_msg:
                raise ValueError("Cannot join private channel or user is banned")
            elif "FloodWait" in error_msg:
                # Extract wait time
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            elif "already in this channel" in error_msg or "already a participant" in error_msg:
                # Already a member, which is fine
                return True
            else:
                raise ValueError(f"Failed to join group: {error_msg}")
    
    async def _forward_message(self, message, target_group, reply_message=None):
        """Forward a message to a target group with optional reply text."""
        try:
            if not self.client:
                raise ValueError("Client not connected")
            
            # First join the target group if needed
            await self._join_group(target_group)
            
            # Get the target entity
            target_entity = await self.client.get_entity(target_group)
            
            # Forward the message
            forwarded_msg = await self.client.forward_messages(
                entity=target_entity,
                messages=message,
                silent=False  # Allow notifications
            )
            
            # If there's a reply message to add
            if reply_message and forwarded_msg:
                # Add a reply to the forwarded message
                await self.client.send_message(
                    entity=target_entity,
                    message=reply_message,
                    reply_to=forwarded_msg
                )
            
            # Return success
            return True
            
        except Exception as e:
            error_msg = str(e)
            if "ChatAdminRequired" in error_msg or "Chat admin required" in error_msg:
                raise ValueError("Admin rights required to post in this group")
            elif "CHAT_WRITE_FORBIDDEN" in error_msg or "not allowed to send" in error_msg:
                raise ValueError("Not allowed to post in this group")
            elif "UserBannedInChannel" in error_msg:
                raise ValueError("User is banned in this channel")
            elif "ChannelPrivate" in error_msg:
                raise ValueError("Channel is private")
            elif "FloodWait" in error_msg:
                # Extract wait time
                match = re.search(r'(\d+)(s|m|h)', error_msg)
                if match:
                    wait_time = match.group(1)
                    raise ValueError(f"FloodWait: need to wait {wait_time} seconds")
                else:
                    raise ValueError("FloodWait error occurred")
            else:
                raise ValueError(f"Failed to forward message: {error_msg}")
    
    def _human_sleep(self, min_seconds, max_seconds):
        """Simulate human-like delay between actions."""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
        return delay
    
    async def _init_forwarding_session(self):
        """Initialize a forwarding session by parsing message links and preparing messages."""
        try:
            # Get settings from database
            if self.forwarder_db:
                settings = self.forwarder_db.get_settings()
            else:
                settings = {}
            
            # Check if task has required fields
            if not self.task:
                raise ValueError("No task specified")
            
            if not self.task.get('message_link'):
                raise ValueError("No message link specified")
            
            # Parse message link
            message_info = await self._parse_message_link(self.task['message_link'])
            
            # Get the message to forward
            self.message_to_forward = await self._get_message(message_info)
            
            if not self.message_to_forward:
                raise ValueError("Failed to retrieve message to forward")
            
            # If there's a part 2 message, get that as well
            if self.task.get('part2_message_link'):
                part2_message_info = await self._parse_message_link(self.task['part2_message_link'])
                self.part2_message_to_forward = await self._get_message(part2_message_info)
            
            # Prepare custom reply message if needed
            self.use_reply_message = settings.get('replyMessage', 'true').lower() == 'true'
            
            if self.use_reply_message:
                # Use task-specific reply message if available, otherwise use global setting
                self.reply_message = self.task.get('customReplyMessage')
                
                if not self.reply_message:
                    self.reply_message = settings.get('message', 'Hi! This is an advertising bot.')
            else:
                self.reply_message = None
            
            return True
            
        except Exception as e:
            self._report_error(f"Failed to initialize forwarding: {str(e)}", fatal=True)
            return False
    
    async def process_task(self):
        """Process a forwarding task by sending messages to all target groups."""
        if self.is_running:
            self._report_error("Task is already running", fatal=False)
            return False
        
        self.is_running = True
        self.should_stop = False
        success = False
        
        try:
            if not self.task:
                raise ValueError("No task specified")
            
            # Connect to Telegram
            if not await self._create_client():
                raise ValueError("Failed to connect to Telegram")
            
            # Initialize the forwarding session
            if not await self._init_forwarding_session():
                raise ValueError("Failed to initialize forwarding session")
            
            # Get target groups
            target_groups = self.task.get('target_groups', '').split(',') if self.task.get('target_groups') else []
            
            if not target_groups:
                target_groups = []
                # Check if we have groups in the task_groups table
                if self.forwarder_db and self.task.get('id'):
                    task = self.forwarder_db.get_task(self.task['id'])
                    if task and 'groups' in task:
                        target_groups = [group['group_link'] for group in task['groups']]
            
            if not target_groups:
                raise ValueError("No target groups specified")
            
            # Part 2 target groups if available
            part2_target_groups = self.task.get('part2_target_groups', '').split(',') if self.task.get('part2_target_groups') else []
            
            if not part2_target_groups and self.task.get('id') and self.forwarder_db:
                task = self.forwarder_db.get_task(self.task['id'])
                if task and 'part2_groups' in task:
                    part2_target_groups = [group['group_link'] for group in task['part2_groups']]
            
            # Update task status to running
            if self.forwarder_db and self.task.get('id'):
                self.forwarder_db.update_task_status(self.task['id'], 'running')
            
            # Get forwarding settings
            interval_min = self.task.get('intervalMin', 20)
            interval_max = self.task.get('intervalMax', 25)
            after_each_break = self.task.get('afterEachSecond', 360)
            random_sleep_min = self.task.get('randomSleepTimeMin', 30)
            random_sleep_max = self.task.get('randomSleepTimeMax', 60)
            
            # Process target groups from the last processed index
            total_groups = len(target_groups)
            processed_count = 0
            
            for i in range(self.last_processed_index, total_groups):
                if self.should_stop:
                    self._report_error("Task stopped by user", fatal=False)
                    break
                
                group_link = target_groups[i].strip()
                if not group_link:
                    continue
                
                self._report_progress(group_link, i + 1, total_groups, "processing")
                
                try:
                    # Forward the message
                    await self._forward_message(self.message_to_forward, group_link, self.reply_message)
                    
                    # Update group status in database
                    if self.forwarder_db and self.task.get('id'):
                        self.forwarder_db.update_group_status(self.task['id'], group_link, 1)
                    
                    # Log success
                    self.logger.info(f"Successfully forwarded message to {group_link}")
                    self._report_progress(group_link, i + 1, total_groups, "success")
                    processed_count += 1
                    
                except Exception as e:
                    error_msg = str(e)
                    
                    # Check for FloodWait
                    if "FloodWait" in error_msg:
                        # Extract wait time
                        match = re.search(r'wait (\d+)', error_msg)
                        if match:
                            wait_seconds = int(match.group(1))
                            # Update task status with wait time
                            if self.forwarder_db and self.task.get('id'):
                                self.forwarder_db.update_task_status(
                                    self.task['id'], 
                                    'paused', 
                                    current_index=i,
                                    error_message=f"FloodWait: {wait_seconds} seconds"
                                )
                                
                    # Log error
                    self.logger.error(f"Forward error: {error_msg}")
                    self._report_progress(group_link, i + 1, total_groups, "error", error_msg)
                    
                    # Update group status in database with error
                    if self.forwarder_db and self.task.get('id'):
                        self.forwarder_db.update_group_status(
                            self.task['id'], 
                            group_link, 
                            0, 
                            error_message=error_msg
                        )
                
                # Sleep between forwarding to avoid rate limits
                sleep_time = random.uniform(interval_min, interval_max)
                self.logger.info(f"Sleeping for {sleep_time:.2f} seconds before next group")
                self._human_sleep(sleep_time, sleep_time)
                
                # Update last processed index
                self.last_processed_index = i + 1
                
                # After each X groups, take a longer break
                if processed_count > 0 and processed_count % 10 == 0:
                    rand_break = random.uniform(random_sleep_min, random_sleep_max)
                    self.logger.info(f"Taking a longer break for {rand_break:.2f} seconds after {processed_count} groups")
                    self._human_sleep(rand_break, rand_break)
            
            # Process part 2 message if available
            if self.part2_message_to_forward and part2_target_groups:
                self.logger.info(f"Processing part 2 message to {len(part2_target_groups)} groups")
                
                # Similar logic for part 2 groups...
                # (Omitted for brevity)
            
            # Update task status to completed if we reached the end
            if self.last_processed_index >= total_groups:
                if self.forwarder_db and self.task.get('id'):
                    self.forwarder_db.update_task_status(
                        self.task['id'], 
                        'completed',
                        error_message=None
                    )
                success = True
            
            return success
            
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"Error processing task: {error_msg}")
            
            # Update task status to error
            if self.forwarder_db and self.task.get('id'):
                self.forwarder_db.update_task_status(
                    self.task['id'], 
                    'error',
                    error_message=error_msg
                )
            
            self._report_error(error_msg, fatal=True)
            return False
            
        finally:
            self.is_running = False
            
            # Close client if open
            if self.client:
                try:
                    self.client.disconnect()
                except Exception:
                        pass

# Import custom modules
from tg_client import TelegramClient
from account_manager import AccountManager
from monitor import Monitor
from logger import setup_logger, log_auth, read_auth_log, read_log_file, filter_logs_by_level, log_usage_checker, read_usage_checker_log

class ForwarderDatabase:
    """Database manager for the Telegram forwarder module."""
    
    def __init__(self, logger=None, db_path="tg_checker.db"):
        self.logger = logger or logging.getLogger(__name__)
        self.db_path = db_path
        self._db_lock = threading.RLock()
        self._busy_handler_calls = 0
        self._max_busy_retries = 25
        
        # Initialize database tables
        self._init_database()
    
    @contextmanager
    def _get_db_connection(self):
        """Context manager for database connections to prevent locking issues."""
        conn = None
        max_retries = 5
        retry_delay = 1.0  # seconds
        
        for attempt in range(max_retries):
            try:
                conn = sqlite3.connect(self.db_path)
                conn.row_factory = self._dict_factory
                
                # Set custom busy handler
                # Fixed set_busy_timeout compatibility
                conn.execute("PRAGMA busy_timeout = 10000")  # 10 seconds
                
                # Return connection to the caller
                yield conn
                
                # If we got here, everything worked fine
                break
                
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    self.logger.warning(f"Database locked, retrying in {retry_delay}s (attempt {attempt+1}/{max_retries})")
                    time.sleep(retry_delay * (1.5 ** attempt))
                    
                    # Try fixing the database if this is the 3rd attempt
                    if attempt == 2:
                        self.logger.warning("Attempting database recovery during locked state")
                        try:
                            self._fix_db_lock()
                        except Exception as fix_err:
                            self.logger.error(f"Failed to fix database lock: {str(fix_err)}")
                else:
                    raise
            except Exception as e:
                self.logger.error(f"Database connection error: {str(e)}")
                raise
            finally:
                # Always close the connection if it was opened
                if conn:
                    conn.close()
    
    def _dict_factory(self, cursor, row):
        """Convert row to dictionary."""
        d = {}
        for idx, col in enumerate(cursor.description):
            d[col[0]] = row[idx]
        return d
    
    def _fix_db_lock(self):
        """Try to fix locked database by creating a temporary connection."""
        try:
            # Close existing connections by connecting briefly
            temp_conn = sqlite3.connect(self.db_path, timeout=20)
            temp_conn.close()
            self.logger.info("Fixed database lock by establishing temporary connection")
        except Exception as e:
            self.logger.error(f"Failed to fix database lock: {str(e)}")
            
    def _init_database(self):
        """Initialize database tables for forwarder functionality."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Create tasks table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_tasks (
                        id TEXT PRIMARY KEY,
                        name TEXT,
                        account TEXT,
                        message_link TEXT,
                        target_groups TEXT,
                        part2_message_link TEXT,
                        part2_target_groups TEXT,
                        status TEXT DEFAULT 'pending',
                        current_index INTEGER DEFAULT 0,
                        error_message TEXT,
                        last_processed_time TEXT,
                        created_at TEXT,
                        intervalMin INTEGER DEFAULT 20,
                        intervalMax INTEGER DEFAULT 25,
                        afterEachSecond INTEGER DEFAULT 360,
                        randomSleepTimeMin INTEGER DEFAULT 30,
                        randomSleepTimeMax INTEGER DEFAULT 60,
                        customReplyMessage TEXT
                    )
                ''')
                
                # Create task_groups table for storing target groups for each task
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create part2_task_groups table for storing part 2 target groups
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS part2_task_groups (
                        task_id TEXT,
                        group_link TEXT,
                        processed INTEGER DEFAULT 0,
                        error_message TEXT,
                        FOREIGN KEY (task_id) REFERENCES forwarder_tasks(id) ON DELETE CASCADE,
                        PRIMARY KEY (task_id, group_link)
                    )
                ''')
                
                # Create settings table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS forwarder_settings (
                        key TEXT PRIMARY KEY,
                        value TEXT
                    )
                ''')
                
                # Initialize default settings if they don't exist
                default_settings = {
                    'useRandomSleepTime': 'true',
                    'logFailedGroups': 'true',
                    'floodwait': 'true',
                    'replyMessage': 'true',
                    'message': 'Hi! This is an advertising bot. @vipstore. DMs won\'t be seen here'
                }
                
                for key, value in default_settings.items():
                    cursor.execute(
                        "INSERT OR IGNORE INTO forwarder_settings (key, value) VALUES (?, ?)",
                        (key, value)
                    )
                
                conn.commit()
                
                self.logger.info("Initialized forwarder database tables")
                
        except Exception as e:
            self.logger.error(f"Database initialization error: {str(e)}")
    
    def create_task(self, task_data):
        """Create a new forwarding task."""
        try:
            task_id = task_data.get('id')
            name = task_data.get('name')
            account = task_data.get('account')
            message_link = task_data.get('message_link')
            target_groups = task_data.get('target_groups', [])
            part2_message_link = task_data.get('part2_message_link')
            part2_target_groups = task_data.get('part2_target_groups', [])
            
            # Get account-specific settings if available, or use defaults
            intervalMin = task_data.get('intervalMin', 20)
            intervalMax = task_data.get('intervalMax', 25)
            afterEachSecond = task_data.get('afterEachSecond', 360)
            randomSleepTimeMin = task_data.get('randomSleepTimeMin', 30)
            randomSleepTimeMax = task_data.get('randomSleepTimeMax', 60)
            customReplyMessage = task_data.get('customReplyMessage')
            
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Insert task
                cursor.execute(
                    """
                    INSERT INTO forwarder_tasks (
                        id, name, account, message_link, target_groups,
                        part2_message_link, part2_target_groups, status,
                        created_at, intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """,
                    (
                        task_id, name, account, message_link, ','.join(target_groups),
                        part2_message_link, ','.join(part2_target_groups), 'pending',
                        datetime.now().isoformat(), intervalMin, intervalMax, afterEachSecond,
                        randomSleepTimeMin, randomSleepTimeMax, customReplyMessage
                    )
                )
                
                # Insert target groups
                for group in target_groups:
                    cursor.execute(
                        "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                # Insert part 2 target groups if available
                for group in part2_target_groups:
                    cursor.execute(
                        "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                        (task_id, group, 0)
                    )
                
                conn.commit()
                
            self.logger.info(f"Created forwarder task: {name} for account {account}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating forwarder task: {str(e)}")
            return False
    
    def update_task(self, task_id, task_data):
        """Update an existing task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # First, update the task details
                update_fields = []
                update_values = []
                
                for field, value in task_data.items():
                    if field in ['target_groups', 'part2_target_groups'] and isinstance(value, list):
                        update_fields.append(f"{field} = ?")
                        update_values.append(','.join(value))
                    elif field not in ['id', 'task_id']:
                        update_fields.append(f"{field} = ?")
                        update_values.append(value)
                
                if update_fields:
                    cursor.execute(
                        f"UPDATE forwarder_tasks SET {', '.join(update_fields)} WHERE id = ?",
                        update_values + [task_id]
                    )
                
                # If target groups are being updated, handle them separately
                if 'target_groups' in task_data and isinstance(task_data['target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['target_groups']:
                        cursor.execute(
                            "INSERT INTO task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                # Same for part 2 target groups
                if 'part2_target_groups' in task_data and isinstance(task_data['part2_target_groups'], list):
                    # Clear existing groups
                    cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                    
                    # Insert new groups
                    for group in task_data['part2_target_groups']:
                        cursor.execute(
                            "INSERT INTO part2_task_groups (task_id, group_link, processed) VALUES (?, ?, ?)",
                            (task_id, group, 0)
                        )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder task: {str(e)}")
            return False
    
    def delete_task(self, task_id):
        """Delete a forwarding task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                # Delete from main tasks table
                cursor.execute("DELETE FROM forwarder_tasks WHERE id = ?", (task_id,))
                
                # Delete associated groups
                cursor.execute("DELETE FROM task_groups WHERE task_id = ?", (task_id,))
                cursor.execute("DELETE FROM part2_task_groups WHERE task_id = ?", (task_id,))
                
                conn.commit()
                
            self.logger.info(f"Deleted forwarder task: {task_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting forwarder task: {str(e)}")
            return False
    
    def get_all_tasks(self):
        """Get all forwarding tasks."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks ORDER BY created_at DESC")
                tasks = cursor.fetchall()
                
                # For each task, get its target groups
                for task in tasks:
                    # Get regular target groups
                    cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task['id'],))
                    task['groups'] = cursor.fetchall()
                    
                    # Get part 2 target groups
                    cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task['id'],))
                    task['part2_groups'] = cursor.fetchall()
                
                return tasks
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder tasks: {str(e)}")
            return []
    
    def get_task(self, task_id):
        """Get a specific task by ID."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM forwarder_tasks WHERE id = ?", (task_id,))
                task = cursor.fetchone()
                
                if not task:
                    return None
                
                # Get target groups
                cursor.execute("SELECT * FROM task_groups WHERE task_id = ?", (task_id,))
                task['groups'] = cursor.fetchall()
                
                # Get part 2 target groups
                cursor.execute("SELECT * FROM part2_task_groups WHERE task_id = ?", (task_id,))
                task['part2_groups'] = cursor.fetchall()
                
                return task
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder task: {str(e)}")
            return None
    
    def update_task_status(self, task_id, status, current_index=None, error_message=None):
        """Update the status of a task."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                update_values = [status, datetime.now().isoformat()]
                update_sql = "UPDATE forwarder_tasks SET status = ?, last_processed_time = ?"
                
                if current_index is not None:
                    update_sql += ", current_index = ?"
                    update_values.append(current_index)
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE id = ?"
                update_values.append(task_id)
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            self.logger.info(f"Updated task {task_id} status to {status}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating task status: {str(e)}")
            return False
    
    def update_group_status(self, task_id, group_link, processed, error_message=None, is_part2=False):
        """Update the processed status for a group."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                table = "part2_task_groups" if is_part2 else "task_groups"
                
                update_values = [processed]
                update_sql = f"UPDATE {table} SET processed = ?"
                
                if error_message is not None:
                    update_sql += ", error_message = ?"
                    update_values.append(error_message)
                
                update_sql += " WHERE task_id = ? AND group_link = ?"
                update_values.extend([task_id, group_link])
                
                cursor.execute(update_sql, update_values)
                conn.commit()
                
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating group status: {str(e)}")
            return False
    
    def get_settings(self):
        """Get all forwarder settings."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute("SELECT key, value FROM forwarder_settings")
                settings = {row['key']: row['value'] for row in cursor.fetchall()}
                
                return settings
                
        except Exception as e:
            self.logger.error(f"Error getting forwarder settings: {str(e)}")
            return {}
    
    def update_setting(self, key, value):
        """Update a specific setting."""
        try:
            with self._get_db_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute(
                    "INSERT OR REPLACE INTO forwarder_settings (key, value) VALUES (?, ?)",
                    (key, value)
                )
                
                conn.commit()
                
            self.logger.info(f"Updated forwarder setting: {key} = {value}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating forwarder setting: {str(e)}")
            return False

class TelegramLoginWorker(QThread):
    login_success = pyqtSignal(dict)
    login_2fa_required = pyqtSignal()
    login_error = pyqtSignal(str)

    def __init__(self, api_id, api_hash, phone, code=None, password=None, phone_code_hash=None, session_file=None, logger=None):
        super().__init__()
        self.api_id = api_id
        self.api_hash = api_hash
        self.phone = phone
        self.code = code
        self.password = password
        self.phone_code_hash = phone_code_hash
        self.session_file = session_file or f'sessions/{phone}'
        self.logger = logger
        self.client = None

    def run(self):
        import asyncio
        asyncio.run(self._login_flow())

    async def _login_flow(self):
        from telethon import TelegramClient
        try:
            self.client = TelegramClient(self.session_file, self.api_id, self.api_hash)
            
            # Connect with timeout
            try:
                await asyncio.wait_for(self.client.connect(), timeout=10)
                if self.logger:
                    self.logger.info(f"Connected to Telegram for {self.phone}")
            except asyncio.TimeoutError:
                if self.logger:
                    self.logger.error(f"Connection timeout for {self.phone}")
                self.login_error.emit("Connection to Telegram timed out. Please try again.")
                return
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Connection error for {self.phone}: {str(e)}")
                self.login_error.emit(f"Connection error: {str(e)}")
                return
                
            if not await self.client.is_user_authorized():
                if self.logger:
                    self.logger.info(f"User not authorized, proceeding to login flow for {self.phone}")
        
                try:
                    if self.password:
                        # 2FA flow
                        if self.logger:
                            self.logger.info(f"Attempting 2FA login for {self.phone}")
                        await asyncio.wait_for(self.client.sign_in(password=self.password), timeout=15)
                        
                        # Get user info after successful 2FA login
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"2FA login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    else:
                        # Code flow
                        if self.logger:
                            self.logger.info(f"Attempting code login for {self.phone} with code {self.code}")
                        await asyncio.wait_for(
                            self.client.sign_in(phone=self.phone, code=self.code, phone_code_hash=self.phone_code_hash),
                            timeout=15
                        )
                        
                        # Get user info
                        me = await self.client.get_me()
                        if self.logger:
                            self.logger.info(f"Login successful for {self.phone}")
                        self.login_success.emit({'user': me})
                    
                except asyncio.TimeoutError:
                    if self.logger:
                        self.logger.error(f"Login timeout for {self.phone}")
                    self.login_error.emit("Login verification timed out. Please try again.")
                except SessionPasswordNeededError:
                    if self.logger:
                        self.logger.info(f"2FA required for {self.phone}")
                    self.login_2fa_required.emit()
                except PhoneCodeInvalidError:
                    if self.logger:
                        self.logger.error(f"Invalid code for {self.phone}")
                    self.login_error.emit("Invalid or expired code.")
                except Exception as e:
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        if self.logger:
                            self.logger.info(f"2FA detected from login error for {self.phone}")
                        self.login_2fa_required.emit()
                    elif any(keyword in error_msg for keyword in ["invalid", "code", "expired", "wrong"]):
                        if self.logger:
                            self.logger.error(f"Invalid verification code for {self.phone}")
                        self.login_error.emit("Invalid or expired verification code.")
                    else:
                        if self.logger:
                            self.logger.error(f"Login error for {self.phone}: {str(e)}")
                        self.login_error.emit(str(e))
            else:
                # Already authorized
                me = await self.client.get_me()
                if self.logger:
                    self.logger.info(f"Already authorized for {self.phone}")
                self.login_success.emit({'user': me})
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Connection error for {self.phone}: {str(e)}")
            self.login_error.emit(str(e))
        finally:
            # Proper cleanup
            if self.client:
                try:
                    await self.client.disconnect()
                except:
                    pass

class TaskWidget(QWidget):
    """Widget for displaying and managing a forwarding task."""
    
    def __init__(self, task, parent=None):
        super().__init__(parent)
        self.task = task
        self.setup_ui()
        
    def setup_ui(self):
        """Set up the UI for the task widget."""
        layout = QVBoxLayout(self)
        
        # Task header
        header_layout = QHBoxLayout()
        
        # Task name and status
        task_info_layout = QVBoxLayout()
        self.name_label = QLabel(f"<b>{self.task.get('name', 'Unnamed task')}</b>")
        
        status_text = self.task.get('status', 'pending').upper()
        status_color = self.get_status_color(status_text)
        self.status_label = QLabel(f"Status: <font color='{status_color}'>{status_text}</font>")
        
        task_info_layout.addWidget(self.name_label)
        task_info_layout.addWidget(self.status_label)
        
        # Task buttons
        buttons_layout = QHBoxLayout()
        
        self.start_button = QPushButton("▶️ Start")
        self.pause_button = QPushButton("⏸️ Pause")
        self.delete_button = QPushButton("🗑️ Delete")
        
        self.start_button.setFixedWidth(80)
        self.pause_button.setFixedWidth(80)
        self.delete_button.setFixedWidth(80)
        
        buttons_layout.addWidget(self.start_button)
        buttons_layout.addWidget(self.pause_button)
        buttons_layout.addWidget(self.delete_button)
        buttons_layout.addStretch()
        
        # Add to header layout
        header_layout.addLayout(task_info_layout)
        header_layout.addStretch()
        header_layout.addLayout(buttons_layout)
        
        # Task details
        details_layout = QFormLayout()
        
        account = self.task.get('account', 'N/A')
        message_link = self.task.get('message_link', 'N/A')
        
        self.account_label = QLabel(account)
        self.message_link_label = QLabel(f"<a href='{message_link}'>{message_link}</a>")
        self.message_link_label.setOpenExternalLinks(True)
        
        # Progress information
        current_index = self.task.get('current_index', 0)
        target_groups = self.task.get('target_groups', '').split(',') if self.task.get('target_groups') else []
        
        if not target_groups and 'groups' in self.task:
            target_groups = [group['group_link'] for group in self.task['groups']]
        
        total_groups = len(target_groups)
        
        self.progress_label = QLabel(f"{current_index}/{total_groups} groups processed")
        
        # Add details to layout
        details_layout.addRow("Account:", self.account_label)
        details_layout.addRow("Message:", self.message_link_label)
        details_layout.addRow("Progress:", self.progress_label)
        
        # Error message if any
        error_message = self.task.get('error_message')
        if error_message:
            self.error_label = QLabel(f"Error: {error_message}")
            self.error_label.setStyleSheet("color: red;")
            details_layout.addRow("", self.error_label)
        
        # Last processed time
        last_processed = self.task.get('last_processed_time')
        if last_processed:
            try:
                # Parse ISO datetime and format it nicely
                dt = datetime.fromisoformat(last_processed)
                formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
                self.time_label = QLabel(formatted_time)
                details_layout.addRow("Last activity:", self.time_label)
            except Exception as e:
                # Skip if datetime parsing fails
                pass
        
        # Add layouts to main layout
        layout.addLayout(header_layout)
        layout.addLayout(details_layout)
        
        # Add separator line
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        layout.addWidget(line)
    
    def get_status_color(self, status):
        """Get color for task status."""
        status = status.lower()
        if status == 'completed':
            return 'green'
        elif status == 'running':
            return 'blue'
        elif status == 'error':
            return 'red'
        elif status == 'paused':
            return 'orange'
        elif status == 'stopped':
            return 'gray'
        else:
            return 'black'
    
    def update_task(self, task):
        """Update the task widget with new task data."""
        self.task = task
        
        # Update name and status
        self.name_label.setText(f"<b>{self.task.get('name', 'Unnamed task')}</b>")
        
        status_text = self.task.get('status', 'pending').upper()
        status_color = self.get_status_color(status_text)
        self.status_label.setText(f"Status: <font color='{status_color}'>{status_text}</font>")
        
        # Update progress
        current_index = self.task.get('current_index', 0)
        target_groups = self.task.get('target_groups', '').split(',') if self.task.get('target_groups') else []
        
        if not target_groups and 'groups' in self.task:
            target_groups = [group['group_link'] for group in self.task['groups']]
        
        total_groups = len(target_groups)
        
        self.progress_label.setText(f"{current_index}/{total_groups} groups processed")
        
        # Update error message if any
        error_message = self.task.get('error_message')
        if hasattr(self, 'error_label'):
            if error_message:
                self.error_label.setText(f"Error: {error_message}")
                self.error_label.setVisible(True)
            else:
                self.error_label.setVisible(False)
        elif error_message:
            self.error_label = QLabel(f"Error: {error_message}")
            self.error_label.setStyleSheet("color: red;")
            # Need to add to layout


class TGCheckerApp(QMainWindow):
    """Main application window for the TG Checker tool."""
    
    # Qt signals for thread-safe UI updates
    update_ui_signal = pyqtSignal()
    update_status_signal = pyqtSignal(str)
    update_analyzing_signal = pyqtSignal(str)
    update_result_counts_signal = pyqtSignal(int, int, int, int, int, int)  # valid_filtered, valid_only, topics, channels, invalid, account_issues
    update_progress_signal = pyqtSignal(int, int)  # current, total
    log_activity_signal = pyqtSignal(str)
    
    # Forwarder signals
    forwarder_task_update_signal = pyqtSignal(dict)  # task
    forwarder_progress_signal = pyqtSignal(str, str, int, int, str, str)  # task_id, group, current, total, status, error
    
    def __init__(self):
        super().__init__()
        
        # Initialize settings
        self.settings = QSettings("TG PY", "TGChecker")
        
        # Set up logging
        self.logger = setup_logger()
        
        # Initialize components
        self.account_manager = AccountManager(self.logger)
        
        # Clean database to fix locking issues
        self.logger.info("Cleaning database to fix potential locking issues...")
        self.account_manager.clean_database()
        
        # Initialize monitor
        self.monitor = Monitor(self.account_manager, self.logger)
        
        # Initialize forwarder manager
        self.forwarder_manager = ForwarderManager(self.account_manager, self.logger)
        
        # Initialize checker state tracking
        self.is_checker_running = False
        self.is_task_checker_running = False
        self.checker_should_stop = False
        self.task_checker_should_stop = False
        
        # Initialize forwarder state tracking
        self.task_widgets = {}  # task_id -> TaskWidget
        
        # Progress tracking for crash recovery
        self.current_group_index = 0
        self.total_groups = 0
        self.progress_file = "last_checked.txt"
        self.current_group_links = []
        
        # Enhanced FloodWait handling
        self.account_states = {}  # Track account availability states
        self.pending_groups_queue = []  # Queue for groups that need reassignment
        self.retry_scheduled_groups = []  # Groups scheduled for retry after wait
        
        # Thread-safe result collections for multi-account task checker
        self.valid_filtered = []
        self.valid_only = []
        self.topics_groups = []
        self.channels_only = []
        self.invalid_groups = []
        self.account_issues = []
        self.join_requests = []
        self.results_lock = threading.RLock()
        self.account_states_lock = threading.RLock()
        
        # Global progress tracking for multi-account checker
        self.global_groups_processed = 0
        self.global_progress_lock = threading.RLock()
        
        # Connect signals to their handlers
        self.update_ui_signal.connect(self.update_ui)
        self.update_status_signal.connect(self._update_status_label)
        self.update_analyzing_signal.connect(self._update_analyzing_label)
        self.update_result_counts_signal.connect(self._update_result_counts)
        self.update_progress_signal.connect(self._update_progress)
        self.log_activity_signal.connect(self._log_activity_to_ui)
        
        # Connect forwarder signals
        self.forwarder_task_update_signal.connect(self._update_forwarder_task)
        self.forwarder_progress_signal.connect(self._update_forwarder_progress)
        
        # Register task completion callback
        self.forwarder_manager.add_task_completion_callback(self._on_task_completed)
        
        # Set up the UI
        self.setup_ui()
        
        # Load and apply settings (including theme)
        self.load_settings()
        
        # Start background processes
        self.start_background_processes()
    

    def stop_checker(self):
        """Stop the checker process."""
        if hasattr(self, 'worker') and self.worker is not None:
            self.worker.stop()
            self.worker = None
        if hasattr(self, 'log_display'):
            self.log_display.append("Checker stopped.")
        if hasattr(self, 'status_label'):
            self.status_label.setText("Status: Stopped")
        if hasattr(self, 'start_checker_button'):
            self.start_checker_button.setEnabled(True)
        if hasattr(self, 'stop_checker_button'):
            self.stop_checker_button.setEnabled(False)
    
    def remove_account(self):
        """Remove selected account from the database."""
        selected_items = self.account_table.selectedItems()
        if not selected_items:
            return
            
        row = selected_items[0].row()
        account_id = self.account_table.item(row, 0).text()
        
        try:
            import sqlite3
            conn = sqlite3.connect(self._get_db_path())
            cursor = conn.cursor()
            cursor.execute("DELETE FROM accounts WHERE id = ?", (account_id,))
            conn.commit()
            conn.close()
            
            self.account_table.removeRow(row)
            if hasattr(self, 'log_display'):
                self.log_display.append(f"Account {account_id} removed successfully.")
        except Exception as e:
            if hasattr(self, 'log_display'):
                self.log_display.append(f"Error removing account: {str(e)}")

    def update_status(self, status):
        """Update the status label."""
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"Status: {status}")

    def _update_status_label(self, text):
        """Thread-safe status label update."""
        self.status_label.setText(text)
    
    def _update_analyzing_label(self, text):
        """Thread-safe analyzing label update."""
        self.currently_analyzing_label.setText(text)
    
    def _update_result_counts(self, valid_filtered, valid_only, topics, channels, invalid, account_issues):
        """Thread-safe result counts update."""
        self.valid_filtered_count.setText(str(valid_filtered))
        self.valid_only_count.setText(str(valid_only))
        self.topics_count.setText(str(topics))
        self.channels_count.setText(str(channels))
        self.invalid_count.setText(str(invalid))
        self.account_issues_count.setText(str(account_issues))
    
    def _update_progress(self, current, total):
        """Thread-safe progress update with console logging."""
        try:
            self.progress_count.setText(f"{current} / {total}")
            # Also log to console for monitoring
            if total > 0:
                percentage = (current / total) * 100
                self.logger.info(f"[PROGRESS] Groups Checked: {current} / {total} ({percentage:.1f}%)")
        except Exception as e:
            self.logger.error(f"Error updating progress: {str(e)}")
    
    def _log_activity_to_ui(self, message):
        """Thread-safe activity logging."""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.activities_text.append(f"[{timestamp}] {message}")
            
            # Also log to the logger
            self.logger.info(message)
            
        except Exception as e:
            print(f"Failed to log activity: {str(e)}")
    
    def setup_ui(self):
        """Set up the user interface."""
        self.setWindowTitle("TG Checker")
        self.setGeometry(100, 100, 1200, 800)
        
        # Create main widget and layout
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        
        # Create tab widget
        self.tabs = QTabWidget()
        
        # Create tabs
        self.dashboard_tab = self.create_dashboard_tab()
        self.accounts_tab = self.create_accounts_tab()
        self.monitor_tab = self.create_monitor_tab()
        self.logs_tab = self.create_logs_tab()
        self.settings_tab = self.create_settings_tab()
        self.forwarder_tab = self.create_forwarder_tab()
        
        # Add tabs to tab widget
        self.tabs.addTab(self.dashboard_tab, "Dashboard")
        self.tabs.addTab(self.accounts_tab, "Accounts")
        self.tabs.addTab(self.monitor_tab, "Monitor")
        self.tabs.addTab(self.forwarder_tab, "Forwarder")
        self.tabs.addTab(self.logs_tab, "Logs")
        self.tabs.addTab(self.settings_tab, "Settings")
        
        # Add tab widget to main layout
        main_layout.addWidget(self.tabs)
        
        # Create status bar
        self.status_label = QLabel("Ready")
        self.status_bar = self.statusBar()
        self.status_bar.addPermanentWidget(self.status_label)
        
        # Set the main widget
        self.setCentralWidget(main_widget)
        
    def create_dashboard_tab(self):
        """Create the dashboard tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add status overview
        status_group = QGroupBox("System Status")
        status_layout = QFormLayout()
        
        self.active_accounts_label = QLabel("0")
        self.total_accounts_label = QLabel("0")
        self.monitor_status_label = QLabel("Stopped")
        self.last_sync_label = QLabel("Never")
        
        status_layout.addRow("Active Accounts:", self.active_accounts_label)
        status_layout.addRow("Total Accounts:", self.total_accounts_label)
        status_layout.addRow("Monitor Status:", self.monitor_status_label)
        status_layout.addRow("Last Sync:", self.last_sync_label)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        # Add quick actions
        actions_group = QGroupBox("Quick Actions")
        actions_layout = QHBoxLayout()
        
        self.sync_button = QPushButton("Sync Now")
        self.toggle_monitor_button = QPushButton("Start Monitor")
        self.sync_and_monitor_button = QPushButton("Sync & Monitor")
        self.start_checker_button = QPushButton("Start Checker")
        self.stop_checker_button = QPushButton("Stop Checker")
        self.start_task_checker_button = QPushButton("Task Checker")
        self.stop_task_checker_button = QPushButton("Stop Task Checker")
        self.theme_toggle_button = QPushButton("🌙 Dark Theme")
        
        self.sync_button.clicked.connect(self.sync_accounts)
        self.toggle_monitor_button.clicked.connect(self.toggle_monitor)
        self.sync_and_monitor_button.clicked.connect(self.sync_and_monitor)
        self.start_checker_button.clicked.connect(self.start_checker)
        self.stop_checker_button.clicked.connect(self.stop_checker)
        self.start_task_checker_button.clicked.connect(self.start_task_checker)
        self.stop_task_checker_button.clicked.connect(self.stop_task_checker)
        self.theme_toggle_button.clicked.connect(self.toggle_theme)
        
        # Initially disable stop buttons
        self.stop_checker_button.setEnabled(False)
        self.stop_task_checker_button.setEnabled(False)
        
        actions_layout.addWidget(self.sync_button)
        actions_layout.addWidget(self.toggle_monitor_button)
        actions_layout.addWidget(self.sync_and_monitor_button)
        actions_layout.addWidget(self.start_checker_button)
        actions_layout.addWidget(self.stop_checker_button)
        actions_layout.addWidget(self.start_task_checker_button)
        actions_layout.addWidget(self.stop_task_checker_button)
        actions_layout.addWidget(self.theme_toggle_button)
        
        # Add Export Pending Groups button in a separate layout
        export_layout = QHBoxLayout()
        self.export_pending_button = QPushButton("📋 Export Pending Groups")
        self.export_pending_button.setStyleSheet("background-color: #ff9c00; color: white; font-weight: bold;")
        self.export_pending_button.clicked.connect(self.export_pending_groups)
        self.export_pending_button.setMinimumHeight(40)  # Make button taller
        export_layout.addWidget(self.export_pending_button)
        
        # Add button to view current pending groups
        self.view_pending_button = QPushButton("👁️ View Pending Groups")
        self.view_pending_button.setStyleSheet("background-color: #0078d4; color: white;")
        self.view_pending_button.clicked.connect(self.view_pending_groups)
        self.view_pending_button.setMinimumHeight(40)  # Make button taller
        export_layout.addWidget(self.view_pending_button)
        
        actions_group.setLayout(actions_layout)
        layout.addWidget(actions_group)
        layout.addLayout(export_layout)  # Add the export button layout
        
        # Add groups input section
        groups_group = QGroupBox("Add Groups")
        groups_layout = QVBoxLayout()
        
        self.groups_input = QTextEdit()
        self.groups_input.setPlaceholderText("Enter Telegram group/channel links (one per line)")
        self.currently_analyzing_label = QLabel("Currently analyzing: None")
        
        groups_layout.addWidget(self.groups_input)
        groups_layout.addWidget(self.currently_analyzing_label)
        
        groups_group.setLayout(groups_layout)
        layout.addWidget(groups_group)
        
        # Add results section
        results_group = QGroupBox("Results")
        results_layout = QFormLayout()
        
        self.valid_filtered_count = QLabel("0")
        self.valid_only_count = QLabel("0")
        self.topics_count = QLabel("0")
        self.channels_count = QLabel("0")
        self.invalid_count = QLabel("0")
        self.account_issues_count = QLabel("0")
        self.progress_count = QLabel("0 / 0")
        
        results_layout.addRow("Groups Checked:", self.progress_count)
        results_layout.addRow("Groups Valid & Filter ON:", self.valid_filtered_count)
        results_layout.addRow("Groups Valid Only:", self.valid_only_count)
        results_layout.addRow("Topics Groups Only Valid:", self.topics_count)
        results_layout.addRow("Channels Only Valid:", self.channels_count)
        results_layout.addRow("Invalid Groups/Channels:", self.invalid_count)
        results_layout.addRow("Account Issues:", self.account_issues_count)
        
        results_group.setLayout(results_layout)
        layout.addWidget(results_group)
        
        # Add recent activities log
        activities_group = QGroupBox("Recent Activities")
        activities_layout = QVBoxLayout()
        
        self.activities_text = QTextEdit()
        self.activities_text.setReadOnly(True)
        
        activities_layout.addWidget(self.activities_text)
        activities_group.setLayout(activities_layout)
        layout.addWidget(activities_group)
        
        return tab
    
    def create_accounts_tab(self):
        """Create the accounts tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add account table
        self.accounts_table = QTableWidget()
        self.accounts_table.setColumnCount(7)
        self.accounts_table.setHorizontalHeaderLabels(["Phone", "Name / Username", "Enabled", "Last Check", "Errors", "Status", "Actions"])
        
        # Set specific column widths
        self.accounts_table.horizontalHeader().setSectionResizeMode(6, QHeaderView.Fixed)  # Actions column
        self.accounts_table.setColumnWidth(6, 320)  # Set Actions column width to fit all buttons
        
        # Set other columns to stretch
        for i in range(6):
            self.accounts_table.horizontalHeader().setSectionResizeMode(i, QHeaderView.Stretch)
        
        layout.addWidget(self.accounts_table)
        
        # Add account actions
        actions_layout = QHBoxLayout()
        
        self.add_account_button = QPushButton("Add Account")
        self.remove_account_button = QPushButton("Remove Account")
        self.fix_account_button = QPushButton("Fix Selected")
        self.refresh_info_button = QPushButton("Refresh Account Info")
        self.check_ages_button = QPushButton("Check Ages")
        self.activate_all_button = QPushButton("Activate All")
        self.deactivate_all_button = QPushButton("Deactivate All")
        
        self.add_account_button.clicked.connect(self.add_account)
        self.remove_account_button.clicked.connect(self.remove_account)
        self.fix_account_button.clicked.connect(self.fix_account)
        self.refresh_info_button.clicked.connect(self.refresh_all_accounts_info)
        self.activate_all_button.clicked.connect(self.activate_all_accounts)
        self.deactivate_all_button.clicked.connect(self.deactivate_all_accounts)
        
        actions_layout.addWidget(self.add_account_button)
        actions_layout.addWidget(self.remove_account_button)
        actions_layout.addWidget(self.fix_account_button)
        actions_layout.addWidget(self.refresh_info_button)
        actions_layout.addWidget(self.activate_all_button)
        actions_layout.addWidget(self.deactivate_all_button)
        layout.addLayout(actions_layout)
        
        return tab
    
    
    def save_account(self):
        """Save new account to database."""
        if not hasattr(self, 'phone_input') or not hasattr(self, 'api_id_input') or not hasattr(self, 'api_hash_input'):
            return
            
        phone = self.phone_input.text().strip()
        api_id = self.api_id_input.text().strip()
        api_hash = self.api_hash_input.text().strip()
        
        if not phone or not api_id or not api_hash:
            if hasattr(self, 'log_display'):
                self.log_display.append("All fields are required.")
            return
            
        if hasattr(self, '_get_db_path'):
            db_path = self._get_db_path()
        else:
            db_path = "tg_checker.db"
            
        try:
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Create table if it doesn't exist
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS accounts (
                    id INTEGER PRIMARY KEY,
                    phone TEXT,
                    api_id TEXT,
                    api_hash TEXT,
                    status TEXT DEFAULT 'active'
                )
            ''')
            
            # Check if phone already exists
            cursor.execute("SELECT id FROM accounts WHERE phone = ?", (phone,))
            if cursor.fetchone():
                if hasattr(self, 'log_display'):
                    self.log_display.append(f"Account with phone {phone} already exists.")
                conn.close()
                if hasattr(self, 'add_account_dialog'):
                    self.add_account_dialog.close()
                return
                
            # Insert new account
            cursor.execute("INSERT INTO accounts (phone, api_id, api_hash) VALUES (?, ?, ?)",
                          (phone, api_id, api_hash))
            conn.commit()
            
            # Get the new account ID
            account_id = cursor.lastrowid
            conn.close()
            
            # Add to the table
            row_position = self.account_table.rowCount()
            self.account_table.insertRow(row_position)
            self.account_table.setItem(row_position, 0, QTableWidgetItem(str(account_id)))
            self.account_table.setItem(row_position, 1, QTableWidgetItem(phone))
            self.account_table.setItem(row_position, 2, QTableWidgetItem(api_id))
            self.account_table.setItem(row_position, 3, QTableWidgetItem(api_hash))
            self.account_table.setItem(row_position, 4, QTableWidgetItem("active"))
            
            if hasattr(self, 'log_display'):
                self.log_display.append(f"Account {phone} added successfully.")
                
            if hasattr(self, 'add_account_dialog'):
                self.add_account_dialog.close()
        except Exception as e:
            if hasattr(self, 'log_display'):
                self.log_display.append(f"Error saving account: {str(e)}")
    def create_monitor_tab(self):
        """Create the monitor tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add monitor settings
        settings_group = QGroupBox("Monitor Settings")
        settings_layout = QFormLayout()
        
        self.check_interval_spin = QSpinBox()
        self.check_interval_spin.setRange(1, 60)
        self.check_interval_spin.setValue(5)
        self.check_interval_spin.setSuffix(" minutes")
        
        self.auto_fix_check = QCheckBox()
        self.auto_fix_check.setChecked(True)
        
        settings_layout.addRow("Check Interval:", self.check_interval_spin)
        settings_layout.addRow("Auto-Fix Issues:", self.auto_fix_check)
        
        settings_group.setLayout(settings_layout)
        layout.addWidget(settings_group)
        
        # Add monitor status
        status_group = QGroupBox("Monitor Status")
        status_layout = QVBoxLayout()
        
        self.monitor_log = QTextEdit()
        self.monitor_log.setReadOnly(True)
        
        status_layout.addWidget(self.monitor_log)
        
        # Add apply settings button
        apply_button = QPushButton("Apply Monitor Settings")
        apply_button.clicked.connect(self.apply_monitor_settings)
        status_layout.addWidget(apply_button)
        
        status_group.setLayout(status_layout)
        layout.addWidget(status_group)
        
        return tab
    
    def create_logs_tab(self):
        """Create the logs tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add log filter controls
        filter_layout = QHBoxLayout()
        
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["All", "Info", "Warning", "Error", "Critical"])
        
        self.log_type_combo = QComboBox()
        self.log_type_combo.addItems(["General", "Authentication", "Usage Checker"])
        self.log_type_combo.currentIndexChanged.connect(self.update_log_display)
        
        self.export_logs_button = QPushButton("Export Logs")
        self.export_logs_button.clicked.connect(self.export_logs)
        
        filter_layout.addWidget(QLabel("Log Type:"))
        filter_layout.addWidget(self.log_type_combo)
        filter_layout.addWidget(QLabel("Filter Level:"))
        filter_layout.addWidget(self.log_level_combo)
        filter_layout.addStretch()
        filter_layout.addWidget(self.export_logs_button)
        
        layout.addLayout(filter_layout)
        
        # Add log display
        self.log_display = QTextEdit()
        self.log_display.setReadOnly(True)
        layout.addWidget(self.log_display)
        
        # Set up a timer to refresh logs
        self.log_refresh_timer = QTimer(self)
        self.log_refresh_timer.timeout.connect(self.update_log_display)
        self.log_refresh_timer.start(2000)  # Refresh every 2 seconds
        
        return tab
    
    def create_settings_tab(self):
        """Create the settings tab."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # General settings
        general_group = QGroupBox("General Settings")
        general_layout = QFormLayout()
        
        self.auto_start_check = QCheckBox()
        self.dark_mode_check = QCheckBox()
        self.dark_mode_check.stateChanged.connect(self.on_theme_changed)
        
        general_layout.addRow("Auto-Start Monitor:", self.auto_start_check)
        general_layout.addRow("Dark Mode:", self.dark_mode_check)
        
        general_group.setLayout(general_layout)
        layout.addWidget(general_group)
        
        
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(1)
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(1)
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
        
# Filter settings
        filter_group = QGroupBox("Filter Settings")
        filter_layout = QFormLayout()
        
        self.min_members_spin = QSpinBox()
        self.min_members_spin.setRange(0, 1000000)
        self.min_members_spin.setValue(500)
        self.min_members_spin.setSingleStep(100)
        
        self.min_message_time_spin = QSpinBox()
        self.min_message_time_spin.setRange(0, 168)  # Up to 1 week (168 hours)
        self.min_message_time_spin.setValue(1)
        self.min_message_time_spin.setSuffix(" hours")
        
        self.min_total_messages_spin = QSpinBox()
        self.min_total_messages_spin.setRange(0, 100000)
        self.min_total_messages_spin.setValue(100)
        self.min_total_messages_spin.setSingleStep(10)
        
        filter_layout.addRow("Min Members:", self.min_members_spin)
        filter_layout.addRow("Min Chat Message Time:", self.min_message_time_spin)
        filter_layout.addRow("Min Total Messages:", self.min_total_messages_spin)
        
        filter_group.setLayout(filter_layout)
        layout.addWidget(filter_group)
        
        # Save settings button
        self.save_settings_button = QPushButton("Save Settings")
        self.save_settings_button.clicked.connect(self.save_settings)
        layout.addWidget(self.save_settings_button)
        
        return tab
    
    
    def create_forwarder_tab(self):
        """Create the forwarder tab for managing message forwarding tasks."""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Add task management section
        task_group = QGroupBox("Message Forwarding")
        task_layout = QVBoxLayout(task_group)
        
        # Add buttons for task management
        button_layout = QHBoxLayout()
        
        self.new_task_button = QPushButton("➕ New Task")
        self.refresh_tasks_button = QPushButton("🔄 Refresh Tasks")
        self.settings_button = QPushButton("⚙️ Settings")
        
        self.new_task_button.clicked.connect(self.create_new_forwarding_task)
        self.refresh_tasks_button.clicked.connect(self.refresh_forwarding_tasks)
        self.settings_button.clicked.connect(self.show_forwarder_settings)
        
        button_layout.addWidget(self.new_task_button)
        button_layout.addWidget(self.refresh_tasks_button)
        button_layout.addWidget(self.settings_button)
        button_layout.addStretch()
        
        task_layout.addLayout(button_layout)
        
        # Add scroll area for tasks
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        
        self.tasks_container = QWidget()
        self.tasks_layout = QVBoxLayout(self.tasks_container)
        self.tasks_layout.setSpacing(10)
        self.tasks_layout.setContentsMargins(0, 0, 0, 0)
        self.tasks_layout.addStretch()
        
        scroll_area.setWidget(self.tasks_container)
        task_layout.addWidget(scroll_area)
        
        # Add empty state message
        self.empty_tasks_label = QLabel("No forwarding tasks found. Click 'New Task' to create one.")
        self.empty_tasks_label.setAlignment(Qt.AlignCenter)
        self.empty_tasks_label.setStyleSheet("color: gray; font-style: italic; padding: 20px;")
        self.tasks_layout.insertWidget(0, self.empty_tasks_label)
        
        layout.addWidget(task_group)
        
        # Add help section
        help_group = QGroupBox("Help")
        help_layout = QVBoxLayout(help_group)
        
        help_text = QLabel(
            "<b>Message Forwarding</b><br>"
            "This feature allows you to forward messages from one group/channel to multiple target groups.<br><br>"
            "<b>How to use:</b><br>"
            "1. Click 'New Task' to create a forwarding task<br>"
            "2. Select an account to use for forwarding<br>"
            "3. Paste the link to the message you want to forward<br>"
            "4. Add target groups to forward to<br>"
            "5. Configure timing settings to avoid rate limits<br>"
            "6. Start the task<br><br>"
            "<b>Note:</b> Forwarding to too many groups too quickly may trigger Telegram's anti-spam measures."
        )
        
        help_text.setWordWrap(True)
        help_layout.addWidget(help_text)
        
        layout.addWidget(help_group)
        
        # Load initial tasks
        self.refresh_forwarding_tasks()
        
        return tab
    
    def refresh_forwarding_tasks(self):
        """Refresh the list of forwarding tasks."""
        try:
            # Clear existing task widgets
            for widget in self.task_widgets.values():
                widget.setParent(None)
            self.task_widgets.clear()
            
            # Get all tasks from the manager
            tasks = self.forwarder_manager.get_all_tasks()
            
            # Show/hide empty state label
            self.empty_tasks_label.setVisible(len(tasks) == 0)
            
            # Add task widgets
            for task in tasks:
                self._add_task_widget(task)
            
            self.log_activity(f"Refreshed forwarding tasks: {len(tasks)} tasks found")
            
        except Exception as e:
            self.logger.error(f"Error refreshing forwarding tasks: {str(e)}")
            self.log_activity(f"Error refreshing forwarding tasks: {str(e)}")
    
    def _add_task_widget(self, task):
        """Add a new task widget to the tasks container."""
        # Hide empty state label if this is the first task
        if self.empty_tasks_label.isVisible():
            self.empty_tasks_label.setVisible(False)
        
        # Create task widget
        task_widget = TaskWidget(task)
        
        # Connect signals from task widget
        task_widget.start_button.clicked.connect(lambda: self.start_forwarding_task(task['id']))
        task_widget.pause_button.clicked.connect(lambda: self.pause_forwarding_task(task['id']))
        task_widget.delete_button.clicked.connect(lambda: self.delete_forwarding_task(task['id']))
        
        # Add to layout and store reference
        self.tasks_layout.insertWidget(0, task_widget)
        self.task_widgets[task['id']] = task_widget
    
    def _update_forwarder_task(self, task):
        """Update a forwarder task widget."""
        task_id = task.get('id')
        if not task_id:
            return
        
        if task_id in self.task_widgets:
            # Update existing widget
            self.task_widgets[task_id].update_task(task)
        else:
            # Create new widget
            self._add_task_widget(task)
    
    def _update_forwarder_progress(self, task_id, group_link, current, total, status, error=None):
        """Update progress for a forwarder task."""
        # Get the task from database to ensure we have latest data
        task = self.forwarder_manager.get_task(task_id)
        if task:
            # Update the task widget
            self._update_forwarder_task(task)
            
            # Log activity
            if status == "success":
                self.log_activity(f"✅ Forwarded to {group_link} ({current}/{total})")
            elif status == "error":
                self.log_activity(f"❌ Failed to forward to {group_link}: {error}")
            elif status == "processing":
                self.log_activity(f"⏳ Forwarding to {group_link}...")
    
    def _on_task_completed(self, task_id, success, error=None):
        """Handle task completion."""
        # Get the updated task from database
        task = self.forwarder_manager.get_task(task_id)
        if task:
            # Update the task widget
            self._update_forwarder_task(task)
            
            # Log activity
            if success:
                self.log_activity(f"✅ Task {task.get('name', task_id)} completed successfully")
            else:
                self.log_activity(f"❌ Task {task.get('name', task_id)} failed: {error}")
    
    def create_new_forwarding_task(self):
        """Show dialog to create a new forwarding task."""
        try:
            # Get active accounts
            accounts = self.account_manager.get_active_accounts()
            if not accounts:
                QMessageBox.warning(
                    self,
                    "No Active Accounts",
                    "You need at least one active account to create a forwarding task. Please activate an account first."
                )
                return
            
            # Create dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Create Forwarding Task")
            dialog.setMinimumWidth(600)
            dialog.setMinimumHeight(500)
            
            # Create layout
            layout = QVBoxLayout(dialog)
            
            # Task details form
            form_layout = QFormLayout()
            
            # Task name
            name_input = QLineEdit()
            name_input.setPlaceholderText("Enter a name for this task")
            form_layout.addRow("Task Name:", name_input)
            
            # Account selection
            account_combo = QComboBox()
            for account in accounts:
                account_combo.addItem(f"{account['phone']} ({account.get('username', '')})", account['phone'])
            form_layout.addRow("Account:", account_combo)
            
            # Message link
            message_link_input = QLineEdit()
            message_link_input.setPlaceholderText("Enter Telegram message link (e.g., https://t.me/username/123)")
            form_layout.addRow("Message Link:", message_link_input)
            
            # Target groups
            target_groups_input = QTextEdit()
            target_groups_input.setPlaceholderText("Enter target group/channel links (one per line)")
            target_groups_input.setMaximumHeight(100)
            form_layout.addRow("Target Groups:", target_groups_input)
            
            # Timing settings
            timing_group = QGroupBox("Timing Settings")
            timing_layout = QFormLayout(timing_group)
            
            interval_min_input = QSpinBox()
            interval_min_input.setRange(5, 60)
            interval_min_input.setValue(20)
            interval_min_input.setSuffix(" seconds")
            
            interval_max_input = QSpinBox()
            interval_max_input.setRange(5, 120)
            interval_max_input.setValue(25)
            interval_max_input.setSuffix(" seconds")
            
            # Connect interval min/max to ensure min <= max
            def update_interval_min(value):
                if interval_max_input.value() < value:
                    interval_max_input.setValue(value)
            
            def update_interval_max(value):
                if interval_min_input.value() > value:
                    interval_min_input.setValue(value)
            
            interval_min_input.valueChanged.connect(update_interval_min)
            interval_max_input.valueChanged.connect(update_interval_max)
            
            timing_layout.addRow("Interval Min:", interval_min_input)
            timing_layout.addRow("Interval Max:", interval_max_input)
            
            # Add advanced settings
            advanced_group = QGroupBox("Advanced Settings")
            advanced_layout = QFormLayout(advanced_group)
            
            # Custom reply message
            custom_reply_check = QCheckBox("Use custom reply message")
            custom_reply_message = QTextEdit()
            custom_reply_message.setPlaceholderText("Enter custom reply message")
            custom_reply_message.setMaximumHeight(80)
            custom_reply_message.setEnabled(False)
            
            # Connect checkbox to enable/disable reply message
            custom_reply_check.stateChanged.connect(lambda state: custom_reply_message.setEnabled(state == Qt.Checked))
            
            advanced_layout.addRow("", custom_reply_check)
            advanced_layout.addRow("Reply Message:", custom_reply_message)
            
            # Add layouts to main layout
            layout.addLayout(form_layout)
            layout.addWidget(timing_group)
            layout.addWidget(advanced_group)
            
            # Add buttons
            button_layout = QHBoxLayout()
            create_button = QPushButton("Create Task")
            cancel_button = QPushButton("Cancel")
            
            button_layout.addStretch()
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(create_button)
            
            layout.addLayout(button_layout)
            
            # Connect buttons
            cancel_button.clicked.connect(dialog.reject)
            
            # Create task function
            def create_task():
                # Validate inputs
                name = name_input.text().strip()
                if not name:
                    QMessageBox.warning(dialog, "Validation Error", "Please enter a task name")
                    return
                
                account = account_combo.currentData()
                if not account:
                    QMessageBox.warning(dialog, "Validation Error", "Please select an account")
                    return
                
                message_link = message_link_input.text().strip()
                if not message_link:
                    QMessageBox.warning(dialog, "Validation Error", "Please enter a message link")
                    return
                
                target_groups_text = target_groups_input.toPlainText().strip()
                if not target_groups_text:
                    QMessageBox.warning(dialog, "Validation Error", "Please enter at least one target group")
                    return
                
                # Parse target groups
                target_groups = [line.strip() for line in target_groups_text.split("\n") if line.strip()]
                
                # Create task data
                task_data = {
                    'id': str(uuid.uuid4()),
                    'name': name,
                    'account': account,
                    'message_link': message_link,
                    'target_groups': target_groups,
                    'intervalMin': interval_min_input.value(),
                    'intervalMax': interval_max_input.value()
                }
                
                # Add custom reply message if enabled
                if custom_reply_check.isChecked():
                    task_data['customReplyMessage'] = custom_reply_message.toPlainText().strip()
                
                try:
                    # Create the task
                    task_id = self.forwarder_manager.create_task(task_data)
                    if task_id:
                        self.log_activity(f"Created new forwarding task: {name}")
                        
                        # Refresh tasks
                        self.refresh_forwarding_tasks()
                        
                        # Close the dialog
                        dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "Error", "Failed to create task")
                        
                except Exception as e:
                    QMessageBox.critical(dialog, "Error", f"Error creating task: {str(e)}")
            
            create_button.clicked.connect(create_task)
            
            # Show the dialog
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"Error creating forwarding task: {str(e)}")
            self.log_activity(f"Error creating forwarding task: {str(e)}")
    
    def start_forwarding_task(self, task_id):
        """Start a forwarding task."""
        try:
            # Define callbacks
            def progress_callback(task_id, group_link, current, total, status, error=None):
                self.forwarder_progress_signal.emit(task_id, group_link, current, total, status, error)
            
            def error_callback(task_id, error_message, fatal=False):
                self.log_activity(f"Forwarder error: {error_message}")
                if fatal:
                    # Update task in UI
                    task = self.forwarder_manager.get_task(task_id)
                    if task:
                        self.forwarder_task_update_signal.emit(task)
            
            # Start the task
            success = self.forwarder_manager.start_task(task_id, progress_callback, error_callback)
            
            if success:
                self.log_activity(f"Started forwarding task: {task_id}")
                
                # Get the updated task
                task = self.forwarder_manager.get_task(task_id)
                if task:
                    self.forwarder_task_update_signal.emit(task)
            else:
                self.log_activity(f"Failed to start forwarding task: {task_id}")
            
        except Exception as e:
            self.logger.error(f"Error starting forwarding task: {str(e)}")
            self.log_activity(f"Error starting forwarding task: {str(e)}")
    
    def pause_forwarding_task(self, task_id):
        """Pause a forwarding task."""
        try:
            success = self.forwarder_manager.pause_task(task_id)
            
            if success:
                self.log_activity(f"Paused forwarding task: {task_id}")
                
                # Get the updated task
                task = self.forwarder_manager.get_task(task_id)
                if task:
                    self.forwarder_task_update_signal.emit(task)
            else:
                self.log_activity(f"Failed to pause forwarding task: {task_id}")
            
        except Exception as e:
            self.logger.error(f"Error pausing forwarding task: {str(e)}")
            self.log_activity(f"Error pausing forwarding task: {str(e)}")
    
    def delete_forwarding_task(self, task_id):
        """Delete a forwarding task."""
        try:
            # Ask for confirmation
            reply = QMessageBox.question(
                self,
                "Confirm Deletion",
                "Are you sure you want to delete this forwarding task?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # Delete the task
            success = self.forwarder_manager.delete_task(task_id)
            
            if success:
                self.log_activity(f"Deleted forwarding task: {task_id}")
                
                # Remove the task widget
                if task_id in self.task_widgets:
                    self.task_widgets[task_id].setParent(None)
                    del self.task_widgets[task_id]
                
                # Show empty state if no tasks left
                if not self.task_widgets:
                    self.empty_tasks_label.setVisible(True)
            else:
                self.log_activity(f"Failed to delete forwarding task: {task_id}")
            
        except Exception as e:
            self.logger.error(f"Error deleting forwarding task: {str(e)}")
            self.log_activity(f"Error deleting forwarding task: {str(e)}")
    
    def show_forwarder_settings(self):
        """Show dialog for forwarder settings."""
        try:
            # Get current settings
            settings = self.forwarder_manager.get_settings()
            
            # Create dialog
            dialog = QDialog(self)
            dialog.setWindowTitle("Forwarder Settings")
            dialog.setMinimumWidth(400)
            
            # Create layout
            layout = QVBoxLayout(dialog)
            
            # Settings form
            form_layout = QFormLayout()
            
            # Use random sleep time
            use_random_sleep = QCheckBox()
            use_random_sleep.setChecked(settings.get('useRandomSleepTime', 'true').lower() == 'true')
            form_layout.addRow("Use Random Sleep Time:", use_random_sleep)
            
            # Log failed groups
            log_failed_groups = QCheckBox()
            log_failed_groups.setChecked(settings.get('logFailedGroups', 'true').lower() == 'true')
            form_layout.addRow("Log Failed Groups:", log_failed_groups)
            
            # Handle flood wait
            handle_floodwait = QCheckBox()
            handle_floodwait.setChecked(settings.get('floodwait', 'true').lower() == 'true')
            form_layout.addRow("Handle FloodWait:", handle_floodwait)
            
            # Use reply message
            use_reply_message = QCheckBox()
            use_reply_message.setChecked(settings.get('replyMessage', 'true').lower() == 'true')
            form_layout.addRow("Use Reply Message:", use_reply_message)
            
            # Default reply message
            default_reply = QTextEdit()
            default_reply.setText(settings.get('message', ''))
            default_reply.setMaximumHeight(80)
            form_layout.addRow("Default Reply Message:", default_reply)
            
            # Connect use reply message to enable/disable default reply
            use_reply_message.stateChanged.connect(lambda state: default_reply.setEnabled(state == Qt.Checked))
            default_reply.setEnabled(use_reply_message.isChecked())
            
            layout.addLayout(form_layout)
            
            # Add buttons
            button_layout = QHBoxLayout()
            save_button = QPushButton("Save Settings")
            cancel_button = QPushButton("Cancel")
            
            button_layout.addStretch()
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(save_button)
            
            layout.addLayout(button_layout)
            
            # Connect buttons
            cancel_button.clicked.connect(dialog.reject)
            
            # Save settings function
            def save_settings():
                # Create settings dict
                new_settings = {
                    'useRandomSleepTime': 'true' if use_random_sleep.isChecked() else 'false',
                    'logFailedGroups': 'true' if log_failed_groups.isChecked() else 'false',
                    'floodwait': 'true' if handle_floodwait.isChecked() else 'false',
                    'replyMessage': 'true' if use_reply_message.isChecked() else 'false',
                    'message': default_reply.toPlainText().strip()
                }
                
                try:
                    # Update settings
                    success = self.forwarder_manager.update_settings(new_settings)
                    
                    if success:
                        self.log_activity("Updated forwarder settings")
                        dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "Error", "Failed to update settings")
                        
                except Exception as e:
                    QMessageBox.critical(dialog, "Error", f"Error updating settings: {str(e)}")
            
            save_button.clicked.connect(save_settings)
            
            # Show the dialog
            dialog.exec_()
            
        except Exception as e:
            self.logger.error(f"Error showing forwarder settings: {str(e)}")
            self.log_activity(f"Error showing forwarder settings: {str(e)}")
    
    def update_speed_check_range(self):
        """Ensure min seconds <= max seconds."""
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
        
    def start_background_processes(self):
        """Start all background monitoring and maintenance processes."""
        # Wait for UI to be fully initialized
        time.sleep(1)
        
        # Auto-refresh account info for accounts missing name/username
        QTimer.singleShot(3000, self.auto_refresh_missing_account_info)  # Wait 3 seconds after startup
        
        # Sync and validate session accounts
        QTimer.singleShot(5000, self.validate_session_accounts)  # Wait 5 seconds after startup
        
        # Update UI
        self.update_ui_timer = QTimer(self)
        self.update_ui_timer.timeout.connect(self.update_ui)
        self.update_ui_timer.start(1000)  # Update every second
    
    def validate_session_accounts(self):
        """Validate and sync all session-based accounts."""
        try:
            accounts = self.account_manager.get_accounts()
            session_accounts = [acc for acc in accounts if acc.get("account_type") == "session"]
            
            if not session_accounts:
                return
            
            self.log_activity(f"Validating {len(session_accounts)} session accounts...")
            
            # Run enhanced validation in background thread
            threading.Thread(target=self._validate_session_accounts_thread, args=(session_accounts,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Error starting session validation: {str(e)}")
    
    def _validate_session_accounts_thread(self, session_accounts):
        """Background thread to validate session accounts and detect frozen accounts."""
        try:
            validated_count = 0
            frozen_count = 0
            
            for account in session_accounts:
                phone = account.get("phone", "")
                if not phone:
                    continue
                
                try:
                    # Check if session file exists
                    clean_phone = phone.replace('+', '')
                    session_path = f"sessions/{clean_phone}.session"
                    
                    if not os.path.exists(session_path):
                        self.log_activity_signal.emit(f"⚠️ Session file missing for {phone}")
                        self.account_manager.update_account_status(phone, "session_missing")
                        continue
                    
                    # Test actual session status
                    session_status = self._test_session_status(phone, session_path)
                    
                    if session_status == "frozen":
                        self.log_activity_signal.emit(f"🔒 Account {phone} is frozen")
                        self.account_manager.update_account_status(phone, "frozen")
                        frozen_count += 1
                    elif session_status == "active":
                        self.log_activity_signal.emit(f"✅ Session account {phone} is active")
                        self.account_manager.update_account_status(phone, "active")
                        validated_count += 1
                    elif session_status == "auth_invalid":
                        self.log_activity_signal.emit(f"❌ Session {phone} authorization expired")
                        self.account_manager.update_account_status(phone, "auth_expired")
                    else:
                        self.log_activity_signal.emit(f"⚠️ Could not validate session {phone}")
                        self.account_manager.update_account_status(phone, "validation_failed")
                    
                    time.sleep(1)  # Small delay between validations
                    
                except Exception as e:
                    self.logger.error(f"Error validating session account {phone}: {str(e)}")
                    self.account_manager.update_account_status(phone, "validation_error")
            
            # Update UI and log completion
            self.update_ui_signal.emit()
            result_msg = f"✅ Session validation completed: {validated_count} active, {frozen_count} frozen"
            self.log_activity_signal.emit(result_msg)
            
        except Exception as e:
            self.logger.error(f"Error in session validation thread: {str(e)}")
            self.log_activity_signal.emit(f"❌ Session validation error: {str(e)}")
    
    def _test_session_status(self, phone, session_path):
        """Test session status to detect if account is frozen, active, or invalid."""
        try:
            from telethon import TelegramClient as TelethonClient
            from telethon.errors import AuthKeyInvalidError, UserDeactivatedError, UserDeactivatedBanError
            from telethon.tl.functions.account import GetPrivacyRequest
            import asyncio
            
            # Get API credentials
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                return "error"
            
            api_id = account.get("api_id", "611335")
            api_hash = account.get("api_hash", "session_import_placeholder")
            
            # Create event loop for this thread
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            async def test_session():
                client = None
                try:
                    client = TelethonClient(session_path, api_id, api_hash)
                    await asyncio.wait_for(client.connect(), timeout=10.0)
                    
                    if not await client.is_user_authorized():
                        return "auth_invalid"
                    
                    # Try to get user info to detect frozen status
                    me = await asyncio.wait_for(client.get_me(), timeout=10.0)
                    if me:
                        # Multiple checks to detect frozen status
                        try:
                            # Test 1: Try to get dialogs
                            dialogs = await asyncio.wait_for(client.get_dialogs(limit=1), timeout=8.0)
                            
                            # Test 2: Try to get chat info (frozen accounts often fail here)
                            try:
                                await asyncio.wait_for(client.get_entity("me"), timeout=5.0)
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "suspended", "limited", "deactivated"]):
                                    return "frozen"
                            
                            # Test 3: Try to access account settings/info (more sensitive check)
                            try:
                                # Try to get privacy settings - frozen accounts typically can't access this
                                await asyncio.wait_for(client(GetPrivacyRequest(key="phone_number")), timeout=5.0)
                            except ImportError:
                                # GetPrivacyRequest not available, skip this test
                                pass
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "suspended", "account"]):
                                    return "frozen"
                            
                            # Test 4: Try a simple message-related operation
                            try:
                                # Try to get message history from saved messages
                                await asyncio.wait_for(client.get_messages("me", limit=1), timeout=5.0)
                            except Exception as e:
                                error_msg = str(e).lower()
                                if any(keyword in error_msg for keyword in ["frozen", "restricted", "account", "suspended"]):
                                    return "frozen"
                                
                            return "active"
                            
                        except Exception as e:
                            error_msg = str(e).lower()
                            # Comprehensive frozen detection with more specific patterns
                            frozen_patterns = [
                                "frozen", "restricted", "suspended", "limited", 
                                "deactivated", "account", "banned", "disabled",
                                "your account", "not allowed", "access denied",
                                "account is", "account has been", "temporarily"
                            ]
                            
                            if any(pattern in error_msg for pattern in frozen_patterns):
                                return "frozen"
                            elif "flood" in error_msg or "wait" in error_msg:
                                return "active"  # Flood wait doesn't mean frozen
                            else:
                                # If we can get user info but fail other operations, likely frozen
                                return "frozen"
                    else:
                        return "auth_invalid"
                        
                except UserDeactivatedError:
                    return "frozen"
                except UserDeactivatedBanError:
                    return "frozen"
                except AuthKeyInvalidError:
                    return "auth_invalid"
                except asyncio.TimeoutError:
                    return "timeout"
                except Exception as e:
                    error_msg = str(e).lower()
                    # Enhanced frozen detection with more comprehensive patterns
                    frozen_patterns = [
                        "frozen", "restricted", "suspended", "deactivated", "banned", 
                        "disabled", "limited", "account", "your account", "not allowed",
                        "access denied", "terminated", "violation", "account is",
                        "account has been", "temporarily", "permanently"
                    ]
                    
                    if any(pattern in error_msg for pattern in frozen_patterns):
                        return "frozen"
                    elif "unauthorized" in error_msg or "auth" in error_msg:
                        return "auth_invalid"
                    else:
                        return "error"
                finally:
                    if client and client.is_connected():
                        try:
                            await asyncio.wait_for(client.disconnect(), timeout=3.0)
                        except:
                            pass
            
            # Run the test
            try:
                result = loop.run_until_complete(test_session())
                return result
            finally:
                # Clean up event loop
                try:
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    if pending:
                        loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                except:
                    pass
                finally:
                    loop.close()
                    
        except ImportError:
            return "error"
        except Exception as e:
            error_msg = str(e).lower()
            if "frozen" in error_msg or "restricted" in error_msg or "suspended" in error_msg:
                return "frozen"
            return "error"
    
    def auto_refresh_missing_account_info(self):
        """Automatically refresh account info for accounts missing name/username."""
        try:
            accounts = self.account_manager.get_accounts()
            accounts_needing_refresh = []
            
            for account in accounts:
                if account is None:
                    continue
                    
                name = account.get("name", "").strip() if account.get("name") else ""
                username = account.get("username", "").strip() if account.get("username") else ""
                phone = account.get("phone", "")
                account_type = account.get("account_type", "normal")
                
                # Check if account info is missing or incomplete
                needs_refresh = False
                
                # Session accounts especially need info refresh if missing data
                if account_type == "session":
                    if not name and not username:
                        needs_refresh = True
                        self.log_activity(f"Session account {phone} missing name/username - will refresh")
                    elif name == "N/A" or username == "N/A":
                        needs_refresh = True
                        self.log_activity(f"Session account {phone} has N/A values - will refresh")
                else:
                    # Regular accounts
                    if not name and not username:
                        needs_refresh = True
                
                if needs_refresh and phone:
                    accounts_needing_refresh.append(phone)
            
            if accounts_needing_refresh:
                self.log_activity(f"Found {len(accounts_needing_refresh)} accounts missing info. Auto-refreshing...")
                # Start background refresh for these accounts
                threading.Thread(target=self._auto_refresh_accounts_thread, args=(accounts_needing_refresh,), daemon=True).start()
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh: {str(e)}")
    
    def _auto_refresh_accounts_thread(self, phone_list):
        """Background thread for auto-refreshing missing account info."""
        try:
            updated_count = 0
            
            for phone in phone_list:
                try:
                    if self.refresh_account_info(phone):
                        updated_count += 1
                    time.sleep(1)  # Small delay between requests
                except Exception as e:
                    self.logger.error(f"Error refreshing {phone}: {str(e)}")
                    continue
            
            if updated_count > 0:
                # Update UI on main thread using signal
                self.update_ui_signal.emit()
                self.log_activity_signal.emit(f"Auto-refresh completed: {updated_count} accounts updated")
            
        except Exception as e:
            self.logger.error(f"Error in auto refresh thread: {str(e)}")
    
    def sync_accounts(self):
        """Synchronize accounts with the backend."""
        self.update_status_signal.emit("Syncing accounts...")
        self.log_activity_signal.emit("Syncing accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_accounts_thread, daemon=True).start()
    
    def _sync_accounts_thread(self):
        """Background thread for account synchronization."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Check for accounts needing age verification
            accounts_needing_check = self.account_manager.get_accounts_needing_age_check()
            
            if accounts_needing_check:
                log_usage_checker(self.logger, f"Found {len(accounts_needing_check)} accounts needing age check during sync")
                self.update_status_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                self.log_activity_signal.emit(f"Auto-checking ages for {len(accounts_needing_check)} accounts...")
                
                # Run age check in a separate thread to avoid blocking
                age_check_thread = threading.Thread(
                    target=self._age_check_background_thread, 
                    args=(accounts_needing_check,), 
                    daemon=True
                )
                age_check_thread.start()
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Account sync completed")
            self.log_activity_signal.emit("Account sync completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync error: {str(e)}")
            self.logger.error(f"Account sync error: {str(e)}")
            self.log_activity_signal.emit(f"Account sync failed: {str(e)}")
    
    def _age_check_background_thread(self, accounts_needing_check):
        """Background thread specifically for age checking to avoid blocking sync."""
        try:
            import asyncio
            
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                results = loop.run_until_complete(self.account_manager.auto_check_account_ages())
                
                success_count = sum(1 for _, success, _ in results if success)
                log_usage_checker(self.logger, f"Age check completed: {success_count}/{len(results)} successful")
                self.log_activity_signal.emit(f"Age check completed: {success_count}/{len(results)} successful")
                
                # Update UI after age check completes
                self.update_ui_signal.emit()
                
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"Error in age check background thread: {str(e)}"
            self.logger.error(error_msg)
            log_usage_checker(self.logger, error_msg, logging.ERROR)
            self.log_activity_signal.emit(f"Age check failed: {str(e)}")
    
    def toggle_monitor(self):
        """Toggle the monitor on/off."""
        if self.monitor.is_running:
            self.stop_monitor()
        else:
            self.start_monitor()
    
    def start_monitor(self):
        """Start the monitor."""
        try:
            interval = self.check_interval_spin.value() * 60  # Convert to seconds
            self.monitor.start(interval)
            
            self.toggle_monitor_button.setText("Stop Monitor")
            self.monitor_status_label.setText("Running")
            self.log_activity("Monitor started")
            self.status_label.setText("Monitor is running")
            
        except Exception as e:
            self.logger.error(f"Failed to start monitor: {str(e)}")
            self.log_activity(f"Failed to start monitor: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to start monitor: {str(e)}")
    
    def stop_monitor(self):
        """Stop the monitor."""
        try:
            self.monitor.stop()
            
            self.toggle_monitor_button.setText("Start Monitor")
            self.monitor_status_label.setText("Stopped")
            self.log_activity("Monitor stopped")
            self.status_label.setText("Monitor is stopped")
            
        except Exception as e:
            self.logger.error(f"Failed to stop monitor: {str(e)}")
            self.log_activity(f"Failed to stop monitor: {str(e)}")
    
    def sync_and_monitor(self):
        """Sync and monitor accounts."""
        self.update_status_signal.emit("Syncing and monitoring accounts...")
        self.log_activity_signal.emit("Syncing and monitoring accounts...")
        
        # Run in background thread
        threading.Thread(target=self._sync_and_monitor_thread, daemon=True).start()
    
    def _sync_and_monitor_thread(self):
        """Background thread for syncing and monitoring accounts."""
        try:
            # Perform account sync
            self.account_manager.sync_accounts()
            
            # Start monitor (this needs to be called from main thread)
            QMetaObject.invokeMethod(self, "start_monitor", Qt.QueuedConnection)
            
            # Update UI from the main thread using signal
            self.update_ui_signal.emit()
            
            # Update last sync time using signal
            now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            QMetaObject.invokeMethod(
                self.last_sync_label, 
                "setText", 
                Qt.QueuedConnection,
                Q_ARG(str, now)
            )
            
            self.update_status_signal.emit("Sync and monitor completed")
            self.log_activity_signal.emit("Sync and monitor completed successfully")
            
        except Exception as e:
            self.update_status_signal.emit(f"Sync and monitor error: {str(e)}")
            self.logger.error(f"Sync and monitor error: {str(e)}")
            self.log_activity_signal.emit(f"Sync and monitor failed: {str(e)}")
    
    def add_account(self):
        """Add a new account with a simplified, reliable approach."""
        # First, show a dialog to choose between normal login and session login
        login_choice_dialog = QDialog(self)
        login_choice_dialog.setWindowTitle("Choose Login Method")
        login_choice_dialog.setMinimumWidth(300)
        login_choice_layout = QVBoxLayout(login_choice_dialog)
        
        login_choice_label = QLabel("Choose Login Method:")
        login_choice_layout.addWidget(login_choice_label)
        
        button_layout = QHBoxLayout()
        normal_login_button = QPushButton("Normal Login")
        session_login_button = QPushButton("Session Login")
        
        button_layout.addWidget(normal_login_button)
        button_layout.addWidget(session_login_button)
        login_choice_layout.addLayout(button_layout)
        
        # Use a flag to track which method was chosen
        login_method_chosen = {"method": None}
        
        def choose_normal_login():
            login_method_chosen["method"] = "normal"
            login_choice_dialog.accept()
            
        def choose_session_login():
            login_method_chosen["method"] = "session"
            login_choice_dialog.accept()
            
        normal_login_button.clicked.connect(choose_normal_login)
        session_login_button.clicked.connect(choose_session_login)
        
        # Show the login method choice dialog
        login_choice_dialog.exec_()
        
        # If no method was chosen (dialog was closed), return False
        if login_method_chosen["method"] is None:
            return False
            
        # Handle Session Login
        if login_method_chosen["method"] == "session":
            return self.add_account_via_session()
        
        # Normal Login - continue with existing implementation
        # Create a custom dialog class with proper slots
        class AccountDialog(QDialog):
            def __init__(self, parent=None):
                super().__init__(parent)
                self.setWindowTitle("Add Account")
                self.setMinimumWidth(400)
                self.layout = QVBoxLayout(self)
                
                # Get the logger from the parent
                self.logger = parent.logger if parent else logging.getLogger("tg_checker")
                
                # Store parent reference for database cleanup
                self.parent_app = parent
                
                # Main form layout
                self.form_layout = QFormLayout()
                
                # Phone number input
                self.phone_input = QLineEdit()
                self.phone_input.setPlaceholderText("e.g. +*********")
                
                # API credentials
                self.api_id_input = QLineEdit()
                self.api_hash_input = QLineEdit()
                
                # Add basic inputs to form layout
                self.form_layout.addRow("Phone Number:", self.phone_input)
                self.form_layout.addRow("API ID:", self.api_id_input)
                self.form_layout.addRow("API Hash:", self.api_hash_input)
                
                # Create all authentication-related fields
                self.code_input = QLineEdit()
                self.code_input.setPlaceholderText("Verification code")
                self.code_input.setEnabled(False)
                # Connect textChanged signal to enable continue button when code is entered
                self.code_input.textChanged.connect(self.on_code_text_changed)
                
                self.twofa_input = QLineEdit()
                self.twofa_input.setEchoMode(QLineEdit.Password)
                self.twofa_input.setPlaceholderText("2FA Password")
                self.twofa_input.setEnabled(False)
                # Connect textChanged signal to enable continue button when 2FA password is entered
                self.twofa_input.textChanged.connect(self.on_2fa_text_changed)
                
                # Add the basic form to the layout
                self.layout.addLayout(self.form_layout)
                
                # Status and progress indication
                self.status_label = QLabel("Enter your Telegram account details")
                self.layout.addWidget(self.status_label)
                
                # Add a container for additional form fields (code/2FA) to be added later
                self.auth_container = QWidget()
                self.auth_layout = QFormLayout(self.auth_container)
                self.auth_container.setVisible(False)
                self.layout.addWidget(self.auth_container)
                
                # Add authentication log display
                self.auth_log_label = QLabel("Authentication Logs:")
                self.auth_log_display = QTextEdit()
                self.auth_log_display.setReadOnly(True)
                self.auth_log_display.setMaximumHeight(150)
                self.layout.addWidget(self.auth_log_label)
                self.layout.addWidget(self.auth_log_display)
                
                # Buttons
                self.button_layout = QHBoxLayout()
                
                self.connect_button = QPushButton("Connect")
                self.continue_button = QPushButton("Continue")
                self.continue_button.setVisible(False)
                self.cancel_button = QPushButton("Cancel")
                
                self.button_layout.addWidget(self.connect_button)
                self.button_layout.addWidget(self.continue_button)
                self.button_layout.addWidget(self.cancel_button)
                
                self.layout.addLayout(self.button_layout)
                
                # Connect button signals
                self.connect_button.clicked.connect(self.on_connect_clicked)
                self.continue_button.clicked.connect(self.on_continue_clicked)
                self.cancel_button.clicked.connect(self.reject)
                
                # Initialize variables
                self.client = None
                self.phone_code_hash = None
                self.authentication_stage = "initial"  # Can be "initial", "code", "2fa", "complete"
                
                # Load and display initial auth logs
                self.update_auth_log_display()
                
                # Start auth log update timer
                self.log_timer = QTimer(self)
                self.log_timer.timeout.connect(self.update_auth_log_display)
                self.log_timer.start(1000)  # Update logs every second
            
            # Add the update_verification_status method to the AccountDialog class
            def update_verification_status(self, message):
                """Update the status label with verification progress information."""
                try:
                    QMetaObject.invokeMethod(
                        self.status_label,
                        "setText",
                        Qt.QueuedConnection,
                        Q_ARG(str, message)
                    )
                except Exception as e:
                    # Fall back to direct update if invokeMethod fails
                    try:
                        self.status_label.setText(message)
                    except Exception as e2:
                        log_auth(self.logger, f"Failed to update verification status: {str(e)} -> {str(e2)}", logging.ERROR)
            
            def update_auth_log_display(self):
                """Update the authentication log display with recent logs"""
                try:
                    auth_logs = read_auth_log(max_lines=20)
                    self.auth_log_display.clear()
                    for log in auth_logs:
                        self.auth_log_display.append(log.strip())
                    
                    # Scroll to bottom
                    scroll_bar = self.auth_log_display.verticalScrollBar()
                    scroll_bar.setValue(scroll_bar.maximum())
                except Exception as e:
                    print(f"Error updating auth log display: {str(e)}")
            
            def on_code_text_changed(self, text):
                """Enable continue button when code text is entered"""
                if self.authentication_stage == "code":
                    self.continue_button.setEnabled(len(text.strip()) > 0)
            
            def on_2fa_text_changed(self, text):
                """Enable continue button when 2FA password is entered"""
                if self.authentication_stage == "2fa":
                    self.continue_button.setEnabled(len(text.strip()) > 0)
            
            # Proper PyQt slots that can be invoked from other threads
            from PyQt5.QtCore import pyqtSlot
            
            @pyqtSlot()
            def show_code_input(self):
                # Clear the auth container
                while self.auth_layout.count():
                    item = self.auth_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                
                # Add code input to the auth container
                self.auth_layout.addRow("Verification Code:", self.code_input)
                
                # Update state
                self.authentication_stage = "code"
                
                # Enable and show the relevant UI elements
                self.auth_container.setVisible(True)
                self.code_input.setEnabled(True)
                self.code_input.setFocus()
                self.code_input.clear()  # Clear any previous code
                
                self.connect_button.setVisible(False)
                self.continue_button.setVisible(True)
                # Initially disabled until code is entered
                self.continue_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Enter the verification code sent to your Telegram app")
                
                # Log the event
                phone = self.phone_input.text().strip()
                log_auth(self.logger, f"Verification code requested for account {phone}")
            
            @pyqtSlot()
            def show_2fa_input(self):
                # Clear the auth container
                while self.auth_layout.count():
                    item = self.auth_layout.takeAt(0)
                    if item.widget():
                        item.widget().deleteLater()
                
                # Add 2FA input to the auth container
                self.auth_layout.addRow("2FA Password:", self.twofa_input)
                
                # Update state
                self.authentication_stage = "2fa"
                
                # Enable and show the relevant UI elements
                self.auth_container.setVisible(True)
                self.twofa_input.setEnabled(True)
                self.twofa_input.setFocus()
                self.twofa_input.clear()  # Clear any previous password
                
                self.connect_button.setVisible(False)
                self.continue_button.setVisible(True)
                # Initially disabled until 2FA password is entered
                self.continue_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Two-factor authentication required. Please enter your 2FA password.")
                
                # Log the event
                phone = self.phone_input.text().strip()
                log_auth(self.logger, f"2FA password required for account {phone}")
            
            def on_connect_clicked(self):
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                # Validate inputs
                if not phone or not api_id or not api_hash:
                    self.status_label.setText("Please fill all required fields")
                    return
                
                # Log the connection attempt
                log_auth(self.logger, f"Connect button clicked for account {phone}")
                
                # Disable inputs during connection
                self.phone_input.setEnabled(False)
                self.api_id_input.setEnabled(False)
                self.api_hash_input.setEnabled(False)
                self.connect_button.setEnabled(False)
                
                # Update status
                self.status_label.setText("Connecting to Telegram...")
                
                # Process in the background
                threading.Thread(target=self.connect_thread, daemon=True).start()
            
            def connect_thread(self):
                try:
                    # Get account details
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    
                    # Log the attempt
                    log_auth(self.logger, f"Connecting to Telegram for account {phone}")
                    
                    # Create client
                    self.client = TelegramClient(api_id, api_hash, phone)
                    
                    # Request confirmation code (this step includes the connection)
                    log_auth(self.logger, f"Sending code request for account {phone}")
                    result = self.client.send_code_request()
                    
                    # Check result
                    if result.get("success", False):
                        # Store the phone code hash
                        self.phone_code_hash = result.get("phone_code_hash")
                        
                        # Log success
                        log_auth(self.logger, f"Code request successful for account {phone}")
                        
                        # Update UI to show code input
                        QMetaObject.invokeMethod(self, "show_code_input", Qt.QueuedConnection)
                    elif result.get("requires_2fa", False):
                        # 2FA detected during code request
                        log_auth(self.logger, f"2FA detected immediately for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                    elif result.get("already_authorized", False):
                        # Account is already authorized
                        log_auth(self.logger, f"Account {phone} is already authorized")
                        # Get user info and add account
                        user_info = {"user": {"first_name": "Unknown", "last_name": "", "username": ""}}
                        QMetaObject.invokeMethod(
                            self, "on_login_success", 
                            Qt.QueuedConnection,
                            Q_ARG(dict, user_info)
                        )
                    else:
                        # Handle error
                        error_msg = result.get("error", "Unknown error")
                        log_auth(self.logger, f"Code request failed for account {phone}: {error_msg}", logging.ERROR)
                        
                        # Update UI with specific messages for common errors
                        if "timed out" in error_msg.lower() or "timeout" in error_msg.lower():
                            error_display = "Connection to Telegram timed out. Please check your internet connection and try again."
                        elif "flood" in error_msg.lower() or "wait" in error_msg.lower():
                            error_display = error_msg  # Keep the original message as it contains time information
                        elif "invalid" in error_msg.lower():
                            error_display = "Invalid API credentials. Please check your API ID and API Hash."
                        else:
                            error_display = f"Error: {error_msg}"
                            
                        QMetaObject.invokeMethod(
                            self.status_label, 
                            "setText", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_display)
                        )
                        
                        # Re-enable inputs
                        self._enable_inputs()
                        
                except Exception as e:
                    # Log the error
                    phone = self.phone_input.text().strip()
                    log_auth(self.logger, f"Connection error for account {phone}: {str(e)}", logging.ERROR)
                    
                    # Check for 2FA in error
                    error_msg = str(e).lower()
                    if any(keyword in error_msg for keyword in ["password", "2fa", "two-factor", "session password needed"]):
                        log_auth(self.logger, f"2FA detected from exception for account {phone}")
                        QMetaObject.invokeMethod(self, "show_2fa_input", Qt.QueuedConnection)
                    else:
                        # Update UI with error - specific message for timeout
                        if "timeout" in error_msg or "timed out" in error_msg:
                            error_display = "Connection to Telegram timed out. Please check your internet connection and try again."
                        elif "invalid" in error_msg:
                            error_display = "Invalid API credentials. Please check your API ID and API Hash."
                        else:
                            error_display = f"Error: {str(e)}"
                            
                        QMetaObject.invokeMethod(
                            self.status_label, 
                            "setText", 
                            Qt.QueuedConnection,
                            Q_ARG(str, error_display)
                        )
                        
                        # Re-enable inputs
                        self._enable_inputs()
            
            def _enable_inputs(self):
                """Re-enable input fields after an error."""
                QMetaObject.invokeMethod(self.phone_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_id_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.api_hash_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                QMetaObject.invokeMethod(self.connect_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_continue_clicked(self):
                # Get values
                phone = self.phone_input.text().strip()
                api_id = self.api_id_input.text().strip()
                api_hash = self.api_hash_input.text().strip()
                
                if self.authentication_stage == "code":
                    # Get code input
                    code = self.code_input.text().strip()
                    
                    if not code:
                        self.status_label.setText("Please enter the verification code")
                        return
                    
                    # Log code submission
                    log_auth(self.logger, f"Verification code submitted for account {phone}")
                    
                    # Disable inputs during verification
                    self.code_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying code...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_code_thread, args=(code,), daemon=True).start()
                    
                elif self.authentication_stage == "2fa":
                    # Get 2FA password
                    password = self.twofa_input.text().strip()
                    
                    if not password:
                        self.status_label.setText("Please enter your 2FA password")
                        return
                    
                    # Log 2FA password submission
                    log_auth(self.logger, f"2FA password submitted for account {phone}")
                    
                    # Disable inputs
                    self.twofa_input.setEnabled(False)
                    self.continue_button.setEnabled(False)
                    
                    # Update status
                    self.status_label.setText("Verifying 2FA password...")
                    
                    # Process in the background
                    threading.Thread(target=self.verify_2fa_thread, args=(password,), daemon=True).start()
            
            def verify_code_thread(self, code):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    session_file = f"sessions/{phone}"
                    
                    # Create and start login worker
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, code, 
                        session_file=session_file, 
                        phone_code_hash=self.phone_code_hash, 
                        logger=self.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_2fa_required.connect(self.on_2fa_required)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in verify_code_thread: {str(e)}", logging.ERROR)
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, f"Verification error: {str(e)}")
                    )
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.code_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def verify_2fa_thread(self, password):
                try:
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    phone = self.phone_input.text().strip()
                    session_file = f"sessions/{phone}"
                    
                    # Ensure sessions directory exists
                    os.makedirs("sessions", exist_ok=True)
                    
                    # Log the attempt with better detail
                    log_auth(self.logger, f"Verifying 2FA password for account {phone}")
                    
                    # Create and start login worker for 2FA
                    self.login_worker = TelegramLoginWorker(
                        api_id, api_hash, phone, None, 
                        password=password,
                        phone_code_hash=self.phone_code_hash, 
                        session_file=session_file, 
                        logger=self.logger
                    )
                    self.login_worker.login_success.connect(self.on_login_success)
                    self.login_worker.login_error.connect(self.on_login_error)
                    self.login_worker.start()
                    
                except Exception as e:
                    log_auth(self.logger, f"Error in verify_2fa_thread: {str(e)}", logging.ERROR)
                    error_msg = f"2FA verification error: {str(e)}"
                    
                    # Update UI safely
                    QMetaObject.invokeMethod(
                        self.status_label, 
                        "setText", 
                        Qt.QueuedConnection,
                        Q_ARG(str, error_msg)
                    )
                    
                    # Re-enable inputs
                    QMetaObject.invokeMethod(self.twofa_input, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
                    QMetaObject.invokeMethod(self.continue_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))

            def on_login_success(self, user_info):
                try:
                    phone = self.phone_input.text().strip()
                    api_id = self.api_id_input.text().strip()
                    api_hash = self.api_hash_input.text().strip()
                    user = user_info.get('user')
                    
                    # Get parent window reference for UI updates
                    parent_app = self.parent()
                    
                    # Extract user information safely with better handling
                    name = ""
                    username = ""
                    if user:
                        # Handle both Telethon User object and dict
                        if hasattr(user, 'first_name'):  # Telethon User object
                            first_name = getattr(user, 'first_name', '') or ''
                            last_name = getattr(user, 'last_name', '') or ''
                            username = getattr(user, 'username', '') or ''
                        elif isinstance(user, dict):  # Dict format
                            first_name = user.get('first_name', '') or ''
                            last_name = user.get('last_name', '') or ''
                            username = user.get('username', '') or ''
                        else:
                            first_name = str(user.get('first_name', '')) if hasattr(user, 'get') else ''
                            last_name = str(user.get('last_name', '')) if hasattr(user, 'get') else ''
                            username = str(user.get('username', '')) if hasattr(user, 'get') else ''
                        
                        name = f"{first_name} {last_name}".strip()
                    
                    # Add account to database - retry on database lock
                    max_retries = 3
                    for attempt in range(max_retries):
                        try:
                            success = self.account_manager.add_account(phone, api_id, api_hash)
                            if success:
                                break
                        except Exception as db_err:
                            if "database is locked" in str(db_err).lower() and attempt < max_retries - 1:
                                log_auth(self.logger, f"Database locked when adding account, retrying ({attempt+1}/{max_retries})...", logging.WARNING)
                                time.sleep(1.5)  # Wait before retry
                                continue
                            else:
                                raise
                    
                    if success:
                        # Update account info with user details
                        if name or username:
                            self.account_manager.update_account_info(phone, name=name, username=username)
                            log_auth(self.logger, f"Successfully added account {phone} with info: {name} / @{username}")
                        else:
                            # Try to get user info separately if not available
                            try:
                                client = TelegramClient(api_id, api_hash, phone)
                                account_info = client.get_account_info()
                                if account_info:
                                    name = account_info.get("full_name", "").strip()
                                    username = account_info.get("username", "").strip()
                                    if name or username:
                                        self.account_manager.update_account_info(phone, name=name, username=username)
                                        log_auth(self.logger, f"Retrieved and updated account info for {phone}: {name} / @{username}")
                            except Exception as info_e:
                                log_auth(self.logger, f"Could not retrieve additional account info for {phone}: {str(info_e)}", logging.WARNING)
                            
                            log_auth(self.logger, f"Successfully added account {phone}")
                        
                        # Start age check in background for new account
                        log_usage_checker(self.logger, f"Starting automatic age check for new account: {phone}")
                        threading.Thread(target=parent_app._auto_check_new_account_age if parent_app else None, args=(phone,), daemon=True).start()
                        
                        # Notify parent about account update
                        if parent_app:
                            parent_app.log_activity(f"Added account: {phone}")
                            parent_app.update_ui()
                        
                        # Update status and close dialog
                        self.update_verification_status("Login successful! Account added.")
                        QMetaObject.invokeMethod(self, "accept", Qt.QueuedConnection)
                    else:
                        log_auth(self.logger, f"Failed to add account {phone} to database", logging.ERROR)
                        self.update_verification_status("Failed to add account to database.")
                        
                except Exception as e:
                    log_auth(self.logger, f"Error in on_login_success: {str(e)}", logging.ERROR)
                    self.update_verification_status(f"Error processing login: {str(e)}")
    
    def refresh_all_accounts_info(self):
        """Refresh information for all accounts automatically."""
        self.update_status_signal.emit("Refreshing account information...")
        self.log_activity_signal.emit("Refreshing information for all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._refresh_all_accounts_thread, daemon=True).start()
    
    def _refresh_all_accounts_thread(self):
        """Background thread for refreshing all account information."""
        try:
            accounts = self.account_manager.get_accounts()
            updated_count = 0
            
            for account in accounts:
                phone = account.get("phone")
                if phone:
                    # Check if account info is missing or incomplete
                    name = account.get("name", "").strip()
                    username = account.get("username", "").strip()
                    
                    if not name and not username:
                        self.log_activity_signal.emit(f"Refreshing info for {phone}...")
                        if self.refresh_account_info(phone):
                            updated_count += 1
                        time.sleep(2)  # Small delay between requests
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit(f"Account info refresh completed - {updated_count} accounts updated")
            self.log_activity_signal.emit(f"Account info refresh completed - {updated_count} accounts updated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Account info refresh error: {str(e)}")
            self.logger.error(f"Account info refresh error: {str(e)}")
            self.log_activity_signal.emit(f"Account info refresh failed: {str(e)}")

    def refresh_specific_account_info(self, account):
        """Refresh information for a specific account."""
        try:
            phone = account.get("phone", "")
            if not phone:
                return
            
            self.update_status_signal.emit(f"Refreshing info for {phone}...")
            self.log_activity_signal.emit(f"Refreshing info for {phone}...")
            
            # Run in background thread
            threading.Thread(target=self._refresh_specific_account_thread, args=(phone,), daemon=True).start()
        except Exception as e:
            self.logger.error(f"Failed to refresh account info for {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to refresh account info: {str(e)}")
    
    def _refresh_specific_account_thread(self, phone):
        """Background thread for refreshing specific account information."""
        try:
            success = self.refresh_account_info(phone)
            
            if success:
                # Update UI using signal
                self.update_ui_signal.emit()
                self.update_status_signal.emit(f"Account info refreshed for {phone}")
            else:
                self.update_status_signal.emit(f"Failed to refresh info for {phone}")
                
        except Exception as e:
            self.logger.error(f"Error in refresh thread for {phone}: {str(e)}")
            self.update_status_signal.emit(f"Refresh error for {phone}: {str(e)}")

    def _auto_check_new_account_age(self, phone):
        """Background thread method to check age for a newly added account."""
        try:
            import asyncio
            
            # Wait a moment for the account to be fully set up
            time.sleep(3)
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                log_usage_checker(self.logger, f"Checking age for newly added account: {phone}")
                success, message = loop.run_until_complete(
                    self.account_manager.check_account_age_with_bot(phone)
                )
                
                if success:
                    self.update_ui_signal.emit()
                    self.log_activity_signal.emit(f"Age check completed for new account {phone}: {message}")
                    log_usage_checker(self.logger, f"Age check successful for new account {phone}: {message}")
                else:
                    self.log_activity_signal.emit(f"Age check failed for new account {phone}: {message}")
                    log_usage_checker(self.logger, f"Age check failed for new account {phone}: {message}", logging.WARNING)
                    
            finally:
                loop.close()
                
        except Exception as e:
            error_msg = f"Error in auto age check for new account {phone}: {str(e)}"
            self.logger.error(error_msg)
            self.log_activity_signal.emit(error_msg)
            log_usage_checker(self.logger, error_msg, logging.ERROR)

    def check_all_account_ages(self):
        """Manually check ages for all accounts."""
        self.update_status_signal.emit("Checking ages for all accounts...")
        self.log_activity_signal.emit("Starting manual age check for all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._check_all_account_ages_thread, daemon=True).start()
    
    def _check_all_account_ages_thread(self):
        """Background thread for checking ages of all accounts."""
        try:
            import asyncio
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                log_usage_checker(self.logger, "Starting manual age check for all accounts")
                results = loop.run_until_complete(self.account_manager.auto_check_account_ages())
                
                success_count = sum(1 for _, success, _ in results if success)
                total_count = len(results)
                
                self.update_ui_signal.emit()
                self.update_status_signal.emit(f"Age check completed: {success_count}/{total_count} successful")
                self.log_activity_signal.emit(f"Manual age check completed: {success_count}/{total_count} successful")
                log_usage_checker(self.logger, f"Manual age check completed: {success_count}/{total_count} successful")
                
            finally:
                loop.close()
                
        except Exception as e:
            self.update_status_signal.emit(f"Age check error: {str(e)}")
            self.logger.error(f"Age check error: {str(e)}")
            self.log_activity_signal.emit(f"Age check failed: {str(e)}")
            log_usage_checker(self.logger, f"Manual age check failed: {str(e)}", logging.ERROR)

    def save_results_3tier(self, valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests):
        """Save results to text files with 3-tier error classification system."""
        try:
            # Create the Results directory if it doesn't exist
            results_dir = "Results"
            os.makedirs(results_dir, exist_ok=True)
            
            # Convert to lists if needed
            valid_filtered = list(valid_filtered) if valid_filtered else []
            valid_only = list(valid_only) if valid_only else []
            topics_groups = list(topics_groups) if topics_groups else []
            channels_only = list(channels_only) if channels_only else []
            invalid_groups = list(invalid_groups) if invalid_groups else []
            account_issues = list(account_issues) if account_issues else []
            join_requests = list(join_requests) if join_requests else []
            
            # Define result file configurations with 3-tier classification
            result_files = [
                {
                    "filename": "GroupsValid_Filter_On.txt",
                    "data": valid_filtered,
                    "description": "groups (valid & pass all filters)",
                    "summary_desc": "Groups that pass all filters"
                },
                {
                    "filename": "GroupsValidOnly.txt",
                    "data": valid_only,
                    "description": "groups (valid but don't pass filters)",
                    "summary_desc": "Valid groups that don't meet filter criteria"
                },
                {
                    "filename": "TopicsGroups.txt",
                    "data": topics_groups,
                    "description": "topic groups",
                    "summary_desc": "Valid topic groups"
                },
                {
                    "filename": "Channels.txt",
                    "data": channels_only,
                    "description": "channels",
                    "summary_desc": "Valid channels"
                },
                {
                    "filename": "InvalidGroups_Channels.txt",
                    "data": invalid_groups,
                    "description": "invalid groups/channels",
                    "summary_desc": "Invalid groups (username not found, private, etc.)"
                },
                {
                    "filename": "AccountIssue.txt",
                    "data": account_issues,
                    "description": "account issues",
                    "summary_desc": "Account-level problems (rate limits, session issues, etc.)"
                },
                {
                    "filename": "JoinRequest.txt",
                    "data": join_requests,
                    "description": "join requests needed",
                    "summary_desc": "Groups that require join requests"
                }
            ]
            
            # Save each category to its own file
            files_saved = 0
            
            for file_config in result_files:
                filename = file_config["filename"]
                data = file_config["data"]
                description = file_config["description"]
                
                file_path = os.path.join(results_dir, filename)
                
                try:
                    with open(file_path, "w", encoding='utf-8', newline='') as f:
                        if data:
                            for item in data:
                                f.write(f"{item}\n")
                    
                    files_saved += 1
                    self.log_activity(f"📄 Saved {len(data)} {description} to {filename}")
                    
                    # Verify the file was created properly
                    if os.path.isfile(file_path):
                        file_size = os.path.getsize(file_path)
                        self.log_activity(f"✅ Verified: {filename} created ({file_size} bytes)")
                    
                except Exception as file_error:
                    self.log_activity(f"❌ Failed to save {filename}: {str(file_error)}")
                    self.logger.error(f"File save error for {filename}: {str(file_error)}")
            
            # Create summary file with 3-tier classification
            total_results = len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only) + len(invalid_groups) + len(account_issues) + len(join_requests)
            summary_filename = "SUMMARY.txt"
            summary_path = os.path.join(results_dir, summary_filename)
            
            try:
                with open(summary_path, "w", encoding='utf-8', newline='') as f:
                    f.write("=== TG CHECKER RESULTS SUMMARY ===\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    f.write("=== VALID GROUPS ===\n")
                    f.write(f"Groups Valid & Filter ON: {len(valid_filtered)}\n")
                    f.write(f"Groups Valid Only: {len(valid_only)}\n")
                    f.write(f"Topics Groups Only Valid: {len(topics_groups)}\n")
                    f.write(f"Channels Only Valid: {len(channels_only)}\n")
                    f.write(f"SUBTOTAL VALID: {len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only)}\n\n")
                    f.write("=== INVALID/ISSUES ===\n")
                    f.write(f"Invalid Groups/Channels: {len(invalid_groups)}\n")
                    f.write(f"Account Issues: {len(account_issues)}\n")
                    f.write(f"Join Requests Needed: {len(join_requests)}\n")
                    f.write(f"SUBTOTAL INVALID/ISSUES: {len(invalid_groups) + len(account_issues) + len(join_requests)}\n\n")
                    f.write(f"TOTAL PROCESSED: {total_results}\n\n")
                    f.write("=== FILES CREATED ===\n")
                    for file_config in result_files:
                        f.write(f"• {file_config['filename']} - {file_config['summary_desc']}\n")
                
                files_saved += 1
                self.log_activity(f"📄 Created summary file: {summary_filename}")
                
            except Exception as summary_error:
                self.log_activity(f"❌ Failed to create summary file: {str(summary_error)}")
                self.logger.error(f"Summary file error: {str(summary_error)}")
            
            # Final summary log
            self.log_activity(f"✅ RESULTS SAVED WITH 3-TIER CLASSIFICATION!")
            self.log_activity(f"📊 Total: {total_results} groups processed")
            self.log_activity(f"🎯 Valid: {len(valid_filtered) + len(valid_only) + len(topics_groups) + len(channels_only)}")
            self.log_activity(f"❌ Invalid: {len(invalid_groups)}")
            self.log_activity(f"⚠️ Account Issues: {len(account_issues)}")
            self.log_activity(f"📋 Join Requests: {len(join_requests)}")
            self.log_activity(f"📁 {files_saved} files created in {results_dir}/ directory")
            
        except Exception as e:
            error_msg = f"❌ Failed to save 3-tier results: {str(e)}"
            self.logger.error(error_msg)
            self.log_activity(error_msg)


    def start_checker(self):
        """Start the group/channel checker."""
        if self.is_checker_running:
            QMessageBox.information(self, "Info", "Checker is already running.")
            return
            
        # Get the list of groups/channels from the input
        group_links = self.groups_input.toPlainText().strip().split('\n')
        group_links = [link.strip() for link in group_links if link.strip()]
        
        if not group_links:
            QMessageBox.warning(self, "Warning", "Please enter at least one group or channel link.")
            return
        
        # Check for resume option
        is_resuming, final_group_links, start_index = self.check_resume_option(group_links)
        
        # Make sure we have at least one active account
        accounts = self.account_manager.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        
        if not active_accounts:
            QMessageBox.warning(self, "Warning", "Please activate at least one account before starting the checker.")
            return
        
        # Clear previous results if not resuming
        if not is_resuming:
            self.clear_results()
        
        # Set up progress tracking
        self.current_group_links = group_links
        self.total_groups = len(group_links)
        self.current_group_index = start_index
        
        # Update progress display
        self.update_progress_signal.emit(self.current_group_index, self.total_groups)
        
        # Update state and UI
        self.is_checker_running = True
        self.checker_should_stop = False
        self.start_checker_button.setEnabled(False)
        self.stop_checker_button.setEnabled(True)
        
        # Get speed check settings
        min_seconds = self.settings.value("min_check_seconds", 2, type=int)
        max_seconds = self.settings.value("max_check_seconds", 5, type=int)
        
        # Start checker thread
        self.checker_thread = threading.Thread(
            target=self._checker_thread,
            args=(final_group_links, start_index, min_seconds, max_seconds),
            daemon=True
        )
        self.checker_thread.start()
        
        if is_resuming:
            self.log_activity(f"🚀 Checker resumed from group {start_index+1} of {len(final_group_links)}")
        else:
            self.log_activity(f"🚀 Checker started with {len(final_group_links)} groups")

    def check_resume_option(self, group_links):
        """Check if we should resume from a previous check."""
        try:
            # Check if we have a saved progress file
            if os.path.exists(self.progress_file):
                with open(self.progress_file, 'r') as f:
                    saved_index = int(f.read().strip())
                    
                # Ask user if they want to resume
                reply = QMessageBox.question(
                    self, 
                    "Resume Checking", 
                    f"Found a saved progress (group {saved_index+1} of {len(group_links)}).\nWould you like to resume from where you left off?",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    # Make sure the index is valid
                    if 0 <= saved_index < len(group_links):
                        return True, group_links, saved_index
            
            # If no saved progress or user chose not to resume
            return False, group_links, 0
        except Exception as e:
            self.logger.error(f"Error checking resume option: {str(e)}")
            return False, group_links, 0

    def _checker_thread(self, group_links, start_index=0, min_seconds=2, max_seconds=5):
        """Background thread for checking groups/channels."""
        try:
            # Get active account for checking
            accounts = self.account_manager.get_accounts()
            active_accounts = [acc for acc in accounts if acc.get("active", False)]
            
            if not active_accounts:
                self.update_status_signal.emit("Error: No active accounts available")
                self.log_activity_signal.emit("❌ No active accounts available for checking")
                return
            
            # Select the first active account
            account = active_accounts[0]
            phone = account.get("phone", "")
            
            self.log_activity_signal.emit(f"Using account: {phone}")
            
            # Get filter settings
            min_members = self.settings.value("min_members", 500, type=int)
            min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
            min_total_messages = self.settings.value("min_total_messages", 100, type=int)
            
            # Initialize result collections
            valid_filtered = []
            valid_only = []
            topics_groups = []
            channels_only = []
            invalid_groups = []
            account_issues = []
            join_requests = []
            
            # Start checking from the given index
            for i in range(start_index, len(group_links)):
                # Check if we should stop
                if self.checker_should_stop:
                    self.log_activity_signal.emit("🛑 Checker stopped by user")
                    break
                
                link = group_links[i]
                
                # Update progress
                self.current_group_index = i
                self.update_progress_signal.emit(i, len(group_links))
                self.update_analyzing_signal.emit(f"Currently analyzing: {link}")
                
                # Save progress to file
                self._save_progress(i)
                
                # Anti-flood delay
                if i > start_index:
                    delay = random.uniform(min_seconds, max_seconds)
                    time.sleep(delay)
                
                # Check group/channel
                try:
                    self.log_activity_signal.emit(f"Checking: {link}")
                    
                    result = self.check_group_or_channel(link)
                    
                    if not result["valid"]:
                        # Handle invalid groups
                        error_type = result.get("error_type", "invalid_group")
                        
                        if error_type == "invalid_group":
                            invalid_groups.append(link)
                            self.log_activity_signal.emit(f"❌ INVALID: {link}")
                        elif error_type == "account_issue":
                            account_issues.append(link)
                            self.log_activity_signal.emit(f"⚠️ ACCOUNT ISSUE: {result.get('reason', 'Unknown error')}")
                        elif error_type == "join_request":
                            # Add to separate list for join requests
                            join_requests.append(link)
                            self.log_activity_signal.emit(f"👋 JOIN REQUEST: {link}")
                        
                        # Update UI
                        self.update_result_counts_signal.emit(
                            len(valid_filtered), len(valid_only), len(topics_groups), 
                            len(channels_only), len(invalid_groups), len(account_issues)
                        )
                        continue
                    
                    # Handle valid groups
                    if result["type"] == "channel":
                        channels_only.append(link)
                        self.log_activity_signal.emit(f"📺 Valid channel: {link}")
                    elif result["type"] == "topic":
                        topics_groups.append(link)
                        self.log_activity_signal.emit(f"💬 Valid topic: {link}")
                    elif result["type"] == "group":
                        # Check if it passes filters
                        passes_filters = (
                            result["members"] >= min_members and
                            result["last_message_age_hours"] <= min_message_time_hours and
                            result["total_messages"] >= min_total_messages
                        )
                        
                        if passes_filters:
                            valid_filtered.append(link)
                            self.log_activity_signal.emit(f"✅ Valid group (passes filters): {link}")
                        else:
                            valid_only.append(link)
                            self.log_activity_signal.emit(f"⚠️ Valid group (doesn't pass filters): {link}")
                    
                    # Update UI
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues)
                    )
                    
                except Exception as e:
                    self.logger.error(f"Error checking {link}: {str(e)}")
                    account_issues.append(link)
                    self.log_activity_signal.emit(f"❌ ERROR: {link} - {str(e)}")
                    
                    # Update UI
                    self.update_result_counts_signal.emit(
                        len(valid_filtered), len(valid_only), len(topics_groups), 
                        len(channels_only), len(invalid_groups), len(account_issues)
                    )
            
            # Save results to files
            self.save_results_3tier(
                valid_filtered, valid_only, topics_groups, channels_only, invalid_groups, account_issues, join_requests
            )
            
            # Remove progress file when complete
            if os.path.exists(self.progress_file) and not self.checker_should_stop:
                try:
                    os.remove(self.progress_file)
                except:
                    pass
            
            # Update UI when done
            self.update_analyzing_signal.emit("Currently analyzing: None")
            if self.checker_should_stop:
                self.update_status_signal.emit("Checking stopped")
                self.log_activity_signal.emit("Checking stopped by user")
            else:
                self.update_status_signal.emit("Checking completed")
                self.log_activity_signal.emit("✅ Checking completed")
            
        except Exception as e:
            self.logger.error(f"Checker thread error: {str(e)}")
            self.log_activity_signal.emit(f"❌ Checker error: {str(e)}")
            self.update_status_signal.emit(f"Checker error: {str(e)}")
        finally:
            # Reset state and UI
            self.is_checker_running = False
            self.checker_should_stop = False
            QMetaObject.invokeMethod(self.start_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            QMetaObject.invokeMethod(self.stop_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, False))

    def _save_progress(self, index):
        """Save current progress to a file for potential resume."""
        try:
            with open(self.progress_file, 'w') as f:
                f.write(str(index))
        except Exception as e:
            self.logger.error(f"Error saving progress: {str(e)}")

    def clear_results(self):
        """Clear previous results."""
        with self.results_lock:
            self.valid_filtered = []
            self.valid_only = []
            self.topics_groups = []
            self.channels_only = []
            self.invalid_groups = []
            self.account_issues = []
            self.join_requests = []
            
            # Update UI
            self.update_result_counts_signal.emit(0, 0, 0, 0, 0, 0)
    def start_task_checker(self):
        """Start the enhanced multi-account task checker with auto group distribution."""
        if self.is_task_checker_running:
            QMessageBox.information(self, "Info", "Task checker is already running.")
            return
            
        # Get the list of groups/channels from the input
        group_links = self.groups_input.toPlainText().strip().split('\n')
        group_links = [link.strip() for link in group_links if link.strip()]
        
        if not group_links:
            QMessageBox.warning(self, "Warning", "Please enter at least one group or channel link.")
            return
        
        # Check for resume option
        is_resuming, final_group_links, start_index = self.check_resume_option(group_links)
        
        # Make sure we have at least one active account
        accounts = self.account_manager.get_accounts()
        active_accounts = [a for a in accounts if a.get("active", False)]
        
        if not active_accounts:
            QMessageBox.warning(self, "Warning", "Please activate at least one account before starting the task checker.")
            return
        
        # Clear previous results if not resuming
        if not is_resuming:
            self.clear_results()
            
            # Clear pending groups queue
            with self.account_states_lock:
                self.pending_groups_queue.clear()
        
        # Reset global progress counter for this session
        with self.global_progress_lock:
            self.global_groups_processed = self.current_group_index
        
        # Set up progress tracking
        self.current_group_links = group_links
        self.total_groups = len(group_links)
        self.current_group_index = start_index
        
        # Update progress display
        self.update_progress_signal.emit(self.current_group_index, self.total_groups)
        
        # Update state and UI
        self.is_task_checker_running = True
        self.task_checker_should_stop = False
        self.start_task_checker_button.setEnabled(False)
        self.stop_task_checker_button.setEnabled(True)
        
        # Count available backup accounts
        inactive_accounts = [a for a in accounts if not a.get("active", False)]
        available_backups = []
        
        for account in inactive_accounts:
            status = account.get("status", "").lower()
            if "banned" not in status and "limit" not in status:
                available_backups.append(account)
        
        # Display distribution and auto-replacement status info
        self.log_activity(f"📊 Account Status: {len(active_accounts)} active accounts, {len(available_backups)} backup accounts available")
        if available_backups:
            self.log_activity(f"🔄 AUTO-REPLACEMENT: Enabled - Will automatically use backup accounts if needed")
        else:
            self.log_activity(f"⚠️ AUTO-REPLACEMENT: Limited - No backup accounts available")
        
        # Calculate groups per account - ensure even distribution
        total_groups = len(final_group_links)
        groups_per_account = max(1, total_groups // len(active_accounts))
        self.log_activity(f"⚖️ AUTO DISTRIBUTION: {total_groups} groups divided among {len(active_accounts)} accounts (~{groups_per_account} per account)")
        
        # Initialize account states
        with self.account_states_lock:
            for account in active_accounts:
                phone = account.get("phone")
                self.account_states[phone] = {
                    "status": "available",
                    "groups_remaining": [],
                    "flood_wait_until": None
                }
        
        # Distribute groups among active accounts
        tasks = []
        
        for i, account in enumerate(active_accounts):
            phone = account.get("phone")
            start_index_for_account = i * groups_per_account
            
            # Last account gets remaining groups
            if i == len(active_accounts) - 1:
                account_groups = final_group_links[start_index_for_account:]
            else:
                end_index = start_index_for_account + groups_per_account
                account_groups = final_group_links[start_index_for_account:end_index]
            
            if account_groups:
                self.log_activity(f"📋 Assigned {len(account_groups)} groups to account {phone}")
                
                # Create task thread for this account
                task_thread = threading.Thread(
                    target=self._enhanced_account_task_thread,
                    args=(phone, account_groups, start_index_for_account),
                    daemon=True
                )
                
                tasks.append({
                    "phone": phone,
                    "thread": task_thread,
                    "groups": account_groups
                })
                
                task_thread.start()
        
        # Start monitoring thread
        monitor_thread = threading.Thread(target=self._monitor_tasks_thread, args=(tasks,), daemon=True)
        monitor_thread.start()
        
        # Start reassignment monitor for load balancing and auto-replacement
        reassignment_thread = threading.Thread(target=self._task_reassignment_monitor, daemon=True)
        reassignment_thread.start()
        
        if is_resuming:
            self.log_activity(f"🚀 Task checker resumed: {len(final_group_links)} remaining groups distributed among {len(active_accounts)} accounts")
        else:
            self.log_activity(f"🚀 Task checker started: {len(final_group_links)} groups distributed among {len(active_accounts)} accounts")
    
    def stop_task_checker(self):
        """Stop the multi-account task checker."""
        if not self.is_task_checker_running:
            QMessageBox.information(self, "Info", "Task checker is not currently running.")
            return
        
        # Set stop flag
        self.task_checker_should_stop = True
        self.log_activity("Stopping multi-account task checker...")
        self.status_label.setText("Stopping task checker...")
    
    def _enhanced_account_task_thread(self, phone, group_links, start_index=0):
        """Enhanced account task thread with auto-detection of account limitations."""
        try:
            account = None
            accounts = self.account_manager.get_accounts()
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.log_activity_signal.emit(f"❌ Account {phone} not found")
                return
            
            # Get filter settings (same as regular checker)
            min_members = self.settings.value("min_members", 500, type=int)
            min_message_time_hours = self.settings.value("min_message_time", 1, type=int)
            min_total_messages = self.settings.value("min_total_messages", 100, type=int)
            
            local_groups_processed = 0
            consecutive_errors = 0  # Track consecutive errors for failure detection
            total_groups = len(group_links)
            
            # Log start of processing for this account
            self.log_activity_signal.emit(f"🏁 Account {phone} starting to process {total_groups} groups")
            
            for link in group_links:
                # Check if we should stop
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Account {phone} stopping due to user request")
                    break
                
                # Check account status
                with self.account_states_lock:
                    if phone in self.account_states:
                        if self.account_states[phone]["status"] != "available":
                            self.log_activity_signal.emit(f"⏸️ Account {phone} is not available, skipping remaining groups")
                            # Add remaining groups to pending queue
                            remaining_groups = group_links[local_groups_processed:]
                            remaining_count = len(remaining_groups)
                            self.pending_groups_queue.extend(remaining_groups)
                            self.log_activity_signal.emit(f"⏩ {remaining_count} groups moved to pending queue for redistribution")
                            break
                
                # Anti-flood delay (same as regular checker)
                if local_groups_processed > 0:
                    delay = random.uniform(1.0, 2.0)  # Match regular checker timing
                    time.sleep(delay)
                
                # Rest cycle for long runs (every 100 groups per account)
                if local_groups_processed > 0 and local_groups_processed % 100 == 0:
                    self.log_activity_signal.emit(f"🛌 Account {phone} rest cycle: pausing for 2 minutes ({local_groups_processed}/{total_groups} completed)")
                    for rest_seconds in range(120, 0, -10):  # 2 minutes
                        if self.task_checker_should_stop:
                            break
                        time.sleep(10)
                    
                    if self.task_checker_should_stop:
                        break
                    
                    self.log_activity_signal.emit(f"✅ Account {phone} rest cycle completed")
                
                self.log_activity_signal.emit(f"🔍 Account {phone} checking: {link}")
                
                try:
                    # *** USE THE SAME BACKEND LOGIC AS REGULAR CHECKER ***
                    result = self.check_group_or_channel(link)
                    
                    # Reset consecutive error count on success
                    consecutive_errors = 0
                    
                    # Increment global progress counter (thread-safe)
                    with self.global_progress_lock:
                        self.global_groups_processed += 1
                        current_total = self.global_groups_processed
                    
                    local_groups_processed += 1
                    
                    # Update global progress display
                    self.update_progress_signal.emit(current_total, self.total_groups)
                    
                    # Log progress periodically (every 10 groups)
                    if local_groups_processed % 10 == 0:
                        completion_percent = int((local_groups_processed / total_groups) * 100)
                        self.log_activity_signal.emit(f"📊 Account {phone} progress: {local_groups_processed}/{total_groups} groups ({completion_percent}%)")
                    
                    # Process result with 3-tier classification (same as regular checker)
                    with self.results_lock:
                        if not result["valid"]:
                            error_type = result.get("error_type", "invalid_group")
                            
                            if error_type == "invalid_group":
                                self.invalid_groups.append(link)
                                self.log_activity_signal.emit(f"[{phone}] INVALID: {link} → InvalidGroups_Channels.txt")
                            elif error_type == "account_issue":
                                # Add to account issues collection
                                self.account_issues.append(link)
                                
                                # Enhanced detection of account limitations
                                error_message = result.get("reason", "").lower()
                                
                                # Handle FloodWait specifically
                                wait_seconds = result.get("wait_seconds", 0)
                                if wait_seconds > 0:
                                    self.log_activity_signal.emit(f"[{phone}] 🔒 FLOOD WAIT: {wait_seconds}s → AccountIssue.txt → Pausing account")
                                    
                                    # Update account status
                                    with self.account_states_lock:
                                        if phone in self.account_states:
                                            self.account_states[phone]["status"] = "flood_wait"
                                            self.account_states[phone]["flood_wait_until"] = datetime.now() + timedelta(seconds=wait_seconds)
                                    
                                    # Update account in database
                                    self.account_manager.update_check_time(phone, f"FloodWait {wait_seconds}s")
                                    
                                    # Schedule retry and add remaining groups to pending
                                    remaining_groups = group_links[local_groups_processed:]
                                    remaining_count = len(remaining_groups)
                                    self.pending_groups_queue.extend(remaining_groups)
                                    self.log_activity_signal.emit(f"⚠️ Account {phone} limited - {remaining_count} groups queued for redistribution")
                                    self._schedule_account_retry(phone, wait_seconds)
                                    break
                                # Check for permanent limitations or bans
                                elif "limit" in error_message or "restricted" in error_message or "banned" in error_message:
                                    self.log_activity_signal.emit(f"[{phone}] 🚫 ACCOUNT BANNED/LIMITED: {result['reason']} → Replacing account")
                                    
                                    # Update account status
                                    with self.account_states_lock:
                                        if phone in self.account_states:
                                            self.account_states[phone]["status"] = "banned"
                                    
                                    # Update account in database
                                    self.account_manager.update_check_time(phone, f"Banned: {result['reason']}")
                                    
                                    # Add remaining groups to pending for redistribution
                                    remaining_groups = group_links[local_groups_processed:]
                                    remaining_count = len(remaining_groups)
                                    self.pending_groups_queue.extend(remaining_groups)
                                    self.log_activity_signal.emit(f"⚠️ Account {phone} banned - {remaining_count} groups queued for redistribution")
                                    break
                                else:
                                    self.log_activity_signal.emit(f"[{phone}] ACCOUNT ISSUE: {result['reason']} → AccountIssue.txt")
                            elif error_type == "join_request":
                                self.join_requests.append(link)
                                self.log_activity_signal.emit(f"[{phone}] JOIN REQUEST: {link} → JoinRequest.txt")
                            
                            # Update UI in real-time
                            self.update_result_counts_signal.emit(
                                len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                                len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                            )
                            continue
                        
                        # Valid group - categorize (same logic as regular checker)
                        if result["type"] == "channel":
                            self.channels_only.append(link)
                            self.log_activity_signal.emit(f"[{phone}] 📺 Valid channel: {link}")
                        elif result["type"] == "topic":
                            self.topics_groups.append(link)
                            self.log_activity_signal.emit(f"[{phone}] 💬 Valid topic: {link}")
                        elif result["type"] == "group":
                            # Check filters (same as regular checker)
                            passes_filters = (
                                result["members"] >= min_members and
                                result["last_message_age_hours"] <= min_message_time_hours and
                                result["total_messages"] >= min_total_messages
                            )
                            
                            if passes_filters:
                                self.valid_filtered.append(link)
                                self.log_activity_signal.emit(f"[{phone}] ✅ Valid group (filtered): {link}")
                            else:
                                self.valid_only.append(link)
                                self.log_activity_signal.emit(f"[{phone}] ⚠️ Valid group (no filter): {link}")
                        
                        # Update UI in real-time
                        self.update_result_counts_signal.emit(
                            len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                            len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                        )
                
                except Exception as e:
                    # Handle unexpected errors (same as regular checker)
                    consecutive_errors += 1
                    
                    # Increment global progress counter even for errors (thread-safe)
                    with self.global_progress_lock:
                        self.global_groups_processed += 1
                        current_total = self.global_groups_processed
                    
                    local_groups_processed += 1
                    
                    # Update progress display for errors too
                    self.update_progress_signal.emit(current_total, self.total_groups)
                    
                    # Log the error with progress
                    self.logger.info(f"[INFO] Account {phone} checked group {current_total} of {self.total_groups}: {link}")
                    
                    # Classify as account issue (same as regular checker)
                    with self.results_lock:
                        self.account_issues.append(link)
                        self.log_activity_signal.emit(f"[{phone}] ACCOUNT ISSUE: Error checking {link}: {str(e)} → AccountIssue.txt")
                        
                        # Update UI
                        self.update_result_counts_signal.emit(
                            len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                            len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                        )
                    
                    # Enhanced error detection - check for ban/limitation keywords in error
                    error_str = str(e).lower()
                    if "banned" in error_str or "limit" in error_str or "restrict" in error_str or "not available" in error_str:
                        self.log_activity_signal.emit(f"🚫 Account {phone} appears to be banned or limited: {str(e)}")
                        
                        # Mark account as banned
                        with self.account_states_lock:
                            if phone in self.account_states:
                                self.account_states[phone]["status"] = "banned"
                        
                        # Add remaining groups to pending queue for redistribution
                        remaining_groups = group_links[local_groups_processed:]
                        remaining_count = len(remaining_groups)
                        self.pending_groups_queue.extend(remaining_groups)
                        self.log_activity_signal.emit(f"⚠️ Account {phone} banned - {remaining_count} groups queued for redistribution")
                        break
                    
                    # If too many consecutive errors, pause account
                    if consecutive_errors >= 5:
                        self.log_activity_signal.emit(f"⚠️ Account {phone} has {consecutive_errors} consecutive errors - pausing for 5 minutes")
                        
                        # Mark account as temporarily unavailable
                        with self.account_states_lock:
                            if phone in self.account_states:
                                self.account_states[phone]["status"] = "error_recovery"
                        
                        # Add remaining groups to pending queue
                        remaining_groups = group_links[local_groups_processed:]
                        remaining_count = len(remaining_groups)
                        self.pending_groups_queue.extend(remaining_groups)
                        self.log_activity_signal.emit(f"⏩ {remaining_count} groups moved to pending queue for redistribution")
                        
                        # Schedule recovery after 5 minutes
                        self._schedule_account_retry(phone, 300)  # 5 minutes
                        break
                    
                    continue
            
            # Mark account as completed
            with self.account_states_lock:
                if phone in self.account_states and self.account_states[phone]["status"] == "available":
                    self.account_states[phone]["status"] = "completed"
                    completion_percent = int((local_groups_processed / total_groups) * 100)
                    self.log_activity_signal.emit(f"✅ Account {phone} completed: {local_groups_processed}/{total_groups} groups processed ({completion_percent}%)")
            
        except Exception as e:
            self.log_activity_signal.emit(f"❌ Account {phone} task error: {str(e)}")
            self.logger.error(f"Task thread error for {phone}: {str(e)}")
            
            # Mark account as failed
            with self.account_states_lock:
                if phone in self.account_states:
                    self.account_states[phone]["status"] = "failed"
                    
                    # Add any remaining groups to pending queue
                    remaining_groups = group_links[local_groups_processed:]
                    if remaining_groups:
                        self.pending_groups_queue.extend(remaining_groups)
                        self.log_activity_signal.emit(f"⚠️ Account {phone} failed - {len(remaining_groups)} groups queued for redistribution")
    
    def _monitor_tasks_thread(self, tasks):
        """Monitor the task threads and update UI when all are done."""
        try:
            # Wait for all tasks to complete
            for task in tasks:
                task["thread"].join()
            
            # Update the UI with results using signals
            with self.results_lock:
                self.update_result_counts_signal.emit(
                    len(self.valid_filtered), len(self.valid_only), len(self.topics_groups), 
                    len(self.channels_only), len(self.invalid_groups), len(self.account_issues)
                )
                
                # Save results to files if not stopped
                if not self.task_checker_should_stop:
                    # Use 3-tier system for task checker too
                    self.save_results_3tier(
                        self.valid_filtered.copy(), 
                        self.valid_only.copy(), 
                        self.topics_groups.copy(), 
                        self.channels_only.copy(), 
                        self.invalid_groups.copy(),
                        self.account_issues.copy(),  # Now properly tracked
                        self.join_requests.copy()   # Now properly tracked
                    )
            
            # Update status using signals
            self.update_analyzing_signal.emit("Currently analyzing: None")
            if self.task_checker_should_stop:
                self.update_status_signal.emit("Task-based checking stopped")
                self.log_activity_signal.emit("All tasks stopped")
            else:
                self.update_status_signal.emit("Task-based checking completed")
                self.log_activity_signal.emit("All tasks completed")
            
            # Update accounts table to reflect any changes in status
            self.update_ui_signal.emit()
            
        except Exception as e:
            self.logger.error(f"Task monitoring error: {str(e)}")
            self.log_activity_signal.emit(f"Task monitoring error: {str(e)}")
            self.update_status_signal.emit(f"Task error: {str(e)}")
        finally:
            # Reset state and UI
            self.is_task_checker_running = False
            self.task_checker_should_stop = False
            QMetaObject.invokeMethod(self.start_task_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, True))
            QMetaObject.invokeMethod(self.stop_task_checker_button, "setEnabled", Qt.QueuedConnection, Q_ARG(bool, False))
    
    def _task_reassignment_monitor(self):
        """Monitor for task reassignment when accounts hit FloodWait or errors."""
        while not self.task_checker_should_stop:
            try:
                time.sleep(5)  # Check every 5 seconds
                
                # Check if there are pending groups to reassign
                with self.account_states_lock:
                    if not self.pending_groups_queue:
                        continue
                    
                    # Find available accounts (not in flood_wait, error_recovery, or failed)
                    available_accounts = []
                    for phone, state in self.account_states.items():
                        if state["status"] == "available":
                            available_accounts.append(phone)
                    
                    if available_accounts:
                        # Reassign groups to available accounts
                        groups_to_reassign = self.pending_groups_queue.copy()
                        self.pending_groups_queue.clear()
                        
                        self.log_activity_signal.emit(f"🔄 REASSIGNMENT: Found {len(available_accounts)} available accounts for {len(groups_to_reassign)} groups")
                        
                        # Distribute groups among available accounts
                        groups_per_account = max(1, len(groups_to_reassign) // len(available_accounts))
                        group_index = 0
                        
                        for account_phone in available_accounts:
                            if group_index >= len(groups_to_reassign):
                                break
                            
                            # Calculate groups for this account
                            start_index = group_index
                            end_index = min(group_index + groups_per_account, len(groups_to_reassign))
                            
                            # Add remaining groups to the last account
                            if account_phone == available_accounts[-1]:
                                end_index = len(groups_to_reassign)
                            
                            account_groups = groups_to_reassign[start_index:end_index]
                            
                            if account_groups:
                                self.log_activity_signal.emit(f"➡️ REASSIGNED: {len(account_groups)} groups to account {account_phone}")
                                
                                # Start new task thread for this account
                                task_thread = threading.Thread(
                                    target=self._enhanced_account_task_thread,
                                    args=(account_phone, account_groups, start_index),
                                    daemon=True
                                )
                                task_thread.start()
                                
                                group_index = end_index
                        
                        self.log_activity_signal.emit(f"✅ REASSIGNMENT COMPLETE: All {len(groups_to_reassign)} groups redistributed")
                    
                    else:
                        # No available accounts - check for inactive accounts that can be activated
                        # Get all accounts including inactive ones
                        accounts = self.account_manager.get_accounts()
                        inactive_accounts = []
                        
                        for account in accounts:
                            phone = account.get("phone")
                            # Check if account exists in states but is not active or if it's not in states at all
                            if (phone not in self.account_states or 
                                phone in self.account_states and self.account_states[phone]["status"] == "inactive"):
                                # Skip accounts with known issues
                                status = account.get("status", "").lower()
                                if "banned" in status or "limit" in status:
                                    continue
                                    
                                inactive_accounts.append(phone)
                        
                        # If we found inactive accounts that can be activated
                        if inactive_accounts:
                            newly_activated = []
                            
                            for phone in inactive_accounts[:3]:  # Activate up to 3 accounts at a time
                                try:
                                    # Activate the account in the database
                                    self.account_manager.activate_account(phone)
                                    
                                    # Add it to our account states
                                    self.account_states[phone] = {
                                        "status": "available",
                                        "groups_remaining": [],
                                        "flood_wait_until": None
                                    }
                                    
                                    newly_activated.append(phone)
                                    self.log_activity_signal.emit(f"🔄 AUTO-REPLACEMENT: Activated idle account {phone}")
                                except Exception as e:
                                    self.logger.error(f"Error activating account {phone}: {str(e)}")
                            
                            if newly_activated:
                                self.log_activity_signal.emit(f"✅ AUTO-REPLACEMENT: Activated {len(newly_activated)} new accounts to replace limited/banned ones")
                                # Update UI to reflect new active accounts
                                self.update_ui_signal.emit()
                                
                                # No need to continue - on the next loop, we'll find these accounts as available
                                continue
                        
                        # No available accounts - check what's happening
                        status_counts = {}
                        for phone, state in self.account_states.items():
                            status = state["status"]
                            status_counts[status] = status_counts.get(status, 0) + 1
                        
                        status_summary = ", ".join([f"{count} {status}" for status, count in status_counts.items()])
                        
                        if self.pending_groups_queue:
                            pending_count = len(self.pending_groups_queue)
                            self.log_activity_signal.emit(f"⏳ PAUSED: {pending_count} groups pending | Account Status: {status_summary}")
                            
                            # Every minute remind user they can export pending groups
                            if time.time() % 60 < 5:  # Execute approximately every minute
                                self.log_activity_signal.emit(f"📋 REMINDER: You can export {pending_count} pending groups for later processing")
                        
                        # Check if all accounts are in permanent failure state
                        active_or_recoverable = any(
                            state["status"] in ["available", "flood_wait", "error_recovery"] 
                            for state in self.account_states.values()
                        )
                        
                        if not active_or_recoverable and self.pending_groups_queue:
                            self.log_activity_signal.emit(f"🚨 CRITICAL: All accounts failed permanently - {len(self.pending_groups_queue)} groups cannot be processed")
                            
                            # Every 2 minutes when all accounts are permanently unusable
                            if time.time() % 120 < 5:  # Execute approximately every 2 minutes
                                self.log_activity_signal.emit(f"📋 ACTION NEEDED: Please use 'Export Pending Groups' feature to save {len(self.pending_groups_queue)} unprocessed groups")
                
            except Exception as e:
                self.logger.error(f"Task reassignment monitor error: {str(e)}")
                self.log_activity_signal.emit(f"❌ Reassignment monitor error: {str(e)}")
                
        self.log_activity_signal.emit("🛑 Task reassignment monitor stopped")
    
    def _schedule_account_retry(self, phone, wait_seconds):
        """Schedule an account retry after FloodWait or error recovery period with improved error handling."""
        retry_type = "FloodWait" if wait_seconds >= 300 else "Error Recovery"
        self.log_activity_signal.emit(f"⏰ RETRY SCHEDULED: Account {phone} {retry_type} - will retry in {wait_seconds}s ({wait_seconds//60}m {wait_seconds%60}s)")
        
        def retry_thread():
            try:
                # Use the actual wait time, with minimum safety margins
                if wait_seconds >= 300:  # FloodWait
                    actual_wait = max(wait_seconds, 300)  # Minimum 5 minutes for safety
                else:  # Error recovery
                    actual_wait = max(wait_seconds, 60)   # Minimum 1 minute for error recovery
                
                # Log countdown every minute for longer waits
                if actual_wait > 60:
                    remaining_time = actual_wait
                    while remaining_time > 0 and not self.task_checker_should_stop:
                        if remaining_time % 60 == 0:  # Log every minute
                            minutes = remaining_time // 60
                            self.log_activity_signal.emit(f"⏰ Account {phone} retry in {minutes} minutes")
                        time.sleep(30)  # Check every 30 seconds
                        remaining_time -= 30
                else:
                    time.sleep(actual_wait)
                
                if self.task_checker_should_stop:
                    self.log_activity_signal.emit(f"🛑 Retry cancelled for account {phone}")
                    return
                
                # Re-enable account
                with self.account_states_lock:
                    if phone in self.account_states:
                        old_status = self.account_states[phone]["status"]
                        self.account_states[phone]["status"] = "available"
                        self.account_states[phone]["flood_wait_until"] = None
                        
                        self.log_activity_signal.emit(f"🔄 RETRY: Account {phone} recovered from {old_status} → now available")
                
                # Update account status in database
                self.account_manager.update_check_time(phone, "OK - Recovered")
                
                self.log_activity_signal.emit(f"✅ RECOVERY COMPLETE: Account {phone} is ready for new tasks")
            except Exception as e:
                self.logger.error(f"Retry thread error for {phone}: {str(e)}")
                self.log_activity_signal.emit(f"❌ Retry error for account {phone}: {str(e)}")
                
                # Mark as failed if recovery fails
                with self.account_states_lock:
                    if phone in self.account_states:
                        self.account_states[phone]["status"] = "failed"
        
        # Start retry thread
        threading.Thread(target=retry_thread, daemon=True).start()
    
    def update_log_display(self):
        """Update the log display based on selected filter."""
        try:
            log_type = self.log_type_combo.currentText()
            log_level = self.log_level_combo.currentText()
            
            if log_type == "Authentication":
                # Load authentication logs
                logs = read_auth_log(max_lines=1000)
            elif log_type == "Usage Checker":
                # Load usage checker logs
                logs = read_usage_checker_log(max_lines=1000)
            else:
                # Load general logs
                log_file = os.path.join("logs", "tg_checker.log")
                logs = read_log_file(log_file, max_lines=1000)
            
            # Filter by level if needed (skip for Usage Checker as it has its own format)
            if log_level != "All" and log_type != "Usage Checker":
                logs = filter_logs_by_level(logs, log_level)
            
            # Update the display
            self.log_display.clear()
            for log in logs:
                self.log_display.append(log.strip())
            
            # Scroll to bottom
            scroll_bar = self.log_display.verticalScrollBar()
            scroll_bar.setValue(scroll_bar.maximum())
            
        except Exception as e:
            self.logger.error(f"Error updating log display: {str(e)}")
            self.log_display.clear()
            self.log_display.append(f"Error loading logs: {str(e)}")

    def test_progress_signals(self):
        """Test method to verify progress signals are working."""
        try:
            self.log_activity("🧪 Testing progress signals...")
            
            # Test progress signal
            for i in range(1, 6):
                self.update_progress_signal.emit(i, 5)
                self.log_activity(f"Progress test: {i}/5")
                time.sleep(1)
            
            self.log_activity("✅ Progress signal test completed")
            
        except Exception as e:
            self.logger.error(f"Progress signal test failed: {str(e)}")
            self.log_activity(f"❌ Progress signal test failed: {str(e)}")

    def open_telegram_link(self, username):
        """Open Telegram link when username is clicked."""
        try:
            import webbrowser
            telegram_url = f"https://t.me/{username}"
            webbrowser.open(telegram_url)
            self.log_activity_signal.emit(f"Opened Telegram link: {telegram_url}")
        except Exception as e:
            self.logger.error(f"Failed to open Telegram link for {username}: {str(e)}")
            self.log_activity_signal.emit(f"Failed to open Telegram link for {username}")
    
    def update_monitor_log(self, logs):
        """Update the monitor log with recent logs."""
        try:
            if logs:
                self.monitor_log.clear()
                filtered_logs = []
                
                # Filter out redundant messages
                redundant_messages = ["No accounts were checked"]
                
                for log in logs:
                    if not any(msg in log for msg in redundant_messages):
                        filtered_logs.append(log)
                
                for log in filtered_logs:
                    self.monitor_log.append(log)
        except Exception as e:
            self.logger.error(f"Failed to update monitor log: {str(e)}")

    def add_account_via_session(self):
        """Add a new account using a session file."""
        # Show file selector dialog to choose the session file
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "Select Telegram Session File", 
            "", 
            "Telegram Sessions (*.session);;All Files (*)"
        )
        
        if not file_path:
            # User canceled the file selection
            return False
            
        try:
            # Create a simple progress dialog
            progress_dialog = QDialog(self)
            progress_dialog.setWindowTitle("Importing Session")
            progress_dialog.setFixedSize(400, 150)
            progress_layout = QVBoxLayout(progress_dialog)
            
            status_label = QLabel("Analyzing session file...")
            progress_bar = QProgressBar()
            progress_bar.setRange(0, 0)  # Indeterminate progress
            
            progress_layout.addWidget(status_label)
            progress_layout.addWidget(progress_bar)
            
            # Show the dialog without blocking (modeless)
            progress_dialog.show()
            QApplication.processEvents()
            
            # Get session file information
            session_file_name = os.path.basename(file_path)
            session_dir = os.path.dirname(file_path)
            
            # Extract phone number from session filename if possible
            phone_number = None
            if session_file_name.endswith('.session'):
                # Try to extract phone from filename (common naming convention)
                base_name = os.path.splitext(session_file_name)[0]
                if base_name.startswith('+'):
                    phone_number = base_name
                elif base_name.isdigit() or (base_name[1:].isdigit() and base_name[0] == '-'):
                    # Convert to international format with + if it's all digits
                    phone_number = f"+{base_name.lstrip('+-')}"
            
            # If we couldn't extract phone number from filename, generate a unique ID
            if not phone_number:
                import uuid
                unique_id = str(uuid.uuid4())[:8]
                phone_number = f"session-{unique_id}"
                
            # Update progress
            status_label.setText(f"Validating session: {phone_number}...")
            QApplication.processEvents()
            
            # Enhanced session file validation
            session_valid = False
            extracted_api_id = None
            extracted_api_hash = None
            extracted_phone = None
            
            try:
                if file_path.endswith('.session'):
                    import sqlite3
                    conn = sqlite3.connect(file_path, timeout=10)
                    cursor = conn.cursor()
                    
                    # Check if the session file has the required tables
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    if 'sessions' not in tables:
                        raise Exception("Invalid session file - missing sessions table")
                    
                    # Try to get API ID and phone from sessions table
                    cursor.execute("SELECT api_id, server_address FROM sessions LIMIT 1")
                    result = cursor.fetchone()
                    if result and result[0]:
                        extracted_api_id = str(result[0])
                        self.log_activity_signal.emit(f"Extracted API ID {extracted_api_id} from session file")
                        
                        # Try to get phone from session if available
                        try:
                            cursor.execute("SELECT auth_key FROM sessions WHERE auth_key IS NOT NULL LIMIT 1")
                            auth_result = cursor.fetchone()
                            if auth_result:
                                session_valid = True
                                self.log_activity_signal.emit("Session file contains valid authorization data")
                        except:
                            pass
                    
                    # Additional validation - check if session has entities table (indicates it's been used)
                    try:
                        cursor.execute("SELECT COUNT(*) FROM entities LIMIT 1")
                        entities_count = cursor.fetchone()[0] if cursor.fetchone() else 0
                        if entities_count > 0:
                            self.log_activity_signal.emit("Session file appears to have been actively used")
                    except:
                        # Entities table might not exist in newer sessions, that's okay
                        pass
                    
                    conn.close()
                    
                    # Use extracted credentials or fallback to defaults
                    if extracted_api_id:
                        api_id = extracted_api_id
                        api_hash = "session_import_placeholder"
                    else:
                        # Use default API credentials for session imports
                        api_id = "611335"  # Default Telegram app API ID
                        api_hash = "session_import_placeholder"
                        
            except Exception as e:
                self.log_activity_signal.emit(f"Session file validation warning: {str(e)}")
                # Don't fail completely, try with default credentials
                api_id = "611335"
                api_hash = "session_import_placeholder"
            
            # Create target session directory if it doesn't exist
            sessions_dir = "sessions"
            os.makedirs(sessions_dir, exist_ok=True)
            
            # Create the target path in our sessions directory
            clean_phone = phone_number.replace('+', '')
            target_session_path = os.path.join(sessions_dir, f"{clean_phone}.session")
            
            # Copy the session file to our sessions directory
            import shutil
            shutil.copy2(file_path, target_session_path)
            
            # Update progress
            status_label.setText("Testing connection to Telegram...")
            QApplication.processEvents()
            
            # Enhanced session testing with better error handling
            connection_successful = False
            account_info = None
            connection_error = None
            
            def test_session():
                nonlocal connection_successful, account_info, connection_error
                try:
                    # Import telethon directly for more robust testing
                    from telethon import TelegramClient as TelethonClient
                    from telethon.errors import SessionPasswordNeededError, PhoneNumberInvalidError, AuthKeyInvalidError
                    
                    # Create and set up event loop for this thread
                    import asyncio
                    
                    # Check if there's already an event loop in this thread
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_closed():
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                    except RuntimeError:
                        # No event loop exists in this thread, create one
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                    
                    async def test_connection():
                        client = None
                        try:
                            # Create client with session file
                            client = TelethonClient(target_session_path, api_id, api_hash)
                            
                            # Connect to Telegram with timeout
                            await asyncio.wait_for(client.connect(), timeout=15.0)
                            
                            # Check if user is authorized
                            if await client.is_user_authorized():
                                # Get user info with timeout
                                me = await asyncio.wait_for(client.get_me(), timeout=10.0)
                                if me:
                                    account_info = {
                                        'phone': getattr(me, 'phone', phone_number),
                                        'full_name': f"{getattr(me, 'first_name', '')} {getattr(me, 'last_name', '')}".strip(),
                                        'username': getattr(me, 'username', ''),
                                        'id': getattr(me, 'id', None)
                                    }
                                    return True
                                else:
                                    raise Exception("Could not retrieve user information from session")
                            else:
                                raise Exception("Session is not authorized")
                                
                        except AuthKeyInvalidError:
                            raise Exception("Session authorization key is invalid or expired")
                        except asyncio.TimeoutError:
                            raise Exception("Connection timeout - session validation took too long")
                        except Exception as e:
                            raise e
                        finally:
                            if client and client.is_connected():
                                try:
                                    await asyncio.wait_for(client.disconnect(), timeout=5.0)
                                except:
                                    pass
                    
                    # Run the async test
                    try:
                        result = loop.run_until_complete(test_connection())
                        if result and account_info:
                            connection_successful = True
                    finally:
                        # Clean up the event loop
                        try:
                            # Cancel any pending tasks
                            pending = asyncio.all_tasks(loop)
                            for task in pending:
                                task.cancel()
                            
                            # Wait for tasks to complete cancellation
                            if pending:
                                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
                        except:
                            pass
                        finally:
                            loop.close()
                        
                except ImportError:
                    connection_error = "Telethon library is not available"
                except Exception as e:
                    connection_error = str(e)
                    if "api id invalid" in str(e).lower() or "api_id invalid" in str(e).lower():
                        connection_error = "Session contains invalid API credentials"
                    elif "unauthorized" in str(e).lower() or "not authorized" in str(e).lower():
                        connection_error = "Session is not authorized or has expired"
                    elif "auth key invalid" in str(e).lower():
                        connection_error = "Session authorization key is invalid or expired"
                    elif "phone number invalid" in str(e).lower():
                        connection_error = "Phone number associated with session is invalid"
                    elif "event loop" in str(e).lower():
                        connection_error = "Internal threading error occurred during validation"
                    elif "network" in str(e).lower() or "connection" in str(e).lower():
                        connection_error = "Network connection failed during session validation"
                    elif "timeout" in str(e).lower():
                        connection_error = "Session validation timed out"
                    elif "sqlite" in str(e).lower() or "database" in str(e).lower():
                        connection_error = "Session file appears to be corrupted"
                    elif not connection_error or connection_error == str(e):
                        # Provide more context for generic errors
                        connection_error = f"Session validation failed: {str(e)}"
            
            # Run the test in a background thread to keep UI responsive
            import threading
            test_thread = threading.Thread(target=test_session, daemon=True)
            test_thread.start()
            
            # Log the start of validation
            self.log_activity_signal.emit(f"Starting session validation for {phone_number}...")
            
            # Wait for the test to complete with timeout
            max_wait_time = 20  # seconds - increased timeout
            for i in range(max_wait_time * 10):  # check every 100ms
                if not test_thread.is_alive():
                    break
                # Update progress periodically
                if i % 10 == 0:  # Every 1 second
                    status_label.setText(f"Testing connection... ({i // 10}s)")
                    QApplication.processEvents()
                time.sleep(0.1)
            
            # Log validation timeout if thread is still alive
            if test_thread.is_alive():
                self.log_activity_signal.emit(f"⚠️ Session validation for {phone_number} timed out after {max_wait_time}s")
                if not connection_error:
                    connection_error = f"Session validation timed out after {max_wait_time} seconds"
            
            # Close the progress dialog
            progress_dialog.close()
            
            # Check if the session is valid
            if connection_successful and account_info:
                # Session is valid, add the account to our database
                name = account_info.get("full_name", "").strip()
                username = account_info.get("username", "").strip()
                phone = account_info.get("phone", phone_number).strip()
                
                # Ensure we have a valid phone number
                if not phone or phone == "None":
                    phone = phone_number
                
                # Update dialog with success message
                success_dialog = QDialog(self)
                success_dialog.setWindowTitle("Session Import Successful")
                success_dialog.setFixedSize(400, 200)
                success_layout = QVBoxLayout(success_dialog)
                
                # Show account details
                account_info_text = f"✅ Session validated successfully!\n\n"
                account_info_text += f"📱 Phone: {phone}\n"
                account_info_text += f"👤 Name: {name or 'Not available'}\n"
                account_info_text += f"🔖 Username: @{username}" if username else "🔖 Username: Not set\n"
                
                success_label = QLabel(account_info_text)
                success_layout.addWidget(success_label)
                
                # Add a close button
                close_button = QPushButton("Close")
                close_button.clicked.connect(success_dialog.accept)
                success_layout.addWidget(close_button)
                
                # Add account to database - retry on database lock
                max_retries = 3
                success = False
                
                for attempt in range(max_retries):
                    try:
                        if parent_app:
                            success = parent_app.account_manager.add_account(phone, api_id, api_hash)
                            if success:
                                break
                        else:
                            log_auth(self.logger, "Cannot access account manager, parent reference not found", logging.ERROR)
                            self.update_verification_status("Error: Cannot access account manager")
                            return
                    except Exception as db_err:
                        error_str = str(db_err).lower()
                        if "database is locked" in error_str and attempt < max_retries - 1:
                            log_auth(self.logger, f"Database locked when adding account, retrying ({attempt+1}/{max_retries})...", logging.WARNING)
                            
                            # Try to fix the database
                            if parent_app:
                                parent_app.account_manager.clean_database()
                                log_auth(self.logger, "Database cleanup completed, retrying operation", logging.INFO)
                            
                            time.sleep(1.5)  # Wait before retry
                            continue
                        else:
                            log_auth(self.logger, f"Database error: {error_str}", logging.ERROR)
                            self.update_verification_status(f"Database error: {error_str[:100]}...")
                            return
                
                if success:
                    # Update account info with actual values
                    self.account_manager.update_account_info(phone, name=name, username=username)
                    
                    # Set status to active instead of leaving it as default
                    self.account_manager.update_account_status(phone, "active")
                    
                    # Log success
                    log_auth(self.logger, f"Successfully imported session for {phone} ({name} / @{username})")
                    self.log_activity(f"Imported session account: {phone}")
                    
                    # Start age check in background for new account
                    log_usage_checker(self.logger, f"Starting automatic age check for new session account: {phone}")
                    threading.Thread(target=self._auto_check_new_account_age, args=(phone,), daemon=True).start()
                    
                    # Update UI
                    self.update_ui()
                    
                    # Show success dialog
                    success_dialog.exec_()
                    return True
                else:
                    QMessageBox.critical(
                        self, 
                        "Import Failed", 
                        f"Failed to add account to database. The session file was valid, but there was a database error."
                    )
                    return False
            elif session_valid and not connection_error:
                # Session file structure is valid but connection test failed or timed out
                # Try to import anyway with basic validation
                try:
                    # Use phone number from filename or generate one
                    phone = phone_number
                    
                    # Store in database with minimal info
                    success = self.account_manager.add_account(
                        phone, api_id, api_hash, active=True, account_type="session"
                    )
                    
                    if success:
                        # Set status to indicate it needs validation
                        self.account_manager.update_account_status(phone, "needs_validation")
                        
                        # Show success dialog with warning
                        info_dialog = QDialog(self)
                        info_dialog.setWindowTitle("Session Import - Validation Pending")
                        info_dialog.setFixedSize(450, 250)
                        info_layout = QVBoxLayout(info_dialog)
                        
                        info_text = f"⚠️ Session imported with pending validation\n\n"
                        info_text += f"📱 Phone: {phone}\n"
                        info_text += f"The session file appears valid but could not be fully tested.\n"
                        info_text += f"The account will be validated automatically in the background.\n\n"
                        info_text += f"If validation fails, you may need to re-import the session."
                        
                        info_label = QLabel(info_text)
                        info_layout.addWidget(info_label)
                        
                        close_button = QPushButton("OK")
                        close_button.clicked.connect(info_dialog.accept)
                        info_layout.addWidget(close_button)
                        
                        # Log partial success
                        self.log_activity(f"Imported session account {phone} - validation pending")
                        
                        # Update UI
                        self.update_ui()
                        
                        # Show info dialog
                        info_dialog.exec_()
                        return True
                        
                except Exception as e:
                    connection_error = f"Database error during fallback import: {str(e)}"
            
            # Session validation failed
            if not connection_error:
                connection_error = "Session validation failed: Unable to connect to Telegram servers or authenticate session"
            
            # Show appropriate error message
            error_msg = connection_error
            
            # Provide more helpful error messages
            if "event loop" in error_msg.lower() or "thread" in error_msg.lower():
                error_msg += "\n\nThis appears to be a temporary issue. Please try again."
            elif "network" in error_msg.lower() or "connection" in error_msg.lower():
                error_msg += "\n\nPlease check your internet connection and try again."
            elif "auth key invalid" in error_msg.lower() or "unauthorized" in error_msg.lower():
                error_msg += "\n\nThe session may have expired. Please log in again to create a new session."
            
            QMessageBox.critical(
                self, 
                "Session Validation Failed", 
                f"Session validation failed: {error_msg}\n\nPlease ensure the session file is valid and not expired."
            )
            
            # Clean up the copied session file
            try:
                if os.path.exists(target_session_path):
                    os.remove(target_session_path)
            except:
                pass
                
            return False
                
        except Exception as e:
            # Clean up the copied session file on any error
            try:
                if os.path.exists(target_session_path):
                    os.remove(target_session_path)
            except:
                pass
            
            self.logger.error(f"Error importing session: {str(e)}")
            self.log_activity(f"Error importing session: {str(e)}")
            QMessageBox.critical(self, "Import Error", f"Failed to import session file: {str(e)}")
            return False

    def delete_specific_account(self, account):
        """Delete a specific account with confirmation."""
        try:
            phone = account.get("phone", "")
            if not phone:
                return
            
            # Show confirmation dialog
            reply = QMessageBox.question(
                self, 
                "Delete Account", 
                f"Are you sure you want to delete account {phone}?\n\n"
                f"This will remove the account from:\n"
                f"• Database\n"
                f"• Session files\n"
                f"• All application data\n\n"
                f"This action cannot be undone!",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.update_status_signal.emit(f"Deleting account {phone}...")
                self.log_activity_signal.emit(f"Deleting account {phone}...")
                
                # Run in background thread
                threading.Thread(target=self._delete_specific_account_thread, args=(phone,), daemon=True).start()
                
        except Exception as e:
            self.logger.error(f"Failed to delete account {account.get('phone', '')}: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to delete account: {str(e)}")
    
    def _delete_specific_account_thread(self, phone):
        """Background thread for deleting a specific account completely."""
        try:
            # Log the start of deletion
            log_auth(self.logger, f"Starting complete account deletion for {phone}")
            
            # Remove session file if it exists
            session_file_paths = [
                f"sessions/{phone}.session",
                f"sessions/{phone.replace('+', '')}.session",
                f"sessions/{phone}"
            ]
            
            for session_path in session_file_paths:
                try:
                    if os.path.exists(session_path):
                        os.remove(session_path)
                        self.log_activity_signal.emit(f"Removed session file: {session_path}")
                        log_auth(self.logger, f"Removed session file: {session_path}")
                except Exception as e:
                    self.logger.warning(f"Could not remove session file {session_path}: {str(e)}")
            
            # Remove from database
            result = self.account_manager.remove_account(phone)
            
            # Handle both boolean and tuple returns
            if isinstance(result, tuple):
                success, message = result
            else:
                success = result
                message = "Account removed" if success else "Failed to remove account"
                
            if success:
                self.log_activity_signal.emit(f"Account {phone} deleted completely from database")
                log_auth(self.logger, f"Account {phone} deleted successfully from database")
                
                # Update UI using signal
                self.update_ui_signal.emit()
                
                self.update_status_signal.emit(f"Account {phone} deleted successfully")
                self.log_activity_signal.emit(f"✅ Account {phone} has been completely removed")
            else:
                error_msg = f"Failed to delete account {phone} from database: {message}"
                self.update_status_signal.emit(error_msg)
                self.log_activity_signal.emit(error_msg)
                log_auth(self.logger, error_msg, logging.ERROR)
                
                # Show error on main thread
                QMetaObject.invokeMethod(
                    self, "show_delete_error", 
                    Qt.QueuedConnection,
                    Q_ARG(str, error_msg)
                )
                
        except Exception as e:
            error_msg = f"Error deleting account {phone}: {str(e)}"
            self.logger.error(error_msg)
            log_auth(self.logger, error_msg, logging.ERROR)
            
            self.update_status_signal.emit(error_msg)
            self.log_activity_signal.emit(error_msg)
            
            # Show error on main thread
            QMetaObject.invokeMethod(
                self, "show_delete_error", 
                Qt.QueuedConnection,
                Q_ARG(str, error_msg)
            )
    
    @pyqtSlot(str)
    def show_delete_error(self, error_msg):
        """Show delete error message on main thread."""
        QMessageBox.critical(self, "Delete Error", error_msg)

    def export_pending_groups(self):
        """Export pending groups to a file for later processing."""
        try:
            with self.account_states_lock:
                if not self.pending_groups_queue:
                    QMessageBox.information(
                        self, 
                        "No Pending Groups",
                        "There are no pending groups to export at this time."
                    )
                    return
                
                # Create the export directory if it doesn't exist
                export_dir = "Exports"
                os.makedirs(export_dir, exist_ok=True)
                
                # Generate filename with timestamp
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = os.path.join(export_dir, f"pending_groups_{timestamp}.txt")
                
                # Write the pending groups to the file
                with open(filename, "w", encoding='utf-8') as f:
                    for group in self.pending_groups_queue:
                        f.write(f"{group}\n")
                
                # Create a detailed info file
                info_filename = os.path.join(export_dir, f"pending_info_{timestamp}.txt")
                with open(info_filename, "w", encoding='utf-8') as f:
                    f.write("=== PENDING GROUPS EXPORT ===\n")
                    f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                    
                    # Account status summary
                    f.write("=== ACCOUNT STATUS ===\n")
                    status_counts = {}
                    for phone, state in self.account_states.items():
                        status = state["status"]
                        status_counts[status] = status_counts.get(status, 0) + 1
                    
                    for status, count in status_counts.items():
                        f.write(f"{status}: {count} accounts\n")
                    
                    f.write("\n=== PENDING GROUPS ===\n")
                    f.write(f"Total: {len(self.pending_groups_queue)} groups\n\n")
                    
                    # Write the list of pending groups
                    for i, group in enumerate(self.pending_groups_queue, 1):
                        f.write(f"{i}. {group}\n")
                
                self.log_activity(f"Exported {len(self.pending_groups_queue)} pending groups to {filename}")
                self.log_activity(f"Detailed export information saved to {info_filename}")
                
                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Successfully exported {len(self.pending_groups_queue)} pending groups to:\n{filename}"
                )
                
                # Open the export directory
                import webbrowser
                webbrowser.open(os.path.abspath(export_dir))
                
        except Exception as e:
            self.logger.error(f"Failed to export pending groups: {str(e)}")
            self.log_activity(f"Failed to export pending groups: {str(e)}")
            QMessageBox.critical(self, "Export Error", f"Failed to export pending groups: {str(e)}")
    
    def view_pending_groups(self):
        """Display pending groups in a dialog window."""
        try:
            with self.account_states_lock:
                if not self.pending_groups_queue:
                    QMessageBox.information(
                        self,
                        "No Pending Groups",
                        "There are no pending groups waiting to be processed."
                    )
                    return
                
                # Create a dialog to display the pending groups
                dialog = QDialog(self)
                dialog.setWindowTitle("Pending Groups")
                dialog.setMinimumSize(600, 400)
                
                layout = QVBoxLayout(dialog)
                
                # Add header with count and status
                header_label = QLabel(f"<h2>Pending Groups: {len(self.pending_groups_queue)}</h2>")
                layout.addWidget(header_label)
                
                # Add status info
                status_counts = {}
                for phone, state in self.account_states.items():
                    status = state["status"]
                    status_counts[status] = status_counts.get(status, 0) + 1
                
                status_text = "Account Status: "
                for status, count in status_counts.items():
                    status_text += f"{status}: {count}, "
                
                status_label = QLabel(status_text.rstrip(", "))
                layout.addWidget(status_label)
                
                # Add a text display for the groups
                groups_text = QTextEdit()
                groups_text.setReadOnly(True)
                
                for i, group in enumerate(self.pending_groups_queue, 1):
                    groups_text.append(f"{i}. {group}")
                
                layout.addWidget(groups_text)
                
                # Add buttons
                button_layout = QHBoxLayout()
                
                export_button = QPushButton("Export to File")
                export_button.clicked.connect(self.export_pending_groups)
                
                copy_button = QPushButton("Copy to Clipboard")
                
                def copy_to_clipboard():
                    clipboard = QApplication.clipboard()
                    text = "\n".join(self.pending_groups_queue)
                    clipboard.setText(text)
                    QMessageBox.information(dialog, "Copied", f"{len(self.pending_groups_queue)} groups copied to clipboard")
                
                copy_button.clicked.connect(copy_to_clipboard)
                
                close_button = QPushButton("Close")
                close_button.clicked.connect(dialog.accept)
                
                button_layout.addWidget(export_button)
                button_layout.addWidget(copy_button)
                button_layout.addWidget(close_button)
                
                layout.addLayout(button_layout)
                
                # Show the dialog
                dialog.exec_()
                
        except Exception as e:
            self.logger.error(f"Failed to view pending groups: {str(e)}")
            QMessageBox.critical(self, "View Error", f"Failed to view pending groups: {str(e)}")

    def _fix_db_locked_error(self):
        """Fix database locked error by running cleanup and creating a new database connection."""
        try:
            log_auth(self.logger, "Attempting to fix database locked error", logging.WARNING)
            if self.parent_app and hasattr(self.parent_app, 'account_manager'):
                # Run database cleanup to fix locking issues
                self.parent_app.account_manager.clean_database()
                log_auth(self.logger, "Database cleanup completed", logging.INFO)
                return True
            else:
                log_auth(self.logger, "Cannot clean database - parent reference not found", logging.ERROR)
                return False
        except Exception as e:
            log_auth(self.logger, f"Error fixing database locked error: {str(e)}", logging.ERROR)
            return False

    def on_theme_changed(self):
        """Handle theme change when checkbox is toggled."""
        if self.dark_mode_check.isChecked():
            self.set_dark_mode()
            if hasattr(self, 'theme_toggle_button'):
                self.theme_toggle_button.setText("☀️ Light Theme")
        else:
            self.set_light_mode()
            if hasattr(self, 'theme_toggle_button'):
                self.theme_toggle_button.setText("🌙 Dark Theme")
    
    def toggle_theme(self):
        """Toggle between dark and light theme."""
        current_state = self.dark_mode_check.isChecked()
        self.dark_mode_check.setChecked(not current_state)
        # The on_theme_changed method will be called automatically
    
    def set_dark_mode(self):
        """Set the application to dark mode."""
        dark_style = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        
        QTabWidget::pane {
            border: 1px solid #555555;
            background-color: #2b2b2b;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #3c3c3c;
            color: #ffffff;
            border: 1px solid #555555;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #404040;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            background-color: #353535;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #ffffff;
        }
        
        QLabel {
            color: #ffffff;
            background-color: transparent;
        }
        """
        
        self.setStyleSheet(dark_style)
        self.log_activity("Dark theme applied")
    
    def set_light_mode(self):
        """Set the application to light mode."""
        light_style = """
        QMainWindow {
            background-color: #ffffff;
            color: #000000;
        }
        
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: #ffffff;
        }
        
        QTabWidget::tab-bar {
            left: 5px;
        }
        
        QTabBar::tab {
            background-color: #f0f0f0;
            color: #000000;
            border: 1px solid #cccccc;
            padding: 8px 16px;
            margin-right: 2px;
        }
        
        QTabBar::tab:selected {
            background-color: #0078d4;
            color: #ffffff;
            border-bottom: 2px solid #0078d4;
        }
        
        QTabBar::tab:hover {
            background-color: #e6e6e6;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            background-color: #f9f9f9;
            color: #000000;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 10px 0 10px;
            color: #000000;
        }
        
        QLabel {
            color: #000000;
            background-color: transparent;
        }
        """
        
        self.setStyleSheet(light_style)
        self.log_activity("Light theme applied")

    def update_ui(self):
        """Update the user interface with current data."""
        try:
            # Refresh accounts table
            accounts = self.account_manager.get_accounts()
            self.update_accounts_table(accounts)
            
            # Update dashboard counters
            active_accounts = self.account_manager.get_active_accounts()
            self.active_accounts_label.setText(str(len(active_accounts)))
            self.total_accounts_label.setText(str(len(accounts)))
            
            # Update monitor status
            monitor_status = "Running" if self.monitor and self.monitor.is_running() else "Stopped"
            self.monitor_status_label.setText(monitor_status)
            
            # Update UI based on the current theme
            self.on_theme_changed()
            
        except Exception as e:
            self.logger.error(f"Error updating UI: {str(e)}")
    
    def update_accounts_table(self, accounts):
        """Update the accounts table with the given list of accounts."""
        self.accounts_table.setRowCount(0)
        for account in accounts:
            self.add_account_to_table(account)
    
    def add_account_to_table(self, account):
        """Add a single account to the accounts table."""
        row_position = self.accounts_table.rowCount() - 1
        self.accounts_table.insertRow(row_position)
        self.accounts_table.setItem(row_position, 0, QTableWidgetItem(account['phone']))
        self.accounts_table.setItem(row_position, 1, QTableWidgetItem(f"{account['name']} / {account['username']}"))
        self.accounts_table.setItem(row_position, 2, QTableWidgetItem("Yes" if account['active'] else "No"))
        self.accounts_table.setItem(row_position, 3, QTableWidgetItem(account['last_check']))
        self.accounts_table.setItem(row_position, 4, QTableWidgetItem(str(account['errors'])))
        self.accounts_table.setItem(row_position, 5, QTableWidgetItem(account['status']))
        self.accounts_table.setItem(row_position, 6, QTableWidgetItem(account['actions']))

    def fix_account(self):
        """Fix the selected account."""
        selected_rows = self.account_table.selectedItems()
        if not selected_rows:
            QMessageBox.warning(self, "Warning", "Please select an account to fix")
            return
        
        # Get phone number from the first column of the selected row
        phone = self.account_table.item(selected_rows[0].row(), 1).text()
        
        self.update_status_signal.emit(f"Fixing account {phone}...")
        self.log_activity_signal.emit(f"Fixing account {phone}...")
        
        # Run in background thread
        threading.Thread(target=self._fix_account_thread, args=(phone,), daemon=True).start()
    
    def _fix_account_thread(self, phone):
        """Background thread for fixing an account."""
        try:
            # First, get the account to see its current status
            accounts = self.account_manager.get_accounts()
            account = None
            for acc in accounts:
                if acc.get("phone") == phone:
                    account = acc
                    break
            
            if not account:
                self.update_status_signal.emit(f"Account {phone} not found")
                self.log_activity_signal.emit(f"Account {phone} not found")
                return
            
            # Try to create a client and connect
            client = None
            try:
                # Create client and try to connect
                client = TelegramClient(
                    f"sessions/{phone}", 
                    account.get("api_id"), 
                    account.get("api_hash")
                )
                
                # Try to connect
                client.connect()
                
                if client.is_connected():
                    # Check if we can get user info
                    if client.is_user_authorized():
                        # Get user info and update account
                        me = client.get_me()
                        
                        if me:
                            name = f"{me.first_name or ''} {me.last_name or ''}".strip()
                            username = me.username or ""
                            
                            # Update account info
                            self.account_manager.update_account_info(phone, name=name, username=username)
                            self.account_manager.update_account_status(phone, "active")
                            
                            self.update_ui_signal.emit()
                            self.update_status_signal.emit(f"Account {phone} fixed")
                            self.log_activity_signal.emit(f"Account {phone} has been fixed")
                        else:
                            self.update_status_signal.emit(f"Could not get user info for {phone}")
                            self.log_activity_signal.emit(f"Could not get user info for {phone}")
                    else:
                        self.update_status_signal.emit(f"Account {phone} is not authorized")
                        self.log_activity_signal.emit(f"Account {phone} is not authorized")
                else:
                    self.update_status_signal.emit(f"Could not connect to Telegram for {phone}")
                    self.log_activity_signal.emit(f"Could not connect to Telegram for {phone}")
            finally:
                if client and client.is_connected():
                    client.disconnect()
            
        except Exception as e:
            self.update_status_signal.emit(f"Fix error: {str(e)}")
            self.logger.error(f"Account fix error for {phone}: {str(e)}")
            self.log_activity_signal.emit(f"Account fix failed for {phone}: {str(e)}")

    def activate_all_accounts(self):
        """Activate all accounts."""
        self.update_status_signal.emit("Activating all accounts...")
        self.log_activity_signal.emit("Activating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._activate_all_accounts_thread, daemon=True).start()
    
    def _activate_all_accounts_thread(self):
        """Background thread for activating all accounts."""
        try:
            self.account_manager.activate_all()
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit("All accounts activated")
            self.log_activity_signal.emit("All accounts have been activated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Activation error: {str(e)}")
            self.logger.error(f"Account activation error: {str(e)}")
            self.log_activity_signal.emit(f"Account activation failed: {str(e)}")
    
    def deactivate_all_accounts(self):
        """Deactivate all accounts."""
        self.update_status_signal.emit("Deactivating all accounts...")
        self.log_activity_signal.emit("Deactivating all accounts...")
        
        # Run in background thread
        threading.Thread(target=self._deactivate_all_accounts_thread, daemon=True).start()
    
    def _deactivate_all_accounts_thread(self):
        """Background thread for deactivating all accounts."""
        try:
            self.account_manager.deactivate_all()
            
            # Update UI using signal
            self.update_ui_signal.emit()
            
            self.update_status_signal.emit("All accounts deactivated")
            self.log_activity_signal.emit("All accounts have been deactivated")
            
        except Exception as e:
            self.update_status_signal.emit(f"Deactivation error: {str(e)}")
            self.logger.error(f"Account deactivation error: {str(e)}")
            self.log_activity_signal.emit(f"Account deactivation failed: {str(e)}")

    def apply_monitor_settings(self):
        """Apply the monitor settings."""
        try:
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # Save filter settings
            self.settings.setValue("min_members", self.min_members_spin.value())
            self.settings.setValue("min_message_time", self.min_message_time_spin.value())
            self.settings.setValue("min_total_messages", self.min_total_messages_spin.value())
            
            # If monitor is running, restart it with new settings
            if self.monitor.is_running:
                self.stop_monitor()
                self.start_monitor()
            
            self.status_label.setText("Monitor settings applied")
            self.log_activity("Monitor settings applied")
            
        except Exception as e:
            self.logger.error(f"Failed to apply monitor settings: {str(e)}")
            self.log_activity(f"Failed to apply monitor settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to apply monitor settings: {str(e)}")

    def export_logs(self):
        """Export logs to a file."""
        file_path, _ = QFileDialog.getSaveFileName(self, "Export Logs", "", "Log Files (*.log);;Text Files (*.txt);;All Files (*)")
        if file_path:
            try:
                with open(file_path, "w") as f:
                    f.write(self.log_display.toPlainText())
                self.update_status_signal.emit(f"Logs exported to {file_path}")
            except Exception as e:
                self.logger.error(f"Failed to export logs: {str(e)}")
                QMessageBox.critical(self, "Error", f"Failed to export logs: {str(e)}")

    def save_settings(self):
        """Save settings to the configuration file."""
        try:
            # Save general settings
            self.settings.setValue("auto_start_monitor", self.auto_start_check.isChecked())
            self.settings.setValue("dark_mode", self.dark_mode_check.isChecked())
            
            # Save monitor settings
            self.settings.setValue("check_interval", self.check_interval_spin.value())
            self.settings.setValue("auto_fix", self.auto_fix_check.isChecked())
            
            # Save filter settings
            self.settings.setValue("min_members", self.min_members_spin.value())
            self.settings.setValue("min_message_time", self.min_message_time_spin.value())
            self.settings.setValue("min_total_messages", self.min_total_messages_spin.value())
            
            self.update_status_signal.emit("Settings saved")
            self.log_activity_signal.emit("Settings saved")
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
            else:
                self.set_light_mode()
                
        except Exception as e:
            self.logger.error(f"Failed to save settings: {str(e)}")
            self.log_activity_signal.emit(f"Failed to save settings: {str(e)}")
            QMessageBox.critical(self, "Error", f"Failed to save settings: {str(e)}")
    
    def load_settings(self):
        """Load settings from the configuration file."""
        try:
            # Load general settings
            self.auto_start_check.setChecked(self.settings.value("auto_start_monitor", False, type=bool))
            self.dark_mode_check.setChecked(self.settings.value("dark_mode", False, type=bool))
            
            # Load monitor settings
            self.check_interval_spin.setValue(self.settings.value("check_interval", 5, type=int))
            self.auto_fix_check.setChecked(self.settings.value("auto_fix", True, type=bool))
            
            # Load filter settings
            self.min_members_spin.setValue(self.settings.value("min_members", 500, type=int))
            self.min_message_time_spin.setValue(self.settings.value("min_message_time", 1, type=int))
            self.min_total_messages_spin.setValue(self.settings.value("min_total_messages", 100, type=int))
            
            # Apply settings that can be applied immediately
            if self.settings.value("dark_mode", False, type=bool):
                self.set_dark_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("☀️ Light Theme")
            else:
                self.set_light_mode()
                if hasattr(self, 'theme_toggle_button'):
                    self.theme_toggle_button.setText("🌙 Dark Theme")
                
        except Exception as e:
            self.logger.error(f"Failed to load settings: {str(e)}")

    def log_activity(self, message):
        """Log an activity to the activities text box (legacy method for compatibility)."""
        # Use the signal for thread-safe logging
        self.log_activity_signal.emit(message)

def main():
    """Main application entry point."""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # Modern style
    
    # Set application metadata
    app.setApplicationName("TG Checker")
    app.setApplicationVersion("1.0")
    app.setOrganizationName("TG Tools")
    
    # Create and show main window
    window = TGCheckerApp()
    window.show()
    
    # Start the application
    sys.exit(app.exec_())


    def update_status(self, status):
        """Update the status label."""
        if hasattr(self, 'status_label'):
            self.status_label.setText(f"Status: {status}")
        

if __name__ == "__main__":
    main() 