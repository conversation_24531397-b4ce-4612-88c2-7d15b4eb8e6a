﻿"""
High-Performance Concurrency Manager for TG Checker
Handles 100+ accounts with optimal resource utilization
"""

import asyncio
import threading
import time
import queue
import psutil
import logging
import gc
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Callable, Any, Optional
from dataclasses import dataclass
from collections import deque
from threading import RLock, Event
import sqlite3
from contextlib import contextmanager
import weakref

@dataclass
class TaskConfig:
    """Configuration for different task types"""
    max_workers: int
    priority: int
    timeout: int
    retry_count: int
    memory_limit_mb: int

class ResourceMonitor:
    """Monitors and manages system resources"""
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.memory_threshold = 1024 * 1024 * 1024  # 1GB
        self.cpu_threshold = 80.0  # 80%
        self.monitoring = False
        self._monitor_thread = None
        
    def start_monitoring(self):
        """Start resource monitoring"""
        if not self.monitoring:
            self.monitoring = True
            self._monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self._monitor_thread.start()
            
    def stop_monitoring(self):
        """Stop resource monitoring"""
        self.monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1)
            
    def _monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring:
            try:
                memory_usage = psutil.Process().memory_info().rss
                cpu_percent = psutil.cpu_percent()
                
                if memory_usage > self.memory_threshold:
                    self.logger.warning(f"High memory usage: {memory_usage / 1024 / 1024:.1f}MB")
                    self._trigger_cleanup()
                    
                if cpu_percent > self.cpu_threshold:
                    self.logger.warning(f"High CPU usage: {cpu_percent:.1f}%")
                    
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Resource monitoring error: {e}")
                
    def _trigger_cleanup(self):
        """Trigger garbage collection and cleanup"""
        gc.collect()
        self.logger.info("Triggered garbage collection")

class DatabasePool:
    """Database connection pool for concurrent access"""
    
    def __init__(self, db_path: str, max_connections: int = 20):
        self.db_path = db_path
        self.max_connections = max_connections
        self._pool = queue.Queue(maxsize=max_connections)
        self._lock = RLock()
        self._total_connections = 0
        
        # Pre-populate pool
        for _ in range(5):  # Start with 5 connections
            self._create_connection()
            
    def _create_connection(self):
        """Create a new database connection"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA busy_timeout=30000")
        conn.execute("PRAGMA synchronous=NORMAL")
        self._pool.put(conn)
        self._total_connections += 1
        
    @contextmanager
    def get_connection(self):
        """Get a connection from the pool"""
        conn = None
        try:
            # Try to get existing connection
            conn = self._pool.get_nowait()
        except queue.Empty:
            with self._lock:
                if self._total_connections < self.max_connections:
                    self._create_connection()
                    conn = self._pool.get_nowait()
                else:
                    # Wait for available connection
                    conn = self._pool.get(timeout=10)
                    
        try:
            yield conn
        finally:
            if conn:
                self._pool.put(conn)

class AsyncTaskManager:
    """Async task manager with proper concurrency control"""
    
    def __init__(self, logger=None):
        self.logger = logger or logging.getLogger(__name__)
        self.task_configs = {
            'join': TaskConfig(max_workers=50, priority=1, timeout=30, retry_count=3, memory_limit_mb=100),
            'check': TaskConfig(max_workers=30, priority=2, timeout=60, retry_count=2, memory_limit_mb=150),
            'forward': TaskConfig(max_workers=20, priority=1, timeout=45, retry_count=3, memory_limit_mb=80),
            'sync': TaskConfig(max_workers=10, priority=3, timeout=120, retry_count=1, memory_limit_mb=200)
        }
        
        self._executors = {}
        self._task_queues = {}
        self._active_tasks = {}
        self._task_results = {}
        self._shutdown_event = Event()
        
        # Initialize executors for each task type
        for task_type, config in self.task_configs.items():
            self._executors[task_type] = ThreadPoolExecutor(
                max_workers=config.max_workers,
                thread_name_prefix=f"TG-{task_type}"
            )
            self._task_queues[task_type] = queue.PriorityQueue()
            self._active_tasks[task_type] = set()
            
        # Start task processors
        self._start_processors()
        
    def _start_processors(self):
        """Start task processor threads"""
        for task_type in self.task_configs:
            processor_thread = threading.Thread(
                target=self._process_tasks,
                args=(task_type,),
                daemon=True,
                name=f"Processor-{task_type}"
            )
            processor_thread.start()
            
    def _process_tasks(self, task_type: str):
        """Process tasks for a specific type"""
        executor = self._executors[task_type]
        task_queue = self._task_queues[task_type]
        
        while not self._shutdown_event.is_set():
            try:
                # Get task from queue
                try:
                    priority, task_id, func, args, kwargs, callback = task_queue.get(timeout=1)
                except queue.Empty:
                    continue
                    
                # Submit to executor
                future = executor.submit(self._execute_task, task_type, task_id, func, args, kwargs)
                
                # Handle completion
                def handle_completion(fut):
                    try:
                        result = fut.result()
                        self._active_tasks[task_type].discard(task_id)
                        if callback:
                            callback(result)
                    except Exception as e:
                        self.logger.error(f"Task {task_id} failed: {e}")
                        
                future.add_done_callback(handle_completion)
                self._active_tasks[task_type].add(task_id)
                
            except Exception as e:
                self.logger.error(f"Task processor error for {task_type}: {e}")
                
    def _execute_task(self, task_type: str, task_id: str, func: Callable, args: tuple, kwargs: dict):
        """Execute a single task with monitoring"""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            
            # Log performance metrics
            duration = time.time() - start_time
            memory_used = psutil.Process().memory_info().rss - start_memory
            
            self.logger.debug(f"Task {task_id} completed in {duration:.2f}s, memory: {memory_used/1024/1024:.1f}MB")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Task {task_id} execution failed: {e}")
            raise
            
    def submit_task(self, task_type: str, task_id: str, func: Callable, 
                   args: tuple = (), kwargs: dict = None, priority: int = None, callback: Callable = None):
        """Submit a task for execution"""
        if kwargs is None:
            kwargs = {}
            
        if task_type not in self.task_configs:
            raise ValueError(f"Unknown task type: {task_type}")
            
        config = self.task_configs[task_type]
        task_priority = priority if priority is not None else config.priority
        
        self._task_queues[task_type].put((task_priority, task_id, func, args, kwargs, callback))
        self.logger.debug(f"Submitted task {task_id} of type {task_type}")
        
    def get_statistics(self) -> Dict:
        """Get performance statistics"""
        stats = {}
        for task_type in self.task_configs:
            stats[task_type] = {
                'active_tasks': len(self._active_tasks[task_type]),
                'queued_tasks': self._task_queues[task_type].qsize(),
                'max_workers': self.task_configs[task_type].max_workers
            }
        return stats
        
    def shutdown(self):
        """Shutdown all executors"""
        self._shutdown_event.set()
        for executor in self._executors.values():
            executor.shutdown(wait=True)

class PerformanceManager:
    """Main performance manager coordinating all components"""
    
    def __init__(self, db_path: str = "tg_checker.db", logger=None):
        self.logger = logger or logging.getLogger(__name__)
        
        # Initialize components
        self.resource_monitor = ResourceMonitor(logger)
        self.db_pool = DatabasePool(db_path)
        self.task_manager = AsyncTaskManager(logger)
        
        # Account management
        self.account_workers = {}
        self.account_states = {}
        self.account_locks = {}
        
        # Performance metrics
        self.metrics = {
            'tasks_completed': 0,
            'tasks_failed': 0,
            'avg_task_time': 0,
            'peak_memory': 0,
            'start_time': time.time()
        }
        
        # Start monitoring
        self.resource_monitor.start_monitoring()
        
    def register_account_worker(self, phone: str, worker_count: int = 5):
        """Register workers for a specific account"""
        self.account_workers[phone] = {
            'count': worker_count,
            'active': 0,
            'queued': 0
        }
        self.account_states[phone] = 'available'
        self.account_locks[phone] = RLock()
        
    def submit_account_task(self, phone: str, task_type: str, func: Callable,
                          args: tuple = (), kwargs: dict = None, priority: int = None):
        """Submit a task for a specific account"""
        if phone not in self.account_workers:
            self.register_account_worker(phone)
            
        task_id = f"{phone}_{task_type}_{int(time.time()*1000)}"
        
        # Wrap function to handle account state
        def wrapped_func(*args, **kwargs):
            with self.account_locks[phone]:
                self.account_states[phone] = 'busy'
                self.account_workers[phone]['active'] += 1
                
            try:
                return func(*args, **kwargs)
            finally:
                with self.account_locks[phone]:
                    self.account_workers[phone]['active'] -= 1
                    if self.account_workers[phone]['active'] == 0:
                        self.account_states[phone] = 'available'
                        
        self.task_manager.submit_task(task_type, task_id, wrapped_func, args, kwargs, priority)
        
    def bulk_submit_tasks(self, tasks: List[Dict]):
        """Submit multiple tasks in bulk"""
        for task in tasks:
            self.submit_account_task(
                phone=task['phone'],
                task_type=task['type'],
                func=task['func'],
                args=task.get('args', ()),
                kwargs=task.get('kwargs', {}),
                priority=task.get('priority')
            )
            
    def get_db_connection(self):
        """Get database connection from pool"""
        return self.db_pool.get_connection()
        
    def get_performance_report(self) -> Dict:
        """Get comprehensive performance report"""
        current_memory = psutil.Process().memory_info().rss / 1024 / 1024
        uptime = time.time() - self.metrics['start_time']
        
        return {
            'uptime_seconds': uptime,
            'current_memory_mb': current_memory,
            'peak_memory_mb': self.metrics['peak_memory'],
            'tasks_completed': self.metrics['tasks_completed'],
            'tasks_failed': self.metrics['tasks_failed'],
            'avg_task_time': self.metrics['avg_task_time'],
            'task_stats': self.task_manager.get_statistics(),
            'account_workers': self.account_workers,
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent
        }
        
    def optimize_resources(self):
        """Optimize resource usage"""
        # Trigger garbage collection
        gc.collect()
        
        # Log current state
        report = self.get_performance_report()
        self.logger.info(f"Resource optimization - Memory: {report['current_memory_mb']:.1f}MB, "
                        f"CPU: {report['cpu_percent']:.1f}%, Active tasks: {sum(stats['active_tasks'] for stats in report['task_stats'].values())}")
        
    def shutdown(self):
        """Shutdown performance manager"""
        self.logger.info("Shutting down performance manager...")
        self.resource_monitor.stop_monitoring()
        self.task_manager.shutdown()

# Singleton instance
_performance_manager = None

def get_performance_manager(db_path: str = "tg_checker.db", logger=None) -> PerformanceManager:
    """Get global performance manager instance"""
    global _performance_manager
    if _performance_manager is None:
        _performance_manager = PerformanceManager(db_path, logger)
    return _performance_manager
