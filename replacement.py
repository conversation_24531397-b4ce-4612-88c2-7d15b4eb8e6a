#!/usr/bin/env python3
"""
REPLACEMENT FIX: Provides a direct replacement for the problematic function
"""

import os
import shutil
import re
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def replace_function():
    """Replace the problematic function with a corrected version."""
    try:
        # Read the file content
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Define the problematic function pattern
        pattern = r"def stop_joining_task\(self, task_id\):.*?(?=\n    def|\Z)"
        
        # Find the function in the file
        match = re.search(pattern, content, re.DOTALL)
        if not match:
            print("⚠️ Could not find the stop_joining_task function")
            return False
        
        # Get the matched function
        original_function = match.group(0)
        print(f"Found the problematic function ({len(original_function)} characters)")
        
        # Define the replacement with proper indentation
        replacement = """    def stop_joining_task(self, task_id):
        """Stop a specific joining task with high-performance system."""
        try:
            if hasattr(self, 'active_hp_tasks') and task_id in self.active_hp_tasks:
                self.active_hp_tasks[task_id]['stop_requested'] = True
                self.logger.info(f"Stop requested for joining task: {task_id}")
                return True
            else:
                self.logger.warning(f"Cannot stop task {task_id}: Task not found in active tasks")
                return False
        except Exception as e:
            self.logger.error(f"Error stopping joining task {task_id}: {str(e)}")
            return False"""
        
        # Replace the function in the content
        new_content = content.replace(original_function, replacement)
        
        # Write the fixed content back
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(new_content)
        
        print(f"✅ Replaced the problematic function with a properly indented version")
        return True
    except Exception as e:
        print(f"❌ Error replacing function: {str(e)}")
        return False

def main():
    print("🚨 REPLACEMENT FIX: Replacing the problematic function...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply replacement
    fixed = replace_function()
    
    if fixed:
        print("\n✅ Function replacement completed successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fix was applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 