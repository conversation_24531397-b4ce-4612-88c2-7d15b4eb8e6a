import os

def update_settings():
    with open("settings.py", "r") as f:
        content = f.read()
    
    # Update min_members and max_last_message_hours
    content = content.replace('"min_members": 1000', '"min_members": 500')
    content = content.replace('"max_last_message_hours": 24', '"max_last_message_hours": 1')
    
    # Add min_total_messages if not already present
    if '"min_total_messages"' not in content:
        content = content.replace(
            '"max_last_message_hours": 1,', 
            '"max_last_message_hours": 1,\n    "min_total_messages": 100,'
        )
    
    # Add results_dir if not already present
    if '"results_dir"' not in content:
        content = content.replace(
            '"backup_dir": "backups"', 
            '"backup_dir": "backups",\n    "results_dir": "Results"'
        )
    
    # Write the updated content back to the file
    with open("settings.py", "w") as f:
        f.write(content)
    
    print("Settings updated successfully")

def update_group_checker():
    with open("group_checker.py", "r") as f:
        content = f.read()
    
    # Update min_members, max_last_message_hours and default values
    content = content.replace('self.min_members = settings.get("min_members", 1000)', 
                            'self.min_members = settings.get("min_members", 500)')
    content = content.replace('self.max_last_message_hours = settings.get("max_last_message_hours", 24)', 
                            'self.max_last_message_hours = settings.get("max_last_message_hours", 1)')
    
    # Add min_total_messages
    if 'self.min_total_messages' not in content:
        content = content.replace(
            'self.max_last_message_hours = settings.get("max_last_message_hours", 1)', 
            'self.max_last_message_hours = settings.get("max_last_message_hours", 1)\n        self.min_total_messages = settings.get("min_total_messages", 100)'
        )
    
    # Update member_count default
    content = content.replace('member_count = 1000  # Default to avoid false negatives', 
                            'member_count = 500  # Default to avoid false negatives')
    
    # Add total_messages field to result dictionary
    if '"total_messages"' not in content:
        content = content.replace(
            '"is_channel": hasattr(group, \'broadcast\') and group.broadcast', 
            '"is_channel": hasattr(group, \'broadcast\') and group.broadcast,\n                "total_messages": 0'
        )
    
    # Add total_messages counting
    if 'result["total_messages"]' not in content:
        content = content.replace(
            'messages = await self.client.get_messages(group, limit=self.history_limit)', 
            'messages = await self.client.get_messages(group, limit=self.history_limit)\n                    result["total_messages"] = len(messages)'
        )
    
    # Add total_messages filter
    if 'Filter by total messages' not in content:
        content = content.replace(
            '# Filter by language', 
            '# Filter by total messages\n        if result["total_messages"] < self.min_total_messages:\n            result["status"] = "invalid" \n            result["error"] = f"Group has too few messages: {result[\'total_messages\']} < {self.min_total_messages}"\n            self.results["non_filter_groups"].append(result)\n            return False\n            \n        # Filter by language'
        )
    
    # Update save_results output directory and file names
    content = content.replace('def save_results(self, output_dir="results"):', 
                            'def save_results(self, output_dir="Results"):')
    
    # Update to write specific file formats
    if 'GroupsVaild_Filter_On.txt' not in content:
        content = content.replace(
            '# Save all categories\n        for category, groups in self.results.items():\n            if groups:\n                filename = os.path.join(output_dir, f"{category}.json")\n                with open(filename, \'w\', encoding=\'utf-8\') as f:\n                    json.dump(groups, f, ensure_ascii=False, indent=2)\n                logger.info(f"Saved {len(groups)} groups to {filename}")', 
            '# Save categories to specific files\n        if self.results["good_groups"]:\n            with open(os.path.join(output_dir, "GroupsVaild_Filter_On.txt"), \'w\', encoding=\'utf-8\') as f:\n                for group in self.results["good_groups"]:\n                    f.write(f"https://t.me/{group[\'group_id\']}\\n")\n            logger.info(f"Saved {len(self.results[\'good_groups\'])} groups to GroupsVaild_Filter_On.txt")\n            \n        if self.results["non_filter_groups"]:\n            with open(os.path.join(output_dir, "GroupsVaildOnly.txt"), \'w\', encoding=\'utf-8\') as f:\n                for group in self.results["non_filter_groups"]:\n                    f.write(f"https://t.me/{group[\'group_id\']}\\n")\n            logger.info(f"Saved {len(self.results[\'non_filter_groups\'])} groups to GroupsVaildOnly.txt")\n            \n        if self.results["topics"]:\n            with open(os.path.join(output_dir, "TopicsGroups.txt"), \'w\', encoding=\'utf-8\') as f:\n                for group in self.results["topics"]:\n                    f.write(f"https://t.me/{group[\'group_id\']}\\n")\n            logger.info(f"Saved {len(self.results[\'topics\'])} groups to TopicsGroups.txt")\n            \n        if self.results["channels"]:\n            with open(os.path.join(output_dir, "Channels.txt"), \'w\', encoding=\'utf-8\') as f:\n                for group in self.results["channels"]:\n                    f.write(f"https://t.me/{group[\'group_id\']}\\n")\n            logger.info(f"Saved {len(self.results[\'channels\'])} groups to Channels.txt")\n            \n        if self.results["broke_groups"]:\n            with open(os.path.join(output_dir, "InvalidGroups_Channels.txt"), \'w\', encoding=\'utf-8\') as f:\n                for group in self.results["broke_groups"]:\n                    f.write(f"https://t.me/{group[\'group_id\']}\\n")\n            logger.info(f"Saved {len(self.results[\'broke_groups\'])} groups to InvalidGroups_Channels.txt")'
        )
    
    with open("group_checker.py", "w") as f:
        f.write(content)
    
    print("Group checker updated successfully")

def update_monitor():
    with open("monitor.py", "r") as f:
        content = f.read()
    
    # Ensure auto_fix is always True in full-sync-mode
    content = content.replace('self.auto_fix = True', 'self.auto_fix = True  # Always auto-fix by default')
    
    # Add code to ensure auto_fix is always enabled
    if 'Always ensure auto_fix is enabled' not in content:
        content = content.replace(
            'if sync_callback:\n            self.sync_callback = sync_callback', 
            'if sync_callback:\n            self.sync_callback = sync_callback\n        \n        # Always ensure auto_fix is enabled with full-sync-mode    \n        self.auto_fix = True'
        )
    
    # Make sure auto_fix is always used
    content = content.replace(
        '# Auto-fix accounts if enabled\n                if self.auto_fix:', 
        '# Auto-fix accounts is always enabled in full-sync-mode'
    )
    
    # Update set_auto_fix method to prevent disabling
    if 'In full-sync-mode, auto-fix is always enabled' not in content:
        content = content.replace(
            'def set_auto_fix(self, enabled):\n        """Enable or disable automatic fixing of accounts."""\n        self.auto_fix = enabled\n        self.logger.info(f"Auto-fix accounts set to: {enabled}")', 
            'def set_auto_fix(self, enabled):\n        """Enable or disable automatic fixing of accounts."""\n        # In full-sync-mode, auto-fix is always enabled\n        if not enabled:\n            self.logger.info("Auto-fix cannot be disabled in full-sync-mode")\n            return\n            \n        self.auto_fix = True\n        self.logger.info("Auto-fix accounts enabled (required for full-sync-mode)")'
        )
    
    with open("monitor.py", "w") as f:
        f.write(content)
    
    print("Monitor updated successfully")

def update_group_checker_tab():
    with open("group_checker_tab.py", "r") as f:
        content = f.read()
    
    # Add QTabWidget import if not already present
    if 'QTabWidget' not in content:
        content = content.replace(
            'QListWidget, QListWidgetItem, QProgressBar)', 
            'QListWidget, QListWidgetItem, QProgressBar, QTabWidget)'
        )
    
    # Update min_members and max_last_message_hours default values
    content = content.replace('self.min_members_spin.setValue(self.settings.get("min_members", 1000))', 
                            'self.min_members_spin.setValue(self.settings.get("min_members", 500))')
    content = content.replace('self.max_hours_spin.setValue(self.settings.get("max_last_message_hours", 24))', 
                            'self.max_hours_spin.setValue(self.settings.get("max_last_message_hours", 1))')
    
    # Add min_total_messages settings
    if 'self.min_messages_spin = QSpinBox()' not in content:
        content = content.replace(
            '# Language filter', 
            '# Min total messages\n        self.min_messages_spin = QSpinBox()\n        self.min_messages_spin.setRange(0, 10000)\n        self.min_messages_spin.setValue(self.settings.get("min_total_messages", 100))\n        filter_layout.addRow("Min Total Messages:", self.min_messages_spin)\n        \n        # Language filter'
        )
    
    # Update layout to include dashboard structure
    if 'dashboard_group' not in content:
        # Replace the entire setup_ui method with the new one
        start_idx = content.find('def setup_ui(self):')
        end_idx = content.find('def create_result_tab(self', start_idx)
        if end_idx == -1:  # if create_result_tab doesn't exist
            end_idx = content.find('def update_account_list(self)', start_idx)
        
        if start_idx != -1 and end_idx != -1:
            setup_ui_method = """def setup_ui(self):
        \"\"\"Set up the user interface.\"\"\"
        main_layout = QVBoxLayout(self)
        
        # Create dashboard section
        dashboard_group = QGroupBox("Dashboard")
        dashboard_layout = QVBoxLayout()
        
        # Start checker button
        self.start_checker_button = QPushButton("Start Checker")
        self.start_checker_button.setMinimumHeight(40)
        self.start_checker_button.setStyleSheet("font-weight: bold; font-size: 14px;")
        self.start_checker_button.clicked.connect(self.start_checking)
        dashboard_layout.addWidget(self.start_checker_button)
        
        # Add Groups section
        groups_layout = QHBoxLayout()
        
        self.group_links_input = QTextEdit()
        self.group_links_input.setPlaceholderText("Enter group links, one per line.\\nExample: https://t.me/example or @example")
        groups_layout.addWidget(self.group_links_input, 3)
        
        # Currently being analyzed
        current_layout = QVBoxLayout()
        current_layout.addWidget(QLabel("Currently Being Analyzed:"))
        self.current_group_label = QLabel("None")
        self.current_group_label.setStyleSheet("font-weight: bold;")
        current_layout.addWidget(self.current_group_label)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        current_layout.addWidget(self.progress_bar)
        
        # Add buttons for loading and clearing links
        btn_layout = QHBoxLayout()
        self.load_links_button = QPushButton("Load from File")
        self.clear_links_button = QPushButton("Clear Links")
        self.stop_button = QPushButton("Stop")
        
        self.load_links_button.clicked.connect(self.load_links_from_file)
        self.clear_links_button.clicked.connect(self.clear_links)
        self.stop_button.clicked.connect(self.stop_checking)
        
        self.stop_button.setEnabled(False)
        
        btn_layout.addWidget(self.load_links_button)
        btn_layout.addWidget(self.clear_links_button)
        btn_layout.addWidget(self.stop_button)
        current_layout.addLayout(btn_layout)
        
        groups_layout.addLayout(current_layout, 2)
        dashboard_layout.addLayout(groups_layout)
        
        dashboard_group.setLayout(dashboard_layout)
        main_layout.addWidget(dashboard_group)
        
        # Create splitter for settings and results
        splitter = QSplitter(Qt.Horizontal)
        
        # Settings section (left panel)
        settings_widget = QWidget()
        settings_layout = QVBoxLayout(settings_widget)
        
        # Filter settings
        filter_group = QGroupBox("Filter Settings")
        filter_layout = QFormLayout()
        
        # Min members
        self.min_members_spin = QSpinBox()
        self.min_members_spin.setRange(0, 1000000)
        self.min_members_spin.setValue(self.settings.get("min_members", 500))
        filter_layout.addRow("Min Members:", self.min_members_spin)
        
        # Min chat message time (renamed from max_last_message_hours)
        self.max_hours_spin = QSpinBox()
        self.max_hours_spin.setRange(1, 720)
        self.max_hours_spin.setValue(self.settings.get("max_last_message_hours", 1))
        filter_layout.addRow("Min Chat Message Time (hours):", self.max_hours_spin)
        
        # Min total messages
        self.min_messages_spin = QSpinBox()
        self.min_messages_spin.setRange(0, 10000)
        self.min_messages_spin.setValue(self.settings.get("min_total_messages", 100))
        filter_layout.addRow("Min Total Messages:", self.min_messages_spin)
        
        # Language filter
        self.language_combo = QComboBox()
        self.language_combo.addItem("All Languages", "all")
        for lang_code in self.settings.get("supported_languages", ["en", "ar", "ru", "es", "fr", "de", "zh", "hi", "pt", "tr", "ku"]):
            self.language_combo.addItem(lang_code.upper(), lang_code)
        self.language_combo.setCurrentText(self.settings.get("language_filter", "all").upper())
        filter_layout.addRow("Language Filter:", self.language_combo)
        
        # Additional checks
        self.public_only_check = QCheckBox()
        self.public_only_check.setChecked(self.settings.get("public_groups_only", True))
        filter_layout.addRow("Public Groups Only:", self.public_only_check)
        
        self.check_post_freq_check = QCheckBox()
        self.check_post_freq_check.setChecked(self.settings.get("check_post_frequency", True))
        filter_layout.addRow("Check Post Frequency:", self.check_post_freq_check)
        
        self.check_real_users_check = QCheckBox()
        self.check_real_users_check.setChecked(self.settings.get("check_real_users", True))
        filter_layout.addRow("Check Real Users %:", self.check_real_users_check)
        
        # Batch size
        self.batch_size_spin = QSpinBox()
        self.batch_size_spin.setRange(1, 50)
        self.batch_size_spin.setValue(self.settings.get("batch_size", 10))
        filter_layout.addRow("Batch Size:", self.batch_size_spin)
        
        filter_group.setLayout(filter_layout)
        settings_layout.addWidget(filter_group)
        
        # Account selection
        account_group = QGroupBox("Account Selection")
        account_layout = QFormLayout()
        
        self.account_combo = QComboBox()
        self.update_account_list()
        
        account_layout.addRow("Use Account:", self.account_combo)
        
        # Add button to refresh accounts
        self.refresh_accounts_button = QPushButton("Refresh Accounts")
        self.refresh_accounts_button.clicked.connect(self.update_account_list)
        account_layout.addRow("", self.refresh_accounts_button)
        
        account_group.setLayout(account_layout)
        settings_layout.addWidget(account_group)
        
        # Add settings widget to splitter
        splitter.addWidget(settings_widget)
        
        # Results section (right panel)
        results_widget = QWidget()
        results_layout = QVBoxLayout(results_widget)
        
        # Create tabs for different result categories
        self.results_tabs = QTabWidget()
        
        # Create tabs for each category
        self.good_tab = self.create_result_tab("Groups Valid & Filter ON")
        self.valid_only_tab = self.create_result_tab("Groups Valid Only")
        self.topics_tab = self.create_result_tab("Topics Groups Only Valid")
        self.channels_tab = self.create_result_tab("Channels Only Valid")
        self.invalid_tab = self.create_result_tab("Invalid Groups/Channels")
        
        # Add tabs to tab widget
        self.results_tabs.addTab(self.good_tab, "Groups Valid & Filter ON")
        self.results_tabs.addTab(self.valid_only_tab, "Groups Valid Only")
        self.results_tabs.addTab(self.topics_tab, "Topics Groups Only Valid")
        self.results_tabs.addTab(self.channels_tab, "Channels Only Valid")
        self.results_tabs.addTab(self.invalid_tab, "Invalid Groups/Channels")
        
        results_layout.addWidget(self.results_tabs)
        
        # Add log area
        log_group = QGroupBox("Log")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        results_layout.addWidget(log_group)
        
        # Add results widget to splitter
        splitter.addWidget(results_widget)
        
        # Set the splitter sizes
        splitter.setSizes([300, 700])
        
        main_layout.addWidget(splitter, 1)  # Give the splitter more weight
    """
            
            new_content = content[:start_idx] + setup_ui_method + content[end_idx:]
            content = new_content
            
            # Add create_result_tab method if it doesn't exist
            if 'def create_result_tab(self' not in content:
                create_result_tab = """
    def create_result_tab(self, title):
        \"\"\"Create a tab for a result category.\"\"\"
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # Create table for results
        table = QTableWidget()
        table.setColumnCount(5)
        table.setHorizontalHeaderLabels(["Group", "Members", "Last Active", "Messages", "Details"])
        table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        
        layout.addWidget(table)
        
        return tab
    """
                
                idx = content.find('def update_account_list(self):')
                if idx != -1:
                    content = content[:idx] + create_result_tab + content[idx:]
    
    # Update references from start_button to start_checker_button
    content = content.replace('self.start_button.setEnabled(False)', 'self.start_checker_button.setEnabled(False)')
    content = content.replace('self.start_button.setEnabled(True)', 'self.start_checker_button.setEnabled(True)')
    
    # Update clear_result_tables method
    if 'def clear_result_tables(self):' not in content:
        content = content.replace(
            'def add_result_to_table(self, result):', 
            'def clear_result_tables(self):\n        """Clear all result tables."""\n        for tab in [self.good_tab, self.valid_only_tab, self.topics_tab, self.channels_tab, self.invalid_tab]:\n            table = tab.findChild(QTableWidget)\n            if table:\n                table.setRowCount(0)\n    \n    def add_result_to_table(self, result):'
        )
    
    # Update result table method
    start_idx = content.find('def add_result_to_table(self, result):')
    end_idx = content.find('def checking_finished(self, results):', start_idx)
    
    if start_idx != -1 and end_idx != -1:
        add_result_method = """def add_result_to_table(self, result):
        \"\"\"Add a result to the appropriate result table.\"\"\"
        # Determine which table to use
        status = result.get("status", "unknown")
        
        if status == "good":
            table = self.good_tab.findChild(QTableWidget)
        elif status == "invalid" and "non_filter_groups" in result.get("error", ""):
            table = self.valid_only_tab.findChild(QTableWidget)
        elif status == "topic":
            table = self.topics_tab.findChild(QTableWidget)
        elif status == "channel":
            table = self.channels_tab.findChild(QTableWidget)
        elif status == "error" or status == "invalid":
            table = self.invalid_tab.findChild(QTableWidget)
        else:
            # Unknown status, skip
            return
            
        # Add to table
        row = table.rowCount()
        table.insertRow(row)
        
        # Get values
        group_id = result.get("group_id", "")
        title = result.get("title", group_id)
        member_count = str(result.get("member_count", 0))
        last_message_date = result.get("last_message_date", "")
        total_messages = str(result.get("total_messages", 0))
        
        # Format last message date
        if last_message_date:
            try:
                dt = datetime.fromisoformat(last_message_date.replace('Z', '+00:00'))
                hours = (datetime.now() - dt.replace(tzinfo=None)).total_seconds() / 3600
                last_message = f"{int(hours)}h ago"
            except:
                last_message = last_message_date
        else:
            last_message = "N/A"
        
        # Details
        details = ""
        if status == "invalid" or status == "error":
            details = result.get("error", "")
        elif status == "good":
            post_freq = result.get("post_frequency", 0)
            real_users = result.get("real_users_percentage", 0)
            if post_freq > 0:
                details = f"Posts: {post_freq:.1f}/day"
            if real_users > 0:
                if details:
                    details += ", "
                details += f"Real users: {real_users:.1f}%"
        
        # Create table items
        group_item = QTableWidgetItem(title)
        member_item = QTableWidgetItem(member_count)
        last_message_item = QTableWidgetItem(last_message)
        total_messages_item = QTableWidgetItem(total_messages)
        details_item = QTableWidgetItem(details)
        
        # Set items
        table.setItem(row, 0, group_item)
        table.setItem(row, 1, member_item)
        table.setItem(row, 2, last_message_item)
        table.setItem(row, 3, total_messages_item)
        table.setItem(row, 4, details_item)
    """
        new_content = content[:start_idx] + add_result_method + content[end_idx:]
        content = new_content
    
    # Update process_result method to update current group label
    if 'self.current_group_label.setText(title)' not in content:
        content = content.replace(
            '# Add to results list\n        self.results.append(result)', 
            '# Add to results list\n        self.results.append(result)\n        \n        # Update currently analyzing label\n        group_id = result.get("group_id", "unknown")\n        title = result.get("title", group_id)\n        self.current_group_label.setText(title)'
        )
    
    # Update checking_finished to set current_group_label to Finished
    content = content.replace(
        'self.start_checker_button.setEnabled(True)\n        self.stop_button.setEnabled(False)', 
        'self.start_checker_button.setEnabled(True)\n        self.stop_button.setEnabled(False)\n        self.current_group_label.setText("Finished")'
    )
    
    # Update stop_checking to reset current_group_label
    content = content.replace(
        '# Update UI\n        self.start_checker_button.setEnabled(True)\n        self.stop_button.setEnabled(False)', 
        '# Update UI\n        self.start_checker_button.setEnabled(True)\n        self.stop_button.setEnabled(False)\n        self.current_group_label.setText("None")'
    )
    
    # Update results summary to use the new category names
    if 'valid_only_count' not in content:
        content = content.replace(
            'good_count = sum(1 for r in self.results if r.get("status") == "good")\n        topic_count = sum(1 for r in self.results if r.get("status") == "topic")\n        channel_count = sum(1 for r in self.results if r.get("status") == "channel")\n        invalid_count = sum(1 for r in self.results if r.get("status") == "invalid")\n        error_count = sum(1 for r in self.results if r.get("status") == "error")', 
            'good_count = sum(1 for r in self.results if r.get("status") == "good")\n        valid_only_count = sum(1 for r in self.results if r.get("status") == "invalid" and "non_filter_groups" in r.get("error", ""))\n        topic_count = sum(1 for r in self.results if r.get("status") == "topic")\n        channel_count = sum(1 for r in self.results if r.get("status") == "channel")\n        invalid_count = sum(1 for r in self.results if r.get("status") == "error" or (r.get("status") == "invalid" and "non_filter_groups" not in r.get("error", "")))'
        )
        
        content = content.replace(
            'summary += f"{good_count} good, {topic_count} topics, {channel_count} channels, "\n        summary += f"{invalid_count} invalid, {error_count} errors"', 
            'summary += f"{good_count} groups passed all filters, {valid_only_count} groups valid only, "\n        summary += f"{topic_count} topics, {channel_count} channels, {invalid_count} invalid"'
        )
    
    # Add min_total_messages to save_settings
    if 'settings.set("min_total_messages"' not in content:
        content = content.replace(
            'settings.set("max_last_message_hours", self.max_hours_spin.value())', 
            'settings.set("max_last_message_hours", self.max_hours_spin.value())\n        settings.set("min_total_messages", self.min_messages_spin.value())'
        )
    
    # Remove save_results method
    save_results_start = content.find('def save_results(self):')
    if save_results_start != -1:
        save_results_end = content.find('def save_settings(self):', save_results_start)
        if save_results_end != -1:
            content = content[:save_results_start] + content[save_results_end:]
    
    # Remove filter_results and should_show_result methods
    filter_results_start = content.find('def filter_results(self, filter_text):')
    if filter_results_start != -1:
        filter_results_end = content.find('def log_message(self, message, level="info"):', filter_results_start)
        if filter_results_end != -1:
            content = content[:filter_results_start] + content[filter_results_end:]
    
    # Update worker to save results automatically
    if 'self.checker.save_results()' not in content:
        content = content.replace(
            '# Update progress\n            self.progress_signal.emit(min(i + self.batch_size, total), total)', 
            '# Update progress\n            self.progress_signal.emit(min(i + self.batch_size, total), total)\n            \n            # Automatically save results\n            if self.checker and self.running:\n                self.checker.save_results()'
        )
    
    # Remove references to save_results_button
    content = content.replace('self.save_results_button.setEnabled(False)', '')
    content = content.replace('self.save_results_button.setEnabled(len(self.results) > 0)', '')
    content = content.replace('self.save_results_button.clicked.connect(self.save_results)', '')
    content = content.replace('self.save_results_button = QPushButton("Save Results")', '')
    content = content.replace('actions_layout.addWidget(self.save_results_button)', '')
    
    with open("group_checker_tab.py", "w") as f:
        f.write(content)
    
    print("Group checker tab updated successfully")

if __name__ == "__main__":
    update_settings()
    update_group_checker()
    update_monitor()
    update_group_checker_tab()
    print("All files updated successfully") 