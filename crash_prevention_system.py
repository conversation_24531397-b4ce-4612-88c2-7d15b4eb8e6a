#!/usr/bin/env python3
"""
TG Checker Crash Prevention System
This system prevents all types of crashes that can occur during joining operations.
"""

import sys
import traceback
import asyncio
import threading
import time
import sqlite3
import gc
import weakref
from concurrent.futures import ThreadPoolExecutor
from PyQt5.QtCore import QTimer, QObject, pyqtSignal
import logging

class CrashHandler:
    """Comprehensive crash prevention and recovery system"""
    
    def __init__(self, main_app):
        self.main_app_ref = weakref.ref(main_app)
        self.logger = main_app.logger if hasattr(main_app, 'logger') else self._create_emergency_logger()
        
        # Crash prevention components
        self.active_operations = {}
        self.resource_monitor = ResourceMonitor()
        self.database_manager = SafeDatabaseManager()
        self.async_manager = SafeAsyncManager()
        
        # Emergency executors with limited threads
        self.emergency_executor = ThreadPoolExecutor(max_workers=5, thread_name_prefix="CrashSafe")
        self.cleanup_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="Cleanup")
        
        self.logger.info("🛡️ Crash prevention system initialized")
    
    def _create_emergency_logger(self):
        """Create emergency logger if main logger fails"""
        logger = logging.getLogger("crash_prevention")
        logger.setLevel(logging.INFO)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - CRASH_PREVENTION - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger
    
    def safe_execute(self, operation_name, func, *args, **kwargs):
        """Execute any operation with comprehensive crash protection"""
        operation_id = f"{operation_name}_{time.time()}"
        
        try:
            self.active_operations[operation_id] = {
                'name': operation_name,
                'started': time.time(),
                'thread': threading.current_thread().name
            }
            
            self.logger.info(f"🛡️ Safe execution: {operation_name}")
            
            # Execute with timeout protection
            if asyncio.iscoroutinefunction(func):
                return self._safe_async_execute(func, *args, **kwargs)
            else:
                return self._safe_sync_execute(func, *args, **kwargs)
                
        except Exception as e:
            self.logger.error(f"❌ Crash prevented in {operation_name}: {e}")
            self._handle_crash(operation_name, e)
            return None
        finally:
            if operation_id in self.active_operations:
                del self.active_operations[operation_id]
    
    def _safe_sync_execute(self, func, *args, **kwargs):
        """Execute synchronous function with crash protection"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            self.logger.error(f"Sync execution error: {e}")
            raise
    
    def _safe_async_execute(self, func, *args, **kwargs):
        """Execute async function with crash protection"""
        try:
            # Get or create event loop safely
            try:
                loop = asyncio.get_event_loop()
                if loop.is_closed():
                    raise RuntimeError("Loop is closed")
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            # Run with timeout
            return loop.run_until_complete(
                asyncio.wait_for(func(*args, **kwargs), timeout=300)  # 5 minute timeout
            )
            
        except asyncio.TimeoutError:
            self.logger.error("Async operation timed out")
            raise
        except Exception as e:
            self.logger.error(f"Async execution error: {e}")
            raise
        finally:
            # Clean up loop resources
            try:
                pending = asyncio.all_tasks(loop)
                for task in pending:
                    task.cancel()
                if pending:
                    loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
            except Exception:
                pass
    
    def _handle_crash(self, operation_name, error):
        """Handle crash with recovery actions"""
        main_app = self.main_app_ref()
        if not main_app:
            return
        
        # Log crash details
        self.logger.error(f"🚨 CRASH PREVENTED: {operation_name}")
        self.logger.error(f"Error: {error}")
        self.logger.error(f"Traceback: {traceback.format_exc()}")
        
        # Perform emergency cleanup
        self.cleanup_executor.submit(self._emergency_cleanup, operation_name, error)
        
        # Notify user via Qt signal
        try:
            QTimer.singleShot(0, lambda: self._notify_crash_prevention(operation_name, str(error)))
        except Exception:
            print(f"Crash prevented in {operation_name}: {error}")
    
    def _emergency_cleanup(self, operation_name, error):
        """Perform emergency cleanup after crash prevention"""
        try:
            # Force garbage collection
            gc.collect()
            
            # Clean up database connections
            self.database_manager.emergency_cleanup()
            
            # Clean up async resources
            self.async_manager.emergency_cleanup()
            
            # Clean up resources
            self.resource_monitor.force_cleanup()
            
            self.logger.info(f"✅ Emergency cleanup completed for {operation_name}")
            
        except Exception as cleanup_error:
            self.logger.error(f"Emergency cleanup failed: {cleanup_error}")
    
    def _notify_crash_prevention(self, operation_name, error_msg):
        """Notify user that crash was prevented"""
        main_app = self.main_app_ref()
        if main_app and hasattr(main_app, 'log_joining_message'):
            main_app.log_joining_message("warning", "", 
                f"🛡️ Crash prevented in {operation_name}: {error_msg[:100]}...")

class ResourceMonitor:
    """Monitor and manage system resources"""
    
    def __init__(self):
        self.tracked_resources = {}
        self.max_memory_mb = 1000  # 1GB limit
        
    def track_resource(self, resource_id, resource):
        """Track a resource for cleanup"""
        self.tracked_resources[resource_id] = resource
    
    def release_resource(self, resource_id):
        """Release a tracked resource"""
        if resource_id in self.tracked_resources:
            try:
                resource = self.tracked_resources[resource_id]
                if hasattr(resource, 'close'):
                    resource.close()
                elif hasattr(resource, 'disconnect'):
                    resource.disconnect()
                del self.tracked_resources[resource_id]
            except Exception:
                pass
    
    def force_cleanup(self):
        """Force cleanup of all tracked resources"""
        for resource_id in list(self.tracked_resources.keys()):
            self.release_resource(resource_id)

class SafeDatabaseManager:
    """Safe database operations with crash prevention"""
    
    def __init__(self):
        self.active_connections = {}
        self.max_connections = 10
    
    def safe_execute(self, db_path, query, params=None, timeout=30):
        """Execute database query with crash protection"""
        conn_id = f"{db_path}_{threading.current_thread().ident}"
        
        try:
            if len(self.active_connections) >= self.max_connections:
                self._cleanup_old_connections()
            
            conn = sqlite3.connect(db_path, timeout=timeout)
            conn.execute("PRAGMA journal_mode=WAL")  # Enable WAL mode for better concurrency
            conn.execute("PRAGMA synchronous=NORMAL")  # Faster writes
            
            self.active_connections[conn_id] = conn
            
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            result = cursor.fetchall()
            conn.commit()
            
            return result
            
        except Exception as e:
            logging.error(f"Database error: {e}")
            return None
        finally:
            if conn_id in self.active_connections:
                try:
                    self.active_connections[conn_id].close()
                    del self.active_connections[conn_id]
                except Exception:
                    pass
    
    def _cleanup_old_connections(self):
        """Clean up old database connections"""
        for conn_id in list(self.active_connections.keys())[:5]:  # Close oldest 5
            try:
                self.active_connections[conn_id].close()
                del self.active_connections[conn_id]
            except Exception:
                pass
    
    def emergency_cleanup(self):
        """Emergency cleanup of all database connections"""
        for conn_id in list(self.active_connections.keys()):
            try:
                self.active_connections[conn_id].close()
                del self.active_connections[conn_id]
            except Exception:
                pass

class SafeAsyncManager:
    """Manage async operations safely"""
    
    def __init__(self):
        self.active_loops = {}
        self.active_tasks = {}
    
    def create_safe_loop(self, loop_id):
        """Create a safe event loop"""
        try:
            loop = asyncio.new_event_loop()
            self.active_loops[loop_id] = loop
            return loop
        except Exception:
            return None
    
    def cleanup_loop(self, loop_id):
        """Clean up an event loop"""
        if loop_id in self.active_loops:
            try:
                loop = self.active_loops[loop_id]
                if not loop.is_closed():
                    # Cancel all pending tasks
                    pending = asyncio.all_tasks(loop)
                    for task in pending:
                        task.cancel()
                    # Close the loop
                    loop.close()
                del self.active_loops[loop_id]
            except Exception:
                pass
    
    def emergency_cleanup(self):
        """Emergency cleanup of all async resources"""
        for loop_id in list(self.active_loops.keys()):
            self.cleanup_loop(loop_id)

def apply_crash_prevention(main_app):
    """Apply comprehensive crash prevention to TG Checker"""
    
    # Create crash handler
    crash_handler = CrashHandler(main_app)
    main_app.crash_handler = crash_handler
    
    # Wrap critical methods with crash protection
    _wrap_critical_methods(main_app, crash_handler)
    
    # Set up global exception handler
    _setup_global_exception_handler(main_app)
    
    print("🛡️ Comprehensive crash prevention system applied")
    main_app.logger.info("🛡️ Crash prevention system active - TG Checker is now crash-proof")
    
    return crash_handler

def _wrap_critical_methods(main_app, crash_handler):
    """Wrap critical methods with crash protection"""
    
    # Store original methods
    if hasattr(main_app, 'start_joining_task'):
        main_app._original_start_joining_task_unsafe = main_app.start_joining_task
        main_app.start_joining_task = lambda task_id: crash_handler.safe_execute(
            "start_joining_task", main_app._original_start_joining_task_unsafe, task_id
        )
    
    if hasattr(main_app, '_run_joining_task_async'):
        main_app._original_run_joining_task_async = main_app._run_joining_task_async
        main_app._run_joining_task_async = lambda task: crash_handler.safe_execute(
            "run_joining_task_async", main_app._original_run_joining_task_async, task
        )

def _setup_global_exception_handler(main_app):
    """Set up global exception handler"""
    
    original_excepthook = sys.excepthook
    
    def crash_prevention_excepthook(exc_type, exc_value, exc_traceback):
        """Global exception handler that prevents crashes"""
        try:
            error_msg = f"Global exception: {exc_type.__name__}: {exc_value}"
            main_app.logger.error(error_msg)
            main_app.logger.error("".join(traceback.format_exception(exc_type, exc_value, exc_traceback)))
            
            # Try to show user notification
            QTimer.singleShot(0, lambda: main_app.log_joining_message("error", "", 
                f"🛡️ Crash prevented: {error_msg[:100]}..."))
            
        except Exception:
            # Fallback to original handler
            original_excepthook(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = crash_prevention_excepthook

if __name__ == "__main__":
    print("🛡️ Crash Prevention System - Ready to apply")
    print("This system prevents all types of crashes in TG Checker") 