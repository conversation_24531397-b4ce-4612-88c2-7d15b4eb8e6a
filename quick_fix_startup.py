#!/usr/bin/env python3
"""
🚨 QUICK FIX: Remove problematic function calls
==============================================

This script simply removes all the problematic apply_continue_button_patch calls
so your TG Checker can start normally. We'll apply the continue button fix later.
"""

def quick_fix():
    """Remove all problematic function calls."""
    try:
        # Read main.py
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        print("📁 Reading main.py...")
        
        # Remove ALL calls to apply_continue_button_patch
        print("🧹 Removing all apply_continue_button_patch calls...")
        
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Skip lines that contain the problematic function call
            if "apply_continue_button_patch(self)" in line:
                print(f"   Removing: {line.strip()}")
                continue
            elif "# Apply continue button thread fix" in line:
                print(f"   Removing: {line.strip()}")
                continue
            else:
                cleaned_lines.append(line)
        
        # Write the cleaned content
        cleaned_content = '\n'.join(cleaned_lines)
        
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(cleaned_content)
        
        print("✅ Removed all problematic calls!")
        print("📝 Note: Continue button fix will be re-applied later")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_startup():
    """Test if the app can start now."""
    print("\n🧪 Testing if main.py can start...")
    print("Running: python -c \"import main; print('✅ Import successful!')\"")
    
    import subprocess
    try:
        result = subprocess.run(
            ["python", "-c", "import main; print('✅ Import successful!')"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Main.py imports successfully!")
            return True
        else:
            print(f"❌ Import failed: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⏱️ Import test timed out (app might be trying to start GUI)")
        return True  # Timeout is actually okay - means no import errors
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main function."""
    print("🚨 QUICK FIX: Remove Problematic Function Calls")
    print("=" * 55)
    print()
    print("This will remove the apply_continue_button_patch calls")
    print("that are preventing your TG Checker from starting.")
    print()
    
    if quick_fix():
        if test_startup():
            print("\n🎉 SUCCESS!")
            print("=" * 15)
            print("✅ Your TG Checker should now start normally!")
            print("🚀 Try running: python main.py")
            print()
            print("📌 Note: The continue button fix has been removed.")
            print("   We'll re-apply it properly once your app is working.")
        else:
            print("\n⚠️ App still has startup issues.")
    else:
        print("\n❌ Quick fix failed.")

if __name__ == "__main__":
    main() 