@echo off
chcp 65001 > nul
echo چاککردنی بۆشایی TG Checker
echo ===================================
echo ئەمە کێشەکانی بۆشایی لە main.py چاک دەکات
echo تکایە چاوەڕێ بکە، پرۆسەکە دەست پێدەکات...
echo.

REM Check if Python is installed
python --version > nul 2>&1
if %errorlevel% neq 0 (
    echo پایسۆن دانەمەزراوە یان لە PATH دا نییە.
    echo تکایە پایسۆن 3.7 یان دواتر دامەزرێنە و دووبارە هەوڵ بدەوە.
    pause
    exit /b 1
)

REM Run the indentation fix script
python fix_indentation.py

echo.
if %errorlevel% equ 0 (
    echo هەموو چاککردنەکان بە سەرکەوتوویی جێبەجێ کران!
    echo دەتوانیت ئێستا TG Checker بەکاربهێنیت بە بەکارهێنانی فایلی Run_Fixed_TG_Checker_Kurdish.bat
) else (
    echo هەڵەیەک ڕوویدا لە کاتی پرۆسەی چاککردندا.
    echo تکایە سەیری نامەکانی سەرەوە بکە بۆ زانیاری زیاتر.
)

pause 