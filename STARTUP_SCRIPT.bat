@echo off
echo TG Checker - Startup Script
echo ===========================
echo.

echo Clearing results folders...
rmdir /s /q "Results\Groups_Valid_Filter" 2>nul
rmdir /s /q "Results\Groups_Valid_Only" 2>nul
rmdir /s /q "Results\Topics_Groups_Only_Valid" 2>nul
rmdir /s /q "Results\Channels_Only_Valid" 2>nul
rmdir /s /q "Results\Invalid_Groups_Channels" 2>nul
rmdir /s /q "Results\Account_Issues" 2>nul

echo Creating empty folders...
mkdir "Results\Groups_Valid_Filter" 2>nul
mkdir "Results\Groups_Valid_Only" 2>nul
mkdir "Results\Topics_Groups_Only_Valid" 2>nul
mkdir "Results\Channels_Only_Valid" 2>nul
mkdir "Results\Invalid_Groups_Channels" 2>nul
mkdir "Results\Account_Issues" 2>nul

echo Folders cleared successfully!
echo.

echo Starting TG Checker...
start python main.py

echo.
echo If main.py doesn't start, please ensure Python is installed and in your PATH
echo. 