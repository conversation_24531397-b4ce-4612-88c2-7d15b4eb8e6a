#!/usr/bin/env python3
"""
A script to extract the Speed Check settings and create a focused application
"""

import os
import sys
import random
import time
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QLabel, QPushButton, QSpinBox, QGroupBox, QFormLayout, 
    QMessageBox, QTabWidget
)
from PyQt5.QtCore import QSettings, Qt, QTimer

class SpeedCheckApp(QMainWindow):
    """A simplified app with Speed Check settings"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("TG Checker - Speed Check Settings")
        self.setGeometry(100, 100, 500, 400)
        
        # Initialize settings
        self.settings = QSettings("TGChecker", "SpeedCheck")
        
        # Create the main widget and layout
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # Create tabs
        tabs = QTabWidget()
        main_layout.addWidget(tabs)
        
        # Settings tab
        settings_tab = QWidget()
        tabs.addTab(settings_tab, "Settings")
        
        # Create layout for settings tab
        layout = QVBoxLayout(settings_tab)
        
        # Speed Check Time settings
        speed_check_group = QGroupBox("Speed Check Time Per 1 Group")
        speed_check_layout = QFormLayout()
        
        # Min Seconds
        self.min_seconds_input = QSpinBox()
        self.min_seconds_input.setMinimum(1)
        self.min_seconds_input.setMaximum(60)
        self.min_seconds_input.setValue(self.settings.value("min_check_seconds", 2, type=int))
        self.min_seconds_input.setSuffix(" seconds")
        
        # Max Seconds
        self.max_seconds_input = QSpinBox()
        self.max_seconds_input.setMinimum(1)
        self.max_seconds_input.setMaximum(120)
        self.max_seconds_input.setValue(self.settings.value("max_check_seconds", 5, type=int))
        self.max_seconds_input.setSuffix(" seconds")
        
        # Connect signals to ensure min <= max
        self.min_seconds_input.valueChanged.connect(self.update_speed_check_range)
        self.max_seconds_input.valueChanged.connect(self.update_speed_check_range)
        
        # Add to layout
        speed_check_layout.addRow("Min Seconds:", self.min_seconds_input)
        speed_check_layout.addRow("Max Seconds:", self.max_seconds_input)
        
        # Add help text
        help_label = QLabel("Sets random delay between each group check to simulate human-like timing")
        help_label.setStyleSheet("color: gray; font-style: italic;")
        speed_check_layout.addRow("", help_label)
        
        speed_check_group.setLayout(speed_check_layout)
        layout.addWidget(speed_check_group)
        
        # Buttons
        button_layout = QHBoxLayout()
        
        # Save button
        save_button = QPushButton("Save Settings")
        save_button.clicked.connect(self.save_settings)
        button_layout.addWidget(save_button)
        
        # Test button
        test_button = QPushButton("Test Speed")
        test_button.clicked.connect(self.test_speed)
        button_layout.addWidget(test_button)
        
        layout.addLayout(button_layout)
        
        # Log area
        self.log_label = QLabel("Status: Ready")
        layout.addWidget(self.log_label)
        
        # Simulation tab
        sim_tab = QWidget()
        tabs.addTab(sim_tab, "Simulation")
        
        sim_layout = QVBoxLayout(sim_tab)
        
        # Group check simulation
        sim_group = QGroupBox("Group Check Simulation")
        sim_group_layout = QVBoxLayout()
        
        # Start simulation button
        start_sim_button = QPushButton("Start Simulation")
        start_sim_button.clicked.connect(self.start_simulation)
        sim_group_layout.addWidget(start_sim_button)
        
        # Stop simulation button
        stop_sim_button = QPushButton("Stop Simulation")
        stop_sim_button.clicked.connect(self.stop_simulation)
        sim_group_layout.addWidget(stop_sim_button)
        
        # Simulation log
        self.sim_log = QLabel("Simulation log will appear here...")
        self.sim_log.setAlignment(Qt.AlignTop | Qt.AlignLeft)
        self.sim_log.setWordWrap(True)
        self.sim_log.setStyleSheet("background-color: #f0f0f0; padding: 10px; min-height: 200px;")
        sim_group_layout.addWidget(self.sim_log)
        
        sim_group.setLayout(sim_group_layout)
        sim_layout.addWidget(sim_group)
        
        # Initialize simulation variables
        self.sim_timer = QTimer()
        self.sim_timer.timeout.connect(self.run_simulation_step)
        self.sim_counter = 0
        self.sim_running = False
    
    def update_speed_check_range(self):
        """Ensure min seconds <= max seconds"""
        if self.min_seconds_input.value() > self.max_seconds_input.value():
            # If min is higher than max, set max to min
            self.max_seconds_input.setValue(self.min_seconds_input.value())
    
    def save_settings(self):
        """Save the settings"""
        # Save Speed Check Time settings
        self.settings.setValue("min_check_seconds", self.min_seconds_input.value())
        self.settings.setValue("max_check_seconds", self.max_seconds_input.value())
        
        # Show confirmation
        QMessageBox.information(self, "Settings Saved", "Settings have been saved successfully.")
        self.log_label.setText("Status: Settings saved")
    
    def test_speed(self):
        """Test the speed settings with a simple simulation"""
        min_seconds = self.min_seconds_input.value()
        max_seconds = self.max_seconds_input.value()
        
        # Generate a random delay
        delay = random.uniform(min_seconds, max_seconds)
        
        self.log_label.setText(f"Status: Testing with {delay:.2f} seconds delay...")
        
        # Wait for the delay
        for i in range(int(delay * 10)):
            QApplication.processEvents()
            time.sleep(0.1)
        
        self.log_label.setText(f"Status: Test completed. Delay was {delay:.2f} seconds")
    
    def start_simulation(self):
        """Start the group check simulation"""
        if not self.sim_running:
            self.sim_running = True
            self.sim_counter = 0
            self.sim_log.setText("Starting simulation...\n")
            self.sim_timer.start(100)  # Update every 100ms
    
    def stop_simulation(self):
        """Stop the group check simulation"""
        if self.sim_running:
            self.sim_running = False
            self.sim_timer.stop()
            self.sim_log.setText(self.sim_log.text() + "\nSimulation stopped.")
    
    def run_simulation_step(self):
        """Run one step of the simulation"""
        if self.sim_counter == 0:
            # First group doesn't have a delay
            self.sim_log.setText(self.sim_log.text() + f"\nChecking Group {self.sim_counter + 1}...")
            self.sim_counter += 1
        else:
            # Apply random delay for subsequent groups
            min_seconds = self.min_seconds_input.value()
            max_seconds = self.max_seconds_input.value()
            delay = random.uniform(min_seconds, max_seconds)
            
            self.sim_log.setText(self.sim_log.text() + f"\n⏱️ Waiting {delay:.1f}s before checking next group...")
            
            # Pause the timer and restart after the delay
            self.sim_timer.stop()
            QTimer.singleShot(int(delay * 1000), self.continue_simulation)
    
    def continue_simulation(self):
        """Continue the simulation after a delay"""
        if self.sim_running:
            self.sim_log.setText(self.sim_log.text() + f"\nChecking Group {self.sim_counter + 1}...")
            self.sim_counter += 1
            
            # Stop after 5 groups
            if self.sim_counter >= 5:
                self.stop_simulation()
                self.sim_log.setText(self.sim_log.text() + "\nSimulation completed for 5 groups.")
            else:
                self.sim_timer.start(100)

def main():
    """Run the Speed Check settings application"""
    app = QApplication(sys.argv)
    window = SpeedCheckApp()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 