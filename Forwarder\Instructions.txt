### STEPS ###
Download Python. (https://www.python.org/ftp/python/3.12.5/python-3.12.5-amd64.exe for Windows)
!!! Make sure to tick "Add python.exe to PATH" while installing python. !!!
Install the requirements: Run the "first install.bat" file, and it will automatically install all the required packages.
Edit resources/config.json and groups.txt. (!!! IF THE groups.txt FILE IS EMPTY, THE BOT WON'T WORK !!!)
Run the script using "run the bot.bat" or "run  the bot 2.bat" —one of them will work depending on your Python version.


### HOW TO CREATE AN APP TO FILL CONFIG ###
Log in to Telegram Apps. (https://my.telegram.org/apps)
Create an app.



### FILLING THE CONFIG.JSON ###
name: App title (you can find it after creating your app).
appId: App api_id (you can find it after creating your app).
appHash: App api_hash (you can find it after creating your app).
messageToForward: Right-click on the message you want to forward and copy the post link (works with channels/groups). Example: t.me/eviona/68.
shouldReplyMessage: If set to true, it automatically sends a reply message to users who send private messages to the account.
replyMessage: Users will receive this auto-reply when they message your bot, allowing them to contact your main account. You can use Telegram markdown like bold, italic, etc., to make it look fancier.
useRandomSleepTime: If you want to turn on the delay randomizer (a delay between each forward cycle), set this to true; if you don't want it, set it to false. (This feature randomizes the delay between cycles because some marketplaces don't allow ad bots. It makes your bot appear more human-like.)
randomSleepTimeMin: The minimum delay in seconds for the randomizer. For example, if you set this to 10 and set randomSleepTimeMax to 20, it will randomize the delay between 10-20 seconds. So, one cycle might take 12 seconds, while another might take 18 seconds, etc.
randomSleepTimeMax: The maximum delay in seconds for the randomizer.
interval: The delay between forwarding messages (this is not between cycles but between messages in the same cycle). Adjust this based on the number of groups you advertise in to avoid getting rate limited for spamming.
intervalBetweenLoops: This will only work if useRandomSleepTime is set to false. It specifies a static delay between cycles in seconds.
logFailedChannels: If set to true, this option will log all your failed forwards/channels to a file called failed_channels.txt, so you can track and remove failed ones to prevent getting rate limited.


### IMPORTANT TIPS & TRICKS ###
Always use your alternate account for advertising, preferably accounts that were created before 2018.

Once you log in to your account from an advertiser, we highly recommend not using the advertiser and letting the session age for 48 hours. New Telegram sessions/logins have a 48-hour grace period; otherwise, it will rate limit your account.

Always monitor your delays and intervals between messages and cycle delays, because if you flood Telegram, it will rate limit you.

Always check failed_channels and periodically remove failed channels from your groups file to avoid rate limits and failed forward attempts.