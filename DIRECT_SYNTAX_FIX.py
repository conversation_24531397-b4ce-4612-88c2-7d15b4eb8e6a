#!/usr/bin/env python3
"""
DIRECT SYNTAX FIX
=================

Directly fixes the syntax error by removing the exact problematic code.
"""

import re

def direct_syntax_fix():
    """Directly fix the syntax error in main.py"""
    
    print("🚨 DIRECT SYNTAX FIX")
    print("=" * 25)
    
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        print("✅ Loaded main.py")
    except Exception as e:
        print(f"❌ Failed to load: {e}")
        return False
    
    # Find and remove the exact problematic code
    lines = content.split('\n')
    
    # Find the problematic lines and remove them
    new_lines = []
    skip_until_update = False
    
    for i, line in enumerate(lines):
        # Start skipping when we hit the problematic persistent results code
        if "# PERSISTENT RESULTS - Store for display" in line:
            skip_until_update = True
            continue
        
        # Stop skipping after the update results line
        if skip_until_update and "# Update results in real-time" in line:
            skip_until_update = False
            new_lines.append(line)
            continue
        
        # Skip lines while we're in the problematic section
        if skip_until_update:
            continue
        
        new_lines.append(line)
    
    # Join the lines back together
    fixed_content = '\n'.join(new_lines)
    
    try:
        with open("main.py", "w", encoding="utf-8") as f:
            f.write(fixed_content)
        print("✅ Removed problematic code")
        return True
    except Exception as e:
        print(f"❌ Failed to save: {e}")
        return False

if __name__ == "__main__":
    success = direct_syntax_fix()
    
    if success:
        print()
        print("🎉 SYNTAX ERROR SHOULD BE FIXED!")
        print("✅ Removed problematic persistent results code")
        print("🚀 TG Checker should now start without syntax errors")
    else:
        print("❌ Fix failed") 