    def reset_forwarder_task_progress(self, task_id):
        """Reset forwarder task progress to allow re-running."""
        try:
            conn = sqlite3.connect(self.forwarder_db_path, timeout=30)
            cursor = conn.cursor()
            
            query = """UPDATE forwarder_tasks SET 
                       current_index = 0, 
                       current_index_2 = 0, 
                       successful_forwards = 0, 
                       failed_forwards = 0,
                       updated_at = ?
                       WHERE id = ?"""
            
            cursor.execute(query, [datetime.now().isoformat(), task_id])
            conn.commit()
            conn.close()
            
            # Update local cache
            if task_id in self.forwarder_tasks:
                self.forwarder_tasks[task_id]['current_index'] = 0
                self.forwarder_tasks[task_id]['current_index_2'] = 0
                self.forwarder_tasks[task_id]['successful_forwards'] = 0
                self.forwarder_tasks[task_id]['failed_forwards'] = 0
            
            self.logger.info(f"Task {task_id} progress reset successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to reset task progress {task_id}: {str(e)}")
            raise
            
    def reset_joining_task_progress(self, task_id):
        """Reset joining task progress to allow re-running or new task creation."""
        try:
            conn = sqlite3.connect(self.joining_db_path, timeout=30)
            cursor = conn.cursor()
            
            query = """UPDATE joining_tasks SET 
                       current_index = 0, 
                       successful_joins = 0, 
                       failed_joins = 0,
                       updated_at = ?
                       WHERE id = ?"""
            
            cursor.execute(query, [datetime.now().isoformat(), task_id])
            conn.commit()
            conn.close()
            
            # Update local cache
            if task_id in self.joining_tasks:
                self.joining_tasks[task_id]['current_index'] = 0
                self.joining_tasks[task_id]['successful_joins'] = 0
                self.joining_tasks[task_id]['failed_joins'] = 0
            
            self.logger.info(f"Joining task {task_id} progress reset successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to reset joining task progress {task_id}: {str(e)}")
            raise

    def update_forwarder_task_status(self, task_id, status, **kwargs): 