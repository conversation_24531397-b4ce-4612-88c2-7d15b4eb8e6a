#!/usr/bin/env python3
"""
EMERGENCY FIX: Resolves task completion logic issues
"""

import os
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_skip_task_resume_logic():
    """Fix the _should_skip_joining_task_resume function to prevent false 'already completed' messages."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # Find the skip task resume function
        skip_function = "def _should_skip_joining_task_resume(self, task_id, task):"
        if skip_function in content:
            # Extract the function
            start_idx = content.find(skip_function)
            end_idx = content.find("def ", start_idx + 10)
            if start_idx >= 0 and end_idx > start_idx:
                # Create the fixed function
                fixed_function = """def _should_skip_joining_task_resume(self, task_id, task):
        \"\"\"🔍 ULTRA-CONSERVATIVE SKIP: Never skip unless absolutely certain task is 100% done.\"\"\"
        try:
            # ❌ NEVER skip running tasks
            if task.get('status') == 'running':
                self.log_joining_message("info", task_id, "🔄 Continuing - task is running")
                return False
            
            # Parse group links properly to get actual total
            try:
                import json
                if isinstance(task.get('group_links'), str):
                    try:
                        target_groups = json.loads(task['group_links'])
                        if not isinstance(target_groups, list):
                            target_groups = [str(task['group_links']).strip()]
                    except (json.JSONDecodeError, ValueError):
                        target_groups = task.get('group_links', '').split('\\n')
                else:
                    target_groups = task.get('group_links', [])
            except:
                target_groups = task.get('group_links', [])
            
            # Clean empty entries
            target_groups = [g.strip() for g in target_groups if g and str(g).strip()] if target_groups else []
            total_groups = len(target_groups)
            
            # Get actual progress
            current_index = task.get('current_index', 0)
            successful_joins = task.get('successful_joins', 0)
            failed_joins = task.get('failed_joins', 0)
            actual_processed = successful_joins + failed_joins
            
            # NEVER skip if we haven't processed all groups
            if current_index < total_groups:
                self.log_joining_message("info", task_id, 
                    f"🔄 Resuming incomplete task: {current_index}/{total_groups} groups")
                return False
            
            # NEVER skip if status is explicitly 'paused'
            if task.get('status') == 'paused':
                self.log_joining_message("info", task_id, "🔄 Resuming explicitly paused task")
                return False
                
            # Only skip if genuinely 100% completed
            if (task.get('status') == 'completed' and 
                current_index >= total_groups and 
                actual_processed >= total_groups):
                self.log_joining_message("info", task_id, 
                    f"✅ Verified complete - skipping ({actual_processed}/{total_groups} groups processed)")
                return True
            
            # Skip if no groups to process
            if total_groups == 0:
                self.log_joining_message("warning", task_id, "⚠️ Skipping - no groups found")
                return True
                
            # Safety default: Never skip when uncertain
            self.log_joining_message("info", task_id, 
                f"🔄 Safety default - resuming (status: {task.get('status')}, processed: {actual_processed}/{total_groups})")
            return False
                
        except Exception as e:
            self.log_joining_message("error", task_id, f"⚠️ Skip check error: {e} - RESUMING for safety")
            # If ANY error in checking, NEVER skip - always safer to resume
            return False

"""
                
                # Replace the old function with the new one
                fixed_content = content[:start_idx] + fixed_function + content[end_idx:]
                
                # Write the fixed content back to the file
                with open("main.py", "w", encoding="utf-8") as f:
                    f.write(fixed_content)
                
                print("✅ Fixed task resume skip logic")
                return True
            else:
                print("⚠️ Could not find the end of the _should_skip_joining_task_resume function")
                return False
        else:
            print("⚠️ Could not find _should_skip_joining_task_resume function")
            return False
    except Exception as e:
        print(f"❌ Error fixing skip task resume logic: {str(e)}")
        return False

def main():
    print("🚨 EMERGENCY FIX: Resolving task completion logic issues...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fixed = fix_skip_task_resume_logic()
    
    if fixed:
        print("\n✅ Emergency fix applied successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fixes were applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 