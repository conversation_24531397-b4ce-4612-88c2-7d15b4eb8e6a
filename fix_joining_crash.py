#!/usr/bin/env python3
"""
EMERGENCY FIX: Resolves critical issues with the joining task system in TG Checker
- Fixes crashes when continuing joining tasks
- Corrects progress tracking and "already completed" false positives
"""

import os
import re
import shutil
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_continue_all_joining_tasks():
    """Fix the continue_all_joining_tasks function that's causing crashes."""
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix 1: Correct the indentation in the continue_all_joining_tasks function
    fixed_content = content.replace(
        "def continue_all_joining_tasks(self):\n        \"\"\"🚀 CRASH FIX: Continue all paused joining tasks with staggered resume logic.\"\"\"\n        try:\n            # 🔧 SMART RESUME DETECTION: Find all incomplete tasks (even if marked \"completed\")\n            resumable_tasks = []\n            for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n            \n            # Parse group links",
        "def continue_all_joining_tasks(self):\n        \"\"\"🚀 CRASH FIX: Continue all paused joining tasks with staggered resume logic.\"\"\"\n        try:\n            # 🔧 SMART RESUME DETECTION: Find all incomplete tasks (even if marked \"completed\")\n            resumable_tasks = []\n            for task_id in self.joining_tasks:\n                task = self.joining_tasks[task_id]\n                \n                # Parse group links"
    )
    
    # Fix 2: Improve the start_joining_task function to properly handle resuming tasks
    fixed_content = fixed_content.replace(
        "def start_joining_task(self, task_id):\n        \"\"\"Start a specific joining task with high-performance system.\"\"\"\n        try:\n            if task_id not in self.joining_tasks:\n                self.log_joining_message(\"error\", task_id, \"Task not found\")\n                return\n            \n            # ALWAYS reset task progress when manually starting (allows re-running any task)\n            self.logger.info(f\"Resetting joining task progress for manual restart: {task_id}\")\n            self.log_joining_message(\"info\", task_id, \"🔄 Resetting progress counters for clean start\")\n            self.reset_joining_task_progress(task_id)",
        "def start_joining_task(self, task_id):\n        \"\"\"Start a specific joining task with high-performance system.\"\"\"\n        try:\n            if task_id not in self.joining_tasks:\n                self.log_joining_message(\"error\", task_id, \"Task not found\")\n                return\n            \n            # Check if we're continuing a task or starting fresh\n            task = self.joining_tasks[task_id]\n            is_continuing = False\n            \n            # Get actual progress\n            import json\n            try:\n                group_links = json.loads(task['group_links']) if isinstance(task['group_links'], str) else task['group_links']\n                total_groups = len(group_links)\n                current_index = task['current_index']\n                \n                # Detect if we're continuing a task\n                if (task['status'] == 'paused' or task['status'] == 'running') and current_index > 0 and current_index < total_groups:\n                    is_continuing = True\n                    self.logger.info(f\"Continuing joining task from index {current_index}/{total_groups}: {task_id}\")\n                    self.log_joining_message(\"info\", task_id, f\"🔄 Continuing from group #{current_index+1} ({current_index}/{total_groups})\")\n                else:\n                    # Reset task progress for fresh start\n                    self.logger.info(f\"Resetting joining task progress for fresh start: {task_id}\")\n                    self.log_joining_message(\"info\", task_id, \"🔄 Resetting progress counters for clean start\")\n                    self.reset_joining_task_progress(task_id)\n            except:\n                # If any error in parsing, default to reset for safety\n                self.logger.info(f\"Resetting joining task progress (parse error): {task_id}\")\n                self.log_joining_message(\"info\", task_id, \"🔄 Resetting progress counters (parse error)\")\n                self.reset_joining_task_progress(task_id)"
    )
    
    # Fix 3: Fix the _should_skip_joining_task_resume function to prevent false "already completed" messages
    fixed_content = fixed_content.replace(
        "def _should_skip_joining_task_resume(self, task_id, task):\n        \"\"\"🔍 ULTRA-CONSERVATIVE SKIP: Never skip unless absolutely certain task is 100% done.\"\"\"\n        try:\n            # ❌ NEVER skip running tasks\n            if task.get('status') == 'running':\n                self.log_joining_message(\"info\", task_id, \"🔄 Continuing - task is running\")\n                return False\n            \n            # ❌ NEVER skip based on \"completed\" status alone - verify actual progress\n            current_index = task.get('current_index', 0)\n            \n            # 🔧 Parse group links properly to get actual total\n            try:\n                import json\n                if isinstance(task.get('group_links'), str):\n                    try:\n                        target_groups = json.loads(task['group_links'])\n                        if not isinstance(target_groups, list):\n                            target_groups = [str(task['group_links']).strip()]\n                    except (json.JSONDecodeError, ValueError):\n                        target_groups = task.get('group_links', '').split('\\n')\n                else:\n                    target_groups = task.get('group_links', [])\n            except:\n                target_groups = task.get('group_links', [])\n            \n            # Clean empty entries\n            target_groups = [g.strip() for g in target_groups if g and str(g).strip()] if target_groups else []\n            total_groups = len(target_groups)\n            \n            # 🔍 VERIFICATION: Check actual progress vs expected completion\n            progress_percentage = (current_index / total_groups * 100) if total_groups > 0 else 0\n            \n            # ✅ ONLY skip if task is GENUINELY 100% completed AND marked as completed\n            if (task.get('status') == 'completed' and \n                total_groups > 0 and \n                current_index >= total_groups and\n                progress_percentage >= 100):\n                self.log_joining_message(\"info\", task_id, f\"✅ Verified complete - skipping ({current_index}/{total_groups} = 100%)\")\n                return True\n            \n            # ❌ NEVER skip incomplete tasks - regardless of status\n            if total_groups > 0 and current_index < total_groups:\n                actual_status = task.get('status', 'unknown')\n                self.log_joining_message(\"info\", task_id, \n                    f\"🔄 RESUMING incomplete task: {current_index}/{total_groups} ({progress_percentage:.1f}%) - Status: {actual_status}\")\n                return False\n                    \n            # ❌ Skip only if literally no groups to process\n            if total_groups == 0:\n                self.log_joining_message(\"warning\", task_id, \"⚠️ Skipping - no groups found\")\n                return True\n                    \n            # 🛡️ SAFETY DEFAULT: Never skip when uncertain\n            self.log_joining_message(\"info\", task_id, f\"🔄 Safety default - resuming (status: {task.get('status')}, {current_index}/{total_groups})\")\n            return False",
        "def _should_skip_joining_task_resume(self, task_id, task):\n        \"\"\"🔍 ULTRA-CONSERVATIVE SKIP: Never skip unless absolutely certain task is 100% done.\"\"\"\n        try:\n            # ❌ NEVER skip running tasks\n            if task.get('status') == 'running':\n                self.log_joining_message(\"info\", task_id, \"🔄 Continuing - task is running\")\n                return False\n            \n            # Parse group links properly to get actual total\n            try:\n                import json\n                if isinstance(task.get('group_links'), str):\n                    try:\n                        target_groups = json.loads(task['group_links'])\n                        if not isinstance(target_groups, list):\n                            target_groups = [str(task['group_links']).strip()]\n                    except (json.JSONDecodeError, ValueError):\n                        target_groups = task.get('group_links', '').split('\\n')\n                else:\n                    target_groups = task.get('group_links', [])\n            except:\n                target_groups = task.get('group_links', [])\n            \n            # Clean empty entries\n            target_groups = [g.strip() for g in target_groups if g and str(g).strip()] if target_groups else []\n            total_groups = len(target_groups)\n            \n            # Get actual progress\n            current_index = task.get('current_index', 0)\n            successful_joins = task.get('successful_joins', 0)\n            failed_joins = task.get('failed_joins', 0)\n            actual_processed = successful_joins + failed_joins\n            \n            # NEVER skip if we haven't processed all groups\n            if current_index < total_groups:\n                self.log_joining_message(\"info\", task_id, \n                    f\"🔄 Resuming incomplete task: {current_index}/{total_groups} groups\")\n                return False\n            \n            # NEVER skip if status is explicitly 'paused'\n            if task.get('status') == 'paused':\n                self.log_joining_message(\"info\", task_id, \"🔄 Resuming explicitly paused task\")\n                return False\n                \n            # Only skip if genuinely 100% completed\n            if (task.get('status') == 'completed' and \n                current_index >= total_groups and \n                actual_processed >= total_groups):\n                self.log_joining_message(\"info\", task_id, \n                    f\"✅ Verified complete - skipping ({actual_processed}/{total_groups} groups processed)\")\n                return True\n            \n            # Skip if no groups to process\n            if total_groups == 0:\n                self.log_joining_message(\"warning\", task_id, \"⚠️ Skipping - no groups found\")\n                return True\n                \n            # Safety default: Never skip when uncertain\n            self.log_joining_message(\"info\", task_id, \n                f\"🔄 Safety default - resuming (status: {task.get('status')}, processed: {actual_processed}/{total_groups})\")\n            return False"
    )
    
    # Fix 4: Improve _run_joining_task_async to better handle already joined groups
    fixed_content = fixed_content.replace(
        "# Pre-check if already a member to avoid unnecessary join attempts\n                if await self._is_already_member(client, group_link, current_memberships):\n                    already_joined_count += 1\n                    self.joining_log_signal.emit(\"info\", task_id, \n                        f\"⏭️ Skipping (already joined): {group_link}\")\n                    continue",
        "# Pre-check if already a member to avoid unnecessary join attempts\n                if await self._is_already_member(client, group_link, current_memberships):\n                    already_joined_count += 1\n                    self.joining_log_signal.emit(\"info\", task_id, \n                        f\"⏭️ Skipping (already joined): {group_link}\")\n                    \n                    # IMPORTANT: Update progress counters but don't increment current_index\n                    # This ensures we don't count skipped groups in the progress\n                    self.joining_progress_signal.emit(\n                        task_id, \"running\", current_index, successful_joins, failed_joins\n                    )\n                    continue"
    )
    
    # Fix 5: Fix the completion logic in _run_joining_task_async
    fixed_content = fixed_content.replace(
        "# 🎯 FIXED COMPLETION LOGIC: Only mark as completed if ALL groups were processed\n            total_groups = len(group_links)\n            final_index = current_index  # This should be the last processed index\n            \n            if final_index >= total_groups:\n                # Task is truly 100% completed - processed all groups\n                self.joining_progress_signal.emit(\n                    task_id, \"completed\", final_index, successful_joins, failed_joins\n                )\n                self.joining_log_signal.emit(\"success\", task_id, \n                    f\"✅ Task 100% completed! Processed ALL {total_groups} groups | \"\n                    f\"Joined: {successful_joins} | Failed: {failed_joins} | \"\n                    f\"Already joined (skipped): {already_joined_count}\")",
        "# 🎯 FIXED COMPLETION LOGIC: Only mark as completed if ALL groups were processed\n            total_groups = len(group_links)\n            final_index = current_index  # This should be the last processed index\n            \n            # Calculate actual progress (only count processed groups, not skipped ones)\n            actual_processed = successful_joins + failed_joins\n            \n            if final_index >= total_groups:\n                # Only mark as completed if we've actually joined or attempted all groups\n                # This prevents false \"completed\" status when many groups are skipped\n                if actual_processed >= (total_groups - already_joined_count):\n                    self.joining_progress_signal.emit(\n                        task_id, \"completed\", actual_processed, successful_joins, failed_joins\n                    )\n                    self.joining_log_signal.emit(\"success\", task_id, \n                        f\"✅ Task 100% completed! Processed ALL {total_groups} groups | \"\n                        f\"Joined: {successful_joins} | Failed: {failed_joins} | \"\n                        f\"Already joined (skipped): {already_joined_count}\")\n                else:\n                    # Some groups were skipped but not properly accounted for\n                    self.joining_progress_signal.emit(\n                        task_id, \"paused\", actual_processed, successful_joins, failed_joins\n                    )\n                    self.joining_log_signal.emit(\"warning\", task_id, \n                        f\"⚠️ Task needs review - index at {final_index}/{total_groups} but only \"\n                        f\"{actual_processed} groups were processed | \"\n                        f\"Joined: {successful_joins} | Failed: {failed_joins} | \"\n                        f\"Already joined (skipped): {already_joined_count}\")"
    )
    
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(fixed_content)
    
    print("✅ Fixed continue_all_joining_tasks function")
    print("✅ Improved task completion status tracking")
    print("✅ Enhanced already-joined group detection")
    print("✅ Fixed progress counter issues")

def main():
    print("🚨 EMERGENCY FIX: Resolving joining task crashes and progress issues...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fix_continue_all_joining_tasks()
    
    print("\n✅ All fixes applied successfully!")
    print("You can now restart the TG Checker application.")

if __name__ == "__main__":
    main() 