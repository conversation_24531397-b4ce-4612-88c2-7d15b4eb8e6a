#!/usr/bin/env python3
"""
MANUAL FIX: Line by line approach with careful attention to indentation
"""

import os
import shutil
from datetime import datetime

def manual_fix():
    """Fix the indentation and try-else errors with direct line-by-line modifications"""
    print("=== MANUAL FIX: Line by line approach ===")
    
    # Files
    input_file = "main.py"
    output_file = "main_working.py"
    backup_file = "main.py.manual_backup"
    
    # Create backup if not exists
    if not os.path.exists(backup_file):
        shutil.copy2(input_file, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the file line by line
    with open(input_file, 'r', encoding='utf-8', errors='replace') as f:
        lines = f.readlines()
    
    # Create a copy of the lines to modify
    fixed_lines = lines.copy()
    
    # 1. Fix the problematic method at line 659
    print("Fixing indentation error at line 659...")
    method_start = -1
    method_end = -1
    
    # Find the problematic method
    for i, line in enumerate(lines):
        if "def auto_refresh_missing_account_info(self):" in line:
            method_start = i
            # Find the end of the method
            for j in range(i+1, len(lines)):
                # Look for the next method definition at the same indentation level
                if "    def " in lines[j] and lines[j].strip().startswith("def "):
                    method_end = j
                    break
            
            if method_end == -1:  # If no next method found
                method_end = len(lines)
            
            print(f"Found method at lines {method_start+1}-{method_end}")
            break
    
    if method_start >= 0:
        # Replace with a fixed version
        fixed_method = [
            "    def auto_refresh_missing_account_info(self):\n",
            "        # Method disabled due to indentation issues\n",
            "        if hasattr(self, 'logger'):\n",
            "            self.logger.info(\"Auto-refresh account info requested (function disabled)\")\n",
            "        if hasattr(self, 'log_activity_signal'):\n",
            "            self.log_activity_signal.emit(\"Auto-refresh account info function is disabled\")\n",
            "        return\n",
            "\n"
        ]
        
        # Remove the old method and insert the new one
        fixed_lines[method_start:method_end] = fixed_method
    
    # 2. Fix try-else blocks without except
    print("Fixing try-else blocks without except...")
    
    # Process lines one by one
    i = 0
    while i < len(fixed_lines):
        line = fixed_lines[i].rstrip()
        
        # Look for try: statements
        if line.strip() == "try:":
            indent_level = len(line) - len(line.lstrip())
            indent = " " * indent_level
            
            # Look for a matching else without except in between
            try_pos = i
            has_except = False
            else_pos = -1
            
            # Search for matching else at the same indentation level
            for j in range(try_pos + 1, min(try_pos + 30, len(fixed_lines))):
                if j >= len(fixed_lines):
                    break
                
                current_line = fixed_lines[j].rstrip()
                current_indent = len(current_line) - len(current_line.lstrip())
                
                # If we find a line with same or less indentation as the try
                if current_indent <= indent_level:
                    # Check if it's an else
                    if current_line.strip() == "else:":
                        else_pos = j
                        break
                    # Check if it's an except
                    elif current_line.strip().startswith("except"):
                        has_except = True
                    # If it's another statement at same level, we've gone past the try block
                    elif current_indent == indent_level and current_line.strip() != "finally:":
                        break
            
            # If we found a matching else without except, add an except block
            if else_pos >= 0 and not has_except:
                # Insert the except block right before the else
                fixed_lines.insert(else_pos, f"{indent}except Exception as e:\n")
                fixed_lines.insert(else_pos + 1, f"{indent}    print(f\"Error: {{e}}\")\n")
                
                print(f"Added except block before else at line {else_pos+1}")
                
                # Adjust the loop counter
                i = else_pos + 2
                continue
        
        i += 1
    
    # Write the fixed content to the output file
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    
    print(f"Created fixed version at {output_file}")
    
    # Create batch files
    with open("Run_Working_TG_Checker.bat", 'w') as f:
        f.write(f"""@echo off
echo =============================
echo   TG Checker (Working Version)
echo =============================
python {output_file}
if %errorlevel% neq 0 (
    echo Error occurred! See details above.
    pause
)
pause
""")
    
    with open("Run_Working_TG_Checker_Kurdish.bat", 'w') as f:
        f.write(f"""@echo off
echo =============================
echo   TG Checker - Washa Kara
echo =============================
python {output_file}
if %errorlevel% neq 0 (
    echo Halayak ruida! Bo zaniari ziatr sairi log bka.
    pause
)
pause
""")
    
    print("Created batch files: Run_Working_TG_Checker.bat and Run_Working_TG_Checker_Kurdish.bat")
    
    # Test if the fixed file compiles
    print("\nTesting fixed file for syntax errors...")
    try:
        import subprocess
        result = subprocess.run(
            ["python", "-m", "py_compile", output_file],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print(f"SUCCESS! {output_file} compiles without syntax errors.")
            print(f"\nRun the application using: python {output_file}")
            print(f"Or double-click on Run_Working_TG_Checker.bat")
            return True
        else:
            print(f"ERROR: Syntax errors remain in {output_file}:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"Error testing file: {e}")
        return False

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_indentation():
    """Manually fix the indentation in the stop_joining_task function."""
    try:
        with open("main.py", "r", encoding="utf-8") as f:
            lines = f.readlines()
        
        # Find the problematic function
        function_line = None
        for i, line in enumerate(lines):
            if "def stop_joining_task(self, task_id):" in line:
                function_line = i
                break
        
        if function_line is None:
            print("⚠️ Could not find the stop_joining_task function")
            return False
        
        print(f"Found stop_joining_task function at line {function_line + 1}")
        
        # Get the correct indentation from a previous function
        prev_function_line = None
        for i in range(function_line - 1, 0, -1):
            if "def " in lines[i] and ":" in lines[i]:
                prev_function_line = i
                break
        
        if prev_function_line is None:
            print("⚠️ Could not find a previous function for reference")
            return False
        
        # Get the indentation of the previous function
        prev_indent = len(lines[prev_function_line]) - len(lines[prev_function_line].lstrip())
        print(f"Previous function at line {prev_function_line + 1} has {prev_indent} spaces of indentation")
        
        # Fix the indentation of the function definition
        lines[function_line] = " " * prev_indent + lines[function_line].lstrip()
        
        # Fix the indentation of the docstring
        docstring_line = function_line + 1
        if docstring_line < len(lines) and '"""' in lines[docstring_line]:
            lines[docstring_line] = " " * (prev_indent + 4) + lines[docstring_line].lstrip()
        
        # Fix the indentation of the try statement
        try_line = function_line + 2
        if try_line < len(lines) and "try:" in lines[try_line]:
            lines[try_line] = " " * (prev_indent + 4) + lines[try_line].lstrip()
        
        # Fix the indentation of the if statement and the rest of the function body
        for i in range(function_line + 3, len(lines)):
            # If we hit another function definition at the same level, we're done
            if "def " in lines[i] and len(lines[i]) - len(lines[i].lstrip()) <= prev_indent:
                break
            
            # Fix the indentation of this line
            stripped_line = lines[i].lstrip()
            if stripped_line:  # Skip empty lines
                if "if " in stripped_line and ":" in stripped_line:
                    # This is the if statement
                    lines[i] = " " * (prev_indent + 8) + stripped_line
                elif "else:" in stripped_line:
                    # This is the else statement
                    lines[i] = " " * (prev_indent + 8) + stripped_line
                elif "except " in stripped_line:
                    # This is the except statement
                    lines[i] = " " * (prev_indent + 4) + stripped_line
                elif "return " in stripped_line:
                    # This is a return statement - check if it's in the if/else or try/except
                    if i > 0 and ("if " in lines[i-1] or "else:" in lines[i-1]):
                        # It's in the if/else block
                        lines[i] = " " * (prev_indent + 12) + stripped_line
                    else:
                        # It's in the try/except block
                        lines[i] = " " * (prev_indent + 8) + stripped_line
                else:
                    # This is some other statement - assume it's in the if/else block
                    lines[i] = " " * (prev_indent + 12) + stripped_line
        
        # Write the fixed content back
        with open("main.py", "w", encoding="utf-8") as f:
            f.writelines(lines)
        
        print(f"✅ Fixed indentation of stop_joining_task function and its body")
        return True
    except Exception as e:
        print(f"❌ Error fixing indentation: {str(e)}")
        return False

def main():
    print("🚨 MANUAL INDENTATION FIX: Fixing indentation in stop_joining_task function...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fixed = fix_indentation()
    
    if fixed:
        print("\n✅ Manual indentation fix applied successfully!")
        print("You can now restart the TG Checker application.")
    else:
        print("\n⚠️ No fix was applied. Please check the logs above for details.")

if __name__ == "__main__":
    main() 