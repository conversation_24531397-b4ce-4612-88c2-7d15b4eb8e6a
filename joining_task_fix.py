#!/usr/bin/env python3
"""
EMERGENCY FIX: Resolves critical issues with the joining task system in TG Checker
- Fixes crashes when continuing joining tasks
- Corrects progress tracking and "already completed" false positives
- Improves memory management and UI responsiveness
- <PERSON>perly handles already-joined groups
"""

import os
import re
import shutil
import time
import sqlite3
from datetime import datetime

def backup_main_file():
    """Create a backup of the main.py file before modifications."""
    backup_file = f"main.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    try:
        shutil.copy("main.py", backup_file)
        print(f"✅ Created backup at: {backup_file}")
        return True
    except Exception as e:
        print(f"⚠️ Warning: Could not create backup: {str(e)}")
        return False

def fix_continue_all_joining_tasks():
    """Fix the continue_all_joining_tasks function that's causing crashes."""
    with open("main.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    # Fix 1: Correct the indentation in the continue_all_joining_tasks function
    # The code has a logical error where the loop over tasks is outside the loop body
    pattern = r"def continue_all_joining_tasks\(self\):[^#]*?# 🔧 SMART RESUME DETECTION: Find all incomplete tasks[^\n]*\n\s+resumable_tasks = \[\]\n\s+for task_id in self\.joining_tasks:\n\s+task = self\.joining_tasks\[task_id\]\n\s+\n\s+# Parse group links"
    replacement = """def continue_all_joining_tasks(self):
        \"\"\"🚀 CRASH FIX: Continue all paused joining tasks with staggered resume logic.\"\"\"
        try:
            # 🔧 SMART RESUME DETECTION: Find all incomplete tasks (even if marked "completed")
            resumable_tasks = []
            for task_id in self.joining_tasks:
                task = self.joining_tasks[task_id]
                
                # Parse group links properly to get actual total"""
    
    fixed_content = re.sub(pattern, replacement, content)
    
    # Fix 2: Correct the _run_joining_task_async function to properly handle task completion status
    pattern = r"# 🎯 FIXED COMPLETION LOGIC: Only mark as completed if ALL groups were processed[^\n]*\n\s+total_groups = len\(group_links\)[^\n]*\n\s+final_index = current_index[^\n]*\n\s+\n\s+if final_index >= total_groups:[^}]*?self\.joining_log_signal\.emit\(\"info\", task_id,[^\n]*?\n\s+\)"
    replacement = """# 🎯 FIXED COMPLETION LOGIC: Only mark as completed if ALL groups were processed
            total_groups = len(group_links)
            final_index = current_index  # This should be the last processed index
            
            # Calculate actual progress (only count processed groups, not skipped ones)
            actual_processed = successful_joins + failed_joins
            
            if final_index >= total_groups:
                # Only mark as completed if we've actually joined or attempted all groups
                # This prevents false "completed" status when many groups are skipped
                if actual_processed >= (total_groups - already_joined_count):
                    self.joining_progress_signal.emit(
                        task_id, "completed", actual_processed, successful_joins, failed_joins
                    )
                    self.joining_log_signal.emit("success", task_id, 
                        f"✅ Task 100% completed! Processed ALL {total_groups} groups | "
                        f"Joined: {successful_joins} | Failed: {failed_joins} | "
                        f"Already joined (skipped): {already_joined_count}")
                else:
                    # Some groups were skipped but not properly accounted for
                    self.joining_progress_signal.emit(
                        task_id, "paused", actual_processed, successful_joins, failed_joins
                    )
                    self.joining_log_signal.emit("warning", task_id, 
                        f"⚠️ Task needs review - index at {final_index}/{total_groups} but only "
                        f"{actual_processed} groups were processed | "
                        f"Joined: {successful_joins} | Failed: {failed_joins} | "
                        f"Already joined (skipped): {already_joined_count}")
            else:
                # Task was interrupted before completion - mark as paused 
                self.joining_progress_signal.emit(
                    task_id, "paused", actual_processed, successful_joins, failed_joins
                )
                self.joining_log_signal.emit("info", task_id, 
                    f"⏸️ Task paused at {final_index}/{total_groups} groups | "
                    f"Joined: {successful_joins} | Failed: {failed_joins} | "
                    f"Can be resumed from group #{final_index + 1}")
            
            self.joining_log_signal.emit("info", task_id,
                f"📈 Summary: {final_index}/{total_groups} processed "
                f"({((final_index/total_groups)*100):.1f}% complete)")"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # Fix 3: Improve the start_joining_task function to properly handle resuming tasks
    pattern = r"def start_joining_task\(self, task_id\):[^#]*?# ALWAYS reset task progress when manually starting[^\n]*\n\s+self\.logger\.info[^\n]*\n\s+self\.log_joining_message[^\n]*\n\s+self\.reset_joining_task_progress\(task_id\)"
    replacement = """def start_joining_task(self, task_id):
        \"\"\"Start a specific joining task with high-performance system.\"\"\"
        try:
            if task_id not in self.joining_tasks:
                self.log_joining_message("error", task_id, "Task not found")
                return
            
            # Check if we're continuing a task or starting fresh
            task = self.joining_tasks[task_id]
            is_continuing = False
            
            # Only reset progress if:
            # 1. Task is marked as completed but has incomplete progress
            # 2. Task is being started fresh (not resumed)
            # 3. Task is explicitly being restarted
            
            # Get actual progress
            import json
            try:
                group_links = json.loads(task['group_links']) if isinstance(task['group_links'], str) else task['group_links']
                total_groups = len(group_links)
                current_index = task['current_index']
                
                # Detect if we're continuing a task
                if (task['status'] == 'paused' or task['status'] == 'running') and current_index > 0 and current_index < total_groups:
                    is_continuing = True
                    self.logger.info(f"Continuing joining task from index {current_index}/{total_groups}: {task_id}")
                    self.log_joining_message("info", task_id, f"🔄 Continuing from group #{current_index+1} ({current_index}/{total_groups})")
                else:
                    # Reset task progress for fresh start
                    self.logger.info(f"Resetting joining task progress for fresh start: {task_id}")
                    self.log_joining_message("info", task_id, "🔄 Resetting progress counters for clean start")
                    self.reset_joining_task_progress(task_id)
            except:
                # If any error in parsing, default to reset for safety
                self.logger.info(f"Resetting joining task progress (parse error): {task_id}")
                self.log_joining_message("info", task_id, "🔄 Resetting progress counters (parse error)")
                self.reset_joining_task_progress(task_id)"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # Fix 4: Improve _is_already_member to better detect already joined groups
    pattern = r"async def _is_already_member\(self, client, group_link, memberships\):[^}]*?return False[^}]*?return False"
    replacement = """async def _is_already_member(self, client, group_link, memberships):
        \"\"\"Check if already a member of the group before attempting to join.\"\"\"
        try:
            # Normalize the group link for comparison
            normalized_link = group_link.strip()
            
            # Direct membership check
            if normalized_link in memberships:
                return True
            
            # Extract username/identifier for checking
            if normalized_link.startswith('https://t.me/'):
                parts = normalized_link.split('/')
                username = parts[-1]
                
                # Check various formats
                if f"@{username}" in memberships:
                    return True
                if f"https://t.me/{username}" in memberships:
                    return True
                
                # Check if it's a joinchat link (can't pre-check these)
                if len(parts) >= 4 and parts[3] == 'joinchat':
                    return False
                
            elif normalized_link.startswith('@'):
                username = normalized_link[1:]
                if normalized_link in memberships:
                    return True
                if f"https://t.me/{username}" in memberships:
                    return True
            
            # Try to get entity info for more precise checking
            try:
                if normalized_link.startswith('https://t.me/joinchat/'):
                    # Can't pre-check invite links without joining
                    return False
                elif normalized_link.startswith('https://t.me/+'):
                    # Private invite link - can't pre-check
                    return False
                elif normalized_link.startswith('https://t.me/'):
                    username = normalized_link.split('/')[-1]
                    if username.startswith('+'):
                        # Private invite link - can't pre-check
                        return False
                    else:
                        entity = await client.get_entity(username)
                elif normalized_link.startswith('@'):
                    entity = await client.get_entity(normalized_link[1:])
                else:
                    # Try direct entity resolution as fallback
                    try:
                        entity = await client.get_entity(normalized_link)
                    except:
                        return False
                
                # Check if entity ID is in memberships
                if str(entity.id) in memberships:
                    return True
                    
                # Check if entity title is in memberships
                if hasattr(entity, 'title') and entity.title in memberships:
                    return True
                    
            except Exception:
                # If we can't get entity info, assume not a member
                pass
            
            return False
            
        except Exception as e:
            # If checking fails, assume not a member to be safe
            return False"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # Fix 5: Improve _run_joining_task_async to better handle already joined groups
    pattern = r"# Pre-check if already a member to avoid unnecessary join attempts[^\n]*\n\s+if await self\._is_already_member\(client, group_link, current_memberships\):[^\n]*\n\s+already_joined_count \+= 1[^\n]*\n\s+self\.joining_log_signal\.emit[^\n]*\n\s+continue"
    replacement = """# Pre-check if already a member to avoid unnecessary join attempts
                if await self._is_already_member(client, group_link, current_memberships):
                    already_joined_count += 1
                    self.joining_log_signal.emit("info", task_id, 
                        f"⏭️ Skipping (already joined): {group_link}")
                    
                    # IMPORTANT: Update progress counters but don't increment current_index
                    # This ensures we don't count skipped groups in the progress
                    self.joining_progress_signal.emit(
                        task_id, "running", current_index, successful_joins, failed_joins
                    )
                    continue"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # Fix 6: Fix the _should_skip_joining_task_resume function to prevent false "already completed" messages
    pattern = r"def _should_skip_joining_task_resume\(self, task_id, task\):[^}]*?# 🛡️ SAFETY DEFAULT: Never skip when uncertain[^\n]*\n\s+self\.log_joining_message[^\n]*\n\s+return False"
    replacement = """def _should_skip_joining_task_resume(self, task_id, task):
        \"\"\"🔍 ULTRA-CONSERVATIVE SKIP: Never skip unless absolutely certain task is 100% done.\"\"\"
        try:
            # ❌ NEVER skip running tasks
            if task.get('status') == 'running':
                self.log_joining_message("info", task_id, "🔄 Continuing - task is running")
                return False
            
            # Parse group links properly to get actual total
            try:
                import json
                if isinstance(task.get('group_links'), str):
                    try:
                        target_groups = json.loads(task['group_links'])
                        if not isinstance(target_groups, list):
                            target_groups = [str(task['group_links']).strip()]
                    except (json.JSONDecodeError, ValueError):
                        target_groups = task.get('group_links', '').split('\\n')
                else:
                    target_groups = task.get('group_links', [])
            except:
                target_groups = task.get('group_links', [])
            
            # Clean empty entries
            target_groups = [g.strip() for g in target_groups if g and str(g).strip()] if target_groups else []
            total_groups = len(target_groups)
            
            # Get actual progress
            current_index = task.get('current_index', 0)
            successful_joins = task.get('successful_joins', 0)
            failed_joins = task.get('failed_joins', 0)
            actual_processed = successful_joins + failed_joins
            
            # NEVER skip if we haven't processed all groups
            if current_index < total_groups:
                self.log_joining_message("info", task_id, 
                    f"🔄 Resuming incomplete task: {current_index}/{total_groups} groups")
                return False
            
            # NEVER skip if status is explicitly 'paused'
            if task.get('status') == 'paused':
                self.log_joining_message("info", task_id, "🔄 Resuming explicitly paused task")
                return False
                
            # Only skip if genuinely 100% completed
            if (task.get('status') == 'completed' and 
                current_index >= total_groups and 
                actual_processed >= total_groups):
                self.log_joining_message("info", task_id, 
                    f"✅ Verified complete - skipping ({actual_processed}/{total_groups} groups processed)")
                return True
            
            # Skip if no groups to process
            if total_groups == 0:
                self.log_joining_message("warning", task_id, "⚠️ Skipping - no groups found")
                return True
                
            # Safety default: Never skip when uncertain
            self.log_joining_message("info", task_id, 
                f"🔄 Safety default - resuming (status: {task.get('status')}, processed: {actual_processed}/{total_groups})")
            return False
                
        except Exception as e:
            self.log_joining_message("error", task_id, f"⚠️ Skip check error: {e} - RESUMING for safety")
            # If ANY error in checking, NEVER skip - always safer to resume
            return False"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    # Fix 7: Add memory management to prevent app freezes
    pattern = r"async def _run_joining_task_async\(self, task\):[^}]*?# Process each group"
    replacement = """async def _run_joining_task_async(self, task):
        \"\"\"Run joining task asynchronously with improved memory management.\"\"\"
        task_id = task['id']
        account_phone = task['account_phone']
        
        try:
            # Get Telegram client (WITHOUT auto-reply setup)
            client = await self._get_joining_client(account_phone)
            if not client:
                self.joining_log_signal.emit("error", task_id, f"Failed to get client for {account_phone}")
                return
            
            # Parse group links
            import json
            group_links = json.loads(task['group_links']) if isinstance(task['group_links'], str) else task['group_links']
            
            current_index = task['current_index']
            successful_joins = task['successful_joins']
            failed_joins = task['failed_joins']
            
            # Create account folder
            self.create_account_joining_folder(account_phone)
            
            # Memory optimization: Clear Python's internal memory cache periodically
            import gc
            gc.collect()
            
            # Pre-check current memberships to avoid duplicate joins
            self.joining_log_signal.emit("info", task_id, "🔍 Checking current memberships...")
            current_memberships = await self._get_current_memberships(client)
            already_joined_count = 0
            
            self.joining_log_signal.emit("info", task_id, 
                f"📊 Found {len(current_memberships)} current memberships for duplicate detection")
                
            # Process each group"""
    
    fixed_content = re.sub(pattern, replacement, fixed_content)
    
    with open("main.py", "w", encoding="utf-8") as f:
        f.write(fixed_content)
    
    print("✅ Fixed continue_all_joining_tasks function")
    print("✅ Improved task completion status tracking")
    print("✅ Enhanced already-joined group detection")
    print("✅ Fixed progress counter issues")
    print("✅ Added memory optimization to prevent freezes")

def main():
    print("🚨 EMERGENCY FIX: Resolving joining task crashes and progress issues...")
    
    # Create backup
    if not backup_main_file():
        if input("Continue without backup? (y/n): ").lower() != 'y':
            return
    
    # Apply fixes
    fix_continue_all_joining_tasks()
    
    print("\n✅ All fixes applied successfully!")
    print("You can now restart the TG Checker application.")

if __name__ == "__main__":
    main() 