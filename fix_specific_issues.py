import re
import os
import sys
import shutil

def fix_specific_issues(filename):
    """Fix specific issues identified in the error messages."""
    # Create a backup
    backup_file = f"{filename}.bak"
    if not os.path.exists(backup_file):
        shutil.copy2(filename, backup_file)
        print(f"Created backup at {backup_file}")
    
    # Read the content
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Fix each specific issue
    content = fix_line_576(content)  # Expected indented block
    content = fix_line_2676(content)  # for statement indentation error
    content = fix_line_2981(content)  # self_outer not defined
    content = fix_line_3013_3014(content)  # Expected expression and Unexpected indentation
    content = fix_line_3062(content)  # Try statement must have at least one except or finally clause
    content = fix_line_3073_3075(content)  # Expected expression and undefined 'e'
    content = fix_line_3086(content)  # Expected expression
    
    # Save to a new file
    fixed_file = "main_specific_fixed.py"
    with open(fixed_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Fixed specific issues and saved to {fixed_file}")
    return fixed_file

def fix_line_576(content):
    """Fix indentation issue around line 576."""
    # Find the start of the problematic area
    start_pos = find_line_position(content, 576)
    if start_pos == -1:
        print("Could not find line 576")
        return content
    
    # Extract context (few lines before and after)
    context_start = content.rfind("\n", 0, start_pos-100)
    context_end = content.find("\n", start_pos+100)
    if context_start == -1:
        context_start = 0
    if context_end == -1:
        context_end = len(content)
    
    context = content[context_start:context_end]
    
    # Look for a for/if/while statement that's missing an indented block
    pattern = r"(\s*)(for|if|while|def)[^:]*:\s*\n\s*([^\s])"
    match = re.search(pattern, context)
    
    if match:
        indentation = match.group(1)
        statement = match.group(2)
        next_line = match.group(3)
        
        # Fix by adding proper indentation to the next line
        fixed_context = re.sub(
            pattern, 
            f"\\1\\2\\3:\n{indentation}    {next_line}", 
            context
        )
        
        # Replace in the original content
        return content.replace(context, fixed_context)
    
    print("Could not find indentation issue around line 576")
    return content

def fix_line_2676(content):
    """Fix the indentation error after 'for' statement on line 2676."""
    # Find line 2676
    line_pos = find_line_position(content, 2676)
    if line_pos == -1:
        print("Could not find line 2676")
        return content
    
    # Get the line and the next line
    line_end = content.find('\n', line_pos)
    next_line_start = line_end + 1
    next_line_end = content.find('\n', next_line_start)
    
    if line_end == -1 or next_line_start == -1 or next_line_end == -1:
        print("Could not parse lines around 2676")
        return content
    
    for_line = content[line_pos:line_end]
    next_line = content[next_line_start:next_line_end]
    
    # Check if the line is a for statement
    if "for" in for_line and for_line.strip().endswith(":"):
        # Calculate indentation
        indentation = re.match(r"(\s*)", for_line).group(1)
        expected_indent = indentation + "    "
        
        # If next line doesn't have proper indentation, fix it
        if not next_line.startswith(expected_indent):
            # Strip existing indentation and add proper indentation
            stripped_next_line = next_line.lstrip()
            fixed_next_line = expected_indent + stripped_next_line
            
            # Replace in content
            return content.replace(for_line + "\n" + next_line, for_line + "\n" + fixed_next_line)
    
    print("Could not find indentation issue around line 2676-2677")
    return content

def fix_line_2981(content):
    """Fix undefined self_outer variable on line 2981."""
    # Find line 2981
    line_pos = find_line_position(content, 2981)
    if line_pos == -1:
        print("Could not find line 2981")
        return content
    
    # Get the line
    line_end = content.find('\n', line_pos)
    if line_end == -1:
        line_end = len(content)
    
    line = content[line_pos:line_end]
    
    # Replace self_outer with self
    if "self_outer" in line:
        fixed_line = line.replace("self_outer", "self")
        return content.replace(line, fixed_line)
    
    print("Could not find self_outer reference on line 2981")
    return content

def fix_line_3013_3014(content):
    """Fix indentation issues around lines 3013-3014."""
    # Find line 3013
    line_pos = find_line_position(content, 3013)
    if line_pos == -1:
        print("Could not find line 3013")
        return content
    
    # Extract context (few lines before and after)
    context_start = content.rfind("\n", 0, line_pos-100)
    context_end = content.find("\n", line_pos+100)
    if context_start == -1:
        context_start = 0
    if context_end == -1:
        context_end = len(content)
    
    context = content[context_start:context_end]
    
    # Identify method definition and fix indentation
    method_pattern = r"(\s*)def\s+([^(]+)\([^)]*\):\s*\n((?:[^\n]*\n)*?)(\s*)(else|elif|except|finally)([^\n]*)"
    match = re.search(method_pattern, context)
    
    if match:
        indent = match.group(1)
        method_name = match.group(2)
        body = match.group(3)
        next_indent = match.group(4)
        next_keyword = match.group(5)
        next_rest = match.group(6)
        
        # If else/except/finally is not properly indented, fix it
        if len(next_indent) < len(indent) + 4:
            fixed_context = context.replace(
                f"{next_indent}{next_keyword}{next_rest}",
                f"{indent}    {next_keyword}{next_rest}"
            )
            return content.replace(context, fixed_context)
    
    # If method pattern not found, try to fix any misaligned else/except blocks
    misaligned_pattern = r"(\n)(\s*)(else|elif|except|finally)([^\n]*)\n(\s+)"
    fixed_context = re.sub(misaligned_pattern, lambda m: 
                          f"{m.group(1)}{' ' * (len(m.group(5)) - 4)}{m.group(3)}{m.group(4)}\n{m.group(5)}", 
                          context)
    
    if fixed_context != context:
        return content.replace(context, fixed_context)
    
    print("Could not fix indentation issues around lines 3013-3014")
    return content

def fix_line_3062(content):
    """Fix try statement without except/finally around line 3062."""
    # Find line 3062
    line_pos = find_line_position(content, 3062)
    if line_pos == -1:
        print("Could not find line 3062")
        return content
    
    # Extract context (few lines before and after)
    context_start = content.rfind("\n", 0, line_pos-100)
    context_end = content.find("\n", line_pos+200)  # Look further ahead to find the end of the try block
    if context_start == -1:
        context_start = 0
    if context_end == -1:
        context_end = len(content)
    
    context = content[context_start:context_end]
    
    # Find the try statement
    try_pattern = r"(\s+)try:\s*\n((?:.+\n)+?)(?!\1\s+except|\1\s+finally)"
    match = re.search(try_pattern, context)
    
    if match:
        indent = match.group(1)
        try_body = match.group(2)
        
        # Add an except block
        fixed_context = context.replace(
            f"{indent}try:\n{try_body}",
            f"{indent}try:\n{try_body}{indent}except Exception as e:\n{indent}    self.log_activity(f\"Error refreshing account info: {{str(e)}}\")\n"
        )
        
        return content.replace(context, fixed_context)
    
    print("Could not fix try statement around line 3062")
    return content

def fix_line_3073_3075(content):
    """Fix indentation and undefined 'e' variable around lines 3073-3075."""
    # Find line 3073
    line_pos = find_line_position(content, 3073)
    if line_pos == -1:
        print("Could not find line 3073")
        return content
    
    # Extract context (few lines before and after)
    context_start = content.rfind("\n", 0, line_pos-100)
    context_end = content.find("\n", line_pos+100)
    if context_start == -1:
        context_start = 0
    if context_end == -1:
        context_end = len(content)
    
    context = content[context_start:context_end]
    
    # Find method definition containing this line
    method_pattern = r"(\s+)def\s+([^(]+)\([^)]*\):\s*\n"
    method_match = re.search(method_pattern, context)
    
    if method_match:
        method_indent = method_match.group(1)
        
        # Look for any unindented lines that should be indented
        lines = context.split('\n')
        fixed_lines = []
        
        # Find the expected indentation from surrounding lines
        expected_indent = None
        for i, line in enumerate(lines):
            if i > 0 and line.strip() and not line.startswith(method_indent + "    "):
                # This line is not properly indented
                if expected_indent is None:
                    # Look at previous lines to determine expected indentation
                    for prev_line in reversed(lines[:i]):
                        if prev_line.startswith(method_indent + "    "):
                            expected_indent = method_indent + "    "
                            break
                    if expected_indent is None:
                        expected_indent = method_indent + "    "
                
                # Fix indentation if needed
                if not line.startswith(expected_indent) and line.strip():
                    # Fix undefined 'e' if present
                    if 'str(e)' in line and 'except' not in '\n'.join(lines[:i]):
                        line = line.replace('str(e)', 'str(error)')
                    # Fix indentation
                    line = expected_indent + line.lstrip()
            
            fixed_lines.append(line)
        
        fixed_context = '\n'.join(fixed_lines)
        
        if fixed_context != context:
            return content.replace(context, fixed_context)
    
    print("Could not fix indentation issues around lines 3073-3075")
    return content

def fix_line_3086(content):
    """Fix expected expression issue around line 3086."""
    # Find line 3086
    line_pos = find_line_position(content, 3086)
    if line_pos == -1:
        print("Could not find line 3086")
        return content
    
    # Get the line
    line_end = content.find('\n', line_pos)
    if line_end == -1:
        line_end = len(content)
    
    line = content[line_pos:line_end]
    
    # Look for a syntax error like an incomplete if/else
    if line.strip().startswith("else") and not line.strip().endswith(":"):
        fixed_line = line.rstrip() + ":"
        return content.replace(line, fixed_line)
    
    # Look for incomplete expressions
    incomplete_expr_pattern = r"(\s*)(if|elif|else|while|for|try|except|finally)([^:]*[^\s:])(\s*)$"
    match = re.search(incomplete_expr_pattern, line)
    
    if match:
        indent = match.group(1)
        keyword = match.group(2)
        expr = match.group(3)
        trailing = match.group(4)
        
        # Add missing colon
        fixed_line = f"{indent}{keyword}{expr}:{trailing}"
        return content.replace(line, fixed_line)
    
    print("Could not fix expected expression issue around line 3086")
    return content

def find_line_position(content, line_number):
    """Find the position of the given line number in the content."""
    lines = content.split('\n')
    if line_number <= len(lines):
        pos = 0
        for i in range(line_number - 1):
            pos = content.find('\n', pos) + 1
        return pos
    return -1

if __name__ == "__main__":
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = "main_comprehensive_fixed.py"
    
    # Check if the file exists
    if not os.path.exists(filename):
        print(f"File {filename} not found. Trying main.py instead.")
        filename = "main.py"
    
    fixed_file = fix_specific_issues(filename)
    
    # Create a batch file to run the fixed application
    batch_content = """@echo off
echo Running fixed TG Checker application with specific fixes...
python main_specific_fixed.py
pause
"""
    
    with open("run_specific_fixed_app.bat", "w") as f:
        f.write(batch_content)
    
    print("Created run_specific_fixed_app.bat to run the fixed application.")
    print(f"You can now run the fixed file: python {fixed_file} or use the batch file.") 